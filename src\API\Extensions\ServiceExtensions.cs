using Amazon.S3;
using API.Contracts;
using API.Controllers.Analytics;
using API.Helpers;
using API.Providers;
using Audit.AuditMapper;
using Audit.Services;
using CLO.CQRS.Handlers;
using CLO.Services;
using CLO.Services.interfaces;
using Contract.Account;
using Contract.AccountType;
using Contract.Audit;
using Contract.CashFlow;
using Contract.City;
using Contract.Country;
using Contract.Currency;
using Contract.Deals;
using Contract.Designation;
using Contract.EmailTemplate;
using Contract.Filters;
using Contract.Firm;
using Contract.Funds;
using Contract.Groups;
using Contract.Investor;
using Contract.KPI;
using Contract.Master;
using Contract.MasterMapping;
using Contract.PortfolioCompany;
using Contract.Region;
using Contract.Reports;
using Contract.Sector;
using Contract.State;
using Contract.StockExchange;
using Contract.Strategy;
using Contract.Utility;
using Contract.Workflow;
using CurrencyRates;
using CurrencyRates.Interfaces;
using CurrencyRates.Services;
using DapperRepository;
using DataAccessLayer.DBModel;
using DataAccessLayer.UnitOfWork;
using DataAnalytic.Services;
using DataCollection.Contracts;
using DataCollection.Mappers;
using DataCollection.Services;
using DinkToPdf;
using DinkToPdf.Contracts;
using DocumentCollection.DashboardTracker.Interfaces;
using DocumentCollection.DashboardTracker.Services;
using DocumentCollection.Interfaces;
using DocumentCollection.Services;
using EmailConfiguration;
using EmailConfiguration.Interfaces;
using EmailConfiguration.Services;
using EmailNotification.Services;
using ESG.DtoProfiles;
using Exports.Helpers;
using Exports.Services;
using Financials.Services;
using FormulaCalculator;
using Fund;
using Fund.Services;
using Funds;
using Imports.Documents;
using Master;
using Master.DtoProfiles;
using MediatR;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Notification;
using Notification.Email;
using Notification.Helpers;
using OfficeOpenXml;
using PortfolioCompany;
using PortfolioCompany.Interfaces;
using PortfolioCompany.Services;
using PortfolioCompany.Services.SDG;
using Report.AutoMapper;
using Report.ConsolidatedReport;
using Report.Download;
using Report.GrowthReport;
using Report.InternalReport;
using Report.LpReport.Services;
using ReportService;
using Repository;
using Reveal.Sdk;
using S3FileLayer;
using Serilog;
using Swashbuckle.AspNetCore.SwaggerGen;
using Swashbuckle.AspNetCore.SwaggerUI;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics.CodeAnalysis;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using Utility.Helpers;
using Utility.Services;
using Workflow;
using Workflow.Interface;
using Workflow.Interface.Draft;
using Workflow.ProfilesDto;
using Workflow.Services;
using Workflow.Services.Draft;
using ServiceConfiguration = S3FileLayer.S3ServiceConfiguration;

namespace API.Extensions
{
    [ExcludeFromCodeCoverage]
    public static partial class ServiceExtensions
    {
        public static IServiceCollection AddFSServices(this IServiceCollection services, IConfiguration Configuration)
        {
            IdentityServerConfig identityServerConfiguration = Configuration.GetSection(nameof(IdentityServerConfig)).Get<IdentityServerConfig>();
            services.AddHttpClient();
            services.AddHsts(options =>
            {
                options.IncludeSubDomains = true;
                options.MaxAge = TimeSpan.FromDays(365);
            });
            ///AllowCredentials removed
            ///it will allow the browser to send cookies and other credentials with cross-origin requests. This can be useful if you need to access user data from a different domain, but it is a security risk.
            ///A website at another domain could send a signed-in user's credentials to our app on the user's behalf without the user's knowledge
            services.AddCors(options =>
            {
                options.AddPolicy("CorsPolicy",
                builder =>
                {
                    var allowedOrigins = Configuration.GetValue<string>("AllowedOrigins")?.Split(',');
                    builder.WithOrigins(allowedOrigins).SetIsOriginAllowedToAllowWildcardSubdomains().AllowAnyMethod().AllowAnyHeader().AllowCredentials();
                });
            });
            services.AddSignalR();
            //services.AddControllers(options =>
            //{
            //    options.Filters.Add(new IgnoreAntiforgeryTokenAttribute());//NOSONAR
            //});
            var tokenConfigurations = new TokenConfigurations();
            new ConfigureFromConfigurationOptions<TokenConfigurations>(
                    Configuration.GetSection("TokenConfigurations"))
                .Configure(tokenConfigurations);

            var currencyApiConfigurations = new CurrencyApiConfigurations();
            new ConfigureFromConfigurationOptions<CurrencyApiConfigurations>(
                    Configuration.GetSection("CurrencyApiConfigurations"))
                .Configure(currencyApiConfigurations);

            var signingConfigurations = new SigningConfigurations();
            services.AddSingleton(signingConfigurations);
            AddMapper(services);

            try
            {
                var key = Encoding.UTF8.GetBytes(tokenConfigurations.SecretKey);
                services.AddSingleton(tokenConfigurations);
                services.AddSingleton(currencyApiConfigurations);

                var tokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = false,

                    ValidateAudience = false,

                    ValidateLifetime = true,
                    ClockSkew = TimeSpan.Zero,
                    RequireExpirationTime = true,
                };

                services.AddAuthentication(authOptions =>
                {
                    authOptions.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                    authOptions.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
                    authOptions.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;

                })
                    .AddJwtBearer("identityserver", async bearerOptions =>
                    {
                        bearerOptions.Authority = identityServerConfiguration.Issuer;
                        bearerOptions.TokenValidationParameters = new TokenValidationParameters
                        {
                            ValidateIssuer = true,
                            ValidIssuer = identityServerConfiguration.Issuer,
                            ValidateAudience = true,
                            ValidAudience = identityServerConfiguration.Audience,
                            ValidateLifetime = true,
                            ValidateIssuerSigningKey = true,
                            ClockSkew = TimeSpan.Zero,
                            IssuerSigningKey = await TokenSignatureExtension.GetJwk(Configuration)
                        };
                        bearerOptions.Events = new JwtBearerEvents
                        {
                            OnMessageReceived = context =>
                            {
                                var accessToken = context.Request.Query["access_token"];

                                // If the request is for our hub...
                                var path = context.HttpContext.Request.Path;
                                if (!string.IsNullOrEmpty(accessToken) &&
                                    (path.ToString().Contains("/notify") || path.ToString().Contains("/file-upload-notification")))
                                {
                                    // Read the token out of the query string
                                    context.Token = accessToken;
                                }
                                return Task.CompletedTask;
                            }
                        };
                    })
                    .AddJwtBearer("default", bearerOptions =>
                    {
                        bearerOptions.Authority = identityServerConfiguration.Issuer;
                        bearerOptions.RequireHttpsMetadata = false;
                        bearerOptions.TokenValidationParameters = tokenValidationParameters;
                        bearerOptions.SaveToken = false;
                    });
            }
            catch (Exception exception)
            {
                Log.Logger.Error(exception, "AddAuthentication:Exception: {Message}", exception.Message);
            }
            var ConnectionString = Configuration.GetConnectionString("DefaultDBConnection");
            string connection = AwsSecretsManagerHelper.UpdateConnectionString(ConnectionString, Configuration);
            services.AddDbContext<DBEntities>(options => options.UseSqlServer(connection, op => op.CommandTimeout(3600)))
               .AddHealthChecks().AddSqlServer(connection);
            services.AddScoped<IDbConnectionFactory>((sp) => new DbConnectionFactory(connection));
            AddPDFExportDependency(services);
            services.AddApiVersioning(config =>
            {
                config.DefaultApiVersion = new ApiVersion(1, 0);
                config.AssumeDefaultVersionWhenUnspecified = true;
                config.ReportApiVersions = true;
            });
            AddSwagger(services);
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

            var appSettingsSection = Configuration.GetSection("ServiceConfiguration");
            services.AddAWSService<IAmazonS3>();
            services.Configure<ServiceConfiguration>(appSettingsSection);
            services.Configure<S3ServiceConfiguration>(appSettingsSection);

            return services;
        }

        public static IServiceCollection AddServiceDependancy(this IServiceCollection services, IConfiguration Configuration)
        {
            services.AddTransient<IS3FileUploadService, S3FileUploadService>();
            services.AddSingleton<IMemoryCacher, MemoryCacher>();
            services.AddTransient<IAccountService, Account.AccountService>();
            services.AddTransient<ICountryService, CountryService>();
            services.AddTransient<IGroupService, Groups.GroupService>();
            services.AddTransient<IEmailTemplateService, SendEmailService>();
            services.AddTransient<IAuditService, Audit.AuditService>();
            services.AddTransient<IPortfolioCompanyService, PortfolioCompanyService>();
            services.AddTransient<ICommentaryService, CommentaryService>();
            services.AddTransient<ICashFlowService, CashFlow.CashFlowService>();
            services.AddTransient<IDealService, Deals.DealService>();
            services.AddTransient<IMasterKpiService, MasterKpiService>();
            services.AddTransient<ISectorService, SectorService>();
            services.AddTransient<IMasterService, MasterService>();
            services.AddTransient<IFundService, FundService>();
            services.AddTransient<ICurrencyService, CurrencyService>();
            services.AddTransient<IFirmService, Firm.FirmService>();
            services.AddTransient<IRegionService, RegionService>();
            services.AddTransient<IMasterMappingService, MasterMappingService>();
            services.AddTransient<IDesignationService, DesignationService>();
            services.AddTransient<IStrategyService, StrategyService>();
            services.AddTransient<IAccountTypeService, AccountTypeService>();
            services.AddTransient<ICityService, CityService>();
            services.AddTransient<IMasterService, MasterService>();
            services.AddTransient<IStateService, StateService>();
            services.AddTransient<IStockExchangeService, StockExchangeService>();
            services.AddTransient<ISendEmail, SendEmail>();
            services.AddTransient<Contract.Pipeline.IPipelineService, Pipeline.PipelineService>();
            services.AddTransient<Contract.Repository.IDocumentTypeService, DocumentTypeService>();
            services.AddTransient<Contract.Repository.ITagService, TagService>();
            services.AddTransient<Contract.Repository.IFileService, FileService>();
            services.AddTransient<Contract.Repository.IRepositoryService, RepositoryService>();
            services.AddTransient<Contract.Repository.IIndexSerivce, IndexService>();
            services.AddScoped<IUnitOfWork, UnitOfWork>();
            services.AddScoped<IGlobalConfigurations, GlobalConfigurations>();
            services.AddScoped<IEncryption, Encryption>();
            services.AddScoped<IDapperGenericRepository, DapperGenericRepository>();
            services.AddTransient<IInjectedParameters, InjectedParameters>();
            services.AddTransient<IReportService, ReportService.ReportService>();
            services.AddTransient<IKpiService, KpiService>();
            services.AddTransient<Contract.Repository.IDocumentStatusService, DocumentStatusService>();
            services.AddTransient<IHelperService, HelperService>();
            services.AddTransient<INotificationService, NotificationService>();
            services.AddSingleton(Configuration);
            services.AddTransient<ICurrencyRatesApiService, CurrencyRatesApiService>();
            services.AddTransient<IReportPageConfigurationService, ReportPageConfigurationService>();
            services.AddTransient<IReportTemplateService, ReportTemplateService>();
            services.AddTransient<IWorkflowService, WorkflowService>();
            services.AddTransient<IAccessService, AccessService>();
            services.AddTransient<IWorkFlowStatusServices, WorkFlowStatusServices>();
            services.AddTransient<IWorkflowPCService, WorkflowPCService>();
            services.AddTransient<IPageDetailsConfigurationService, PageDetailsConfigurationService>();
            services.AddTransient<IInvestorService, InvestorService>();
            services.AddTransient<IUnStructuredService, UnStructuredService>();
            services.AddTransient<IPageFieldValueDraftService, PageFieldValueDraftService>();
            services.AddTransient<IFootNote, FootNotesService>();
            services.AddTransient<IPcInvestmentKpiDraft, PcInvestmentKpiDraftService>();
            services.AddTransient<IPortfolioCompanyServiceBeta, PortfolioCompanyServiceBeta>();
            services.AddTransient<IInternalReport, InternalReportService>();
            services.AddTransient<IReportDownload, ReportDownloadService>();
            services.AddTransient<IReportDownloadMonthlyTemplate, ReportDownloadMonthlyTemplateService>();
            services.AddTransient<IEmailSender, EmailSender>();
            services.AddTransient<IPcInvestmentKpiServiceBeta, PcInvestmentKpiServiceBeta>();
            services.AddTransient<IConsolidatedReport, ConsolidatedReportService>();
            services.AddTransient<IConsolidatedDownloadService, ConsolidatedDownloadService>();
            services.AddTransient<IMasterKpiDraftService, MasterKpiDraftService>();
            services.AddTransient<IPcKpiDraftService, PcKpiDraftService>();
            services.AddTransient<IPortfolioInvestmentKpiService, PortfolioInvestmentKpiService>();
            services.AddTransient<IGroupList, GroupListService>();
            services.AddTransient<IKpiFormulaBuilder, KpiFormulaBuilder>();
            services.AddTransient<IPCTradingRecordsDraft, PcTradingRecordsDraftService>();
            services.AddTransient<IPortfolioCompanyServiceBetaV1, PortfolioCompanyServiceBetaV1>();
            services.AddTransient<IPcOperationalKpiDraft, PcOperationalKpiDraftService>();
            services.AddTransient<IInternalReportCommon, InternalReportCommonService>();
            services.AddTransient<IPcKPIsService, PcKPIsService>();
            services.AddTransient<IS3FileLayerService, S3FileLayerService>();
            services.AddTransient<IFinancialExport, FinancialExportService>();
            services.AddTransient<IInvestorFundsBulkUploadService, InvestorFundsBulkUploadService>();
            services.AddTransient<IPcKpisUpdateService, PcKpisUpdateService>();
            services.AddTransient<IKpiDownloadService, KpiDownloadService>();
            services.AddTransient<ICommonResult, CommonResultService>();
            services.AddTransient<IPcAnalyticsService, PcAnalyticsService>();
            services.AddTransient<IFinancialAnalyticsService, FinancialAnalyticsService>();
            services.AddTransient<IFundAnalyticService, FundAnalyticService>();
            services.AddTransient<IkpiChartService, KpiChartService>();
            services.AddTransient<ICurrencyRateConversionService, CurrencyRateConversionService>();
            services.AddTransient<IInvestorAnalyticsService, InvestorAnalyticsService>();
            services.AddTransient<IDataAnalytics, DataAnalyticService>();
            services.AddTransient<IStaticDetailsServices, StaticDetailsServices>();
            services.AddTransient<IFofService, FofService>();
            services.AddTransient<IFinancialAudit, FinancialAuditService>();
            services.AddTransient<IFoFImportService, FoFImportService>();
            services.AddTransient<IDealAnalyticServiceV1, DealAnalyticServiceV1>();
            services.AddTransient<IDocumentInformation, DocumentsInformationService>();
            services.AddTransient<IEsgAnalyticService, EsgAnalyticService>();
            services.AddTransient<IPcAuditLog, PcAuditLogService>();
            services.AddTransient<IDocumentsInformationUploadService, DocumentsInformationUploadService>();
            services.AddTransient<IWorkflowDraftCommentary, WorkflowDraftCommentaryService>();
            services.AddTransient<Imports.Kpi.IBulkUpload, Imports.Services.BulkUploadService>();
            services.AddTransient<IKpiSection, KpiSectionService>();
            AddDI(services, Configuration);
            services.AddTransient<IConfigHeaderService, ConfigHeaderService>();
            services.AddTransient<IKpiAuditService, KpiAuditService>();
            services.AddTransient<ICapTable, CapTableService>();
            services.AddTransient<IAnalyticCapTableService, AnalyticCapTableService>();
            services.AddTransient<IDataRequest, DataRequestService>();
            services.AddTransient<ISubFeatureAccessService, SubFeatureAccessService>();
            services.AddTransient<ICurrencyConversionRate, CurrencyConversionRateService>();
            services.AddTransient<ILpReportTemplateConfiguration, LpReportTemplateConfigurationService>();
            services.AddTransient<ILpReportDownload, LpReportDownload>();
            services.AddTransient<IGrowthReport, GrowthReportService>();
            services.AddTransient<ISDGImageService, SDGImageService>();
            services.AddTransient<IGrowthReportDownload, GrowthReportDownloadService>();
            services.AddTransient<IDataExtraction, DataExtractionService>();
            services.AddTransient<IRepositoryConfigurationService, RepositoryConfigService>();
            services.AddTransient<IDocumentRepositoryService, DocumentRepositoryService>();
            services.AddTransient<IDataIngestionService, DataIngestionService>();
            services.AddTransient<IFundKpiService, FundKpiService>();
            services.AddTransient<IUserInformationService, UserInformationService>();
            services.AddTransient<ICategoryService, CategoryService>();
            services.AddTransient<IEmailGroupService, EmailGroupService>();
            services.AddTransient<IEmailRemainderService, EmailRemainderService>();
            services.AddTransient<IEmailReminderBackgroundService, EmailReminderBackgroundService>();
            services.AddTransient<IDashboardTrackerService, DashboardTrackerService>();
            services.AddTransient<IDashboardTrackerRepository, DashboardTrackerRepository>();

            AddEmailConfiguration(services, Configuration);
            return services;
        }

        /// <summary>
        /// Adds the necessary service dependencies for the CLO Module service to the IServiceCollection.
        /// </summary>
        /// <param name="services">The IServiceCollection to which the dependencies will be added.</param>
        /// <param name="Configuration">The application configuration settings.</param>
        /// <returns>The updated IServiceCollection with the added dependencies.</returns>
        public static IServiceCollection AddCLOServiceDependancy(this IServiceCollection services, IConfiguration Configuration)
        {
            services.AddTransient<IInvestmentCompanyService, InvestmentCompanyService>();
            services.AddTransient<ICLOService, CLOService>();
            services.AddTransient<IExcelService, ExcelService>();
            services.AddTransient<ITableMetadataService, TableMetadataService>();

            //CQRS Pattern
            services.AddMediatR(typeof(GetTableDataQueryHandler).Assembly);
            //services.AddMediatR(typeof(DownloadTemplateQueryHandler).Assembly);
            return services;
        }
        public static IServiceCollection AddPageConfigServiceDependancy(this IServiceCollection services, IConfiguration Configuration)
        {
            services.AddTransient<ICLOPageConfigService, PageConfigService>();
            //CQRS Pattern
            services.AddMediatR(typeof(GetTableDataQueryHandler).Assembly);
            //services.AddMediatR(typeof(DownloadTemplateQueryHandler).Assembly);
            return services;
        }
        public static IServiceCollection AddKPIConfigServiceDependancy(this IServiceCollection services, IConfiguration Configuration)
        {
            services.AddTransient<ICLOKPIConfigService, KPIConfigService>();
            //CQRS Pattern
            services.AddMediatR(typeof(GetTableDataQueryHandler).Assembly);
            //services.AddMediatR(typeof(DownloadTemplateQueryHandler).Assembly);
            return services;
        }
        public static IServiceCollection AddRevealConfiguration(this IServiceCollection services, IConfiguration Configuration)
        {
            var environment = Configuration["ClientCode"];
            services.AddControllers().AddReveal(builder =>
            {
                builder.AddSettings(settings =>
                {
                    settings.License = Configuration.GetValue<string>("RevealBi");
                    settings.LocalFileStoragePath = Configuration.GetValue<string>("ChromiumDownloadPath");
                    settings.Export.CreateChromiumInstancesOnDemand = false;
                   // settings.Export.ChromiumExecutablePath = Configuration.GetValue<string>("ChromiumInstallPath");
                   // settings.Export.ChromiumDownloadFolder = Configuration.GetValue<string>("ChromiumDownloadPath");
                   // settings.Export.MaxConcurrentExportingThreads = 1;
                   // settings.Export.ExportingTimeout = 300000;
                    settings.CachePath = $"Reveal-Cache/{environment}";
                    settings.DataCachePath = "CachePath";
                });
                builder.AddAuthenticationProvider<AuthenticationProvider>();
                builder.AddUserContextProvider<UserContextProvider>();
                builder.AddDashboardProvider<DashboardProvider>();
            });
            return services;
        }

        #region Private Methods

        private static void AddMapper(IServiceCollection services)
        {
            services.AddAutoMapper(Assembly.GetAssembly(typeof(RepositoryProfile)));
            services.AddAutoMapper(Assembly.GetAssembly(typeof(ReportProfile)));
            services.AddAutoMapper(Assembly.GetAssembly(typeof(FundProfile)));
            services.AddAutoMapper(Assembly.GetAssembly(typeof(PageSettingsProfiles)));
            services.AddAutoMapper(Assembly.GetAssembly(typeof(FundInvestorProfile)));
            services.AddAutoMapper(Assembly.GetAssembly(typeof(WorkflowProfiles)));
            services.AddAutoMapper(Assembly.GetAssembly(typeof(FootNoteProfile)));
            services.AddAutoMapper(Assembly.GetAssembly(typeof(WorkflowProfileSetting)));
            services.AddAutoMapper(Assembly.GetAssembly(typeof(GroupListProfile)));
            services.AddAutoMapper(Assembly.GetAssembly(typeof(DataAuditLogProfile)));
            services.AddAutoMapper(Assembly.GetAssembly(typeof(KpiProfile)));
            services.AddAutoMapper(Assembly.GetAssembly(typeof(EsgProfiles)));
            services.AddAutoMapper(Assembly.GetAssembly(typeof(SubPageAnalyticsProfile)));
            services.AddAutoMapper(Assembly.GetAssembly(typeof(InvestorValuationProfile)));
            services.AddAutoMapper(Assembly.GetAssembly(typeof(MappingProfile)));
            services.AddAutoMapper(Assembly.GetAssembly(typeof(DataRequestProfile)));
            services.AddAutoMapper(Assembly.GetAssembly(typeof(MLpReportTemplateProfile)));
            services.AddAutoMapper(Assembly.GetAssembly(typeof(GrowthReportProfile)));
            services.AddAutoMapper(Assembly.GetAssembly(typeof(LpReportCoverProfile)));
            services.AddAutoMapper(Assembly.GetAssembly(typeof(FundKpiProfile)));
            AddAutoMapper(services);
        }
        private static void AddPDFExportDependency(IServiceCollection services)
        {
            var context = new CustomAssemblyLoadContext();
            try
            {
                string _Root = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
                PlatformID PlatformId = Environment.OSVersion.Platform;
                if (PlatformId == PlatformID.Unix)
                    context.LoadUnmanagedLibrary(Path.Combine(_Root, "lib", "libwkhtmltox.dylib"));
                else
                    context.LoadUnmanagedLibrary(Path.Combine(_Root, "lib", "libwkhtmltox.dll"));
            }
            catch (Exception exception)
            {
                Log.Logger.Error(exception, $"AddPDFExportDependency:Exception: {exception.Message}");
            }

            services.AddSingleton(typeof(IConverter), new SynchronizedConverter(new PdfTools()));

        }
        private static void AddSwagger(IServiceCollection services)
        {
            services
                .AddOptions<SwaggerUIOptions>()
                .Configure<IHttpContextAccessor>((swaggerUiOptions, httpContextAccessor) =>
                {
                    // 2. Take a reference of the original Stream factory which reads from Swashbuckle's embedded resources
                    var originalIndexStreamFactory = swaggerUiOptions.IndexStream;

                    // 3. Override the Stream factory
                    swaggerUiOptions.IndexStream = () =>
                    {
                        // 4. Read the original index.html file
                        using var originalStream = originalIndexStreamFactory();
                        using var originalStreamReader = new StreamReader(originalStream);
                        var originalIndexHtmlContents = originalStreamReader.ReadToEnd();

                        // 5. Get the request-specific nonce generated by NetEscapades.AspNetCore.SecurityHeaders
                        var requestSpecificNonce = RandomString(9);

                        // 6. Replace inline `<script>` and `<style>` tags by adding a `nonce` attribute to them
                        var nonceEnabledIndexHtmlContents = originalIndexHtmlContents
                            .Replace("<script>", $"<script nonce=\"{requestSpecificNonce}\">", StringComparison.OrdinalIgnoreCase)
                            .Replace("<style>", $"<style nonce=\"{requestSpecificNonce}\">", StringComparison.OrdinalIgnoreCase);

                        // 7. Return a new Stream that contains our modified contents
                        return new MemoryStream(Encoding.UTF8.GetBytes(nonceEnabledIndexHtmlContents));
                    };
                });
            services.AddSwaggerGen(options =>
            {
                options.SwaggerDoc("v1", new OpenApiInfo { Title = "FolioSure API", Version = "1.0" });
                options.SwaggerDoc("v1.1", new OpenApiInfo { Title = "FolioSure API", Version = "1.0" });

                options.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
                {
                    Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
                    Name = "Authorization",
                    In = ParameterLocation.Header,
                    Type = SecuritySchemeType.ApiKey
                });
                options.AddSecurityRequirement(new OpenApiSecurityRequirement() {
                    {
                        new OpenApiSecurityScheme {
                            Reference = new OpenApiReference {
                                    Type = ReferenceType.SecurityScheme,
                                        Id = "Bearer"
                                },
                                Scheme = "oauth2",
                                Name = "Bearer",
                                In = ParameterLocation.Header,
                        },
                        new List<string> ()
                    }
                });
                options.OperationFilter<RemoveVersionFromParameter>();
                options.DocumentFilter<ReplaceVersionWithExactValueInPath>();
                options.SchemaFilter<SwaggerExcludeFilter>();
            });
        }

        private static string RandomString(int length)
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            byte[] data = new byte[length];
            using (RandomNumberGenerator rng = RandomNumberGenerator.Create())
            {
                rng.GetBytes(data);
            }
            char[] result = new char[length];
            for (int i = 0; i < length; i++)
            {
                result[i] = chars[data[i] % chars.Length];
            }
            return new string(result);
        }
        private class RemoveVersionFromParameter : IOperationFilter
        {
            public void Apply(OpenApiOperation operation, OperationFilterContext context)
            {
                if (operation.Parameters.Any(S => S.Name == "version"))
                {
                    var versionParameter = operation.Parameters.Single(p => p.Name == "version");
                    operation.Parameters.Remove(versionParameter);
                }
            }
        }
        private class ReplaceVersionWithExactValueInPath : IDocumentFilter
        {
            public void Apply(OpenApiDocument swaggerDoc, DocumentFilterContext context)
            {
                var paths = new OpenApiPaths();
                foreach (var path in swaggerDoc.Paths)
                {
                    paths.Add(path.Key.Replace("v{version}", swaggerDoc.Info.Version), path.Value);
                }
                swaggerDoc.Paths = paths;
            }
        }
        public class SwaggerExcludeFilter : ISchemaFilter
        {
            #region ISchemaFilter Members
            public void Apply(OpenApiSchema schema, SchemaFilterContext context)
            {
                if (schema?.Properties?.Count > 0)
                {
                    var excludedProperties = context.Type.GetProperties()
                                                 .Where(t =>
                                                        t.GetCustomAttribute<SwaggerExcludeAttribute>()
                                                        != null);
                    foreach (var excludedProperty in excludedProperties)
                    {
                        var keys = schema.Properties.Where(x => string.Equals(x.Key, excludedProperty.Name, StringComparison.OrdinalIgnoreCase));
                        if (keys?.Any() == true)
                            schema.Properties.Remove(keys.FirstOrDefault().Key);
                    }
                }

            }

            #endregion
        }

        #endregion
        public static void AddEmailConfiguration(IServiceCollection services, IConfiguration configuration)
        {
            var section = configuration.GetSection("EmailConfiguration");
            services.Configure<EmailConfigurationModel>(section);
            var awsSecretsManagerHelper = new EmailAwsSecretsManagerHelper(configuration);
            try
            {
                var mailConfiguration = awsSecretsManagerHelper.GetEmailConfiguration();
                if (mailConfiguration != null && !string.IsNullOrWhiteSpace(mailConfiguration.Host) && !string.IsNullOrWhiteSpace(mailConfiguration.Password) && !string.IsNullOrWhiteSpace(mailConfiguration.UserName))
                    services.AddSingleton(mailConfiguration);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.InnerException);
            }
        }
    }
    public class IdentityServerConfig
    {
        public string Issuer { get; set; }
        public string Audience { get; set; }
        public string IdentityServerClientId { get; set; }
        public string OidcApiName { get; set; }
        public string ApiScope { get; set; }
        public string ApiName { get; set; }
    }
}
