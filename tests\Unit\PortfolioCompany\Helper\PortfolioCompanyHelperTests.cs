﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Moq;
using Xunit;
using PortfolioCompany.Helper;
using Contract.Repository;

namespace PortfolioCompany.UnitTest.Helper
{
    public class PortfolioCompanyHelperTests
    {
        [Fact]
        public async Task GetCompanyLogo_ReturnsNull_WhenImageNameIsNullOrEmpty()
        {
            var fileServiceMock = new Mock<IFileService>();
            var result1 = await PortfolioCompanyHelper.GetCompanyLogo(fileServiceMock.Object, 1, null);
            var result2 = await PortfolioCompanyHelper.GetCompanyLogo(fileServiceMock.Object, 1, "");
            Assert.Null(result1);
            Assert.Null(result2);
        }

        [Fact]
        public async Task GetCompanyLogo_ReturnsNull_WhenFilesIsNullOrEmpty()
        {
            var fileServiceMock = new Mock<IFileService>();
            fileServiceMock.Setup(x => x.GetFiles(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync((List<KeyValuePair<string, string>>)null);
            var result = await PortfolioCompanyHelper.GetCompanyLogo(fileServiceMock.Object, 1, "logo.png");
            Assert.Null(result);

            fileServiceMock.Setup(x => x.GetFiles(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(new List<KeyValuePair<string, string>>());
            result = await PortfolioCompanyHelper.GetCompanyLogo(fileServiceMock.Object, 1, "logo.png");
            Assert.Null(result);
        }

        [Fact]
        public async Task GetCompanyLogo_ReturnsLogoUrl_WhenImageExists()
        {
            var fileServiceMock = new Mock<IFileService>();
            var files = new List<KeyValuePair<string, string>>
            {
                new KeyValuePair<string, string>("logo.png", "url1"),
                new KeyValuePair<string, string>("other.png", "url2")
            };
            fileServiceMock.Setup(x => x.GetFiles(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(files);
            var result = await PortfolioCompanyHelper.GetCompanyLogo(fileServiceMock.Object, 1, "logo.png");
            Assert.Equal("url1", result);
        }

        [Fact]
        public async Task GetCompanyLogo_ReturnsNull_WhenImageDoesNotExist()
        {
            var fileServiceMock = new Mock<IFileService>();
            var files = new List<KeyValuePair<string, string>>
            {
                new KeyValuePair<string, string>("other.png", "url2")
            };
            fileServiceMock.Setup(x => x.GetFiles(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(files);
            var result = await PortfolioCompanyHelper.GetCompanyLogo(fileServiceMock.Object, 1, "logo.png");
            Assert.Null(result);
        }
    }
}
