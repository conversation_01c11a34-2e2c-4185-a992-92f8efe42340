using Amazon.S3;
using Amazon.S3.Model;
using DapperRepository;
using DataAccessLayer.Models.DocumentCollection;
using DataAccessLayer.UnitOfWork;
using DocumentCollection.DTOs;
using DocumentCollection.Helpers;
using DocumentCollection.Interfaces;
using DocumentCollection.Models;
using DocumentCollection.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using S3FileLayer;
using Shared;
using System.Linq.Expressions;

namespace Services
{
    public class DocumentRepositoryServiceTest
    {
        private readonly Mock<IRepositoryConfigurationService> _repoConfigServiceMock;
        private readonly Mock<IUnitOfWork> _unitOfWorkMock;
        private readonly Mock<IAmazonS3> _amazonS3;
        private readonly Mock<IOptions<S3ServiceConfiguration>> _settings;
        private readonly Mock<IDapperGenericRepository> _dapper;

        private readonly Mock<ILogger<DocumentRepositoryService>> _loggerMock;
        private readonly DocumentRepositoryService _documentRepositoryService;
        private readonly Mock<IConfiguration> _mockConfiguration;
        private const int Feature_Id = 14;
        public DocumentRepositoryServiceTest()
        {
            _repoConfigServiceMock = new Mock<IRepositoryConfigurationService>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _amazonS3 = new Mock<IAmazonS3>();
            _settings = new Mock<IOptions<S3ServiceConfiguration>>();
            _loggerMock = new Mock<ILogger<DocumentRepositoryService>>();
            var configurationMock = CreateMockConfiguration();
            _dapper = new Mock<IDapperGenericRepository>();
            _documentRepositoryService = new DocumentRepositoryService(_amazonS3.Object, _settings.Object, _repoConfigServiceMock.Object, _unitOfWorkMock.Object, _dapper.Object, _loggerMock.Object, configurationMock.Object);
        }
        private Mock<IConfiguration> CreateMockConfiguration()
        {
            var configurationMock = new Mock<IConfiguration>();

            // Mock AllowedFileExtensions
            var _allowedExtensions = new Mock<IConfigurationSection>();
            _allowedExtensions.Setup(x => x.Value).Returns(".pdf"); // Correctly mock the value
            configurationMock.Setup(x => x.GetSection("AllowedFileExtensions")).Returns(_allowedExtensions.Object);

            // Mock MaxFileSizeInMB
            var maxFileSizeSection = new Mock<IConfigurationSection>();
            maxFileSizeSection.Setup(x => x.Value).Returns("21"); // 21 MB
            configurationMock.Setup(x => x.GetSection("MaxFileSizeInMB")).Returns(maxFileSizeSection.Object);

            return configurationMock;
        }
        [Fact]
        public async Task GetRepositoryData_ReturnsFolderData_WhenConfigDataExists()
        {
            // Arrange
            var companyId = 1;
            var featureId = Feature_Id;
            var configData = new List<DocumentConfigurationDto>
            {
                new DocumentConfigurationDto { DoctypeID = 1, DoctypeName = "Type1" },
                new DocumentConfigurationDto { DoctypeID = 2, DoctypeName = "Type2" }
            };
            _repoConfigServiceMock.Setup(service => service.GetRepositoryConfigurationDataByCompany(companyId)).ReturnsAsync(configData);
            _dapper.Setup(d => d.Query<FolderAndDocumentModel>(It.IsAny<string>(), It.IsAny<object>())).ReturnsAsync(new List<FolderAndDocumentModel>());
            _repoConfigServiceMock.Setup(service => service.GetDocumentTypes(featureId)).ReturnsAsync(new List<DocumentTypes>());


            // Act
            var result = await _documentRepositoryService.GetRepositoryData(companyId, featureId);

            // Assert
            Assert.True(result.IsSuccess);
            var folderData = Assert.IsType<List<RepositoryTreeModel>>(result.Data);
            Assert.Equal(2, folderData.Count);
            Assert.Equal("Type1", folderData[0].Name);
            Assert.Equal("Type2", folderData[1].Name);
        }

        [Fact]
        public async Task GetRepositoryData_ReturnsError_WhenExceptionThrown()
        {
            // Arrange
            var companyId = 1;
            var featureId = Feature_Id;
            _repoConfigServiceMock.Setup(service => service.GetRepositoryConfigurationDataByCompany(companyId)).ThrowsAsync(new Exception("Test Exception"));

            // Act
            var result = await _documentRepositoryService.GetRepositoryData(companyId, featureId);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal("Failed to get document configurations: Test Exception", result.Message);
        }

        [Fact]
        public async Task GetRepositoryData_CreatesAnnualFolders_WithFixedYearRange()
        {
            // Arrange
            var companyId = 1;
            var featureId = Feature_Id;
            var configData = new List<DocumentConfigurationDto>
            {
                new DocumentConfigurationDto
                {
                    DoctypeID = 1,
                    DoctypeName = "Type1",
                    AnnualConfig = new RangeConfigDto
                    {
                        From = "2020",
                        To = "2023"
                    }
                }
            };
            _repoConfigServiceMock.Setup(service => service.GetRepositoryConfigurationDataByCompany(companyId))
                .ReturnsAsync(configData);
            _dapper.Setup(d => d.Query<FolderAndDocumentModel>(It.IsAny<string>(), It.IsAny<object>())).ReturnsAsync(new List<FolderAndDocumentModel>());
            _repoConfigServiceMock.Setup(service => service.GetDocumentTypes(featureId)).ReturnsAsync(new List<DocumentTypes>());

            // Act
            var result = await _documentRepositoryService.GetRepositoryData(companyId, featureId);

            // Assert
            Assert.True(result.IsSuccess);
            var folderData = Assert.IsType<List<RepositoryTreeModel>>(result.Data);
            Assert.Single(folderData);
            var docTypeFolder = folderData[0];
            Assert.NotNull(docTypeFolder.Children);
            Assert.Equal(4, docTypeFolder.Children.Count);
            Assert.Equal("2023", docTypeFolder.Children[0].Name);
            Assert.Equal("2020", docTypeFolder.Children[3].Name);
            Assert.Equal("1/2023", docTypeFolder.Children[0].Path);
        }

        [Fact]
        public async Task GetRepositoryData_CreatesAnnualFolders_WithAsOfDateRange()
        {
            // Arrange
            var companyId = 1;
            var featureId = Feature_Id;
            var configData = new List<DocumentConfigurationDto>
            {
                new DocumentConfigurationDto
                {
                    DoctypeID = 1,
                    DoctypeName = "Type1",
                    AnnualConfig = new RangeConfigDto
                    {
                        From = "2020",
                        To = "As of Date"
                    }
                }
            };
            _repoConfigServiceMock.Setup(service => service.GetRepositoryConfigurationDataByCompany(companyId))
                .ReturnsAsync(configData);
            _dapper.Setup(d => d.Query<FolderAndDocumentModel>(It.IsAny<string>(), It.IsAny<object>())).ReturnsAsync(new List<FolderAndDocumentModel>());
            _repoConfigServiceMock.Setup(service => service.GetDocumentTypes(featureId)).ReturnsAsync(new List<DocumentTypes>());

            // Act
            var result = await _documentRepositoryService.GetRepositoryData(companyId, featureId);

            // Assert
            Assert.True(result.IsSuccess);
            var folderData = Assert.IsType<List<RepositoryTreeModel>>(result.Data);
            Assert.Single(folderData);
            var docTypeFolder = folderData[0];
            Assert.NotNull(docTypeFolder.Children);
            var expectedYearCount = DateTime.Now.Year - 2020 + 1;
            Assert.Equal(expectedYearCount, docTypeFolder.Children.Count);
            Assert.Equal(DateTime.Now.Year.ToString(), docTypeFolder.Children[0].Name);
            Assert.Equal("2020", docTypeFolder.Children[^1].Name);
        }

        [Fact]
        public async Task GetRepositoryData_SkipsAnnualFolders_WhenInvalidYearFormat()
        {
            // Arrange
            var companyId = 1;
            var featureId = Feature_Id;
            var configData = new List<DocumentConfigurationDto>
            {
                new DocumentConfigurationDto
                {
                    DoctypeID = 1,
                    DoctypeName = "Type1",
                    AnnualConfig = new RangeConfigDto
                    {
                        From = "invalid",
                        To = "2023"
                    }
                }
            };
            _repoConfigServiceMock.Setup(service => service.GetRepositoryConfigurationDataByCompany(companyId))
                .ReturnsAsync(configData);
            _dapper.Setup(d => d.Query<FolderAndDocumentModel>(It.IsAny<string>(), It.IsAny<object>())).ReturnsAsync(new List<FolderAndDocumentModel>());
            _repoConfigServiceMock.Setup(service => service.GetDocumentTypes(featureId)).ReturnsAsync(new List<DocumentTypes>());

            // Act
            var result = await _documentRepositoryService.GetRepositoryData(companyId, featureId);

            // Assert
            Assert.True(result.IsSuccess);
            var folderData = Assert.IsType<List<RepositoryTreeModel>>(result.Data);
            Assert.Single(folderData);
            var docTypeFolder = folderData[0];
            Assert.Null(docTypeFolder.Children);
        }
        [Fact]
        public async Task GetRepositoryData_CreatesQuarterFolders_WithFixedYearRange()
        {
            // Arrange
            var companyId = 1;
            var featureId = Feature_Id;
            var configData = new List<DocumentConfigurationDto>
            {
                new DocumentConfigurationDto
                {
                    DoctypeID = 1,
                    DoctypeName = "Type1",
                    AnnualConfig = new RangeConfigDto
                    {
                        From = "2020",
                        To = "2023"
                    },
                    QuarterConfig = new RangeConfigDto
                    {
                        From = "Q1/2020",
                        To = "Q4/2023"
                    }
                }
            };
            _repoConfigServiceMock.Setup(service => service.GetRepositoryConfigurationDataByCompany(companyId))
                .ReturnsAsync(configData);
            _dapper.Setup(d => d.Query<FolderAndDocumentModel>(It.IsAny<string>(), It.IsAny<object>())).ReturnsAsync(new List<FolderAndDocumentModel>());
            _repoConfigServiceMock.Setup(service => service.GetDocumentTypes(featureId)).ReturnsAsync(new List<DocumentTypes>());


            // Act
            var result = await _documentRepositoryService.GetRepositoryData(companyId, featureId);

            // Assert
            Assert.True(result.IsSuccess);
            var folderData = Assert.IsType<List<RepositoryTreeModel>>(result.Data);
            Assert.Single(folderData);
            var docTypeFolder = folderData[0];
            Assert.NotNull(docTypeFolder.Children);
            Assert.Equal(4, docTypeFolder.Children.Count);
            var year2020 = docTypeFolder.Children.FirstOrDefault(y => y.Name == "2020");
            Assert.NotNull(year2020);
            Assert.Equal(4, year2020.Children.Count);
            Assert.Equal("Quarter 4", year2020.Children[0].Name);
            Assert.Equal("Quarter 1", year2020.Children[3].Name);
        }

        [Fact]
        public async Task GetRepositoryData_CreatesQuarterFolders_WithAsOfDateRange()
        {
            // Arrange
            var companyId = 1;
            var featureId = Feature_Id;
            var configData = new List<DocumentConfigurationDto>
            {
                new DocumentConfigurationDto
                {
                    DoctypeID = 1,
                    DoctypeName = "Type1",
                    AnnualConfig = new RangeConfigDto
                    {
                        From = "2020",
                        To = "As of Date"
                    },
                    QuarterConfig = new RangeConfigDto
                    {
                        From = "Q1/2020",
                        To = "As of Date"
                    }
                }
            };
            _repoConfigServiceMock.Setup(service => service.GetRepositoryConfigurationDataByCompany(companyId))
                .ReturnsAsync(configData);
            _dapper.Setup(d => d.Query<FolderAndDocumentModel>(It.IsAny<string>(), It.IsAny<object>())).ReturnsAsync(new List<FolderAndDocumentModel>());
            _repoConfigServiceMock.Setup(service => service.GetDocumentTypes(featureId)).ReturnsAsync(new List<DocumentTypes>());


            // Act
            var result = await _documentRepositoryService.GetRepositoryData(companyId, featureId);

            // Assert
            Assert.True(result.IsSuccess);
            var folderData = Assert.IsType<List<RepositoryTreeModel>>(result.Data);
            Assert.Single(folderData);
            var docTypeFolder = folderData[0];
            Assert.NotNull(docTypeFolder.Children);
            var expectedYearCount = DateTime.Now.Year - 2020 + 1;
            Assert.Equal(expectedYearCount, docTypeFolder.Children.Count);
            var currentYear = DateTime.Now.Year.ToString();
            var currentYearFolder = docTypeFolder.Children.FirstOrDefault(y => y.Name == currentYear);
            Assert.NotNull(currentYearFolder);
            var currentQuarter = (int)Math.Ceiling(DateTime.Now.Month / 3.0);
            Assert.Equal(currentQuarter, currentYearFolder.Children.Count);
            Assert.Equal($"Quarter {currentQuarter}", currentYearFolder.Children[0].Name);
        }

        [Fact]
        public async Task GetRepositoryData_CreatesMonthFolders_WithFixedYearRange()
        {
            // Arrange
            var companyId = 1;
            var featureId = Feature_Id;
            var configData = new List<DocumentConfigurationDto>
            {
                new DocumentConfigurationDto
                {
                    DoctypeID = 1,
                    DoctypeName = "Type1",
                    AnnualConfig = new RangeConfigDto
                    {
                        From = "2023",
                        To = "2023"
                    },
                    MonthConfig = new RangeConfigDto
                    {
                        From = "Jan/2023",
                        To = "Dec/2023"
                    }
                }
            };
            _repoConfigServiceMock.Setup(service => service.GetRepositoryConfigurationDataByCompany(companyId))
                .ReturnsAsync(configData);
            _dapper.Setup(d => d.Query<FolderAndDocumentModel>(It.IsAny<string>(), It.IsAny<object>())).ReturnsAsync(new List<FolderAndDocumentModel>());
            _repoConfigServiceMock.Setup(service => service.GetDocumentTypes(featureId)).ReturnsAsync(new List<DocumentTypes>());

            // Act
            var result = await _documentRepositoryService.GetRepositoryData(companyId, featureId);

            // Assert
            Assert.True(result.IsSuccess);
            var folderData = Assert.IsType<List<RepositoryTreeModel>>(result.Data);
            Assert.Single(folderData);
            var docTypeFolder = folderData[0];
            Assert.NotNull(docTypeFolder.Children);
            Assert.Single(docTypeFolder.Children);

            var year2023 = docTypeFolder.Children[0];
            Assert.NotNull(year2023.Children);
            Assert.Equal(12, year2023.Children.Count); // Should have all 12 months

            // Verify first and last months
            Assert.Equal("Jan", year2023.Children[11].Name);
            Assert.Equal("Dec", year2023.Children[0].Name);
            Assert.Equal("1/2023/Jan", year2023.Children[11].Path);
            Assert.Equal("1/2023/Dec", year2023.Children[0].Path);
        }

        [Fact]
        public async Task GetRepositoryData_CreatesMonthFolders_WithAsOfDateRange()
        {
            // Arrange
            var companyId = 1;
            var featureId = Feature_Id;
            var currentYear = DateTime.Now.Year;
            var currentMonth = DateTime.Now.Month;
            var configData = new List<DocumentConfigurationDto>
            {
                new DocumentConfigurationDto
                {
                    DoctypeID = 1,
                    DoctypeName = "Type1",
                    AnnualConfig = new RangeConfigDto
                    {
                        From = currentYear.ToString(),
                        To = "As of Date"
                    },
                    MonthConfig = new RangeConfigDto
                    {
                        From = $"Jan/{currentYear}",
                        To = "As of Date"
                    }
                }
            };
            _repoConfigServiceMock.Setup(service => service.GetRepositoryConfigurationDataByCompany(companyId))
                .ReturnsAsync(configData);
            _dapper.Setup(d => d.Query<FolderAndDocumentModel>(It.IsAny<string>(), It.IsAny<object>())).ReturnsAsync(new List<FolderAndDocumentModel>());
            _repoConfigServiceMock.Setup(service => service.GetDocumentTypes(featureId)).ReturnsAsync(new List<DocumentTypes>());

            // Act
            var result = await _documentRepositoryService.GetRepositoryData(companyId, featureId);

            // Assert
            Assert.True(result.IsSuccess);
            var folderData = Assert.IsType<List<RepositoryTreeModel>>(result.Data);
            Assert.Single(folderData);

            var docTypeFolder = folderData[0];
            Assert.NotNull(docTypeFolder.Children);
            Assert.Single(docTypeFolder.Children);

            var currentYearFolder = docTypeFolder.Children[0];
            Assert.NotNull(currentYearFolder.Children);
            Assert.Equal(currentMonth, currentYearFolder.Children.Count);

            // Verify first and last months (descending order)
            Assert.Equal(Constants.Months[currentMonth - 1], currentYearFolder.Children[0].Name);
            Assert.Equal("Jan", currentYearFolder.Children[^1].Name);
            Assert.Equal($"1/{currentYear}/{Constants.Months[currentMonth - 1]}", currentYearFolder.Children[0].Path);
            Assert.Equal($"1/{currentYear}/Jan", currentYearFolder.Children[^1].Path);
        }

        [Fact]
        public async Task UploadDocuments_ReturnsFailure_WhenMappingFails()
        {
            // Arrange
            var companyId = 1;
            var uploadRequest = new DocumentUploadRequestDto
            {
                Files = new List<IFormFile> { new Mock<IFormFile>().Object },
                FolderPath = "1/2023/Jan"
            };
            _unitOfWorkMock.Setup(u => u.DocumentMappingRepository.FindAllAsync(It.IsAny<Expression<Func<RepositoryDocumentMappingDetail, bool>>>()))
                .ThrowsAsync(new Exception("Mapping error"));


            // Act
            var result = await _documentRepositoryService.UploadDocuments(companyId, uploadRequest);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal("Failed to upload documents", result.Message);
        }

        [Fact]
        public async Task UpdateDocumentStoreDetails_ReturnsTrue_WhenItemsAreValid()
        {
            // Arrange
            var itemsStore = new List<DocumentCollectionStore>
            {
                new DocumentCollectionStore { ID = Guid.NewGuid(), FileName = "file1.txt" }
            };

            _unitOfWorkMock.Setup(u => u.DocumentCollectionRepository.AddBulkAsyn(itemsStore))
                .Returns(Task.CompletedTask);


            // _unitOfWorkMock.Setup(u => u.SaveAsync()).Returns((Task<int>)Task.CompletedTask);

            // Act
            var result = await _documentRepositoryService.UpdateDocumentStoreDetails(itemsStore);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task UpdateDocumentStoreDetails_ReturnsFalse_WhenItemsAreNullOrEmpty()
        {
            List<DocumentCollectionStore> itemsStore = new List<DocumentCollectionStore>();

            // Act
            var result = await _documentRepositoryService.UpdateDocumentStoreDetails(itemsStore);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task DeleteDocuments_ReturnsSuccess_WhenDocumentsDeleted()
        {
            // Arrange
            var companyId = 1;
            var deleteRequest = new DocumentDeleteRequestDto
            {
                DocumentIds = new string[] { Guid.NewGuid().ToString(), Guid.NewGuid().ToString() },
                ModifiedBy = 1,
                Path = "1/2024/Q3"
            };

            var documentGuids = deleteRequest.DocumentIds.Select(id => Guid.Parse(id)).ToList();
            var documentsToDelete = documentGuids.Select(id => new DocumentCollectionStore
            {
                ID = id,
                Type = ".pdf",
                IsDeleted = false
            }).ToList();

            _unitOfWorkMock.Setup(u => u.DocumentCollectionRepository.FindAllAsync(
                It.IsAny<Expression<Func<DocumentCollectionStore, bool>>>()))
                .ReturnsAsync(documentsToDelete);

            _amazonS3.Setup(s3 => s3.DeleteObjectAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<CancellationToken>()))
                .ReturnsAsync(new DeleteObjectResponse { HttpStatusCode = System.Net.HttpStatusCode.NoContent });

            // Act
            var result = await _documentRepositoryService.DeleteDocuments(companyId, deleteRequest);

            // Assert
            Assert.True(result.IsSuccess);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Fact]
        public async Task DeleteDocuments_ReturnsFailure_WhenNoDocumentsFound()
        {
            // Arrange
            var companyId = 1;
            var deleteRequest = new DocumentDeleteRequestDto
            {
                DocumentIds = new string[] { Guid.NewGuid().ToString(), Guid.NewGuid().ToString() },
                ModifiedBy = 1,
                Path = "1/2024/Q3"
            };

            _unitOfWorkMock.Setup(u => u.DocumentCollectionRepository.FindAllAsync(
                It.IsAny<Expression<Func<DocumentCollectionStore, bool>>>()))
                .ReturnsAsync(new List<DocumentCollectionStore>());

            // Act
            var result = await _documentRepositoryService.DeleteDocuments(companyId, deleteRequest);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal("No matching documents found to delete", result.Message);
            _unitOfWorkMock.Verify(u => u.DocumentCollectionRepository.UpdateBulk(It.IsAny<List<DocumentCollectionStore>>()), Times.Never);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Never);
        }

        [Fact]
        public async Task DeleteDocuments_ReturnsFailure_WhenExceptionThrown()
        {
            // Arrange
            var companyId = 1;
            var deleteRequest = new DocumentDeleteRequestDto
            {
                DocumentIds = new string[] { Guid.NewGuid().ToString(), Guid.NewGuid().ToString() },
                ModifiedBy = 1,
                Path = "1/2024/Q3"
            };

            _unitOfWorkMock.Setup(u => u.DocumentCollectionRepository.FindAllAsync(
                It.IsAny<Expression<Func<DocumentCollectionStore, bool>>>()))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _documentRepositoryService.DeleteDocuments(companyId, deleteRequest);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Contains("Error deleting documents: Database error", result.Message);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Never);
        }

        [Fact]
        public async Task GetRepositoryData_FillsUnConfiguredFolders_WhenDocumentTypeInDocStoreNotExistinConfiguration()
        {
            // Arrange
            var companyId = 1;
            var featureId = Feature_Id;
            var configData = new List<DocumentConfigurationDto>
            {
                new DocumentConfigurationDto
                {
                    DoctypeID = 1,
                    DoctypeName = "Type1",

                }
            };
            var documentTypes = new List<DocumentTypes>
            {
                new DocumentTypes { Id = 1, DocumentTypeName = "Type1" },
                new DocumentTypes { Id = 2, DocumentTypeName = "Type2" }
            };
            var folderAndDocumentModels = new List<FolderAndDocumentModel>
            {
                new FolderAndDocumentModel { DocumentGuid = Guid.NewGuid(), FolderMappingId = Guid.NewGuid(), DocTypeId = 2 , DocumentName="Some.pdf" }
            };

            _repoConfigServiceMock.Setup(service => service.GetRepositoryConfigurationDataByCompany(companyId))
                .ReturnsAsync(configData);
            _repoConfigServiceMock.Setup(service => service.GetDocumentTypes(featureId))
                .ReturnsAsync(documentTypes);
            _dapper.Setup(d => d.Query<FolderAndDocumentModel>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(folderAndDocumentModels);

            // Act
            var result = await _documentRepositoryService.GetRepositoryData(companyId, featureId);

            // Assert
            Assert.True(result.IsSuccess);
            var folderData = Assert.IsType<List<RepositoryTreeModel>>(result.Data);
            Assert.NotEmpty(folderData);
            Assert.True(folderData.Last().Name == Constants.UnConfigured);
            Assert.True(folderData.Last().Children?.Any());
            Assert.True(folderData.Last().Children?.Count == 1);
            Assert.True(folderData.Last().Children[0].Name == documentTypes[1].DocumentTypeName);
            Assert.True(folderData.Last().Children[0].Path == Constants.UnConfigured + "/" + documentTypes[1].Id);
        }

        [Fact]
        public async Task GetRepositoryData_FillsUnConfiguredFolders_WhenDocumentsOutsideConfiguredYearRange()
        {
            // Arrange
            var companyId = 1;
            var featureId = Feature_Id;
            var configData = new List<DocumentConfigurationDto>
            {
                new DocumentConfigurationDto
                {
                    DoctypeID = 1,
                    DoctypeName = "Type1",
                    AnnualConfig = new RangeConfigDto
                    {
                        From = "2023",
                        To = "2024"
                    },

                },

            };
            var documentTypes = new List<DocumentTypes>
            {
                new DocumentTypes { Id = 1, DocumentTypeName = "Type1" },
                new DocumentTypes { Id = 2, DocumentTypeName = "Type2" }
            };
            var documentsaddedoutsideyearrange = new List<FolderAndDocumentModel>
            {
                new FolderAndDocumentModel { DocumentGuid = Guid.NewGuid(), FolderMappingId = Guid.NewGuid(), DocTypeId = 1 , Year = 2022, DocumentName="Some.pdf" },
                new FolderAndDocumentModel { DocumentGuid = Guid.NewGuid(), FolderMappingId = Guid.NewGuid(), DocTypeId = 1 , Year = 2025, DocumentName="Some.pdf" }
            };

            _repoConfigServiceMock.Setup(service => service.GetRepositoryConfigurationDataByCompany(companyId))
                .ReturnsAsync(configData);
            _repoConfigServiceMock.Setup(service => service.GetDocumentTypes(featureId))
                .ReturnsAsync(documentTypes);
            _dapper.Setup(d => d.Query<FolderAndDocumentModel>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(documentsaddedoutsideyearrange);

            // Act
            var result = await _documentRepositoryService.GetRepositoryData(companyId, featureId);

            // Assert
            Assert.True(result.IsSuccess);
            var folderData = Assert.IsType<List<RepositoryTreeModel>>(result.Data);
            Assert.NotEmpty(folderData);
            Assert.True(folderData.Last().Name == Constants.UnConfigured);
            Assert.True(folderData.Last().Children?.Any());
            Assert.True(folderData.Last().Children?.Count == 1);
            Assert.True(folderData.Last().Children[0].Name == documentTypes[0].DocumentTypeName);
            Assert.True(folderData.Last().Children[0].Path == Constants.UnConfigured + "/" + documentTypes[0].Id);
            Assert.True(folderData.Last().Children[0].Children.Count == documentsaddedoutsideyearrange.Count);
            Assert.True(folderData.Last().Children[0].Children[0].Name == documentsaddedoutsideyearrange[0].Year.ToString());
            Assert.True(folderData.Last().Children[0].Children[0].Path == Constants.UnConfigured + "/" + documentTypes[0].Id + "/" + documentsaddedoutsideyearrange[0].Year.ToString());
            Assert.True(folderData.Last().Children[0].Children[1].Name == documentsaddedoutsideyearrange[1].Year.ToString());
            Assert.True(folderData.Last().Children[0].Children[1].Path == Constants.UnConfigured + "/" + documentTypes[0].Id + "/" + documentsaddedoutsideyearrange[1].Year.ToString());
        }

        [Fact]
        public async Task GetRepositoryData_FillsUnConfiguredFolders_WhenDocumentsOutsideConfiguredQuarterRange()
        {
            // Arrange
            var companyId = 1;
            var featureId = Feature_Id;
            var configData = new List<DocumentConfigurationDto>
            {
                new DocumentConfigurationDto
                {
                    DoctypeID = 1,
                    DoctypeName = "Type1",
                    AnnualConfig = new RangeConfigDto
                    {
                        From = "2023",
                        To = "2023"
                    },
                    QuarterConfig = new RangeConfigDto
                    {
                        From = "Q1/2023",
                        To = "Q3/2023"
                    }
                }
            };
            var documentTypes = new List<DocumentTypes>
            {
                new DocumentTypes { Id = 1, DocumentTypeName = "Type1" }
            };
            var documentsOutsideQuarterRange = new List<FolderAndDocumentModel>
            {
                new FolderAndDocumentModel { DocumentGuid = Guid.NewGuid(), FolderMappingId = Guid.NewGuid(), DocTypeId = 1, Year = 2023, Quarter = "Quarter 4", DocumentName = "Q4Document.pdf" }
            };

            _repoConfigServiceMock.Setup(service => service.GetRepositoryConfigurationDataByCompany(companyId))
                .ReturnsAsync(configData);
            _repoConfigServiceMock.Setup(service => service.GetDocumentTypes(featureId))
                .ReturnsAsync(documentTypes);
            _dapper.Setup(d => d.Query<FolderAndDocumentModel>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(documentsOutsideQuarterRange);

            // Act
            var result = await _documentRepositoryService.GetRepositoryData(companyId, featureId);

            // Assert
            Assert.True(result.IsSuccess);
            var folderData = Assert.IsType<List<RepositoryTreeModel>>(result.Data);
            Assert.NotEmpty(folderData);

            // Verify normal folders are created
            var normalFolder = folderData.FirstOrDefault(f => f.Name == "Type1");
            Assert.NotNull(normalFolder);
            Assert.NotNull(normalFolder.Children);
            var year2023 = normalFolder.Children.FirstOrDefault(y => y.Name == "2023");
            Assert.NotNull(year2023);
            //Assert.Equal(3, year2023.Children.Count); // Only Q1-Q3 should be in regular structure

            // Verify unconfigured folders
            var unconfiguredFolder = folderData.LastOrDefault(f => f.Name == Constants.UnConfigured);
            Assert.NotNull(unconfiguredFolder);
            Assert.NotNull(unconfiguredFolder.Children);
            Assert.Equal(1, unconfiguredFolder.Children.Count);
            Assert.Equal("Type1", unconfiguredFolder.Children[0].Name);
            Assert.Equal($"{Constants.UnConfigured}/1", unconfiguredFolder.Children[0].Path);
            Assert.Equal(1, unconfiguredFolder.Children[0].Children.Count);
            Assert.Equal("2023", unconfiguredFolder.Children[0].Children[0].Name);
            Assert.Equal(1, unconfiguredFolder.Children[0].Children[0].Children.Count);
            Assert.Equal("Quarter 4", unconfiguredFolder.Children[0].Children[0].Children[0].Name);
        }

        [Fact]
        public async Task GetRepositoryData_FillsUnConfiguredFolders_WhenDocumentsOutsideConfiguredMonthRange()
        {
            // Arrange
            var companyId = 1;
            var featureId = Feature_Id;
            var configData = new List<DocumentConfigurationDto>
            {
                new DocumentConfigurationDto
                {
                    DoctypeID = 1,
                    DoctypeName = "Type1",
                    AnnualConfig = new RangeConfigDto
                    {
                        From = "2023",
                        To = "2023"
                    },
                    MonthConfig = new RangeConfigDto
                    {
                        From = "Jan/2023",
                        To = "Jun/2023"
                    }
                }
            };
            var documentTypes = new List<DocumentTypes>
            {
                new DocumentTypes { Id = 1, DocumentTypeName = "Type1" }
            };
            var documentsOutsideMonthRange = new List<FolderAndDocumentModel>
            {
                new FolderAndDocumentModel { DocumentGuid = Guid.NewGuid(), FolderMappingId = Guid.NewGuid(), DocTypeId = 1, Year = 2023, Month = "Dec", DocumentName = "DecDocument.pdf" }
            };

            _repoConfigServiceMock.Setup(service => service.GetRepositoryConfigurationDataByCompany(companyId))
                .ReturnsAsync(configData);
            _repoConfigServiceMock.Setup(service => service.GetDocumentTypes(featureId))
                .ReturnsAsync(documentTypes);
            _dapper.Setup(d => d.Query<FolderAndDocumentModel>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(documentsOutsideMonthRange);

            // Act
            var result = await _documentRepositoryService.GetRepositoryData(companyId, featureId);

            // Assert
            Assert.True(result.IsSuccess);
            var folderData = Assert.IsType<List<RepositoryTreeModel>>(result.Data);
            Assert.NotEmpty(folderData);

            // Verify normal folders are created
            var normalFolder = folderData.FirstOrDefault(f => f.Name == "Type1");
            Assert.NotNull(normalFolder);
            Assert.NotNull(normalFolder.Children);
            var year2023 = normalFolder.Children.FirstOrDefault(y => y.Name == "2023");
            Assert.NotNull(year2023);
            //Assert.Equal(6, year2023.Children.Count); // Only Jan-Jun should be in regular structure

            // Verify unconfigured folders
            var unconfiguredFolder = folderData.LastOrDefault(f => f.Name == Constants.UnConfigured);
            Assert.NotNull(unconfiguredFolder);
            Assert.NotNull(unconfiguredFolder.Children);
            Assert.Equal(1, unconfiguredFolder.Children.Count);
            Assert.Equal("Type1", unconfiguredFolder.Children[0].Name);
            Assert.Equal($"{Constants.UnConfigured}/1", unconfiguredFolder.Children[0].Path);
            Assert.Equal(1, unconfiguredFolder.Children[0].Children.Count);
            Assert.Equal("2023", unconfiguredFolder.Children[0].Children[0].Name);
            Assert.Equal(1, unconfiguredFolder.Children[0].Children[0].Children.Count);
            Assert.Equal("Dec", unconfiguredFolder.Children[0].Children[0].Children[0].Name);
        }
        [Fact]
        public void ValidateUploadedFiles_InvalidExtension_ReturnsErrorMessage()
        {
            // Arrange  
            var files = new List<IFormFile>
           {
               CreateMockFile("test1.exe", 5 * 1024 * 1024)
           };
            var uploadRequest = new DocumentUploadRequestDto
            {
                Files = files,
                FolderPath = "1/2023/Jan"
            };

            // Act  
            var result = _documentRepositoryService.ValidateUploadedFiles(uploadRequest);

            // Assert  
            Assert.Contains("does not have a valid format", result);
        }
        [Fact]
        public void ValidateUploadedFiles_InvalidExtension_ReturnsErrorMessage1()
        {
            // Arrange
            var files = new List<IFormFile>
    {
        CreateMockFile("test1.exe", 5 * 1024 * 1024) // Invalid extension
    };
            var uploadRequest = new DocumentUploadRequestDto
            {
                Files = files,
                FolderPath = "1/2023/Jan"
            };

            // Act
            var result = _documentRepositoryService.ValidateUploadedFiles(uploadRequest);

            // Assert
            Assert.Contains("does not have a valid format", result);
        }
        private IFormFile CreateMockFile(string fileName, long fileSize)
        {
            var content = new byte[fileSize];
            var stream = new MemoryStream(content);
            return new FormFile(stream, 0, fileSize, "file", fileName)
            {
                Headers = new HeaderDictionary(),
                ContentType = "application/octet-stream"
            };
        }

        [Fact]
        public async Task GetDocuments_ReturnsCorrectUploadTypeText()
        {
            // Arrange
            var companyId = 1;
            var featureId = Feature_Id;
            var folderPath = "1/2024/Manual";
            var mappingId = Guid.NewGuid();
            var manualDoc = new DocumentCollectionStore { ID = Guid.NewGuid(), FileName = "manual.pdf", UploadType = 1, S3Path = "s3/manual.pdf", Type = ".pdf", FolderMappingId = mappingId, CreatedOn = DateTime.UtcNow };
            var ingestionDoc = new DocumentCollectionStore { ID = Guid.NewGuid(), FileName = "ingestion.pdf", UploadType = 2, S3Path = "s3/ingestion.pdf", Type = ".pdf", FolderMappingId = mappingId, CreatedOn = DateTime.UtcNow };
            var naDoc = new DocumentCollectionStore { ID = Guid.NewGuid(), FileName = "na.pdf", UploadType = 0, S3Path = "s3/na.pdf", Type = ".pdf", FolderMappingId = mappingId, CreatedOn = DateTime.UtcNow };
            var mappingList = new List<RepositoryDocumentMappingDetail> { new RepositoryDocumentMappingDetail { ID = mappingId, EntityId = companyId,
                    DocTypeID = DocumentServiceHelper.GetDocTypeFromFolderPath(folderPath),
                    Year = DocumentServiceHelper.GetYearFolderPath(folderPath),
                    Quarter = DocumentServiceHelper.GetQuarterFolderPath(folderPath),
                    Month = DocumentServiceHelper.GetMonthFolderPath(folderPath),
                } };
            _unitOfWorkMock.Setup(u => u.DocumentMappingRepository.FindAllAsync(It.IsAny<Expression<Func<RepositoryDocumentMappingDetail, bool>>>()))
                .ReturnsAsync(mappingList);
            _unitOfWorkMock.Setup(u => u.DocumentCollectionRepository.FindAllAsync(It.IsAny<Expression<Func<DocumentCollectionStore, bool>>>()))
                .ReturnsAsync(new List<DocumentCollectionStore> { manualDoc, ingestionDoc, naDoc });

            // Act
            var result = await _documentRepositoryService.GetDocuments(companyId, folderPath, featureId);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.NotNull(result.Data);
            Assert.Equal(3, result.Data.Count);
            Assert.Contains(result.Data, d => d.DocumentName == "manual.pdf" && d.DocumentUploadType == "Manual");
            Assert.Contains(result.Data, d => d.DocumentName == "ingestion.pdf" && d.DocumentUploadType == "Data Ingestion");
            Assert.Contains(result.Data, d => d.DocumentName == "na.pdf" && d.DocumentUploadType == "NA");
        }
    }
}