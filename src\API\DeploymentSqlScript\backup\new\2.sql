
--Investment Company Table--

IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CLO_InvestmentCompanyDetails' AND xtype='U')
BEGIN
   CREATE TABLE [dbo].[CLO_InvestmentCompanyDetails](
	[InvestmentCompanyID] [int] IDENTITY(1,1) NOT NULL,
	[InvestmentCompanyName] [nvarchar](100) NOT NULL,
	[Domicile] [nvarchar](100) NOT NULL,
	[IncorporationDate] [datetime] NOT NULL,
	[FirstClose] [datetime] NOT NULL,
	[FinalClose] [datetime] NOT NULL,
	[InvestmentPeriodEndDate] [datetime] NOT NULL,
	[MaturityDate] [datetime] NOT NULL,
	[Commitments] [nvarchar](100) NOT NULL,
	[BaseCurrency] [nvarchar](50) NOT NULL,
	[Custodian] [nvarchar](100) NOT NULL,
	[Administrator] [nvarchar](100) NOT NULL,
	[ListingAgent] [nvarchar](100) NOT NULL,
	[LegalCounsel] [nvarchar](100) NOT NULL,
	[PortfolioAdviser] [nvarchar](100) NOT NULL,
	[InvestmentSummary] [nvarchar](1000) NOT NULL,
	[CreatedOn] [datetime] NOT NULL,
	[CreatedBy] [int] NOT NULL,
	[ModifiedOn] [datetime] NULL,
	[ModifiedBy] [int] NULL
) ON [PRIMARY]
END

--CLO Details Table--

IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CLO_CloDetails' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[CLO_CloDetails](
	    [CLO_ID] [int] IDENTITY(1,1) NOT NULL,
        [CompanyName] [nvarchar](100) NOT NULL,
		[CompanyID] [int] NOT NULL,
        [Domicile] [nvarchar](100) NOT NULL,
        [Issuer] [nvarchar](100) NOT NULL,
        [Arranger] [nvarchar](100) NULL,
        [Trustee] [nvarchar](100) NULL,
        [Priced] [datetime] NULL,
        [Closed] [datetime] NULL,
        [LastRefiDate] [datetime] NULL,
        [LastResetDate] [datetime] NULL,
        [CallEndDate] [datetime] NULL,
        [Originalendofreinvestmentdate] [datetime] NULL,
        [Currentendofreinvestmentdate] [datetime] NULL,
        [CurrentMaturityDate] [datetime] NULL,
		[UniqueID] [nvarchar](100) NULL,
		[CreatedOn] [datetime] NOT NULL,
[CreatedBy] [int] NOT NULL,
[ModifiedOn] [datetime] NULL,
[ModifiedBy] [int] NULL
    ) ON [PRIMARY];
END;

IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CLO_Commentries' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[CLO_Commentries](
    	[ID] [int] IDENTITY(1,1) NOT NULL,
	    [Glicommentry][nvarchar](max) NULL,
        [MarketCommentry] [nvarchar](max) NULL,
        [InvestmentCompanyId] [int] NOT NUll,
        [CreatedOn] [datetime] NOT NULL,
        [CreatedBy] [int] NOT NULL,
        [ModifiedOn] [datetime] NULL,
        [ModifiedBy] [int] NULL,
        
        CONSTRAINT [PK_CLO_Commentries] PRIMARY KEY CLUSTERED ([ID] ASC)
    ) ON [PRIMARY];
END;
--Footnote Table--

IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CLO_TableFootnotes' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[CLO_TableFootnotes](
	    [Footenote_ID] [int] IDENTITY(1,1) NOT NULL,
        [FootnoteMapping] [nvarchar](100) NOT NULL,
		[FootnoteIdentifier] [nvarchar](100) NOT NULL,
        [FootnoteContent] [nvarchar] (max) NuLL,
 [CreatedOn] [datetime] NOT NULL,
[CreatedBy] [int] NOT NULL,
[ModifiedOn] [datetime] NULL,
[ModifiedBy] [int] NULL
    ) ON [PRIMARY];
END;
 BEGIN
        -- Alter the column to change its data type to NVARCHAR(MAX) Previosuly its 100
        ALTER TABLE [dbo].[CLO_TableFootnotes]
        ALTER COLUMN [FootnoteContent] NVARCHAR(max) NOT NULL;
    END

IF EXISTS(Select * from M_Features Where FeatureID=57 and Feature!='Investment Company' )
BEGIN
-- Disable the constraint
ALTER TABLE dbo.Mapping_GroupFeature
NOCHECK CONSTRAINT FK_Mapping_GroupFeature_M_Features;
ALTER TABLE dbo.M_SubFeature
NOCHECK CONSTRAINT FK_M_SubFeature_M_Features;
ALTER TABLE dbo.Mapping_FeatureAction
NOCHECK CONSTRAINT FK_Mapping_FeatureAction_M_Features;
declare @id int=0;
set @id=(select top 1 FeatureID from M_Features order by FeatureID desc)
-- Perform the update
UPDATE M_Features
SET FeatureID = @id+1
WHERE FeatureID = 57 and Feature!='Investment Company';

-- Re-enable the constraint
ALTER TABLE dbo.Mapping_GroupFeature
CHECK CONSTRAINT FK_Mapping_GroupFeature_M_Features;
ALTER TABLE dbo.M_SubFeature
CHECK CONSTRAINT FK_M_SubFeature_M_Features;
ALTER TABLE dbo.Mapping_FeatureAction
CHECK CONSTRAINT FK_Mapping_FeatureAction_M_Features;
END
GO


IF NOT EXISTS(Select * from M_Features Where Feature='Investment Company' )
BEGIN
INSERT [dbo].[M_Features] ([FeatureID], [Feature], [ParentID], [Description], [IsActive], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedFeatureID], [SequenceNo], [AliasName], [FeaturePath])
VALUES (57, N'Investment Company', NULL, NULL, 1, 0, CAST(N'2021-11-26T12:38:58.390' AS DateTime), 3, NULL, NULL, NULL, 36, N'Investment Company', N'/investment-company')
END
GO
IF NOT EXISTS(Select * from M_Features Where Feature='CLO Page' )
BEGIN
INSERT [dbo].[M_Features] ([FeatureID], [Feature], [ParentID], [Description], [IsActive], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedFeatureID], [SequenceNo], [AliasName], [FeaturePath])
VALUES (58, N'CLO Page', NULL, NULL, 1, 0, CAST(N'2021-11-26T12:38:58.393' AS DateTime), 3, NULL, NULL, NULL, 37, N'CLO Page', N'/clo-list')
END
GO

IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='CompanyFacts')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID],[SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn],
	[ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (71, N'CompanyFacts',57, NULL, 1, 0, getdate(), 49, NULL, NULL, NULL, 0, 0, N'Company Facts')
END
GO

IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='InvestmentSummary')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted],
	[CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (72, N'InvestmentSummary', 57, NULL, 1, 0, getdate(), 49, NULL, NULL, NULL, 0, 0, N'Investment Summary')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='GLI Portfolio Composition')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted],
	[CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (73, N'GLI Portfolio Composition', 57, NULL, 1, 0, getdate(), 49, NULL,NULL, NULL, 0, 0, N'GLI_Portfolio_Composition')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Aggregate CLO Metrics')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
	[CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]) 
	VALUES (74, N'Aggregate CLO Metrics', 57, NULL, 1, 0, getdate(), 49, NULL,NULL, NULL, 0, 0, N'Aggregate_CLO_Metrics')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='NAV & Distribution')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn],
	[CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (75, N'NAV & Distribution', 57, NULL, 1, 0,getdate(), 49, NULL,	NULL, NULL, 0, 0, N'NAV_Distribution')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='PE Performance Indicators')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], 
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (76, N'PE Performance Indicators', 57, NULL,1, 0, getdate(), 49, NULL, NULL, NULL, 0, 0, N'PE_Performance_Indicators')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Return Analysis')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (77, N'Return Analysis', 57, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'Return_Analysis')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Return Composition')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (78, N'Return Composition', 57, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'Return_Composition')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Currency Exposure')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (79, N'Currency Exposure', 57, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'Currency_Exposure')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='GLICommentry')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (80, N'GLICommentry', 57, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'GLI Commentry')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='MarketCommentry')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (81, N'MarketCommentry', 57, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'Market Commentry')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='CLOSummary')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (82, N'CLOSummary', 58, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'CLO Summary')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Capital Structure')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (83, N'Capital Structure', 58, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'Capital_Structure')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Collateral')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (84, N'Collateral', 58, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'Collateral')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Key KPI')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (85, N'Key KPI', 58, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'Key KPI')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Overcollateralisation Test')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (86, N'Overcollateralisation Test', 58, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'Overcollateralisation_Test')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Collateral Quality Test')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (87, N'Collateral Quality Test', 58, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'Collateral_Quality_Test')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeatureID=88)
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (88, N'CLO Versus US CLO Sector', 58, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'CLO_Versus_CLO_Sector')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='CLO Distributions To Date')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (89, N'CLO Distributions To Date', 58, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'CLO Distributions To Date')
END
GO


IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 71 AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], 
	[EncryptedSubFeatureActionMappingID]) VALUES (71, 2, 0, CAST(N'2024-06-21T05:35:00.690' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 71 AND [ActionID] = 3)
BEGIN
	 INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], 
	 [EncryptedSubFeatureActionMappingID]) VALUES (71, 3, 0, CAST(N'2024-06-21T05:35:00.697' AS DateTime), 3, NULL, NULL, NULL)
END
GO
--subfeaturelevel--InvestmentSummary view and edit
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 72 AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], 
	[EncryptedSubFeatureActionMappingID]) VALUES (72, 2, 0, CAST(N'2024-06-21T05:35:00.690' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 72 AND [ActionID] = 3)
BEGIN
	 INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], 
	 [EncryptedSubFeatureActionMappingID]) VALUES (72, 3, 0, CAST(N'2024-06-21T05:35:00.697' AS DateTime), 3, NULL, NULL, NULL)
END
GO
--subfeaturelevel--GLI Portfolio Composition view , edit, export, import


IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 73 AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], 
	[EncryptedSubFeatureActionMappingID]) VALUES (73, 2, 0, CAST(N'2024-06-21T05:35:00.690' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 73 AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (73, 3, 0, CAST(N'2024-06-21T05:35:00.697' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 73 AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], 
	[EncryptedSubFeatureActionMappingID]) VALUES (73, 4, 0, CAST(N'2024-06-21T05:35:00.703' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 73 AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (73, 5, 0, CAST(N'2024-06-21T05:35:00.713' AS DateTime), 3, NULL, NULL, NULL)
END
GO

--subfeaturelevel--Aggregate CLO Metrics view , edit, export, import



IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 74 AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (74, 2, 0, CAST(N'2024-06-21T05:35:00.690' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 74 AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (74, 3, 0, CAST(N'2024-06-21T05:35:00.697' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 74 AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (74, 4, 0, CAST(N'2024-06-21T05:35:00.703' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 74 AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (74, 5, 0, CAST(N'2024-06-21T05:35:00.713' AS DateTime), 3, NULL, NULL, NULL)
END
GO

--subfeaturelevel--NAV & Distribution view , edit, export, import



IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 75 AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (75, 2, 0, CAST(N'2024-06-21T05:35:00.690' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 75 AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction]  ([SubFeatureID],  [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], 
	[EncryptedSubFeatureActionMappingID]) VALUES (75, 3, 0, CAST(N'2024-06-21T05:35:00.697' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 75 AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (75, 4, 0, CAST(N'2024-06-21T05:35:00.703' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 75 AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (75, 5, 0, CAST(N'2024-06-21T05:35:00.713' AS DateTime), 3, NULL, NULL, NULL)
END
GO

--subfeaturelevel--PE Performance Indicators view , edit, export, import


IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 76 AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], 
	[EncryptedSubFeatureActionMappingID]) VALUES (76, 2, 0, CAST(N'2024-06-21T05:35:00.690' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 76 AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (76, 3, 0, CAST(N'2024-06-21T05:35:00.697' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 76 AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], 
	[EncryptedSubFeatureActionMappingID]) VALUES (76, 4, 0, CAST(N'2024-06-21T05:35:00.703' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 76 AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (76, 5, 0, CAST(N'2024-06-21T05:35:00.713' AS DateTime), 3, NULL, NULL, NULL)
END
GO
--subfeaturelevel--Return Analysis view , edit, export, import



IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 77 AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (77, 2, 0, CAST(N'2024-06-21T05:35:00.690' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 77 AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (77, 3, 0, CAST(N'2024-06-21T05:35:00.697' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 77 AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (77, 4, 0, CAST(N'2024-06-21T05:35:00.703' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 77 AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (77, 5, 0, CAST(N'2024-06-21T05:35:00.713' AS DateTime), 3, NULL, NULL, NULL)
END
GO

--subfeaturelevel--Return Compositionn view , edit, export, import


IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 78 AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (78, 2, 0, CAST(N'2024-06-21T05:35:00.690' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 78 AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (78, 3, 0, CAST(N'2024-06-21T05:35:00.697' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 78 AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], 
	[EncryptedSubFeatureActionMappingID]) VALUES (78, 4, 0, CAST(N'2024-06-21T05:35:00.703' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 78 AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (78, 5, 0, CAST(N'2024-06-21T05:35:00.713' AS DateTime), 3, NULL, NULL, NULL)
END
GO
--subfeaturelevel--Currency Exposure view , edit, export, import


IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 79 AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (79, 2, 0, CAST(N'2024-06-21T05:35:00.690' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 79 AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (79, 3, 0, CAST(N'2024-06-21T05:35:00.697' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 79 AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (79, 4, 0, CAST(N'2024-06-21T05:35:00.703' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 79 AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], 
	[EncryptedSubFeatureActionMappingID]) VALUES (79, 5, 0, CAST(N'2024-06-21T05:35:00.713' AS DateTime), 3, NULL, NULL, NULL)
END
GO



--subfeaturelevel-GLICommentry view , edit


IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 80 AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (80, 2, 0, CAST(N'2024-06-21T05:35:00.690' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 80 AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (80, 3, 0, CAST(N'2024-06-21T05:35:00.697' AS DateTime), 3, NULL, NULL, NULL)
END
GO

--subfeaturelevel-MarketCommentry view , edit


IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 81 AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (81, 2, 0, CAST(N'2024-06-21T05:35:00.690' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 81 AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (81, 3, 0, CAST(N'2024-06-21T05:35:00.697' AS DateTime), 3, NULL, NULL, NULL)
END
GO

--subfeaturelevel-CLOSummary view , edit


IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 82 AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (82, 2, 0, CAST(N'2024-06-21T05:35:00.690' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 82 AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (82, 3, 0, CAST(N'2024-06-21T05:35:00.697' AS DateTime), 3, NULL, NULL, NULL)
END
GO

--subfeaturelevel-Capital Structure view , edit, export, import


IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 83 AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (83, 2, 0, CAST(N'2024-06-21T05:35:00.690' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 83 AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (83, 3, 0, CAST(N'2024-06-21T05:35:00.697' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 83 AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (83, 4, 0, CAST(N'2024-06-21T05:35:00.703' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 83 AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], 
	[EncryptedSubFeatureActionMappingID]) VALUES (83, 5, 0, CAST(N'2024-06-21T05:35:00.713' AS DateTime), 3, NULL, NULL, NULL)
END
GO

--subfeaturelevel-Collateral view , edit, export, import


IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 84 AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (84, 2, 0, CAST(N'2024-06-21T05:35:00.690' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 84 AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (84, 3, 0, CAST(N'2024-06-21T05:35:00.697' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 84 AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (84, 4, 0, CAST(N'2024-06-21T05:35:00.703' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 84 AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], 
	[EncryptedSubFeatureActionMappingID]) VALUES (84, 5, 0, CAST(N'2024-06-21T05:35:00.713' AS DateTime), 3, NULL, NULL, NULL)
END
GO

--subfeaturelevel-Key KPI view , edit, export, import


IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 85 AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (85, 2, 0, CAST(N'2024-06-21T05:35:00.690' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 85 AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (85, 3, 0, CAST(N'2024-06-21T05:35:00.697' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 85 AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (85, 4, 0, CAST(N'2024-06-21T05:35:00.703' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 85 AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], 
	[EncryptedSubFeatureActionMappingID]) VALUES (85, 5, 0, CAST(N'2024-06-21T05:35:00.713' AS DateTime), 3, NULL, NULL, NULL)
END
GO

--subfeaturelevel-Overcollateralisation Test view , edit, export, import


IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 86 AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (86, 2, 0, CAST(N'2024-06-21T05:35:00.690' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 86 AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (86, 3, 0, CAST(N'2024-06-21T05:35:00.697' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 86 AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (86, 4, 0, CAST(N'2024-06-21T05:35:00.703' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 86 AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], 
	[EncryptedSubFeatureActionMappingID]) VALUES (86, 5, 0, CAST(N'2024-06-21T05:35:00.713' AS DateTime), 3, NULL, NULL, NULL)
END
GO

--subfeaturelevel-Collateral Quality Test Test view , edit, export, import


IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 87 AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (87, 2, 0, CAST(N'2024-06-21T05:35:00.690' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 87 AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (87, 3, 0, CAST(N'2024-06-21T05:35:00.697' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 87 AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (87, 4, 0, CAST(N'2024-06-21T05:35:00.703' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 87 AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], 
	[EncryptedSubFeatureActionMappingID]) VALUES (87, 5, 0, CAST(N'2024-06-21T05:35:00.713' AS DateTime), 3, NULL, NULL, NULL)
END
GO


--subfeaturelevel-CLO Versus US CLO Sector Test view , edit, export, import


IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 88 AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (88, 2, 0, CAST(N'2024-06-21T05:35:00.690' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 88 AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (88, 3, 0, CAST(N'2024-06-21T05:35:00.697' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 88 AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (88, 4, 0, CAST(N'2024-06-21T05:35:00.703' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 88 AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], 
	[EncryptedSubFeatureActionMappingID]) VALUES (88, 5, 0, CAST(N'2024-06-21T05:35:00.713' AS DateTime), 3, NULL, NULL, NULL)
END
GO


--subfeaturelevel-CLO Distributions To Date view , edit, export, import


IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 89 AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (89, 2, 0, CAST(N'2024-06-21T05:35:00.690' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 89 AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (89, 3, 0, CAST(N'2024-06-21T05:35:00.697' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 89 AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (89, 4, 0, CAST(N'2024-06-21T05:35:00.703' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 89 AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], 
	[EncryptedSubFeatureActionMappingID]) VALUES (89, 5, 0, CAST(N'2024-06-21T05:35:00.713' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF OBJECT_ID('[dbo].[SpGetSubFeatureListByPageconfig]', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE [dbo].[SpGetSubFeatureListByPageconfig]
END
GO
CREATE PROCEDURE [dbo].[SpGetSubFeatureListByPageconfig]
(
@FeatureId INT
)
AS
BEGIN
		IF (@FeatureId = 14)
		BEGIN
			 SELECT 
					f.SubFeatureID,
					f.SubFeature As SubFeatureName,
					COALESCE(s.AliasName, sf.AliasName) AS SubFeatureAliasName,
					f.isDeleted AS IsDeleted,
					f.ParentFeatureID AS ParentFeatureId
				FROM 
					M_SubFeature f
				LEFT JOIN M_SubPageDetails s ON f.PageConfigName = s.Name AND s.SubPageID IN (1, 4) AND s.isDeleted = 0 
				LEFT JOIN M_SubPageFields sf ON f.PageConfigName = sf.Name AND sf.SubPageID IN (2, 3, 38) AND sf.isDeleted = 0 
				WHERE 
					(s.SubPageID IS NOT NULL OR sf.SubPageID IS NOT NULL)
		END
	   ELSE IF(@FeatureId = 13)
			BEGIN
				SELECT 
					f.SubFeatureID,
					f.SubFeature As SubFeatureName,
					COALESCE(s.AliasName, s.AliasName) AS SubFeatureAliasName,
					f.isDeleted AS IsDeleted,
					ParentFeatureId
				FROM 
					M_SubFeature f
				LEFT JOIN M_SubPageDetails s ON f.PageConfigName = s.Name AND s.SubPageID IN (7,8,9,15,12) AND s.isDeleted = 0 
				WHERE s.SubPageID IS NOT NULL and ParentFeatureID = 13
			END
	   ELSE IF(@FeatureId = 15)
			BEGIN
			SELECT 
					f.SubFeatureID,
					f.SubFeature As SubFeatureName,
					COALESCE(s.AliasName, s.AliasName) AS SubFeatureAliasName,
					f.isDeleted AS IsDeleted,
					f.ParentFeatureID AS ParentFeatureId
				FROM 
					M_SubFeature f
				LEFT JOIN M_SubPageDetails s ON f.PageConfigName = s.Name AND s.SubPageID IN (5, 6) AND s.isDeleted = 0 
				WHERE s.SubPageID IS NOT NULL
			END
	   ELSE IF(@FeatureId = 50)
			BEGIN
			SELECT 
					f.SubFeatureID,
					f.SubFeature As SubFeatureName,
					COALESCE(s.AliasName, sf.AliasName) AS SubFeatureAliasName,
					f.isDeleted AS IsDeleted,
					f.ParentFeatureID AS ParentFeatureId
				FROM 
					M_SubFeature f
				LEFT JOIN M_SubPageDetails s ON f.PageConfigName = s.Name AND s.SubPageID IN (19) AND s.isDeleted = 0 
				LEFT JOIN M_SubPageFields sf ON f.PageConfigName = sf.Name AND sf.SubPageID IN (19) AND sf.isDeleted = 0 
				WHERE 
					(s.SubPageID IS NOT NULL OR sf.SubPageID IS NOT NULL) and  ParentFeatureID = 50
			END
	   ELSE IF(@FeatureId = 42)
			BEGIN
			SELECT 
					f.SubFeatureID,
					f.SubFeature As SubFeatureName,
					COALESCE(s.AliasName, s.AliasName) AS SubFeatureAliasName,
					f.isDeleted AS IsDeleted,
					f.ParentFeatureID AS ParentFeatureId
				FROM 
					M_SubFeature f
				LEFT JOIN M_SubPageDetails s ON f.PageConfigName = s.Name AND s.SubPageID IN (13,14,15,16,17,18) AND s.isDeleted = 0 
				WHERE s.SubPageID IS NOT NULL  AND s.SubPageID IN (13,14,15,16,17,18) AND ParentFeatureID = 42
			END
	   ELSE IF(@FeatureId = 19)
			BEGIN
			SELECT 
					SubFeatureID,
					SubFeature As SubFeatureName,
					COALESCE(PageConfigName, PageConfigName) AS SubFeatureAliasName,
					isDeleted AS IsDeleted,
					ParentFeatureID AS ParentFeatureId
				FROM 
					M_SubFeature 
				WHERE  SubFeatureID  BETWEEN 33 AND 48
			END
    ELSE IF (@FeatureId = 57)
		BEGIN
			 SELECT 
					SubFeatureID,
					SubFeature As SubFeatureName,
					COALESCE(PageConfigName, PageConfigName) AS SubFeatureAliasName,
					isDeleted AS IsDeleted,
					ParentFeatureID AS ParentFeatureId
				FROM 
					M_SubFeature 
				WHERE  SubFeatureID  BETWEEN 71 AND 81
		END
		ELSE IF (@FeatureId = 58)
		BEGIN
			 SELECT 
					SubFeatureID,
					SubFeature As SubFeatureName,
					COALESCE(PageConfigName, PageConfigName) AS SubFeatureAliasName,
					isDeleted AS IsDeleted,
					ParentFeatureID AS ParentFeatureId
				FROM 
					M_SubFeature 
				WHERE  SubFeatureID  BETWEEN 82 AND 89
		END
END
GO
IF OBJECT_ID('[dbo].[ProcGetUserFeatures]', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE [dbo].[ProcGetUserFeatures]
END
GO
CREATE PROCEDURE [dbo].[ProcGetUserFeatures](
	@UserId INT
	)
AS
BEGIN
DECLARE @TempTable TABLE 
(
    FeatureID INT,
    UserID INT,
    GroupID INT,
    CanAdd BIT,
    CanEdit BIT,
    CanView BIT,
    CanExport BIT,
    CanImport BIT
)
DECLARE @TempParentTable TABLE 
(
    FeatureID INT
)
INSERT INTO @TempTable
SELECT DISTINCT 
    [FeatureID] = GF.FeatureId,
    [UserID] = @UserId,
    [GroupID] = GF.GroupId,  
    [CanAdd] = GF.CanAdd,
    [CanEdit] = GF.CanEdit,
    [CanView] = GF.CanView,
    [CanExport] = GF.CanExport,
    [CanImport] = GF.CanImport
FROM 
    Mapping_UserGroup UG   
INNER JOIN 
    Mapping_GroupFeature GF  ON GF.GroupId = UG.GroupId AND UG.UserID = @UserId AND GF.IsDeleted =0 AND GF.IsActive =1
INNER JOIN M_Groups G ON UG.GroupID = G.GroupID AND G.IsDeleted =0 
WHERE 
    GF.IsDeleted = 0 AND
    GF.IsActive = 1 AND
    UG.UserId = @UserId AND
    UG.IsDeleted = 0 AND
    G.IsActive = 1 AND
    G.IsDeleted = 0


INSERT INTO @TempParentTable  SELECT DISTINCT ParentID from M_Features where FeatureID in (select FeatureID from @TempTable) AND ParentID IS NOT NULL and IsActive=1 and IsDeleted=0

SELECT *FROM @TempTable

SELECT FeatureID,FeaturePath'Path',SequenceNo,AliasName,Feature,ParentID,IsActive from M_Features where FeatureID in (select FeatureID from @TempTable) and IsActive=1 and IsDeleted=0
UNION 
SELECT FeatureID,FeaturePath'Path',SequenceNo,AliasName,Feature,ParentID,IsActive from M_Features where FeatureID in (select FeatureID from @TempParentTable) and IsActive=1 and IsDeleted=0
ORDER BY SequenceNo ASC
END



GO
IF (NOT EXISTS (SELECT * 
                 FROM [dbo].[Mapping_FeatureAction]
                 WHERE [FeatureID] = 57 
                ))
BEGIN
    INSERT [dbo].[Mapping_FeatureAction] ([FeatureID], [ActionID], [IsEnabled], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedFeatureActionMappingID]) VALUES (57, 1, 1, CAST(N'2022-03-10T17:52:50.437' AS DateTime), 1, NULL, NULL, NULL)
    INSERT [dbo].[Mapping_FeatureAction] ([FeatureID], [ActionID], [IsEnabled], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedFeatureActionMappingID]) VALUES (57, 2, 1, CAST(N'2022-03-10T17:59:16.170' AS DateTime), 1, NULL, NULL, NULL)
    INSERT [dbo].[Mapping_FeatureAction] ([FeatureID], [ActionID], [IsEnabled], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedFeatureActionMappingID]) VALUES (57, 3, 1, CAST(N'2022-03-10T17:50:04.677' AS DateTime), 1, NULL, NULL, NULL)
END
GO
IF (NOT EXISTS (SELECT * 
                 FROM [dbo].[Mapping_FeatureAction]
                 WHERE [FeatureID] = 58 
                ))
BEGIN
    INSERT [dbo].[Mapping_FeatureAction] ([FeatureID], [ActionID], [IsEnabled], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedFeatureActionMappingID]) VALUES (58, 1, 1, CAST(N'2022-03-10T17:52:50.437' AS DateTime), 1, NULL, NULL, NULL)
    INSERT [dbo].[Mapping_FeatureAction] ([FeatureID], [ActionID], [IsEnabled], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedFeatureActionMappingID]) VALUES (58, 2, 1, CAST(N'2022-03-10T17:59:16.170' AS DateTime), 1, NULL, NULL, NULL)
    INSERT [dbo].[Mapping_FeatureAction] ([FeatureID], [ActionID], [IsEnabled], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedFeatureActionMappingID]) VALUES (58, 3, 1, CAST(N'2022-03-10T17:50:04.677' AS DateTime), 1, NULL, NULL, NULL)
END

GO
DELETE FROM [dbo].[Mapping_FeatureAction]
WHERE [FeatureID] = 57 AND [ActionID] = 3

DELETE FROM [dbo].[Mapping_FeatureAction]
WHERE [FeatureID] = 58 AND [ActionID] = 3
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='KPI History')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (90, N'KPI History', 58, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'KPI History')
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Summary')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (91, N'Summary', 58, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'Summary')
END
GO


IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 90 AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (90, 2, 0, CAST(N'2024-06-21T05:35:00.690' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 90 AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (90, 3, 0, CAST(N'2024-06-21T05:35:00.697' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 90 AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (90, 4, 0, CAST(N'2024-06-21T05:35:00.703' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 90 AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], 
	[EncryptedSubFeatureActionMappingID]) VALUES (90, 5, 0, CAST(N'2024-06-21T05:35:00.713' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 91 AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (91, 2, 0, CAST(N'2024-06-21T05:35:00.690' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 91 AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (91, 3, 0, CAST(N'2024-06-21T05:35:00.697' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 91 AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (91, 4, 0, CAST(N'2024-06-21T05:35:00.703' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 91 AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], 
	[EncryptedSubFeatureActionMappingID]) VALUES (91, 5, 0, CAST(N'2024-06-21T05:35:00.713' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF OBJECT_ID('[dbo].[SpGetSubFeatureListByPageconfig]', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE [dbo].[SpGetSubFeatureListByPageconfig]
END
GO
CREATE PROCEDURE [dbo].[SpGetSubFeatureListByPageconfig]
(
@FeatureId INT
)
AS
BEGIN
		IF (@FeatureId = 14)
		BEGIN
			 SELECT 
					f.SubFeatureID,
					f.SubFeature As SubFeatureName,
					COALESCE(s.AliasName, sf.AliasName) AS SubFeatureAliasName,
					f.isDeleted AS IsDeleted,
					f.ParentFeatureID AS ParentFeatureId
				FROM 
					M_SubFeature f
				LEFT JOIN M_SubPageDetails s ON f.PageConfigName = s.Name AND s.SubPageID IN (1, 4) AND s.isDeleted = 0 
				LEFT JOIN M_SubPageFields sf ON f.PageConfigName = sf.Name AND sf.SubPageID IN (2, 3, 38) AND sf.isDeleted = 0 
				WHERE 
					(s.SubPageID IS NOT NULL OR sf.SubPageID IS NOT NULL)
		END
	   ELSE IF(@FeatureId = 13)
			BEGIN
				SELECT 
					f.SubFeatureID,
					f.SubFeature As SubFeatureName,
					COALESCE(s.AliasName, s.AliasName) AS SubFeatureAliasName,
					f.isDeleted AS IsDeleted,
					ParentFeatureId
				FROM 
					M_SubFeature f
				LEFT JOIN M_SubPageDetails s ON f.PageConfigName = s.Name AND s.SubPageID IN (7,8,9,15,12) AND s.isDeleted = 0 
				WHERE s.SubPageID IS NOT NULL and ParentFeatureID = 13
			END
	   ELSE IF(@FeatureId = 15)
			BEGIN
			SELECT 
					f.SubFeatureID,
					f.SubFeature As SubFeatureName,
					COALESCE(s.AliasName, s.AliasName) AS SubFeatureAliasName,
					f.isDeleted AS IsDeleted,
					f.ParentFeatureID AS ParentFeatureId
				FROM 
					M_SubFeature f
				LEFT JOIN M_SubPageDetails s ON f.PageConfigName = s.Name AND s.SubPageID IN (5, 6) AND s.isDeleted = 0 
				WHERE s.SubPageID IS NOT NULL
			END
	   ELSE IF(@FeatureId = 50)
			BEGIN
			SELECT 
					f.SubFeatureID,
					f.SubFeature As SubFeatureName,
					COALESCE(s.AliasName, sf.AliasName) AS SubFeatureAliasName,
					f.isDeleted AS IsDeleted,
					f.ParentFeatureID AS ParentFeatureId
				FROM 
					M_SubFeature f
				LEFT JOIN M_SubPageDetails s ON f.PageConfigName = s.Name AND s.SubPageID IN (19) AND s.isDeleted = 0 
				LEFT JOIN M_SubPageFields sf ON f.PageConfigName = sf.Name AND sf.SubPageID IN (19) AND sf.isDeleted = 0 
				WHERE 
					(s.SubPageID IS NOT NULL OR sf.SubPageID IS NOT NULL) and  ParentFeatureID = 50
			END
	   ELSE IF(@FeatureId = 42)
			BEGIN
			SELECT 
					f.SubFeatureID,
					f.SubFeature As SubFeatureName,
					COALESCE(s.AliasName, s.AliasName) AS SubFeatureAliasName,
					f.isDeleted AS IsDeleted,
					f.ParentFeatureID AS ParentFeatureId
				FROM 
					M_SubFeature f
				LEFT JOIN M_SubPageDetails s ON f.PageConfigName = s.Name AND s.SubPageID IN (13,14,15,16,17,18) AND s.isDeleted = 0 
				WHERE s.SubPageID IS NOT NULL  AND s.SubPageID IN (13,14,15,16,17,18) AND ParentFeatureID = 42
			END
	   ELSE IF(@FeatureId = 19)
			BEGIN
			SELECT 
					SubFeatureID,
					SubFeature As SubFeatureName,
					COALESCE(PageConfigName, PageConfigName) AS SubFeatureAliasName,
					isDeleted AS IsDeleted,
					ParentFeatureID AS ParentFeatureId
				FROM 
					M_SubFeature 
				WHERE  SubFeatureID  BETWEEN 33 AND 48
			END
    ELSE IF (@FeatureId = 57)
		BEGIN
			 SELECT 
					SubFeatureID,
					SubFeature As SubFeatureName,
					COALESCE(PageConfigName, PageConfigName) AS SubFeatureAliasName,
					isDeleted AS IsDeleted,
					ParentFeatureID AS ParentFeatureId
				FROM 
					M_SubFeature 
				WHERE  SubFeatureID  BETWEEN 71 AND 81
		END
		ELSE IF (@FeatureId = 58)
		BEGIN
			 SELECT 
					SubFeatureID,
					SubFeature As SubFeatureName,
					COALESCE(PageConfigName, PageConfigName) AS SubFeatureAliasName,
					isDeleted AS IsDeleted,
					ParentFeatureID AS ParentFeatureId
				FROM 
					M_SubFeature 
				WHERE  SubFeatureID  BETWEEN 82 AND 92
		END
END
GO
IF NOT EXISTS(Select * from M_Features Where Feature='CLOPageConfig')
BEGIN
	INSERT [dbo].[M_Features] ([FeatureID], [Feature], [ParentID], [Description], [IsActive], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedFeatureID], [SequenceNo], [AliasName], [FeaturePath]) 
	VALUES (59, N'CLOPageConfig', 22, null, 1,	0, GETDATE(), 3, null, null, null, 38, N'CLO Page Configuration', N'/clo-page-configuration');
END
GO
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='M_CLOPageDetails' AND xtype='U')
BEGIN
CREATE TABLE [dbo].[M_CLOPageDetails](
	[PageID] [int] NOT NULL,
	[Name] [nvarchar](200) NOT NULL,
	[AliasName] [nvarchar](500) NULL,
	[IsActive] [bit] NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[CreatedOn] [datetime] NOT NULL,
	[CreatedBy] [int] NOT NULL,
	[ModifiedOn] [datetime] NULL,
	[ModifiedBy] [int] NULL
) ON [PRIMARY]
END
GO
IF (NOT EXISTS (SELECT *  FROM [dbo].[M_CLOPageDetails] WHERE [PageID] = 1))
BEGIN
INSERT [dbo].[M_CLOPageDetails] ([PageID], [Name], [AliasName], [IsActive], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy]) VALUES (1, N'Investment Company', N'Investment Company', 1, 0, CAST(N'2025-07-02T08:42:20.460' AS DateTime), 1, NULL, NULL)
END
GO
IF (NOT EXISTS (SELECT *  FROM [dbo].[M_CLOPageDetails] WHERE [PageID] = 2))
BEGIN
INSERT [dbo].[M_CLOPageDetails] ([PageID], [Name], [AliasName], [IsActive], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy]) VALUES (2, N'CLO Page', N'CLO Page', 1, 0, CAST(N'2025-07-02T08:42:20.460' AS DateTime), 1, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='M_CLOSubPageDetails' AND xtype='U')
BEGIN
CREATE TABLE [dbo].[M_CLOSubPageDetails](
	[TabId] [int] NOT NULL,
	[TabName] [nvarchar](200) NULL,
	[IsDeleted] [bit] NOT NULL,
	[CreatedOn] [datetime] NOT NULL,
	[CreatedBy] [int] NOT NULL,
	[ModifiedOn] [datetime] NULL,
	[ModifiedBy] [int] NULL,
	[PageId] [int] NULL,
	[AliasName] [nvarchar](200) NOT NULL,
	[SequenceNo] [int] NULL
) ON [PRIMARY]
END
GO
IF EXISTS (SELECT * FROM sysobjects WHERE name='M_CLOSubPageDetails' AND xtype='U')
BEGIN
TRUNCATE TABLE M_CLOSubPageDetails;
END
GO
IF (NOT EXISTS (SELECT *  FROM [dbo].[M_CLOSubPageDetails] WHERE [TabId] = 1))
BEGIN
INSERT [dbo].[M_CLOSubPageDetails] ([TabId], [TabName], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [PageId], [AliasName], [SequenceNo]) VALUES (1, N'Investment Page', 0, CAST(N'2025-02-07T16:19:18.027' AS DateTime), 1, NULL, NULL, 1, N'Investment Page', 1)
END
IF (NOT EXISTS (SELECT *  FROM [dbo].[M_CLOSubPageDetails] WHERE [TabId] = 2))
BEGIN
INSERT [dbo].[M_CLOSubPageDetails] ([TabId], [TabName], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [PageId], [AliasName], [SequenceNo]) VALUES (2, N'Performance Data', 0, CAST(N'2025-02-07T16:19:18.027' AS DateTime), 1, NULL, NULL, 1, N'Performance Data', 2)
END
IF (NOT EXISTS (SELECT *  FROM [dbo].[M_CLOSubPageDetails] WHERE [TabId] = 3))
BEGIN
INSERT [dbo].[M_CLOSubPageDetails] ([TabId], [TabName], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [PageId], [AliasName], [SequenceNo]) VALUES (3, N'Commentaries', 0, CAST(N'2025-02-07T16:19:18.027' AS DateTime), 1, NULL, NULL, 1, N'Commentries', 3)
END
IF (NOT EXISTS (SELECT *  FROM [dbo].[M_CLOSubPageDetails] WHERE [TabId] = 4))
BEGIN
INSERT [dbo].[M_CLOSubPageDetails] ([TabId], [TabName], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [PageId], [AliasName], [SequenceNo]) VALUES (4, N'CLO Summary', 0, CAST(N'2025-02-07T00:00:00.000' AS DateTime), 1, NULL, NULL, 2, N'CLO Summary', 1)
END
IF (NOT EXISTS (SELECT *  FROM [dbo].[M_CLOSubPageDetails] WHERE [TabId] = 5))
BEGIN
INSERT [dbo].[M_CLOSubPageDetails] ([TabId], [TabName], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [PageId], [AliasName], [SequenceNo]) VALUES (5, N'CLO Performance Indicator', 0, CAST(N'2025-02-07T00:00:00.000' AS DateTime), 1, NULL, NULL, 2, N'CLO Performance Indicator', 2)
END
IF (NOT EXISTS (SELECT *  FROM [dbo].[M_CLOSubPageDetails] WHERE [TabId] = 6))
BEGIN
INSERT [dbo].[M_CLOSubPageDetails] ([TabId], [TabName], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [PageId], [AliasName], [SequenceNo]) VALUES (6, N'CLO Distribution', 0, CAST(N'2025-02-07T00:00:00.000' AS DateTime), 1, NULL, NULL, 2, N'CLO Distribution', 3)
END
GO
IF OBJECT_ID('[dbo].[GetCLOPageConfiguration]', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE [dbo].[GetCLOPageConfiguration]
END
GO
CREATE Procedure [dbo].[GetCLOPageConfiguration] 
@CompanyId int,
@PageId int
as
Begin
SELECT 
    COALESCE(company.InvestmentCompanyID, 0) AS CompanyId,
    company.InvestmentCompanyName AS CompanyName,
    tab.TabId AS TabId,
    tab.PageId AS PageId,
    tab.tabName AS [Name],
    ISNULL(pagetab.isDelete, tab.isDeleted) AS isDeleted,
    ISNULL(pagetab.isActive, 1) AS isActive,
    ISNULL(pagetab.aliasName, tab.aliasName) AS AliasName,
    ISNULL(pagetab.SequenceNo, tab.SequenceNo) AS SequenceNo,
    ISNULL(pagetab.id, 0) AS id
FROM 
    M_CLOPageDetails p
JOIN 
    CLO_MTabDetails tab ON p.pageId = tab.PageId
LEFT JOIN 
    CLO_MappingTabDetails pageTab ON tab.tabid = pageTab.tabId AND pageTab.CompanyId = @CompanyId 
LEFT JOIN 
    CLO_InvestmentCompanyDetails company ON pageTab.CompanyId = company.InvestmentCompanyID
WHERE 
    tab.PageId = @PageId
ORDER BY 
    SequenceNo;
End
GO
IF NOT EXISTS (SELECT * FROM sysobjects WHERE (name='CLOPageTabDetails' OR name='CLO_MappingTabDetails') AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[CLOPageTabDetails](
    [Id] [int] IDENTITY(1,1) NOT NULL,
	[TabId] [int] NOT NULL,
	[CompanyId] [nvarchar](50) NULL,
	[PageId] [int] NULL,
	[AliasName] [nvarchar](50) NULL,
	[SequenceNo] [int] NULL,
	[IsActive] [bit] NULL,
	[IsDelete] [bit] NULL,
	[CreatedOn] [datetime] NOT NULL,
	[CreatedBy] [int] NULL,
	[ModifiedOn] [datetime] NOT NULL,
	[ModifiedBy] [int] NULL,
        
        CONSTRAINT [PK_CLOPageTabDetails] PRIMARY KEY CLUSTERED ([ID] ASC)
    ) ON [PRIMARY];
END;

GO
UPDATE M_Features SET IsActive = 1 WHERE Feature IN ('Investment Company', 'CLO Page', 'Admin', 'AccessAndWorkflow', 'CLOPageConfig', 'CLOKPIConfig')
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Leverage')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (92, N'Leverage', 58, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'Leverage')
END
GO
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 92 AND [ActionID] = 2)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (92, 2, 0, CAST(N'2024-06-21T05:35:00.690' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 92 AND [ActionID] = 3)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (92, 3, 0, CAST(N'2024-06-21T05:35:00.697' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 92 AND [ActionID] = 4)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy],
	[EncryptedSubFeatureActionMappingID]) VALUES (92, 4, 0, CAST(N'2024-06-21T05:35:00.703' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 92 AND [ActionID] = 5)
BEGIN
	INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], 
	[EncryptedSubFeatureActionMappingID]) VALUES (92, 5, 0, CAST(N'2024-06-21T05:35:00.713' AS DateTime), 3, NULL, NULL, NULL)
END
GO
IF (EXISTS (SELECT *  FROM [dbo].[M_CLOSubPageDetails] WHERE [TabId] = 6))
BEGIN
	UPDATE M_CLOSubPageDetails SET TabName = 'CLO Equity Distributions', AliasName = 'CLO Equity Distributions' WHERE TabId = 6
END


--proc bulkupload update table --

GO
IF OBJECT_ID('[dbo].[ProcUpdateTableFromTempTable]', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE [dbo].[ProcUpdateTableFromTempTable]
END
GO
CREATE PROCEDURE [dbo].[ProcUpdateTableFromTempTable]
    @TargetTableName NVARCHAR(128),
    @TempTableName NVARCHAR(128),
    @UpdateColumns NVARCHAR(MAX),
    @JoinClause NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    BEGIN TRY
        -- Validate inputs
        IF @TargetTableName IS NULL OR @TempTableName IS NULL 
           OR @UpdateColumns IS NULL OR @JoinClause IS NULL
        BEGIN
            RAISERROR('All parameters are required', 16, 1)
            RETURN;
        END

        -- Ensure temp table exists
        IF OBJECT_ID('tempdb..' + QUOTENAME(@TempTableName)) IS NULL
        BEGIN
            RAISERROR('Temp table does not exist', 16, 1)
            RETURN;
        END

        DECLARE @SQL NVARCHAR(MAX)
        
        SET @SQL = N'
            UPDATE target
            SET ' + @UpdateColumns + '
            FROM ' + QUOTENAME(@TargetTableName) + ' target
            INNER JOIN ' + QUOTENAME(@TempTableName) + ' source 
            ON ' + @JoinClause + ';'

        

        -- Execute update
        EXEC sp_executesql  @SQL
       

    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE()
        RAISERROR('Error updating records: %s', 16, 1, @ErrorMessage)
    END CATCH
END

-- bulkupload insert data proc --
GO
IF OBJECT_ID('[dbo].[ProcInsertDataFromTempTable]', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE [dbo].[ProcInsertDataFromTempTable]
END

GO
CREATE PROCEDURE [dbo].[ProcInsertDataFromTempTable]
    @TargetTableName NVARCHAR(128),
    @TempTableName NVARCHAR(128),
    @InsertColumns NVARCHAR(MAX),
    @SelectColumns NVARCHAR(MAX),
    @JoinClause NVARCHAR(MAX),
    @WhereClause NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    BEGIN TRY
        -- Input validation
        IF @TargetTableName IS NULL OR @TempTableName IS NULL 
           OR @InsertColumns IS NULL OR @SelectColumns IS NULL
           OR @JoinClause IS NULL OR @WhereClause IS NULL
        BEGIN
            RAISERROR('All parameters are required', 16, 1)
            RETURN;
        END

        DECLARE @SQL NVARCHAR(MAX)
        
        SET @SQL = N'
            INSERT INTO ' + QUOTENAME(@TargetTableName) + ' (' + @InsertColumns + ')
            SELECT ' + @SelectColumns + '
            FROM ' + QUOTENAME(@TempTableName) + ' source
            LEFT JOIN ' + QUOTENAME(@TargetTableName) + ' target
            ON ' + @JoinClause + '
            WHERE ' + @WhereClause + ';'        

        -- Execute insert
        EXEC sp_executesql @SQL;
        
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE()
        DECLARE @ErrorSeverity INT = ERROR_SEVERITY()
        DECLARE @ErrorState INT = ERROR_STATE()

        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState)
    END CATCH
END

-- create table dynamically --
GO
IF OBJECT_ID('[dbo].[ProcCreateTableDynamic]', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE [dbo].[ProcCreateTableDynamic]
END
GO
CREATE OR ALTER PROCEDURE [dbo].[ProcCreateTableDynamic]
    @TableName NVARCHAR(128),
    @ColumnDefinitions NVARCHAR(MAX),
    @SchemaName NVARCHAR(128) = 'dbo'
AS
BEGIN
    SET NOCOUNT ON;
    BEGIN TRY
        -- Validate inputs
        IF @TableName IS NULL OR @ColumnDefinitions IS NULL
        BEGIN
            RAISERROR('TableName and ColumnDefinitions are required', 16, 1)
            RETURN;
        END

        -- Validate schema
        IF NOT EXISTS (SELECT * FROM sys.schemas WHERE name = @SchemaName)
        BEGIN
            RAISERROR('Invalid schema name', 16, 1)
            RETURN;
        END

        DECLARE @SQL NVARCHAR(MAX)
        
        -- Drop if exists
        SET @SQL = N'
            IF OBJECT_ID(''' + QUOTENAME(@SchemaName) + '.' + QUOTENAME(@TableName) + ''') IS NOT NULL 
            BEGIN
                DROP TABLE ' + QUOTENAME(@SchemaName) + '.' + QUOTENAME(@TableName) + '
            END;

            CREATE TABLE ' + QUOTENAME(@SchemaName) + '.' + QUOTENAME(@TableName) + '
            (
                ' + @ColumnDefinitions + '
            );'

        -- Execute creation
        EXEC sp_executesql @SQL
        
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE()
        RAISERROR('Error creating table: %s', 16, 1, @ErrorMessage)
    END CATCH
END

-- create temp table dynamically --
GO
IF OBJECT_ID('[dbo].[ProcCreateTempTableByTable]', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE [dbo].[ProcCreateTempTableByTable]
END
GO
CREATE PROCEDURE [dbo].[ProcCreateTempTableByTable]
    @SourceTableName NVARCHAR(150),
    @TempTableName NVARCHAR(150)
AS
BEGIN
    SET NOCOUNT ON;
    BEGIN TRY
        -- Validate inputs
        IF @SourceTableName IS NULL OR @TempTableName IS NULL
        BEGIN
            RAISERROR('Source table and temp table names are required', 16, 1)
            RETURN;
        END

        -- Ensure temp table starts with #
        IF LEFT(@TempTableName, 1) <> '#'
        BEGIN
            SET @TempTableName = '#' + @TempTableName
        END

        DECLARE @SQL NVARCHAR(MAX)
        
        -- Drop if exists
        SET @SQL = N'
            IF OBJECT_ID(''tempdb..' + QUOTENAME(@TempTableName) + ''') IS NOT NULL 
            BEGIN
                DROP TABLE ' + QUOTENAME(@TempTableName) + '
            END;

            SELECT TOP 0 *
            INTO ' + QUOTENAME(@TempTableName) + '
            FROM ' + QUOTENAME(@SourceTableName) + ';'

        -- Execute creation
        EXEC sp_executesql @SQL

    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE()
        RAISERROR('Error creating temp table: %s', 16, 1, @ErrorMessage)
    END CATCH
END

-- drop temptable proc --
GO
IF OBJECT_ID('[dbo].[ProcDropTempTable]', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE [dbo].[ProcDropTempTable]
END
GO
CREATE PROCEDURE [dbo].[ProcDropTempTable]
    @TempTableName NVARCHAR(150)
AS
BEGIN
    SET NOCOUNT ON;
    BEGIN TRY
        -- Validate input
        IF @TempTableName IS NULL
        BEGIN
            RAISERROR('TempTableName is required', 16, 1)
            RETURN;
        END

        -- Ensure table name starts with #
        IF LEFT(@TempTableName, 1) <> '##'
        BEGIN
            SET @TempTableName = '##' + @TempTableName
        END

        DECLARE @SQL NVARCHAR(MAX)
        
        SET @SQL = N'
            IF OBJECT_ID(''tempdb..' + QUOTENAME(@TempTableName) + ''') IS NOT NULL 
            BEGIN
                DROP TABLE ' + QUOTENAME(@TempTableName) + '
            END'

        -- Execute drop
        EXEC sp_executesql @SQL
        
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE()
        RAISERROR('Error dropping temp table: %s', 16, 1, @ErrorMessage)
    END CATCH
END


-- delete company records --
GO
IF OBJECT_ID('[dbo].[ProcDeletRecordByCompany]', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE [dbo].[ProcDeletRecordByCompany]
END
GO
CREATE PROCEDURE [dbo].[ProcDeletRecordByCompany]
    @TableName NVARCHAR(128),
    @CompanyID NVARCHAR(150)
AS
BEGIN
    SET NOCOUNT ON;
    BEGIN TRY
        -- Validate inputs
        IF @TableName IS NULL OR @CompanyID IS NULL
        BEGIN
            RAISERROR('TableName and CompanyID are required parameters', 16, 1)
            RETURN;
        END

        DECLARE @SQL NVARCHAR(MAX)
        DECLARE @ParmDefinition NVARCHAR(500)

        -- Build dynamic SQL with parameterized query
        SET @SQL = N'
            DELETE FROM ' + QUOTENAME(@TableName) + '
            WHERE CompanyID = @CompanyIDParam'

        SET @ParmDefinition = N'@CompanyIDParam NVARCHAR(150)'

        -- Execute delete with parameters
        EXEC sp_executesql 
            @SQL,
            @ParmDefinition,
            @CompanyIDParam = @CompanyID

    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE()
        RAISERROR('Error deleting records: %s', 16, 1, @ErrorMessage)
    END CATCH
END

-- get record by company --
GO
IF OBJECT_ID('[dbo].[ProcGetRecordByCompany]', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE [dbo].[ProcGetRecordByCompany]
END
GO
CREATE PROCEDURE [dbo].[ProcGetRecordByCompany]
    @ColumnList NVARCHAR(MAX),
	@TableName NVARCHAR(MAX),
    @CompanyID NVARCHAR(MAX)
AS
BEGIN
    DECLARE @SQL NVARCHAR(MAX)
    
    SET @SQL = 'SELECT ' + @ColumnList + ' FROM ' + @TableName + ' WHERE CompanyID IN(@CompanyID)'
    
    EXEC sp_executesql @SQL, N'@CompanyID NVARCHAR(50)', @CompanyID = @CompanyID
END

--Get CLO page Cinfiguration--
GO
IF OBJECT_ID('[dbo].[GetCLOPageConfiguration]', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE [dbo].[GetCLOPageConfiguration]
END
GO
CREATE Procedure [dbo].[GetCLOPageConfiguration] 
@CompanyId int,
@PageId int
as
Begin
SELECT Distinct
    COALESCE(company.InvestmentCompanyID, 0) AS CompanyId,
    company.InvestmentCompanyName AS CompanyName,
    tab.TabId AS TabId,
    tab.PageId AS PageId,
    tab.tabName AS [Name],
    ISNULL(pagetab.isDelete, tab.isDeleted) AS isDeleted,
    ISNULL(pagetab.isActive, 1) AS isActive,
    ISNULL(pagetab.aliasName, tab.aliasName) AS AliasName,
    ISNULL(pagetab.SequenceNo, tab.SequenceNo) AS SequenceNo,
    ISNULL(pagetab.id, 0) AS id
FROM 
    M_CLOPageDetails p
JOIN 
    CLO_MTabDetails tab ON p.pageId = tab.PageId
LEFT JOIN 
    CLO_MappingTabDetails pageTab ON tab.tabid = pageTab.tabId AND pageTab.CompanyId = @CompanyId 
LEFT JOIN 
    CLO_InvestmentCompanyDetails company ON pageTab.CompanyId = company.InvestmentCompanyID
WHERE 
    tab.PageId = @PageId
ORDER BY 
    SequenceNo;
End
GO

IF (EXISTS (SELECT *  FROM [dbo].M_SubFeature WHERE [SubFeature] = 'CLO Versus US CLO Sector'))
BEGIN
UPDATE M_SubFeature SET SubFeature='CLO Versus CLO Sector' WHERE SubFeature='CLO Versus US CLO Sector'
END
GO
IF NOT EXISTS(Select * from M_Features Where Feature='CLOKPIConfig')
BEGIN
	INSERT [dbo].[M_Features] ([FeatureID], [Feature], [ParentID], [Description], [IsActive], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedFeatureID], [SequenceNo], [AliasName], [FeaturePath]) 
	VALUES (60, N'CLOKPIConfig', 22, null, 1,	0, GETDATE(), 3, null, null, null, 38, N'CLO KPI Configuration', N'/clo-kpi-configuration');
END
GO
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CLO_M_Table' AND xtype='U')
BEGIN
CREATE TABLE [dbo].[CLO_M_Table](
	[TableId] [int] NOT NULL,
	[TableName] [nvarchar](max) NOT NULL,
	[IsActive] [bit] NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[CreatedOn] [datetime] NOT NULL,
	[CreatedBy] [int] NOT NULL,
	[ModifiedOn] [datetime] NULL,
	[ModifiedBy] [int] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
END
GO
INSERT [dbo].[CLO_M_Table] ([TableId], [TableName], [IsActive], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy]) VALUES (1, N'Company Facts', 1, 0, CAST(N'2025-02-20T16:19:18.027' AS DateTime), 3, NULL, NULL)
INSERT [dbo].[CLO_M_Table] ([TableId], [TableName], [IsActive], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy]) VALUES (2, N'GLI Portfolio Composition', 1, 0, CAST(N'2025-02-20T16:19:18.027' AS DateTime), 3, NULL, NULL)
INSERT [dbo].[CLO_M_Table] ([TableId], [TableName], [IsActive], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy]) VALUES (3, N'Aggregate CLO Metrics_EU', 1, 0, CAST(N'2025-02-20T16:19:18.027' AS DateTime), 3, NULL, NULL)
INSERT [dbo].[CLO_M_Table] ([TableId], [TableName], [IsActive], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy]) VALUES (4, N'Aggregate CLO Metrics_US', 1, 0, CAST(N'2025-02-20T16:19:18.027' AS DateTime), 3, NULL, NULL)
INSERT [dbo].[CLO_M_Table] ([TableId], [TableName], [IsActive], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy]) VALUES (5, N'NAV & Distribution', 1, 0, CAST(N'2025-02-20T16:19:18.027' AS DateTime), 3, NULL, NULL)
INSERT [dbo].[CLO_M_Table] ([TableId], [TableName], [IsActive], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy]) VALUES (6, N'PE Performance Indicators', 1, 0, CAST(N'2025-02-20T16:19:18.027' AS DateTime), 3, NULL, NULL)
INSERT [dbo].[CLO_M_Table] ([TableId], [TableName], [IsActive], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy]) VALUES (7, N'Return Analysis', 1, 0, CAST(N'2025-02-20T16:19:18.027' AS DateTime), 3, NULL, NULL)
INSERT [dbo].[CLO_M_Table] ([TableId], [TableName], [IsActive], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy]) VALUES (8, N'Return Composition', 1, 0, CAST(N'2025-02-20T16:19:18.027' AS DateTime), 3, NULL, NULL)
INSERT [dbo].[CLO_M_Table] ([TableId], [TableName], [IsActive], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy]) VALUES (9, N'Currency Exposure', 1, 0, CAST(N'2025-02-20T16:19:18.027' AS DateTime), 3, NULL, NULL)
INSERT [dbo].[CLO_M_Table] ([TableId], [TableName], [IsActive], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy]) VALUES (10, N'Capital Structure', 1, 0, CAST(N'2025-02-20T16:19:18.027' AS DateTime), 3, NULL, NULL)
INSERT [dbo].[CLO_M_Table] ([TableId], [TableName], [IsActive], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy]) VALUES (11, N'Leverage', 1, 0, CAST(N'2025-02-20T16:19:18.027' AS DateTime), 3, NULL, NULL)
INSERT [dbo].[CLO_M_Table] ([TableId], [TableName], [IsActive], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy]) VALUES (12, N'Collateral', 1, 0, CAST(N'2025-02-20T16:19:18.027' AS DateTime), 3, NULL, NULL)
INSERT [dbo].[CLO_M_Table] ([TableId], [TableName], [IsActive], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy]) VALUES (13, N'Key KPI_EU', 1, 0, CAST(N'2025-02-20T16:19:18.027' AS DateTime), 3, NULL, NULL)
INSERT [dbo].[CLO_M_Table] ([TableId], [TableName], [IsActive], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy]) VALUES (14, N'KPI History_EU', 1, 0, CAST(N'2025-02-20T16:19:18.027' AS DateTime), 3, NULL, NULL)
INSERT [dbo].[CLO_M_Table] ([TableId], [TableName], [IsActive], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy]) VALUES (15, N'KPI Summary', 1, 0, CAST(N'2025-02-20T16:19:18.027' AS DateTime), 3, NULL, NULL)
INSERT [dbo].[CLO_M_Table] ([TableId], [TableName], [IsActive], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy]) VALUES (16, N'Overcollateralisation Test', 1, 0, CAST(N'2025-02-20T16:19:18.027' AS DateTime), 3, NULL, NULL)
INSERT [dbo].[CLO_M_Table] ([TableId], [TableName], [IsActive], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy]) VALUES (17, N'Collateral Quality Test', 1, 0, CAST(N'2025-02-20T16:19:18.027' AS DateTime), 3, NULL, NULL)
INSERT [dbo].[CLO_M_Table] ([TableId], [TableName], [IsActive], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy]) VALUES (18, N'CLO Versus US CLO Sector', 1, 0, CAST(N'2025-02-20T16:19:18.027' AS DateTime), 3, NULL, NULL)
INSERT [dbo].[CLO_M_Table] ([TableId], [TableName], [IsActive], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy]) VALUES (19, N'CLO Distributions To Date', 1, 0, CAST(N'2025-02-20T16:19:18.027' AS DateTime), 3, NULL, NULL)
INSERT [dbo].[CLO_M_Table] ([TableId], [TableName], [IsActive], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy]) VALUES (20, N'Key KPI_US', 1, 0, CAST(N'2025-02-20T16:19:18.027' AS DateTime), 3, NULL, NULL)
INSERT [dbo].[CLO_M_Table] ([TableId], [TableName], [IsActive], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy]) VALUES (21, N'KPI History_US', 1, 0, CAST(N'2025-02-20T16:19:18.027' AS DateTime), 3, NULL, NULL)
--------------------DB changes related to US 8150----------------------------
GO

IF NOT EXISTS (
    SELECT * 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'M_CLOSubPageDetails' 
    AND COLUMN_NAME = 'ParentId'
) and EXISTS (SELECT * FROM sysobjects WHERE name='M_CLOSubPageDetails'  AND xtype='U')
BEGIN
    ALTER table M_CLOSubPageDetails
    Add ParentId int null;
END
GO
IF (NOT EXISTS (SELECT *  FROM [dbo].[M_CLOSubPageDetails] WHERE TabId=7))
BEGIN
INSERT INTO [dbo].[M_CLOSubPageDetails]
           ([TabId]
           ,[TabName]
           ,[IsDeleted]
           ,[CreatedOn]
           ,[CreatedBy]
           ,[ModifiedOn]
           ,[ModifiedBy]
           ,[PageId]
           ,[AliasName]
           ,[SequenceNo]
           ,[ParentId])
     VALUES
           (7
           ,'NAV & Distribution'
           ,0
           ,GETUTCDATE()
           ,1
           ,null
           ,null
           ,1
           ,'NAV & Distribution'
           ,1
           ,2)
END
IF (NOT EXISTS (SELECT *  FROM [dbo].[M_CLOSubPageDetails] WHERE TabId=8))
BEGIN
INSERT INTO [dbo].[M_CLOSubPageDetails]
           ([TabId]
           ,[TabName]
           ,[IsDeleted]
           ,[CreatedOn]
           ,[CreatedBy]
           ,[ModifiedOn]
           ,[ModifiedBy]
           ,[PageId]
           ,[AliasName]
           ,[SequenceNo]
           ,[ParentId])
     VALUES
           (8
           ,'PE Performance Indicators'
           ,0
           ,GETUTCDATE()
           ,1
           ,null
           ,null
           ,1
           ,'PE Performance Indicators'
           ,2
           ,2)
END
IF (NOT EXISTS (SELECT *  FROM [dbo].[M_CLOSubPageDetails] WHERE TabId=9))
BEGIN
INSERT INTO [dbo].[M_CLOSubPageDetails]
           ([TabId]
           ,[TabName]
           ,[IsDeleted]
           ,[CreatedOn]
           ,[CreatedBy]
           ,[ModifiedOn]
           ,[ModifiedBy]
           ,[PageId]
           ,[AliasName]
           ,[SequenceNo]
           ,[ParentId])
     VALUES
           (9
           ,'Return Analysis'
           ,0
           ,GETUTCDATE()
           ,1
           ,null
           ,null
           ,1
           ,'Return Analysis'
           ,3
           ,2)
END
IF (NOT EXISTS (SELECT *  FROM [dbo].[M_CLOSubPageDetails] WHERE TabId=10))
BEGIN
INSERT INTO [dbo].[M_CLOSubPageDetails]
           ([TabId]
           ,[TabName]
           ,[IsDeleted]
           ,[CreatedOn]
           ,[CreatedBy]
           ,[ModifiedOn]
           ,[ModifiedBy]
           ,[PageId]
           ,[AliasName]
           ,[SequenceNo]
           ,[ParentId])
     VALUES
           (10
           ,'Return Composition'
           ,0
           ,GETUTCDATE()
           ,1
           ,null
           ,null
           ,1
           ,'Return Composition'
           ,4
           ,2)
END
IF (NOT EXISTS (SELECT *  FROM [dbo].[M_CLOSubPageDetails] WHERE TabId=11))
BEGIN
INSERT INTO [dbo].[M_CLOSubPageDetails]
           ([TabId]
           ,[TabName]
           ,[IsDeleted]
           ,[CreatedOn]
           ,[CreatedBy]
           ,[ModifiedOn]
           ,[ModifiedBy]
           ,[PageId]
           ,[AliasName]
           ,[SequenceNo]
           ,[ParentId])
     VALUES
           (11
           ,'Currency Exposure'
           ,0
           ,GETUTCDATE()
           ,1
           ,null
           ,null
           ,1
           ,'Currency Exposure'
           ,5
           ,2)
END
IF (NOT EXISTS (SELECT *  FROM [dbo].[M_CLOSubPageDetails] WHERE TabId=12))
BEGIN
INSERT INTO [dbo].[M_CLOSubPageDetails]
           ([TabId]
           ,[TabName]
           ,[IsDeleted]
           ,[CreatedOn]
           ,[CreatedBy]
           ,[ModifiedOn]
           ,[ModifiedBy]
           ,[PageId]
           ,[AliasName]
           ,[SequenceNo]
           ,[ParentId])
     VALUES
           (12
           ,'Key KPI'
           ,0
           ,GETUTCDATE()
           ,1
           ,null
           ,null
           ,2
           ,'Key KPI'
           ,1
           ,5)
END
IF (NOT EXISTS (SELECT *  FROM [dbo].[M_CLOSubPageDetails] WHERE TabId=13))
BEGIN
INSERT INTO [dbo].[M_CLOSubPageDetails]
           ([TabId]
           ,[TabName]
           ,[IsDeleted]
           ,[CreatedOn]
           ,[CreatedBy]
           ,[ModifiedOn]
           ,[ModifiedBy]
           ,[PageId]
           ,[AliasName]
           ,[SequenceNo]
           ,[ParentId])
     VALUES
           (13
           ,'CLO Tests'
           ,0
           ,GETUTCDATE()
           ,1
           ,null
           ,null
           ,2
           ,'CLO Tests'
           ,2
           ,5)
END
IF (NOT EXISTS (SELECT *  FROM [dbo].[M_CLOSubPageDetails] WHERE TabId=14))
BEGIN
INSERT INTO [dbo].[M_CLOSubPageDetails]
           ([TabId]
           ,[TabName]
           ,[IsDeleted]
           ,[CreatedOn]
           ,[CreatedBy]
           ,[ModifiedOn]
           ,[ModifiedBy]
           ,[PageId]
           ,[AliasName]
           ,[SequenceNo]
           ,[ParentId])
     VALUES
           (14
           ,'CLO Versus Domicile CLO Sector'
           ,0
           ,GETUTCDATE()
           ,1
           ,null
           ,null
           ,2
           ,'CLO Versus Domicile CLO Sector'
           ,3
           ,5)
END
GO

IF OBJECT_ID('[dbo].[GetCLOPageConfiguration]', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE [dbo].[GetCLOPageConfiguration]
END
GO
CREATE Procedure [dbo].[GetCLOPageConfiguration] 
@CompanyId int,
@PageId int
as
Begin
SELECT Distinct
    COALESCE(company.InvestmentCompanyID, 0) AS CompanyId,
    company.InvestmentCompanyName AS CompanyName,
    tab.TabId AS TabId,
    tab.PageId AS PageId,
    tab.tabName AS [Name],
    ISNULL(pagetab.isDelete, tab.isDeleted) AS isDeleted,
    ISNULL(pagetab.isActive, 1) AS isActive,
    ISNULL(pagetab.aliasName, tab.aliasName) AS AliasName,
    ISNULL(pagetab.SequenceNo, tab.SequenceNo) AS SequenceNo,
    ISNULL(pagetab.id, 0) AS id
FROM 
    M_CLOPageDetails p
JOIN 
    CLO_MTabDetails tab ON p.pageId = tab.PageId
LEFT JOIN 
    CLO_MappingTabDetails pageTab ON tab.tabid = pageTab.tabId AND pageTab.CompanyId = @CompanyId 
LEFT JOIN 
    CLO_InvestmentCompanyDetails company ON pageTab.CompanyId = company.InvestmentCompanyID
WHERE 
    tab.PageId = @PageId and tab.ParentId is null
ORDER BY 
    SequenceNo;
End
GO
IF OBJECT_ID('[dbo].[GetCLOSubPageConfiguration]', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE [dbo].[GetCLOSubPageConfiguration]
END
GO
Create Procedure [dbo].[GetCLOSubPageConfiguration] 
@CompanyId int,
@TabId int
as
Begin
SELECT Distinct
    COALESCE(company.InvestmentCompanyID, 0) AS CompanyId,
    company.InvestmentCompanyName AS CompanyName,
    childTab.TabId AS TabId,
    childTab.PageId AS PageId,
    childTab.tabName AS [Name],
    ISNULL(pagetab.isDelete, childTab.isDeleted) AS isDeleted,
    ISNULL(pagetab.isActive, 1) AS isActive,
    ISNULL(pagetab.aliasName, childTab.aliasName) AS AliasName,
    ISNULL(pagetab.SequenceNo, childTab.SequenceNo) AS SequenceNo,
    ISNULL(pagetab.id, 0) AS id,
	ISNULL(childTab.ParentId, 0) AS ParentId
FROM 
    M_CLOPageDetails p
JOIN 
    CLO_MTabDetails tab ON p.pageId = tab.PageId
	inner join CLO_MTabDetails childTab on tab.TabId=childTab.ParentId
LEFT JOIN 
    CLO_MappingTabDetails pageTab ON childTab.TabId = pageTab.tabId AND pageTab.CompanyId = @CompanyId
LEFT JOIN 
    CLO_InvestmentCompanyDetails company ON pageTab.CompanyId = company.InvestmentCompanyID
WHERE 
	childTab.ParentId =@TabId
ORDER BY 
    SequenceNo
end
GO
--------------------END DB changes related to US 8150----------------------------

--------------------------------DB changes related to US 8436----------------------
IF NOT EXISTS (
    SELECT * 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'CLOPageTabDetails' 
    AND COLUMN_NAME = 'CLOId'
) and EXISTS (SELECT * FROM sysobjects WHERE name='CLOPageTabDetails'  AND xtype='U')
BEGIN
    ALTER table CLOPageTabDetails
    Add CLOId int null;
END
GO

IF OBJECT_ID('[dbo].[GetCLOSubPageConfiguration]', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE [dbo].[GetCLOSubPageConfiguration]
END
GO
CREATE Procedure [dbo].[GetCLOSubPageConfiguration]
@CompanyId int,
@TabId int,
@CLOId int=null
as
Begin
SELECT Distinct
    COALESCE(company.InvestmentCompanyID, 0) AS CompanyId,
    company.InvestmentCompanyName AS CompanyName,
    childTab.TabId AS TabId,
    childTab.PageId AS PageId,
    childTab.tabName AS [Name],
    ISNULL(pagetab.isDelete, childTab.isDeleted) AS isDeleted,
    ISNULL(pagetab.isActive, 1) AS isActive,
    ISNULL(pagetab.aliasName, childTab.aliasName) AS AliasName,
    ISNULL(pagetab.SequenceNo, childTab.SequenceNo) AS SequenceNo,
    ISNULL(pagetab.id, 0) AS id,
	ISNULL(childTab.ParentId, 0) AS ParentId,
	ISNULL(pagetab.CLOId, 0) AS CloId
FROM 
    M_CLOPageDetails p
JOIN 
    CLO_MTabDetails tab ON p.pageId = tab.PageId
	inner join CLO_MTabDetails childTab on tab.TabId=childTab.ParentId
LEFT JOIN 
    CLO_MappingTabDetails pageTab ON childTab.TabId = pageTab.tabId AND pageTab.CompanyId = @CompanyId And pageTab.CLOId = @CLOId
LEFT JOIN 
    CLO_InvestmentCompanyDetails company ON pageTab.CompanyId = company.InvestmentCompanyID
WHERE  
	childTab.ParentId =@TabId
ORDER BY 
    SequenceNo
end
GO

IF OBJECT_ID('[dbo].[GetCLOPageConfiguration]', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE [dbo].[GetCLOPageConfiguration]
END
GO
CREATE Procedure [dbo].[GetCLOPageConfiguration] 
@CompanyId int,
@PageId int,
@CLOId int=0
as
Begin
SELECT Distinct
    COALESCE(company.InvestmentCompanyID, 0) AS CompanyId,
    company.InvestmentCompanyName AS CompanyName,
    tab.TabId AS TabId,
    tab.PageId AS PageId,
    tab.tabName AS [Name],
    ISNULL(pagetab.isDelete, tab.isDeleted) AS isDeleted,
    ISNULL(pagetab.isActive, 1) AS isActive,
    ISNULL(pagetab.aliasName, tab.aliasName) AS AliasName,
    ISNULL(pagetab.SequenceNo, tab.SequenceNo) AS SequenceNo,
    ISNULL(pagetab.id, 0) AS id
FROM 
    M_CLOPageDetails p
JOIN 
    CLO_MTabDetails tab ON p.pageId = tab.PageId
LEFT JOIN 
    CLO_MappingTabDetails pageTab ON tab.tabid = pageTab.tabId AND pageTab.CompanyId = @CompanyId And pageTab.CLOId = @CLOId
LEFT JOIN 
    CLO_InvestmentCompanyDetails company ON pageTab.CompanyId = company.InvestmentCompanyID
WHERE 
    tab.PageId = @PageId and tab.ParentId is null
ORDER BY 
    SequenceNo;
End
GO

--------------------------------END DB changes related to US 8436----------------------
----------------------Renaming tables--------------------
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CLO_MappingTabDetails' AND xtype='U')
BEGIN
EXEC sp_rename 'CLOPageTabDetails', 'CLO_MappingTabDetails';
END
GO
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CLO_MTabDetails' AND xtype='U')
BEGIN
EXEC sp_rename 'M_CLOSubPageDetails', 'CLO_MTabDetails';
END
GO
IF NOT EXISTS (
    SELECT * 
    FROM sys.columns 
    WHERE object_id = OBJECT_ID('CLO_MTabDetails') 
    AND name = 'TabId' 
    AND is_identity = 1
)
BEGIN
-- Step 1: Create a new table with the identity column
CREATE TABLE CLO_MTabDetails_New (
    [TabId] INT IDENTITY(1,1) PRIMARY KEY,
    [PageId] [int] NULL,
    [ParentId] int null,
    [TabName] [nvarchar](200) NULL,
    [IsDeleted] [bit] NOT NULL,
    [CreatedOn] [datetime] NOT NULL,
    [CreatedBy] [int] NOT NULL,
    [ModifiedOn] [datetime] NULL,
    [ModifiedBy] [int] NULL,
    [AliasName] [nvarchar](200) NOT NULL,
    [SequenceNo] [int] NULL
);

-- Step 2: Copy the data from the old table to the new table
SET IDENTITY_INSERT CLO_MTabDetails_New ON;
INSERT INTO CLO_MTabDetails_New ([TabId]          ,[TabName]   ,[PageId]          ,[AliasName]          ,[SequenceNo]          ,[ParentId]       ,[IsDeleted]          ,[CreatedOn]          ,[CreatedBy]   ,[ModifiedBy],  [ModifiedOn]       )
SELECT [TabId]          ,[TabName]   ,[PageId]          ,[AliasName]          ,[SequenceNo]          ,[ParentId]       ,[IsDeleted]          ,[CreatedOn]          ,[CreatedBy]  ,[ModifiedBy],[ModifiedOn]   FROM CLO_MTabDetails;
SET IDENTITY_INSERT CLO_MTabDetails_New OFF;
-- Step 3: Drop the old table
DROP TABLE CLO_MTabDetails;

-- Step 4: Rename the new table to the original table name
EXEC sp_rename 'CLO_MTabDetails_New', 'CLO_MTabDetails';

END
GO
-- Step 1: Check if the primary key constraint exists
IF NOT EXISTS (
    SELECT * 
    FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS 
    WHERE CONSTRAINT_TYPE = 'PRIMARY KEY' 
    AND TABLE_NAME = 'CLO_MTabDetails'
)
BEGIN
    -- Step 2: Add the primary key constraint if it doesn't exist
    ALTER TABLE CLO_MTabDetails
    ADD CONSTRAINT PK_CLO_MTabDetails PRIMARY KEY (TabId);
END
GO
----------------------END Renaming tables--------------------
--------------------------------END DB changes related to US 8436----------------------
-- KPI config Table model creation ---
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CLO_MModule' AND xtype='U')
BEGIN
CREATE TABLE [dbo].[CLO_MModule](
	[ModuleID] [int] IDENTITY(1,1) NOT NULL,
	[ModuleType] [nvarchar](150) NULL,
	[CreatedOn] [datetime] NOT NULL,
	[CreatedBy] [int] NOT NULL,
	[ModifiedOn] [datetime] NULL,
	[ModifiedBy] [int] NULL,
	[IsDeleted] [bit] NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[ModuleID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]

-- default dataset for CLO_MModule
INSERT INTO [dbo].[CLO_MModule] ([ModuleType], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [IsDeleted]) VALUES 
(N'Investment Company', GETDATE(), 1, NULL, NULL, 0),
(N'CLO', GETDATE(), 1, NULL, NULL, 0)

END;
--end of Clo module table creation
GO
--Clo table type creation
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CLO_MTableType' AND xtype='U')  
BEGIN
-- if conditon
CREATE TABLE [dbo].[CLO_MTableType](
	[TableTypeID] [int] IDENTITY(1,1) NOT NULL,
	[TableType] [nvarchar](150) NULL,
	[CreatedOn] [datetime] NOT NULL,
	[CreatedBy] [int] NOT NULL,
	[ModifiedOn] [datetime] NULL,
	[ModifiedBy] [int] NULL,
	[IsDeleted] [bit] NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[TableTypeID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]

-- default dataset for CLO_MTableType
INSERT INTO [dbo].[CLO_MTableType] ([TableType], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [IsDeleted]) VALUES
(N'Static', GETDATE(), 1, NULL, NULL, 0),
(N'Flat', GETDATE(), 1, NULL, NULL, 0),
(N'Pivot', GETDATE(), 1, NULL, NULL, 0)

END;
--end of Clo table type creation
GO
--Clo KPI table creation
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CLO_MKPITable' AND xtype='U')
BEGIN
CREATE TABLE [dbo].[CLO_MKPITable](
	[TableID] [int] IDENTITY(1,1) PRIMARY KEY,
	[TableIdentifier] [nvarchar](150) NOT NULL,
	[TableObjectName] [nvarchar](150) NOT NULL,
	[TableAliasName] [nvarchar](250) NULL,
	[TableType] [int] NULL,
    [ModuleType] [int] NULL,
    [TabId] [int] NULL,
	[CreatedOn] [datetime] NOT NULL,
	[CreatedBy] [int] NOT NULL,
	[ModifiedOn] [datetime] NULL,
	[ModifiedBy] [int] NULL,
	[IsDeleted] [bit] NOT NULL,
UNIQUE NONCLUSTERED 
(
	[TableIdentifier] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]


ALTER TABLE [dbo].[CLO_MKPITable]  WITH CHECK ADD FOREIGN KEY([TableType])
REFERENCES [dbo].[CLO_MTableType] ([TableTypeID])


ALTER TABLE [dbo].[CLO_MKPITable]  WITH CHECK ADD FOREIGN KEY([ModuleType])
REFERENCES [dbo].[CLO_MModule] ([ModuleID])

-- default dataset for CLO_MKPITable
INSERT INTO [dbo].[CLO_MKPITable]
([TableIdentifier], [TableObjectName], [TableAliasName], [TableType], [ModuleType], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [IsDeleted])
VALUES
('NAV_Distribution', 'CLO_Nav_Distribution', 'Nav & Distribution', 1, 1, GETDATE(), 1, GETDATE(), 1, 0),
('PE_Performance_Indicators', 'CLO_PE_Performance_Indicator', 'PE Performance Indicator', 2, 1, GETDATE(), 1, GETDATE(), 1, 0),
('Return_Analysis', 'CLO_Return_Analysis', 'Return Analysis', 2, 1, GETDATE(), 1, GETDATE(), 1, 0),
('Currency_Exposure', 'CLO_CurrencyExposure', 'Current Exposer', 1, 1, GETDATE(), 1, GETDATE(), 1, 0),
('GLI_Portfolio_Composition', 'CLO_GLI_Portfolio_Composition', 'GLI Portfolio Composition', 2, 1, GETDATE(), 1, GETDATE(), 1, 0),
('Return_Composition', 'CLO_Return_Composition', 'Return_Composition', 3, 1, GETDATE(), 1, GETDATE(), 1, 0),
('Aggregate_CLO_Metrics_EU', 'CLO_Aggregate_CLO_Metrics_EU', 'Aggregate CLO Metrics', 3, 1, GETDATE(), 1, GETDATE(), 1, 0),
('Aggregate_CLO_Metrics_US', 'CLO_Aggregate_CLO_Metrics_US', 'Aggregate CLO Metrics', 3, 1, GETDATE(), 1, GETDATE(), 1, 0),
('Capital_Structure', 'CLO_Capital_Structure', 'Capital Structure', 2, 2, GETDATE(), 1, GETDATE(), 1, 0),
('Key_KPIs_US', 'Key_KPIs_US', 'Key KPIs', 3, 2, GETDATE(), 1, GETDATE(), 1, 0),
('Key_KPIs_EU', 'CLO_Key_KPIs_EU', 'Key KPIs', 3, 2, GETDATE(), 1, GETDATE(), 1, 0),
('KPI_History_EU', 'CLO_KPI_History_EU', 'CLO KPI History', 3, 2, GETDATE(), 1, GETDATE(), 1, 0),
('KPI_History_US', 'CLO_KPI_History_US', 'CLO KPI History', 3, 2, GETDATE(), 1, GETDATE(), 1, 0),
('KPI_Summary', 'CLO_KPI_Summary', 'Summary', 1, 2, GETDATE(), 1, GETDATE(), 1, 0),
('CLO_Versus_CLO_Sector', 'CLO_Versus_CLO_Sector', 'CLO Versus CLO Sector', 1, 2, GETDATE(), 1, GETDATE(), 1, 0),
('Collateral', 'CLO_Collateral', 'Collateral', 1, 2, GETDATE(), 1, GETDATE(), 1, 0),
('Overcollateralisation_Test', 'CLO_Overcollateralisation_Test', 'Overcollateralisation Test', 2, 2, GETDATE(), 1, GETDATE(), 1, 0),
('Collateral_Quality_Test', 'CLO_Collateral_Quality_Test', 'Collateral Quality Test', 2, 2, GETDATE(), 1, GETDATE(), 1, 0),
('CLO_Distributions_To_Date', 'CLO_Distributions_To_Date', 'CLO Distributions To Date', 2, 2, GETDATE(), 1, GETDATE(), 1, 0),
('Leverage', 'CLO_Leverage', 'Leverage', 1, 2, GETDATE(), 1, GETDATE(), 1, 0);

END
GO
--Clo KPI Type table creation
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CLO_MKPIType' AND xtype='U')
BEGIN
CREATE TABLE [dbo].[CLO_MKPIType]
(
    [KPITypeId] INT PRIMARY KEY,
    [KPITypeName] NVARCHAR(50) NOT NULL,
    CONSTRAINT [UC_KPITypeName] UNIQUE ([KPITypeName])
)
-- default dataset for CLO_MKPIColumnType
INSERT INTO [dbo].[CLO_MKPIType] 
VALUES 
(1, 'String'),
(2, 'Number'),
(3, 'Currency'),
(4, 'Percentage'),
(5, 'Date'),
(6, 'Alphanumeric')

END;
GO
--Clo KPI Currency Unit table creation
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CLO_MCurrencyUnit' AND xtype='U')
BEGIN
CREATE TABLE [dbo].[CLO_MCurrencyUnit]
(
    [CurrencyUnitId] INT PRIMARY KEY,
    [CurrencyUnitName] NVARCHAR(50) NOT NULL,
    CONSTRAINT [UC_CurrencyUnitName] UNIQUE ([CurrencyUnitName])
)

-- default dataset for CLO_MCurrencyUnit
INSERT INTO [dbo].[CLO_MCurrencyUnit]
VALUES
(1, 'Default'),
(2, 'Thousands'),
(3, 'Millions'),
(4, 'Billions')
END;
GO
--Clo KPI Column table creation
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CLO_MColumnKPI' AND xtype='U')
BEGIN
CREATE TABLE [dbo].[CLO_MColumnKPI]
(
    [ColumnKPIId] INT IDENTITY(1,1) PRIMARY KEY,
    [KPITableID] INT NOT NULL,
    [ColumnName] NVARCHAR(150) NOT NULL,
    [ColumnAliasName] NVARCHAR(250),
    [KPIColumType] INT NOT NULL, -- Maps to ColumnType enum
    [IsUniqueIdentifier] BIT NOT NULL DEFAULT(0),
    [IsHidden] BIT NOT NULL DEFAULT(0),
    [ParentColumnKPIId] INT NULL, -- Self-referential foreign key
    [EnableLink] BIT NOT NULL DEFAULT(0),
    [IsStaticTableHeader] BIT NOT NULL DEFAULT(0),
    [CurrencyUnit] INT NULL, -- Maps to CurrencyUnit enum
    [DecimalUnits] INT NULL,
    [DateFormat] NVARCHAR(50) NULL,
    [ConversionMethodology] INT NULL,
    [Description] NVARCHAR(350),
    [CreatedOn] DATETIME NOT NULL DEFAULT(GETDATE()),
    [CreatedBy] INT NOT NULL,
    [ModifiedOn] DATETIME NOT NULL DEFAULT(GETDATE()),
    [ModifiedBy] INT NOT NULL,
    [IsDeleted] BIT NOT NULL DEFAULT(0),
    
    CONSTRAINT [FK_ColumnKPI_TableMetadata] FOREIGN KEY ([KPITableID]) 
        REFERENCES [dbo].[CLO_MKPITable]([TableID]),
    CONSTRAINT [FK_ColumnKPI_KPIType] FOREIGN KEY ([KPIColumType]) 
        REFERENCES [dbo].[CLO_MKPIType]([KPITypeId]),
    CONSTRAINT [FK_ColumnKPI_CurrencyUnit] FOREIGN KEY ([CurrencyUnit]) 
        REFERENCES [dbo].[CLO_MCurrencyUnit]([CurrencyUnitId]),
    CONSTRAINT [UC_TableColumn] UNIQUE ([KPITableID], [ColumnName])
)

-- Primary indexes for performance
CREATE INDEX [IX_KPITableID] ON [dbo].[CLO_MColumnKPI]([KPITableID])
CREATE INDEX [IX_ColumnName] ON [dbo].[CLO_MColumnKPI]([ColumnName])
CREATE INDEX [IX_IsDeleted] ON [dbo].[CLO_MColumnKPI]([IsDeleted])
CREATE INDEX [IX_ParentColumnKPIId] ON [dbo].[CLO_MColumnKPI]([ParentColumnKPIId])

--default dataset for CLO_MColumnKPI
-- Auto-generated INSERT statements for CLO_MColumnKPI
SET NOCOUNT ON;
DECLARE @UserId INT = 1;
DECLARE @Now DATETIME = GETDATE();

-- Columns for CLO_Nav_Distribution
DECLARE @CLO_Nav_DistributionId INT = (SELECT TableID FROM CLO_MKPITable WHERE TableObjectName = 'CLO_Nav_Distribution');

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Nav_DistributionId, 
                    'NAV Per EUR Note (Series 1,2,3 & 4)', 
                    'NAV Per EUR Note (Series 1,2,3 & 4)', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Nav_DistributionId, 
                    'NAV per USD Note (Series 3)', 
                    'NAV per USD Note (Series 3)', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Nav_DistributionId, 
                    'NAV per USD Note (Series 1)', 
                    'NAV per USD Note (Series 1)', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Nav_DistributionId, 
                    'NAV per USD Note (Series 4)', 
                    'NAV per USD Note (Series 4)', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Nav_DistributionId, 
                    'Net Assets Valuation of Notes Issued', 
                    'Net Assets Valuation of Notes Issued', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Nav_DistributionId, 
                    'Distribution: Series 1 EUR - 2023', 
                    'Distribution: Series 1 EUR - 2023', 
                    5, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Nav_DistributionId, 
                    'Distribution: Series 1 EUR - 2024 YTD', 
                    'Distribution: Series 1 EUR - 2024 YTD', 
                    5, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Nav_DistributionId, 
                    'Distribution: Series 1 USD - 2024 YTD', 
                    'Distribution: Series 1 USD - 2024 YTD', 
                    5, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Nav_DistributionId, 
                    'Distribution: Series 1 USD - 2023', 
                    'Distribution: Series 1 USD - 2023', 
                    5, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Nav_DistributionId, 
                    'No.of distributions to date', 
                    'No.of distributions to date', 
                    2, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Nav_DistributionId, 
                    'Frequency of distribution', 
                    'Frequency of distribution', 
                    1, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

-- Columns for CLO_PE_Performance_Indicator
DECLARE @CLO_PE_Performance_IndicatorId INT = (SELECT TableID FROM CLO_MKPITable WHERE TableObjectName = 'CLO_PE_Performance_Indicator');

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_PE_Performance_IndicatorId, 
                    'Metrics', 
                    'Metrics', 
                    1, 
                    1, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_PE_Performance_IndicatorId, 
                    'Quarter to 28 June 2024', 
                    'Quarter to 28 June 2024', 
                    1, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_PE_Performance_IndicatorId, 
                    'YTD 28 June 2024', 
                    'YTD 28 June 2024', 
                    1, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_PE_Performance_IndicatorId, 
                    'Since Inception(September 2019)', 
                    'Since Inception(September 2019)', 
                    1, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

-- Columns for CLO_Return_Analysis
DECLARE @CLO_Return_AnalysisId INT = (SELECT TableID FROM CLO_MKPITable WHERE TableObjectName = 'CLO_Return_Analysis');

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Return_AnalysisId, 
                    'Metrics', 
                    'Metrics', 
                    1, 
                    1, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Return_AnalysisId, 
                    'GLI I Time weighted Net EUR Return (Including FX gain/Loss)', 
                    'GLI I Time weighted Net EUR Return (Including FX gain/Loss)', 
                    5, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Return_AnalysisId, 
                    'European Loans Index (CS West European Index)', 
                    'European Loans Index (CS West European Index)', 
                    5, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Return_AnalysisId, 
                    'U.S.Loans Index (Morningstar Index)', 
                    'U.S.Loans Index (Morningstar Index)', 
                    5, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

-- Columns for CLO_CurrencyExposure
DECLARE @CLO_CurrencyExposureId INT = (SELECT TableID FROM CLO_MKPITable WHERE TableObjectName = 'CLO_CurrencyExposure');

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_CurrencyExposureId, 
                    'USD demonination assets', 
                    'USD demonination assets', 
                    5, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_CurrencyExposureId, 
                    'EURO denomination assets', 
                    'EURO denomination assets', 
                    5, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_CurrencyExposureId, 
                    'Total', 
                    'Total', 
                    5, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

-- Columns for CLO_GLI_Portfolio_Composition
DECLARE @CLO_GLI_Portfolio_CompositionId INT = (SELECT TableID FROM CLO_MKPITable WHERE TableObjectName = 'CLO_GLI_Portfolio_Composition');

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_GLI_Portfolio_CompositionId, 
                    'Transaction', 
                    'Transaction', 
                    1, 
                    1, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_GLI_Portfolio_CompositionId, 
                    'CLO Size', 
                    'CLO Size', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    3, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_GLI_Portfolio_CompositionId, 
                    'PAR Local', 
                    'PAR Local', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_GLI_Portfolio_CompositionId, 
                    'Amortised cost', 
                    'Amortised cost', 
                    2, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_GLI_Portfolio_CompositionId, 
                    'Model valuation', 
                    'Model valuation', 
                    2, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_GLI_Portfolio_CompositionId, 
                    'NAV % (Equity)', 
                    'NAV % (Equity)', 
                    5, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_GLI_Portfolio_CompositionId, 
                    'Model Valuation in Euros', 
                    'Model Valuation in Euros', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_GLI_Portfolio_CompositionId, 
                    'WA Coupon', 
                    'WA Coupon', 
                    2, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_GLI_Portfolio_CompositionId, 
                    'WA Cost of Capital', 
                    'WA Cost of Capital', 
                    2, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_GLI_Portfolio_CompositionId, 
                    'WA Arbritage', 
                    'WA Arbritage', 
                    2, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_GLI_Portfolio_CompositionId, 
                    'Distributions Annualized', 
                    'Distributions Annualized', 
                    5, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_GLI_Portfolio_CompositionId, 
                    'Realised warehouse IRRs', 
                    'Realised warehouse IRRs', 
                    5, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_GLI_Portfolio_CompositionId, 
                    'Indicative Projected IRR', 
                    'Indicative Projected IRR', 
                    5, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_GLI_Portfolio_CompositionId, 
                    'Indicative Projected Multiple', 
                    'Indicative Projected Multiple', 
                    1, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_GLI_Portfolio_CompositionId, 
                    'End reinvestment date', 
                    'End reinvestment date', 
                    1, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

-- Columns for CLO_Return_Composition
DECLARE @CLO_Return_CompositionId INT = (SELECT TableID FROM CLO_MKPITable WHERE TableObjectName = 'CLO_Return_Composition');

                        INSERT INTO [dbo].[CLO_MColumnKPI]
                        (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                        IsHidden, EnableLink, IsStaticTableHeader, CurrencyUnit, DecimalUnits, 
                        DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                        VALUES
                        (@CLO_Return_CompositionId, 
                        'Quarter_2_2024_Total_Return_Composition', 
                        'Quarter 2,2024 Total Return Composition', 
                        1, 
                        0, 
                        0, 
                        0, 
                        0, 
                        NULL, 
                        NULL, 
                        NULL, 
                        @Now, @UserId, @Now, @UserId, 0);
DECLARE @CLO_Return_Composition_Parent_Quarter_2_2024_Total_Return_CompositionId INT = SCOPE_IDENTITY();

                        INSERT INTO [dbo].[CLO_MColumnKPI]
                        (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                        IsHidden, EnableLink, IsStaticTableHeader, CurrencyUnit, DecimalUnits, 
                        DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                        VALUES
                        (@CLO_Return_CompositionId, 
                        'Year_to_Date_2024_Total_Return_Composition', 
                        'Year to Date 2024 Total Return Composition', 
                        1, 
                        0, 
                        0, 
                        0, 
                        0, 
                        NULL, 
                        NULL, 
                        NULL, 
                        @Now, @UserId, @Now, @UserId, 0);
DECLARE @CLO_Return_Composition_Parent_Year_to_Date_2024_Total_Return_CompositionId INT = SCOPE_IDENTITY();

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Return_CompositionId, 
                    'Metrics', 
                    'Metrics', 
                    1, 
                    1, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Return_CompositionId, 
                    'Quarter#Price', 
                    'Price', 
                    5, 
                    0, 
                    0, 
                    @CLO_Return_Composition_Parent_Quarter_2_2024_Total_Return_CompositionId, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Return_CompositionId, 
                    'Quarter#Income', 
                    'Income', 
                    5, 
                    0, 
                    0, 
                    @CLO_Return_Composition_Parent_Quarter_2_2024_Total_Return_CompositionId, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Return_CompositionId, 
                    'Quarter#Fx', 
                    'FX', 
                    5, 
                    0, 
                    0, 
                    @CLO_Return_Composition_Parent_Quarter_2_2024_Total_Return_CompositionId, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Return_CompositionId, 
                    'Quarter#Total', 
                    'Total', 
                    5, 
                    0, 
                    0, 
                    @CLO_Return_Composition_Parent_Quarter_2_2024_Total_Return_CompositionId, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Return_CompositionId, 
                    'Year#Price', 
                    'Price', 
                    5, 
                    0, 
                    0, 
                    @CLO_Return_Composition_Parent_Year_to_Date_2024_Total_Return_CompositionId, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Return_CompositionId, 
                    'Year#Income', 
                    'Income', 
                    5, 
                    0, 
                    0, 
                    @CLO_Return_Composition_Parent_Year_to_Date_2024_Total_Return_CompositionId, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Return_CompositionId, 
                    'Year#Fx', 
                    'FX', 
                    5, 
                    0, 
                    0, 
                    @CLO_Return_Composition_Parent_Year_to_Date_2024_Total_Return_CompositionId, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Return_CompositionId, 
                    'Year#Total', 
                    'Total', 
                    5, 
                    0, 
                    0, 
                    @CLO_Return_Composition_Parent_Year_to_Date_2024_Total_Return_CompositionId, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Return_CompositionId, 
                    'GLI_I_NetAssets', 
                    'GLI I NetAssets', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    3, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

-- Columns for CLO_Aggregate_CLO_Metrics_EU
DECLARE @CLO_Aggregate_CLO_Metrics_EUId INT = (SELECT TableID FROM CLO_MKPITable WHERE TableObjectName = 'CLO_Aggregate_CLO_Metrics_EU');

                        INSERT INTO [dbo].[CLO_MColumnKPI]
                        (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                        IsHidden, EnableLink, IsStaticTableHeader, CurrencyUnit, DecimalUnits, 
                        DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                        VALUES
                        (@CLO_Aggregate_CLO_Metrics_EUId, 
                        '__CCC_Caa_Loans', 
                        '% CCC/Caa Loans', 
                        1, 
                        0, 
                        0, 
                        0, 
                        0, 
                        NULL, 
                        NULL, 
                        NULL, 
                        @Now, @UserId, @Now, @UserId, 0);
DECLARE @CLO_Aggregate_CLO_Metrics_EU_Parent___CCC_Caa_LoansId INT = SCOPE_IDENTITY();

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Aggregate_CLO_Metrics_EUId, 
                    'Transaction', 
                    'Transaction', 
                    1, 
                    1, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Aggregate_CLO_Metrics_EUId, 
                    'AsofDate', 
                    'As of Date', 
                    1, 
                    1, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Aggregate_CLO_Metrics_EUId, 
                    'CLO_AUM', 
                    'CLO AUM', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    3, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Aggregate_CLO_Metrics_EUId, 
                    'Weighted_Average_Spread', 
                    'Weighted Average Spread', 
                    2, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Aggregate_CLO_Metrics_EUId, 
                    'ARB', 
                    'ARB', 
                    5, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Aggregate_CLO_Metrics_EUId, 
                    'Weighted_Average_Rating_Factor', 
                    'Weighted Average Rating Factor', 
                    2, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Aggregate_CLO_Metrics_EUId, 
                    'c1', 
                    'RA 1', 
                    1, 
                    0, 
                    0, 
                    @CLO_Aggregate_CLO_Metrics_EU_Parent___CCC_Caa_LoansId, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Aggregate_CLO_Metrics_EUId, 
                    'c2', 
                    'Rating 1', 
                    5, 
                    0, 
                    0, 
                    @CLO_Aggregate_CLO_Metrics_EU_Parent___CCC_Caa_LoansId, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Aggregate_CLO_Metrics_EUId, 
                    'c3', 
                    'RA 2', 
                    1, 
                    0, 
                    0, 
                    @CLO_Aggregate_CLO_Metrics_EU_Parent___CCC_Caa_LoansId, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Aggregate_CLO_Metrics_EUId, 
                    'c4', 
                    'Rating 2', 
                    5, 
                    0, 
                    0, 
                    @CLO_Aggregate_CLO_Metrics_EU_Parent___CCC_Caa_LoansId, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Aggregate_CLO_Metrics_EUId, 
                    'Percentage_Loans_trading_under80', 
                    '% Loans trading under 80', 
                    5, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Aggregate_CLO_Metrics_EUId, 
                    'Weighter_Average_Purchase_Price', 
                    'Weighted Average Purchase Price', 
                    5, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Aggregate_CLO_Metrics_EUId, 
                    'Diversity_Score', 
                    'Diversity Score', 
                    2, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Aggregate_CLO_Metrics_EUId, 
                    'Pair_Gain_Loss', 
                    'Par Gain/Loss', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    3, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Aggregate_CLO_Metrics_EUId, 
                    'Pair_Gain_Loss_Post_HaircutORDefaults', 
                    'Par Gain/Loss Post Haircut/Defaults', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    3, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Aggregate_CLO_Metrics_EUId, 
                    'Defaulted Obligation PAR', 
                    'Defaulted Obligation PAR', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    3, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Aggregate_CLO_Metrics_EUId, 
                    'Default_Haircut', 
                    'Default Haircut', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    3, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Aggregate_CLO_Metrics_EUId, 
                    'GLI_Par_GainORLoss', 
                    'GLI Par Gain/Loss', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    3, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

-- Columns for CLO_Aggregate_CLO_Metrics_US
DECLARE @CLO_Aggregate_CLO_Metrics_USId INT = (SELECT TableID FROM CLO_MKPITable WHERE TableObjectName = 'CLO_Aggregate_CLO_Metrics_US');

                        INSERT INTO [dbo].[CLO_MColumnKPI]
                        (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                        IsHidden, EnableLink, IsStaticTableHeader, CurrencyUnit, DecimalUnits, 
                        DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                        VALUES
                        (@CLO_Aggregate_CLO_Metrics_USId, 
                        '__CCC_Caa_Loans', 
                        '% CCC/Caa Loans', 
                        1, 
                        0, 
                        0, 
                        0, 
                        0, 
                        NULL, 
                        NULL, 
                        NULL, 
                        @Now, @UserId, @Now, @UserId, 0);
DECLARE @CLO_Aggregate_CLO_Metrics_US_Parent___CCC_Caa_LoansId INT = SCOPE_IDENTITY();

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Aggregate_CLO_Metrics_USId, 
                    'Transaction', 
                    'Transaction', 
                    1, 
                    1, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Aggregate_CLO_Metrics_USId, 
                    'AsofDate', 
                    'As of Date', 
                    1, 
                    1, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Aggregate_CLO_Metrics_USId, 
                    'CLO_AUM', 
                    'CLO AUM', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    3, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Aggregate_CLO_Metrics_USId, 
                    'Weighted_Average_Spread', 
                    'Weighted Average Spread', 
                    2, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Aggregate_CLO_Metrics_USId, 
                    'ARB', 
                    'ARB', 
                    5, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Aggregate_CLO_Metrics_USId, 
                    'Weighted_Average_Rating_Factor', 
                    'Weighted Average Rating Factor', 
                    2, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Aggregate_CLO_Metrics_USId, 
                    'c1', 
                    'RA 1', 
                    1, 
                    0, 
                    0, 
                    @CLO_Aggregate_CLO_Metrics_US_Parent___CCC_Caa_LoansId, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Aggregate_CLO_Metrics_USId, 
                    'c2', 
                    'Rating 1', 
                    5, 
                    0, 
                    0, 
                    @CLO_Aggregate_CLO_Metrics_US_Parent___CCC_Caa_LoansId, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Aggregate_CLO_Metrics_USId, 
                    'c3', 
                    'RA 2', 
                    1, 
                    0, 
                    0, 
                    @CLO_Aggregate_CLO_Metrics_US_Parent___CCC_Caa_LoansId, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Aggregate_CLO_Metrics_USId, 
                    'c4', 
                    'Rating 2', 
                    5, 
                    0, 
                    0, 
                    @CLO_Aggregate_CLO_Metrics_US_Parent___CCC_Caa_LoansId, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Aggregate_CLO_Metrics_USId, 
                    'Percentage_Loans_trading_under80', 
                    '% Loans trading under 80', 
                    5, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Aggregate_CLO_Metrics_USId, 
                    'Weighter_Average_Purchase_Price', 
                    'Weighted Average Purchase Price', 
                    5, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Aggregate_CLO_Metrics_USId, 
                    'Diversity_Score', 
                    'Diversity Score', 
                    2, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Aggregate_CLO_Metrics_USId, 
                    'Pair_Gain_Loss', 
                    'Par Gain/Loss', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    3, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Aggregate_CLO_Metrics_USId, 
                    'Pair_Gain_Loss_Post_HaircutORDefaults', 
                    'Par Gain/Loss Post Haircut/Defaults', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    3, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Aggregate_CLO_Metrics_USId, 
                    'Defaulted_Obligation_PAR', 
                    'Defaulted Obligation PAR', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    3, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Aggregate_CLO_Metrics_USId, 
                    'Carrying_Valueof_Defaulted_Obligation', 
                    'Carrying Value of Defaulted Obligation', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    3, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Aggregate_CLO_Metrics_USId, 
                    'Default_Haircut', 
                    'Default Haircut', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    3, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

-- Columns for CLO_Capital_Structure
DECLARE @CLO_Capital_StructureId INT = (SELECT TableID FROM CLO_MKPITable WHERE TableObjectName = 'CLO_Capital_Structure');

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Capital_StructureId, 
                    'Tranche', 
                    'Tranche', 
                    1, 
                    1, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Capital_StructureId, 
                    'Type', 
                    'Type', 
                    1, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Capital_StructureId, 
                    'OrigRatings(S&P/FT)', 
                    'Orig Ratings (S&P/FT)', 
                    1, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Capital_StructureId, 
                    'CurrRatings(S&P/FT)', 
                    'Curr Ratings (S&P/FT)', 
                    1, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    3, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Capital_StructureId, 
                    'OrigBalance', 
                    'Orig Balance', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Capital_StructureId, 
                    'CurrBalance', 
                    'Curr Balance', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Capital_StructureId, 
                    'Factor', 
                    'Factor', 
                    2, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Capital_StructureId, 
                    'FloaterFormula', 
                    'Floater Formula', 
                    1, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Capital_StructureId, 
                    'Coupon', 
                    'Coupon', 
                    2, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Capital_StructureId, 
                    'CurrParSubord(defatMV)', 
                    'Curr Par Subord (def at MV)', 
                    2, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Capital_StructureId, 
                    'AccumUnrealizedWritedown', 
                    'Accum Unrealized Writedown', 
                    2, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Capital_StructureId, 
                    'AccumIntShortfall', 
                    'Accum Int Shortfall', 
                    2, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

-- Columns for Key_KPIs_US
DECLARE @Key_KPIs_USId INT = (SELECT TableID FROM CLO_MKPITable WHERE TableObjectName = 'Key_KPIs_US');

                        INSERT INTO [dbo].[CLO_MColumnKPI]
                        (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                        IsHidden, EnableLink, IsStaticTableHeader, CurrencyUnit, DecimalUnits, 
                        DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                        VALUES
                        (@Key_KPIs_USId, 
                        '__CCC_Caa_Loans', 
                        '% CCC/Caa Loans', 
                        1, 
                        0, 
                        0, 
                        0, 
                        0, 
                        NULL, 
                        NULL, 
                        NULL, 
                        @Now, @UserId, @Now, @UserId, 0);
DECLARE @Key_KPIs_US_Parent___CCC_Caa_LoansId INT = SCOPE_IDENTITY();

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@Key_KPIs_USId, 
                    'Transaction', 
                    'Transaction', 
                    1, 
                    1, 
                    0, 
                    NULL, 
                    1, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@Key_KPIs_USId, 
                    'AsofDate', 
                    'As of Date', 
                    1, 
                    1, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@Key_KPIs_USId, 
                    'CLO_AUM', 
                    'CLO AUM', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    3, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@Key_KPIs_USId, 
                    'Weighted_Average_Spread', 
                    'Weighted Average Spread', 
                    2, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@Key_KPIs_USId, 
                    'ARB', 
                    'ARB', 
                    5, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@Key_KPIs_USId, 
                    'Weighted_Average_Rating_Factor', 
                    'Weighted Average Rating Factor', 
                    2, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@Key_KPIs_USId, 
                    'c1', 
                    'RA 1', 
                    1, 
                    0, 
                    0, 
                    @Key_KPIs_US_Parent___CCC_Caa_LoansId, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@Key_KPIs_USId, 
                    'c2', 
                    'Rating 1', 
                    5, 
                    0, 
                    0, 
                    @Key_KPIs_US_Parent___CCC_Caa_LoansId, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@Key_KPIs_USId, 
                    'c3', 
                    'RA 2', 
                    1, 
                    0, 
                    0, 
                    @Key_KPIs_US_Parent___CCC_Caa_LoansId, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@Key_KPIs_USId, 
                    'c4', 
                    'Rating 2', 
                    5, 
                    0, 
                    0, 
                    @Key_KPIs_US_Parent___CCC_Caa_LoansId, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@Key_KPIs_USId, 
                    'Percentage_Loans_trading_under80', 
                    '% Loans trading under 80', 
                    5, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@Key_KPIs_USId, 
                    'Weighter_Average_Purchase_Price', 
                    'Weighted Average Purchase Price', 
                    5, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@Key_KPIs_USId, 
                    'Diversity_Score', 
                    'Diversity Score', 
                    2, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@Key_KPIs_USId, 
                    'Pair_Gain_Loss', 
                    'Par Gain/Loss', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    3, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@Key_KPIs_USId, 
                    'Pair_Gain_Loss_Post_HaircutORDefaults', 
                    'Par Gain/Loss Post Haircut/Defaults', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    3, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@Key_KPIs_USId, 
                    'Defaulted_Obligation_PAR', 
                    'Defaulted Obligation PAR', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    3, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@Key_KPIs_USId, 
                    'Carrying_Valueof_Defaulted_Obligation', 
                    'Carrying Value of Defaulted Obligation', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    3, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@Key_KPIs_USId, 
                    'Default_Haircut', 
                    'Default Haircut', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    3, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

-- Columns for CLO_Key_KPIs_EU
DECLARE @CLO_Key_KPIs_EUId INT = (SELECT TableID FROM CLO_MKPITable WHERE TableObjectName = 'CLO_Key_KPIs_EU');

                        INSERT INTO [dbo].[CLO_MColumnKPI]
                        (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                        IsHidden, EnableLink, IsStaticTableHeader, CurrencyUnit, DecimalUnits, 
                        DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                        VALUES
                        (@CLO_Key_KPIs_EUId, 
                        '__CCC_Caa_Loans', 
                        '% CCC/Caa Loans', 
                        1, 
                        0, 
                        0, 
                        0, 
                        0, 
                        NULL, 
                        NULL, 
                        NULL, 
                        @Now, @UserId, @Now, @UserId, 0);
DECLARE @CLO_Key_KPIs_EU_Parent___CCC_Caa_LoansId INT = SCOPE_IDENTITY();

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Key_KPIs_EUId, 
                    'Transaction', 
                    'Transaction', 
                    1, 
                    1, 
                    0, 
                    NULL, 
                    1, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Key_KPIs_EUId, 
                    'AsofDate', 
                    'As of Date', 
                    1, 
                    1, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Key_KPIs_EUId, 
                    'CLO_AUM', 
                    'CLO AUM', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    3, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Key_KPIs_EUId, 
                    'Weighted_Average_Spread', 
                    'Weighted Average Spread', 
                    2, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Key_KPIs_EUId, 
                    'ARB', 
                    'ARB', 
                    5, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Key_KPIs_EUId, 
                    'Weighted_Average_Rating_Factor', 
                    'Weighted Average Rating Factor', 
                    2, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Key_KPIs_EUId, 
                    'c1', 
                    'RA 1', 
                    1, 
                    0, 
                    0, 
                    @CLO_Key_KPIs_EU_Parent___CCC_Caa_LoansId, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Key_KPIs_EUId, 
                    'c2', 
                    'Rating 1', 
                    5, 
                    0, 
                    0, 
                    @CLO_Key_KPIs_EU_Parent___CCC_Caa_LoansId, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Key_KPIs_EUId, 
                    'c3', 
                    'RA 2', 
                    1, 
                    0, 
                    0, 
                    @CLO_Key_KPIs_EU_Parent___CCC_Caa_LoansId, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Key_KPIs_EUId, 
                    'c4', 
                    'Rating 2', 
                    5, 
                    0, 
                    0, 
                    @CLO_Key_KPIs_EU_Parent___CCC_Caa_LoansId, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Key_KPIs_EUId, 
                    'Percentage_Loans_trading_under80', 
                    '% Loans trading under 80', 
                    5, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Key_KPIs_EUId, 
                    'Weighter_Average_Purchase_Price', 
                    'Weighted Average Purchase Price', 
                    5, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Key_KPIs_EUId, 
                    'Diversity_Score', 
                    'Diversity Score', 
                    2, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Key_KPIs_EUId, 
                    'Pair_Gain_Loss', 
                    'Par Gain/Loss', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    3, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Key_KPIs_EUId, 
                    'Pair_Gain_Loss_Post_HaircutORDefaults', 
                    'Par Gain/Loss Post Haircut/Defaults', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    3, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Key_KPIs_EUId, 
                    'Defaulted Obligation PAR', 
                    'Defaulted Obligation PAR', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    3, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Key_KPIs_EUId, 
                    'Default_Haircut', 
                    'Default Haircut', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    3, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Key_KPIs_EUId, 
                    'GLI_Par_GainORLoss', 
                    'GLI Par Gain/Loss', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    3, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

-- Columns for CLO_Collateral
DECLARE @CLO_CollateralId INT = (SELECT TableID FROM CLO_MKPITable WHERE TableObjectName = 'CLO_Collateral');

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_CollateralId, 
                    'CurrBal', 
                    'Curr Bal', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_CollateralId, 
                    'Principal_Collection_Acct', 
                    'Principal Collection Acct', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_CollateralId, 
                    'Interest_Collection_Acct', 
                    'Interest Collection Acct', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_CollateralId, 
                    'Defaulted_Securities', 
                    'Defaulted Securities', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_CollateralId, 
                    'Loan_Bond', 
                    '% Loan / Bond', 
                    1, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

-- Columns for CLO_Overcollateralisation_Test
DECLARE @CLO_Overcollateralisation_TestId INT = (SELECT TableID FROM CLO_MKPITable WHERE TableObjectName = 'CLO_Overcollateralisation_Test');

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Overcollateralisation_TestId, 
                    'Overcollateralisation Tests', 
                    'Overcollateralisation Tests', 
                    1, 
                    1, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Overcollateralisation_TestId, 
                    'Current', 
                    'Current', 
                    6, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Overcollateralisation_TestId, 
                    'Trigger', 
                    'Trigger', 
                    6, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Overcollateralisation_TestId, 
                    'Result', 
                    'Result', 
                    1, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Overcollateralisation_TestId, 
                    'Cushion', 
                    'Cushion', 
                    6, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

-- Columns for CLO_Collateral_Quality_Test
DECLARE @CLO_Collateral_Quality_TestId INT = (SELECT TableID FROM CLO_MKPITable WHERE TableObjectName = 'CLO_Collateral_Quality_Test');

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Collateral_Quality_TestId, 
                    'Collateral_Quality_Tests', 
                    'Collateral Quality Tests', 
                    1, 
                    1, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Collateral_Quality_TestId, 
                    'Actual', 
                    'Actual', 
                    6, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Collateral_Quality_TestId, 
                    'Threshold', 
                    'Threshold', 
                    6, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

-- Columns for CLO_Distributions_To_Date
DECLARE @CLO_Distributions_To_DateId INT = (SELECT TableID FROM CLO_MKPITable WHERE TableObjectName = 'CLO_Distributions_To_Date');

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Distributions_To_DateId, 
                    'Period', 
                    'Period', 
                    1, 
                    1, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Distributions_To_DateId, 
                    'Start_Date', 
                    'Start Date', 
                    1, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Distributions_To_DateId, 
                    'End_Date_Pay_date', 
                    'End Date (Pay date)', 
                    1, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Distributions_To_DateId, 
                    'Amount', 
                    'Amount', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Distributions_To_DateId, 
                    'Percentage_Of_Cost', 
                    '% of cost (PAR)', 
                    5, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Distributions_To_DateId, 
                    'Annualised_Percentage', 
                    'Annualised (PAR) %', 
                    5, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Distributions_To_DateId, 
                    'Status', 
                    'Status', 
                    1, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Distributions_To_DateId, 
                    'Fee_rebate', 
                    'Fee rebate', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Distributions_To_DateId, 
                    'T_Inc_rebate', 
                    'T. Inc. rebate', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Distributions_To_DateId, 
                    'Percentage_Of_Cost_1', 
                    '% of cost (Gross)', 
                    5, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Distributions_To_DateId, 
                    'Annualised_Percentage_1', 
                    'Annualised (Gross) %', 
                    5, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Distributions_To_DateId, 
                    'Percentage_Of_PAR', 
                    '% of PAR', 
                    5, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Distributions_To_DateId, 
                    'Annualised_Percentage_2', 
                    'Annualised %', 
                    5, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

-- Columns for CLO_KPI_History_EU
DECLARE @CLO_KPI_History_EUId INT = (SELECT TableID FROM CLO_MKPITable WHERE TableObjectName = 'CLO_KPI_History_EU');

                        INSERT INTO [dbo].[CLO_MColumnKPI]
                        (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                        IsHidden, EnableLink, IsStaticTableHeader, CurrencyUnit, DecimalUnits, 
                        DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                        VALUES
                        (@CLO_KPI_History_EUId, 
                        '__CCC_Caa_Loans', 
                        '% CCC/Caa Loans', 
                        1, 
                        0, 
                        0, 
                        0, 
                        0, 
                        NULL, 
                        NULL, 
                        NULL, 
                        @Now, @UserId, @Now, @UserId, 0);
DECLARE @CLO_KPI_History_EU_Parent___CCC_Caa_LoansId INT = SCOPE_IDENTITY();

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_History_EUId, 
                    'Transaction', 
                    'Transaction', 
                    1, 
                    1, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_History_EUId, 
                    'AsofDate', 
                    'As of Date', 
                    1, 
                    1, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_History_EUId, 
                    'CLO_AUM', 
                    'CLO AUM', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    3, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_History_EUId, 
                    'Weighted_Average_Spread', 
                    'Weighted Average Spread', 
                    2, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_History_EUId, 
                    'ARB', 
                    'ARB', 
                    5, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_History_EUId, 
                    'Weighted_Average_Rating_Factor', 
                    'Weighted Average Rating Factor', 
                    2, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_History_EUId, 
                    'c1', 
                    'RA 1', 
                    1, 
                    0, 
                    0, 
                    @CLO_KPI_History_EU_Parent___CCC_Caa_LoansId, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_History_EUId, 
                    'c2', 
                    'Rating 1', 
                    5, 
                    0, 
                    0, 
                    @CLO_KPI_History_EU_Parent___CCC_Caa_LoansId, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_History_EUId, 
                    'c3', 
                    'RA 2', 
                    1, 
                    0, 
                    0, 
                    @CLO_KPI_History_EU_Parent___CCC_Caa_LoansId, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_History_EUId, 
                    'c4', 
                    'Rating 2', 
                    5, 
                    0, 
                    0, 
                    @CLO_KPI_History_EU_Parent___CCC_Caa_LoansId, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_History_EUId, 
                    'Percentage_Loans_trading_under80', 
                    '% Loans trading under 80', 
                    5, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_History_EUId, 
                    'Weighter_Average_Purchase_Price', 
                    'Weighted Average Purchase Price', 
                    5, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_History_EUId, 
                    'Diversity_Score', 
                    'Diversity Score', 
                    2, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    0, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_History_EUId, 
                    'Pair_Gain_Loss', 
                    'Par Gain/Loss', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    3, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_History_EUId, 
                    'Pair_Gain_Loss_Post_HaircutORDefaults', 
                    'Par Gain/Loss Post Defaults Haircut', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    3, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_History_EUId, 
                    'Defaulted_Obligation_PAR', 
                    'Defaulted Obligation PAR', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    3, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_History_EUId, 
                    'Default_Haircut', 
                    'Default Haircut', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    3, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_History_EUId, 
                    'GLI_Par_GainORLoss', 
                    'GLI Par Gain/Loss', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    3, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

-- Columns for CLO_KPI_History_US
DECLARE @CLO_KPI_History_USId INT = (SELECT TableID FROM CLO_MKPITable WHERE TableObjectName = 'CLO_KPI_History_US');

                        INSERT INTO [dbo].[CLO_MColumnKPI]
                        (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                        IsHidden, EnableLink, IsStaticTableHeader, CurrencyUnit, DecimalUnits, 
                        DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                        VALUES
                        (@CLO_KPI_History_USId, 
                        '__CCC_Caa_Loans', 
                        '% CCC/Caa Loans', 
                        1, 
                        0, 
                        0, 
                        0, 
                        0, 
                        NULL, 
                        NULL, 
                        NULL, 
                        @Now, @UserId, @Now, @UserId, 0);
DECLARE @CLO_KPI_History_US_Parent___CCC_Caa_LoansId INT = SCOPE_IDENTITY();

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_History_USId, 
                    'Transaction', 
                    'Transaction', 
                    1, 
                    1, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_History_USId, 
                    'AsofDate', 
                    'As of Date', 
                    1, 
                    1, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_History_USId, 
                    'CLO_AUM', 
                    'CLO AUM', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    3, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_History_USId, 
                    'Weighted_Average_Spread', 
                    'Weighted Average Spread', 
                    2, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_History_USId, 
                    'ARB', 
                    'ARB', 
                    5, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_History_USId, 
                    'Weighted_Average_Rating_Factor', 
                    'Weighted Average Rating Factor', 
                    2, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_History_USId, 
                    'c1', 
                    'RA 1', 
                    1, 
                    0, 
                    0, 
                    @CLO_KPI_History_US_Parent___CCC_Caa_LoansId, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_History_USId, 
                    'c2', 
                    'Rating 1', 
                    5, 
                    0, 
                    0, 
                    @CLO_KPI_History_US_Parent___CCC_Caa_LoansId, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_History_USId, 
                    'c3', 
                    'RA 2', 
                    1, 
                    0, 
                    0, 
                    @CLO_KPI_History_US_Parent___CCC_Caa_LoansId, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_History_USId, 
                    'c4', 
                    'Rating 2', 
                    5, 
                    0, 
                    0, 
                    @CLO_KPI_History_US_Parent___CCC_Caa_LoansId, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_History_USId, 
                    'Percentage_Loans_trading_under80', 
                    '% Loans trading under 80', 
                    5, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_History_USId, 
                    'Weighter_Average_Purchase_Price', 
                    'Weighted Average Purchase Price', 
                    5, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_History_USId, 
                    'Diversity_Score', 
                    'Diversity Score', 
                    2, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    0, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_History_USId, 
                    'Pair_Gain_Loss', 
                    'Par Gain/Loss', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    3, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_History_USId, 
                    'Pair_Gain_Loss_Post_HaircutORDefaults', 
                    'Par Gain/Loss Post Defaults Haircut', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    3, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_History_USId, 
                    'Defaulted_Obligation_PAR', 
                    'Defaulted Obligation PAR', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    3, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_History_USId, 
                    'Carrying_value_Defaulted_Obligation', 
                    'Carrying Value of Defaulted Obligation', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    3, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_History_USId, 
                    'Default_Haircut', 
                    'Default Haircut', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    3, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

-- Columns for CLO_KPI_Summary
DECLARE @CLO_KPI_SummaryId INT = (SELECT TableID FROM CLO_MKPITable WHERE TableObjectName = 'CLO_KPI_Summary');

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_SummaryId, 
                    'Cost_of_Capital', 
                    'Cost of Capital', 
                    5, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_SummaryId, 
                    'Target_PAR', 
                    'Target PAR', 
                    1, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    3, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_SummaryId, 
                    'GLI_Owned_Notes', 
                    'GLI Owned Notes', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_SummaryId, 
                    'Total_Notes', 
                    'Total Notes', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_SummaryId, 
                    'Reinvestment_Period_End_Date', 
                    'Reinvestment Period End Date', 
                    4, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    'dd-MMM-yyyy', 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_SummaryId, 
                    'Gross_Management_Fee', 
                    'Gross Management Fee', 
                    5, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_SummaryId, 
                    'Updated_Target_PAR', 
                    'Updated Target PAR', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_KPI_SummaryId, 
                    'PAR_Flush', 
                    'PAR Flush', 
                    3, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

-- Columns for CLO_Versus_CLO_Sector
DECLARE @CLO_Versus_CLO_SectorId INT = (SELECT TableID FROM CLO_MKPITable WHERE TableObjectName = 'CLO_Versus_CLO_Sector');

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Versus_CLO_SectorId, 
                    'Test_type', 
                    'Test type', 
                    1, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    1, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Versus_CLO_SectorId, 
                    'Caa1_CCC_or_Less', 
                    'Caa1/CCC or Less %', 
                    1, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Versus_CLO_SectorId, 
                    'Par_Build_Sen_OC', 
                    'Par Build (Sen OC) %', 
                    1, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Versus_CLO_SectorId, 
                    'Defaulted_Per', 
                    'Defaulted %', 
                    1, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Versus_CLO_SectorId, 
                    'Curr_Collat_Spread', 
                    'Curr Collat Spread', 
                    1, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Versus_CLO_SectorId, 
                    'Curr_Tranche_Spread', 
                    'Curr Tranche Spread', 
                    1, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Versus_CLO_SectorId, 
                    'Curr_Collat_Coupon', 
                    'Curr Collat Coupon', 
                    1, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Versus_CLO_SectorId, 
                    'Curr_Tranche_Coupon', 
                    'Curr Tranche Coupon', 
                    1, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Versus_CLO_SectorId, 
                    'WAL', 
                    'WAL', 
                    1, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Versus_CLO_SectorId, 
                    'WARF', 
                    'WARF', 
                    1, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Versus_CLO_SectorId, 
                    'Diversity_Score', 
                    'Diversity Score', 
                    1, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Versus_CLO_SectorId, 
                    'Cov_Lite', 
                    'Cov-Lite %', 
                    1, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Versus_CLO_SectorId, 
                    'Alt_Cov_Lite', 
                    'Alt Cov-Lite %', 
                    1, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Versus_CLO_SectorId, 
                    'Sen_Secured_Loan', 
                    'Sen Secured Loan %', 
                    1, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Versus_CLO_SectorId, 
                    'Collat_Bond', 
                    'Collat Bond %', 
                    1, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Versus_CLO_SectorId, 
                    'MVOC_BB', 
                    'MVOC (BB)', 
                    1, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Versus_CLO_SectorId, 
                    'MVOC_Equity', 
                    'MVOC (Equity)', 
                    1, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Versus_CLO_SectorId, 
                    'NAV_Equity', 
                    'NAV % (Equity)', 
                    1, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Versus_CLO_SectorId, 
                    'Total_Equity_Yld_Annualized', 
                    'Total Equity Yld (Annualized)', 
                    1, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Versus_CLO_SectorId, 
                    'Collect_Acct_Prin', 
                    'Collect Acct Prin %', 
                    1, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Versus_CLO_SectorId, 
                    'Equity_Leverage', 
                    'Equity Leverage', 
                    1, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Versus_CLO_SectorId, 
                    '2nd_Lien', 
                    '2nd Lien %', 
                    1, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_Versus_CLO_SectorId, 
                    '12mo_Turnover', 
                    '12mo Turnover', 
                    1, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

-- Columns for CLO_Leverage
DECLARE @CLO_LeverageId INT = (SELECT TableID FROM CLO_MKPITable WHERE TableObjectName = 'CLO_Leverage');

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_LeverageId, 
                    'Metrics', 
                    'Metrics', 
                    1, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_LeverageId, 
                    'OrigBalance', 
                    'Orig Balance', 
                    2, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);

                    INSERT INTO [dbo].[CLO_MColumnKPI]
                    (KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
                    IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
                    DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
                    VALUES
                    (@CLO_LeverageId, 
                    'CurrBalance', 
                    'Curr Balance', 
                    2, 
                    0, 
                    0, 
                    NULL, 
                    0, 
                    0, 
                    1, 
                    2, 
                    NULL, 
                    @Now, @UserId, @Now, @UserId, 0);


End;
------- creating RowKPI Master table ------

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[CLO_MRowKPI]') AND type in (N'U'))
BEGIN
CREATE TABLE [dbo].[CLO_MRowKPI]
(
    [RowKPIId] INT IDENTITY(1,1) PRIMARY KEY,
    [KPITableID] INT NOT NULL,
    [RowKPIName] NVARCHAR(150) NOT NULL,
    [RowAliasName] NVARCHAR(250),
    [KPIRowType] INT NOT NULL, -- Maps to ColumnType enum
    [AllowTypeOverride] BIT NOT NULL DEFAULT(0),
    [ParentRowKPIId] INT NULL, -- Self-referential foreign key
    [CurrencyUnit] INT NULL, -- Maps to CurrencyUnit enum
    [DecimalUnits] INT NULL,
    [DateFormat] NVARCHAR(50) NULL,
    [ConversionMethodology] INT NULL,
    [Description] NVARCHAR(350),
    [CreatedOn] DATETIME NOT NULL DEFAULT(GETDATE()),
    [CreatedBy] INT NOT NULL,
    [ModifiedOn] DATETIME NOT NULL DEFAULT(GETDATE()),
    [ModifiedBy] INT NOT NULL,
    [IsDeleted] BIT NOT NULL DEFAULT(0),
    
    CONSTRAINT [FK_MRowKPI_MKPITable] FOREIGN KEY ([KPITableID]) 
        REFERENCES [dbo].[CLO_MKPITable]([TableID]),
    CONSTRAINT [FK_RowKPI_KPIType] FOREIGN KEY ([KPIRowType]) 
        REFERENCES [dbo].[CLO_MKPIType]([KPITypeId]),
    CONSTRAINT [FK_RowKPI_CurrencyUnit] FOREIGN KEY ([CurrencyUnit]) 
        REFERENCES [dbo].[CLO_MCurrencyUnit]([CurrencyUnitId])  
)

-- Primary indexes for performance
CREATE INDEX [IX_KPITableID] ON [dbo].[CLO_MRowKPI]([KPITableID])
CREATE INDEX [IX_ColumnName] ON [dbo].[CLO_MRowKPI]([RowKPIName])
CREATE INDEX [IX_IsDeleted] ON [dbo].[CLO_MRowKPI]([IsDeleted])
CREATE INDEX [IX_ParentColumnKPIId] ON [dbo].[CLO_MRowKPI]([ParentRowKPIId])

-- default data set for RowKPI for different tables

DECLARE @TableMapping TABLE (TableName NVARCHAR(150), TableId INT);
INSERT INTO @TableMapping (TableName, TableId)
SELECT TableIdentifier, TableID FROM CLO_MKPITable;

DECLARE @LastInsertedId TABLE (Id INT);

DECLARE @ParentId_1 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Contego CLO VII',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'GLI_Portfolio_Composition';

SELECT @ParentId_1 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_2 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Contego CLO VIII',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'GLI_Portfolio_Composition';

SELECT @ParentId_2 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_3 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Contego CLO IX',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'GLI_Portfolio_Composition';

SELECT @ParentId_3 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_4 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Contego CLO X',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'GLI_Portfolio_Composition';

SELECT @ParentId_4 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_5 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Ocean Trails CLO 8',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'GLI_Portfolio_Composition';

SELECT @ParentId_5 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_6 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Ocean Trails CLO IX',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'GLI_Portfolio_Composition';

SELECT @ParentId_6 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_7 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Ocean Trails CLO X',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'GLI_Portfolio_Composition';

SELECT @ParentId_7 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_8 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Ocean Trails CLO XI',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'GLI_Portfolio_Composition';

SELECT @ParentId_8 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_9 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Ocean Trails CLO XII',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'GLI_Portfolio_Composition';

SELECT @ParentId_9 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_10 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Secondary Strategy Credit assets',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'GLI_Portfolio_Composition';

SELECT @ParentId_10 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_11 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Total',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'GLI_Portfolio_Composition';

SELECT @ParentId_11 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_12 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Contego VII',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Aggregate_CLO_Metrics_EU';

SELECT @ParentId_12 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_13 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Contego VIII',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Aggregate_CLO_Metrics_EU';

SELECT @ParentId_13 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_14 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Contego IX',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Aggregate_CLO_Metrics_EU';

SELECT @ParentId_14 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_15 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Contego X',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Aggregate_CLO_Metrics_EU';

SELECT @ParentId_15 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_16 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Ocean Trails VIII',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Aggregate_CLO_Metrics_US';

SELECT @ParentId_16 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_17 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Ocean Trails IX',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Aggregate_CLO_Metrics_US';

SELECT @ParentId_17 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_18 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Ocean Trails X',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Aggregate_CLO_Metrics_US';

SELECT @ParentId_18 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_19 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Ocean Trails XI',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Aggregate_CLO_Metrics_US';

SELECT @ParentId_19 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_20 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Ocean Trails XII',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Aggregate_CLO_Metrics_US';

SELECT @ParentId_20 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_21 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Gross IRR',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'PE_Performance_Indicators';

SELECT @ParentId_21 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_22 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Net IRR',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'PE_Performance_Indicators';

SELECT @ParentId_22 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_23 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Paid In Capital',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'PE_Performance_Indicators';

SELECT @ParentId_23 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_24 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Time weighted average invested capital',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'PE_Performance_Indicators';

SELECT @ParentId_24 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_25 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Income Distribution',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'PE_Performance_Indicators';

SELECT @ParentId_25 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_26 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Captital Returned',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'PE_Performance_Indicators';

SELECT @ParentId_26 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_27 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Gross MOIC',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'PE_Performance_Indicators';

SELECT @ParentId_27 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_28 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'TVPI',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'PE_Performance_Indicators';

SELECT @ParentId_28 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_29 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'DPI',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'PE_Performance_Indicators';

SELECT @ParentId_29 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_30 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Quarter 2, 2024',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Return_Analysis';

SELECT @ParentId_30 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_31 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    '2024 TYD',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Return_Analysis';

SELECT @ParentId_31 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_32 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    '2023',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Return_Analysis';

SELECT @ParentId_32 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_33 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Inception to date',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Return_Analysis';

SELECT @ParentId_33 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_34 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'EUR CLOs',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Return_Composition';

SELECT @ParentId_34 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_35 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'USD CLOs',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Return_Composition';

SELECT @ParentId_35 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_36 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'General Credit assets',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Return_Composition';

SELECT @ParentId_36 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_37 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Expenses/net cash',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Return_Composition';

SELECT @ParentId_37 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_38 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'GLI Total EUR Return',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Return_Composition';

SELECT @ParentId_38 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_39 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'A',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Capital_Structure';

SELECT @ParentId_39 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_40 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'B1',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Capital_Structure';

SELECT @ParentId_40 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_41 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'B2',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Capital_Structure';

SELECT @ParentId_41 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_42 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'C',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Capital_Structure';

SELECT @ParentId_42 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_43 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'D',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Capital_Structure';

SELECT @ParentId_43 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_44 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'E',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Capital_Structure';

SELECT @ParentId_44 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_45 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'F',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Capital_Structure';

SELECT @ParentId_45 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_46 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Subord',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Capital_Structure';

SELECT @ParentId_46 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_47 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Total',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Capital_Structure';

SELECT @ParentId_47 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_48 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Leverage',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Capital_Structure';

SELECT @ParentId_48 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_49 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Contego VII',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Key_KPIs_US';

SELECT @ParentId_49 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_50 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Ocean Trails 8',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Key_KPIs_EU';

SELECT @ParentId_50 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_51 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Class A/B Overcollateralization Test',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Overcollateralisation_Test';

SELECT @ParentId_51 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_52 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Class A/B Interest Coverage Test',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Overcollateralisation_Test';

SELECT @ParentId_52 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_53 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Class C Overcollateralization Test',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Overcollateralisation_Test';

SELECT @ParentId_53 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_54 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Class C Interest Coverage Test',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Overcollateralisation_Test';

SELECT @ParentId_54 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_55 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Class D Overcollateralization Test',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Overcollateralisation_Test';

SELECT @ParentId_55 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_56 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Class D Interest Coverage Test',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Overcollateralisation_Test';

SELECT @ParentId_56 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_57 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Class E Overcollateralization Test',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Overcollateralisation_Test';

SELECT @ParentId_57 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_58 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Class E Interest Coverage Test',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Overcollateralisation_Test';

SELECT @ParentId_58 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_59 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Class F Overcollateralization Test',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Overcollateralisation_Test';

SELECT @ParentId_59 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_60 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Interest Diversion Test',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Overcollateralisation_Test';

SELECT @ParentId_60 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_61 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Event of Default Trigger',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Overcollateralisation_Test';

SELECT @ParentId_61 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_62 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'EOD Acceleration',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Overcollateralisation_Test';

SELECT @ParentId_62 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_63 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'US Risk Retention',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Overcollateralisation_Test';

SELECT @ParentId_63 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_64 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'EU Risk Retention',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Overcollateralisation_Test';

SELECT @ParentId_64 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_65 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Wtd Avg Rating Factor',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Collateral_Quality_Test';

SELECT @ParentId_65 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_66 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Fitch Wtd Avg Rating Factor',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Collateral_Quality_Test';

SELECT @ParentId_66 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_67 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Moody''s Diversity',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Collateral_Quality_Test';

SELECT @ParentId_67 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_68 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Caa1/CCC+ or Less',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Collateral_Quality_Test';

SELECT @ParentId_68 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_69 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'S&P CCC+ or Less',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Collateral_Quality_Test';

SELECT @ParentId_69 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_70 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'S&P CCC+ or Less Balance',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Collateral_Quality_Test';

SELECT @ParentId_70 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_71 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Fitch CCC+ or Less',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Collateral_Quality_Test';

SELECT @ParentId_71 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_72 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Fitch CCC+ or Less Balance',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Collateral_Quality_Test';

SELECT @ParentId_72 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_73 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Wtd Avg Spread',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Collateral_Quality_Test';

SELECT @ParentId_73 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_74 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Wtd Avg Coupon',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Collateral_Quality_Test';

SELECT @ParentId_74 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_75 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Wtd Avg Life',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Collateral_Quality_Test';

SELECT @ParentId_75 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_76 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'S&P Recovery Rate',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Collateral_Quality_Test';

SELECT @ParentId_76 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_77 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Fitch Recovery Rate',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Collateral_Quality_Test';

SELECT @ParentId_77 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_78 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Contego VII',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_EU';

SELECT @ParentId_78 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_79 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Ocean Trails VIII',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_US';

SELECT @ParentId_79 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_80 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Q3 2020',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'CLO_Distributions_To_Date';

SELECT @ParentId_80 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_81 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Q4 2020',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'CLO_Distributions_To_Date';

SELECT @ParentId_81 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_82 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Q1 2021',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'CLO_Distributions_To_Date';

SELECT @ParentId_82 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_83 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Q2 2021',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'CLO_Distributions_To_Date';

SELECT @ParentId_83 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_84 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Q3 2021',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'CLO_Distributions_To_Date';

SELECT @ParentId_84 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_85 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Q4 2021',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'CLO_Distributions_To_Date';

SELECT @ParentId_85 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_86 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Q1 2022',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'CLO_Distributions_To_Date';

SELECT @ParentId_86 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_87 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Q2 2022',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'CLO_Distributions_To_Date';

SELECT @ParentId_87 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_88 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Q3 2022',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'CLO_Distributions_To_Date';

SELECT @ParentId_88 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_89 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Q4 2022',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'CLO_Distributions_To_Date';

SELECT @ParentId_89 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_90 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Q1 2023',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'CLO_Distributions_To_Date';

SELECT @ParentId_90 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_91 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Q2 2023',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'CLO_Distributions_To_Date';

SELECT @ParentId_91 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_92 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Q3 2023',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'CLO_Distributions_To_Date';

SELECT @ParentId_92 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_93 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Q4 2023',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'CLO_Distributions_To_Date';

SELECT @ParentId_93 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_94 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Q1 2024',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'CLO_Distributions_To_Date';

SELECT @ParentId_94 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_95 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Q2 2024',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'CLO_Distributions_To_Date';

SELECT @ParentId_95 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_96 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Q3 2024',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'CLO_Distributions_To_Date';

SELECT @ParentId_96 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

DECLARE @ParentId_97 INT;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, KPIRowType, CreatedBy, ModifiedBy)
OUTPUT inserted.RowKPIId INTO @LastInsertedId
SELECT 
    tm.TableId,
    'Cummulative distributions',
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'CLO_Distributions_To_Date';

SELECT @ParentId_97 = Id FROM @LastInsertedId;
DELETE FROM @LastInsertedId;

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '30-Aug',
    @ParentId_12,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Aggregate_CLO_Metrics_EU';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    'Qtr Chg',
    @ParentId_12,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Aggregate_CLO_Metrics_EU';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    'Annual Chg',
    @ParentId_12,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Aggregate_CLO_Metrics_EU';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '16-Sep',
    @ParentId_13,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Aggregate_CLO_Metrics_EU';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    'Qtr Chg',
    @ParentId_13,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Aggregate_CLO_Metrics_EU';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    'Annual Chg',
    @ParentId_13,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Aggregate_CLO_Metrics_EU';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '30-Aug',
    @ParentId_14,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Aggregate_CLO_Metrics_EU';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    'Qtr Chg',
    @ParentId_14,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Aggregate_CLO_Metrics_EU';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    'Annual Chg',
    @ParentId_14,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Aggregate_CLO_Metrics_EU';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '30-Aug',
    @ParentId_15,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Aggregate_CLO_Metrics_EU';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    'Qtr Chg',
    @ParentId_15,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Aggregate_CLO_Metrics_EU';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    'Annual Chg',
    @ParentId_15,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Aggregate_CLO_Metrics_EU';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '6-Sep',
    @ParentId_16,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Aggregate_CLO_Metrics_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    'Qtr Chg',
    @ParentId_16,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Aggregate_CLO_Metrics_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    'Annual Chg',
    @ParentId_16,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Aggregate_CLO_Metrics_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '6-Sep',
    @ParentId_17,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Aggregate_CLO_Metrics_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    'Qtr Chg',
    @ParentId_17,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Aggregate_CLO_Metrics_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    'Annual Chg',
    @ParentId_17,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Aggregate_CLO_Metrics_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '6-Sep',
    @ParentId_18,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Aggregate_CLO_Metrics_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    'Qtr Chg',
    @ParentId_18,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Aggregate_CLO_Metrics_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    'Annual Chg',
    @ParentId_18,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Aggregate_CLO_Metrics_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '6-Sep',
    @ParentId_19,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Aggregate_CLO_Metrics_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    'Qtr Chg',
    @ParentId_19,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Aggregate_CLO_Metrics_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    'Annual Chg',
    @ParentId_19,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Aggregate_CLO_Metrics_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '13-Sep',
    @ParentId_20,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Aggregate_CLO_Metrics_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    'Qtr Chg',
    @ParentId_20,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Aggregate_CLO_Metrics_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    'Annual Chg',
    @ParentId_20,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Aggregate_CLO_Metrics_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '31-May',
    @ParentId_49,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Key_KPIs_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    'Qtr Chg',
    @ParentId_49,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Key_KPIs_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    'Annual Chg',
    @ParentId_49,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Key_KPIs_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '31-May',
    @ParentId_50,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Key_KPIs_EU';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    'Qtr Chg',
    @ParentId_50,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Key_KPIs_EU';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    'Annual Chg',
    @ParentId_50,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'Key_KPIs_EU';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '31-Aug-22',
    @ParentId_78,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_EU';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '12-Oct-22',
    @ParentId_78,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_EU';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '31-Oct-22',
    @ParentId_78,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_EU';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '30-Nov-22',
    @ParentId_78,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_EU';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '11-Jan-23',
    @ParentId_78,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_EU';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '31-Jan-23',
    @ParentId_78,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_EU';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '28-Feb-23',
    @ParentId_78,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_EU';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '12-Apr-23',
    @ParentId_78,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_EU';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '28-Apr-23',
    @ParentId_78,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_EU';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '31-May-23',
    @ParentId_78,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_EU';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '12-Jul-23',
    @ParentId_78,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_EU';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '31-Jul-23',
    @ParentId_78,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_EU';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '31-Aug-23',
    @ParentId_78,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_EU';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '11-Oct-23',
    @ParentId_78,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_EU';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '31-Oct-23',
    @ParentId_78,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_EU';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '30-Nov-23',
    @ParentId_78,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_EU';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '11-Jan-24',
    @ParentId_78,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_EU';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '31-Jan-24',
    @ParentId_78,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_EU';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '29-Feb-24',
    @ParentId_78,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_EU';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '11-Apr-24',
    @ParentId_78,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_EU';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '30-Apr-24',
    @ParentId_78,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_EU';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '31-May-24',
    @ParentId_78,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_EU';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '11-Jul-24',
    @ParentId_78,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_EU';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '31-Jul-24',
    @ParentId_78,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_EU';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '30-Aug-24',
    @ParentId_78,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_EU';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '31-Aug-22',
    @ParentId_79,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '12-Oct-22',
    @ParentId_79,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '31-Oct-22',
    @ParentId_79,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '30-Nov-22',
    @ParentId_79,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '11-Jan-23',
    @ParentId_79,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '31-Jan-23',
    @ParentId_79,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '28-Feb-23',
    @ParentId_79,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '12-Apr-23',
    @ParentId_79,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '28-Apr-23',
    @ParentId_79,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '31-May-23',
    @ParentId_79,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '12-Jul-23',
    @ParentId_79,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '31-Jul-23',
    @ParentId_79,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '31-Aug-23',
    @ParentId_79,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '11-Oct-23',
    @ParentId_79,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '31-Oct-23',
    @ParentId_79,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '30-Nov-23',
    @ParentId_79,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '11-Jan-24',
    @ParentId_79,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '31-Jan-24',
    @ParentId_79,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '29-Feb-24',
    @ParentId_79,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '11-Apr-24',
    @ParentId_79,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '30-Apr-24',
    @ParentId_79,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '31-May-24',
    @ParentId_79,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '11-Jul-24',
    @ParentId_79,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '31-Jul-24',
    @ParentId_79,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_US';

INSERT INTO CLO_MRowKPI (KPITableID, RowKPIName, ParentRowKPIId, KPIRowType, CreatedBy, ModifiedBy)
SELECT 
    tm.TableId,
    '30-Aug-24',
    @ParentId_79,
    1, -- Default KPIRowType
    1, -- Default CreatedBy
    1  -- Default ModifiedBy
FROM @TableMapping tm
WHERE tm.TableName = 'KPI_History_US';

End;
GO
IF EXISTS (SELECT 1 FROM [CLO_MKPITable] WHERE TableIdentifier = 'Capital_Structure')
BEGIN

    DECLARE @TableID INT;
    SELECT @TableID = TableID FROM [CLO_MKPITable] WHERE TableIdentifier = 'Capital_Structure';
    
    UPDATE [CLO_MColumnKPI]
    SET DecimalUnits = 0
    WHERE KPITableID = @TableID
    AND ColumnName IN ('OrigBalance', 'CurrBalance');
END

GO
--------------------------Table-level Page configuration changes-----------------------
IF NOT EXISTS (
    SELECT * 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'CLO_MKPITable' 
    AND COLUMN_NAME = 'SequenceNo'
) and EXISTS (SELECT * FROM sysobjects WHERE name='CLO_MKPITable'  AND xtype='U')
BEGIN
    ALTER table CLO_MKPITable
    ADD SequenceNo int Not NULL default(0);
END
GO
IF NOT EXISTS (
    SELECT * 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'CLO_MKPITable' 
    AND COLUMN_NAME = 'TableName'
) AND EXISTS (SELECT * FROM sysobjects WHERE name='CLO_MKPITable' AND xtype='U')
BEGIN
    ALTER TABLE CLO_MKPITable
    ADD TableName nvarchar(200) NULL;
END
GO

UPDATE CLO_MKPITable 
SET TableName = TableAliasName;
GO
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CLO_MappingTableColDetails' AND xtype='U')
BEGIN
CREATE TABLE [dbo].[CLO_MappingTableColDetails](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[CompanyId] [nvarchar](50) NULL,
	[CLOId] [int] NULL,
	[ColumnAliasName] [nvarchar](50) NULL,
	[SequenceNo] [int] NULL,
	[IsActive] [bit] NULL,
	[IsDeleted] [bit] NULL,
	[ColumnId] [int] NULL,
	[ModifiedOn] [datetime] NULL,
	[ModifiedBy] [int] NULL,
	[CreatedBy] [int] NOT NULL,
	[CreatedOn] [datetime] NOT NULL,
	[KPITableId] [int] NOT NULL,	
	[Name] NVARCHAR(100) NOT NULL,
    [IsCustom] BIT NOT NULL DEFAULT(0),
PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
END
GO
IF NOT EXISTS (
    SELECT 1
    FROM CLO_MKPITable
    WHERE tabid = 4 AND tableId IN (16, 9, 20, 14)
)
BEGIN
    UPDATE CLO_MKPITable
    SET tabid = 4
    WHERE tableId IN (16, 9, 20, 14);
END
GO
IF NOT EXISTS (
    SELECT 1
    FROM CLO_MKPITable
    WHERE tabid = 7 AND tableId = 1
)
BEGIN
    UPDATE CLO_MKPITable
    SET tabid = 7
    WHERE tableId = 1;
END
GO
IF NOT EXISTS (
    SELECT 1
    FROM CLO_MKPITable
    WHERE tabid = 8 AND tableId = 2
)
BEGIN
    UPDATE CLO_MKPITable
    SET tabid = 8
    WHERE tableId = 2;
END
GO
IF NOT EXISTS (
    SELECT 1
    FROM CLO_MKPITable
    WHERE tabid = 9 AND tableId = 3
)
BEGIN
    UPDATE CLO_MKPITable
    SET tabid = 9
    WHERE tableId = 3;
END
GO
IF NOT EXISTS (
    SELECT 1
    FROM CLO_MKPITable
    WHERE tabid = 10 AND tableId = 6
)
BEGIN
    UPDATE CLO_MKPITable
    SET tabid = 10
    WHERE tableId = 6;
END
GO
IF NOT EXISTS (
    SELECT 1
    FROM CLO_MKPITable
    WHERE tabid = 11 AND tableId = 4
)
BEGIN
    UPDATE CLO_MKPITable
    SET tabid = 11
    WHERE tableId = 4;
END
GO
IF NOT EXISTS (
    SELECT 1
    FROM CLO_MKPITable
    WHERE tabid = 12 AND tableId IN (10,11)
)
BEGIN
    UPDATE CLO_MKPITable
    SET tabid = 12
    WHERE tableId IN (10,11);
END
GO
IF NOT EXISTS (
    SELECT 1
    FROM CLO_MKPITable
    WHERE tabid = 13 AND tableId IN (17,18)
)
BEGIN
    UPDATE CLO_MKPITable
    SET tabid = 13
    WHERE tableId IN (17,18);
END
GO
IF NOT EXISTS (
    SELECT 1
    FROM CLO_MKPITable
    WHERE tabid = 1 AND tableId IN (5,7,8)
)
BEGIN
    UPDATE CLO_MKPITable
    SET tabid = 1
    WHERE tableId IN (5,7,8);
END
GO
IF NOT EXISTS (
    SELECT 1
    FROM CLO_MKPITable
    WHERE tabid = 14 AND tableId = 15
)
BEGIN
    UPDATE CLO_MKPITable
    SET tabid = 14
    WHERE tableId = 15;
END
GO
IF NOT EXISTS (
    SELECT 1
    FROM CLO_MKPITable
    WHERE tabid = 6 AND tableId = 19
)
BEGIN
    UPDATE CLO_MKPITable
    SET tabid = 6
    WHERE tableId = 19;
END
GO
IF NOT EXISTS (
    SELECT 1
    FROM CLO_MKPITable
    WHERE SequenceNo = 1 AND tableId IN (1,2,3,4,6,10,11,17,15,19)
)
BEGIN
    UPDATE CLO_MKPITable
    SET SequenceNo = 1
    WHERE tableId IN (1,2,3,4,6,10,11,17,15,19);
END
GO
IF NOT EXISTS (
    SELECT 1
    FROM CLO_MKPITable
    WHERE SequenceNo = 2 AND tableId IN (9,18)
)
BEGIN
    UPDATE CLO_MKPITable
    SET SequenceNo = 2
    WHERE tableId IN (9,18);
END
GO
IF NOT EXISTS (
    SELECT 1
    FROM CLO_MKPITable
    WHERE SequenceNo = 3 AND tableId IN (5,20)
)
BEGIN
    UPDATE CLO_MKPITable
    SET SequenceNo = 3
    WHERE tableId IN (5,20);
END
GO
IF NOT EXISTS (
    SELECT 1
    FROM CLO_MKPITable
    WHERE SequenceNo = 4 AND tableId in (7,8,16)
)
BEGIN
    UPDATE CLO_MKPITable
    SET SequenceNo = 4
    WHERE tableId in (7,8,16);
END
GO
IF NOT EXISTS (
    SELECT 1
    FROM CLO_MKPITable
    WHERE tableId = 21
)
BEGIN
SET IDENTITY_INSERT CLO_MKPITable ON
INSERT INTO [dbo].[CLO_MKPITable]
([TableID], [TableIdentifier], [TableObjectName], [TableAliasName], [TableType], [ModuleType], [CreatedOn], [CreatedBy], [IsDeleted],[SequenceNo],[TableName],[TabId])
VALUES
(21, 'Company_Facts', 'CLO_InvestmentCompanyDetails', 'Company Facts', 1, 1, GETDATE(), 1, 0, 1, 'Company Facts',1)

SET IDENTITY_INSERT CLO_MKPITable OFF
END
GO
IF NOT EXISTS (
    SELECT 1
    FROM CLO_MKPITable
    WHERE tableId = 22
)
BEGIN
SET IDENTITY_INSERT CLO_MKPITable ON
INSERT INTO [dbo].[CLO_MKPITable]
([TableID], [TableIdentifier], [TableObjectName], [TableAliasName], [TableType], [ModuleType], [CreatedOn], [CreatedBy], [IsDeleted],[SequenceNo],[TableName],[TabId])
VALUES
(22, 'Investment_Summary', 'CLO_InvestmentCompanyDetails', 'Investment Summary', 1, 1, GETDATE(), 1, 0, 2, 'Investment Summary',1)

SET IDENTITY_INSERT CLO_MKPITable OFF
END
GO

IF NOT EXISTS (
    SELECT 1
    FROM CLO_MTabDetails
    WHERE TabId = 15
)
BEGIN
SET IDENTITY_INSERT CLO_MTabDetails ON
INSERT INTO [dbo].[CLO_MTabDetails]
           ([TabId]
		   ,[PageId]
           ,[ParentId]
           ,[TabName]
           ,[IsDeleted]
           ,[CreatedOn]
           ,[CreatedBy]
           ,[AliasName]
           ,[SequenceNo])
     VALUES
           (15
		   ,2
           ,5
           ,'Key KPI History'
           ,0
           ,GETUTCDATE()
           ,1
           ,'Key KPI History'
           ,4)
SET IDENTITY_INSERT CLO_MTabDetails OFF
END
GO
IF NOT EXISTS (
    SELECT 1
    FROM CLO_MKPITable
    WHERE tableId = 23
)
BEGIN
SET IDENTITY_INSERT CLO_MKPITable ON
INSERT INTO [dbo].[CLO_MKPITable]
([TableID], [TableIdentifier], [TableObjectName], [TableAliasName], [TableType], [ModuleType], [CreatedOn], [CreatedBy], [IsDeleted],[SequenceNo],[TableName],[TabId])
VALUES
(23, 'CLO_CloDetails', 'CLO_CloDetails', 'Summary', 1, 1, GETDATE(), 1, 0, 1, 'Summary',4)

SET IDENTITY_INSERT CLO_MKPITable OFF
END
GO
IF NOT EXISTS (
    SELECT 1
    FROM CLO_MKPITable
    WHERE tableId = 24
)
BEGIN
SET IDENTITY_INSERT CLO_MKPITable ON
INSERT INTO [dbo].[CLO_MKPITable]
([TableID], [TableIdentifier], [TableObjectName], [TableAliasName], [TableType], [ModuleType], [CreatedOn], [CreatedBy], [IsDeleted],[SequenceNo],[TableName],[TabId])
VALUES
(24, 'CLO_Commentries', 'CLO_Commentries', 'GLI Commentary', 1, 1, GETDATE(), 1, 0, 1, 'GLI Commentary',3)

SET IDENTITY_INSERT CLO_MKPITable OFF
END
GO
IF NOT EXISTS (
    SELECT 1
    FROM CLO_MKPITable
    WHERE tableId = 25
)
BEGIN
SET IDENTITY_INSERT CLO_MKPITable ON
INSERT INTO [dbo].[CLO_MKPITable]
([TableID], [TableIdentifier], [TableObjectName], [TableAliasName], [TableType], [ModuleType], [CreatedOn], [CreatedBy], [IsDeleted],[SequenceNo],[TableName],[TabId])
VALUES
(25, 'CLO_Commentries1', 'CLO_Commentries', 'Market Commentary - Global loans and CLOs', 1, 1, GETDATE(), 1, 0, 2, 'Market Commentary - Global loans and CLOs',3)

SET IDENTITY_INSERT CLO_MKPITable OFF
END
GO

IF NOT EXISTS (
    SELECT 1
    FROM CLO_MKPITable
    WHERE tabid = 15 AND SequenceNo=1 AND tableId IN (12,13)
)
BEGIN
update CLO_MKPITable set TabId=15, SequenceNo=1 where TableID in (12,13)
END
GO
IF NOT EXISTS (
    SELECT 1
    FROM CLO_MKPITable
    WHERE tabid = 15 AND SequenceNo=2 AND tableId IN (14)
)
BEGIN
update CLO_MKPITable set TabId=15,SequenceNo=2 where TableID in (14)
END
GO
IF OBJECT_ID('[dbo].[GetCLOTableConfiguration]', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE [dbo].[GetCLOTableConfiguration]
END
GO
CREATE PROCEDURE [dbo].[GetCLOTableConfiguration] 
    @CompanyId INT,
    @TabId INT,
    @CLOId INT = 0
AS
BEGIN
    SELECT DISTINCT
        COALESCE(company.InvestmentCompanyID, 0) AS CompanyId,
        company.InvestmentCompanyName AS CompanyName,
        tab.TabId AS TabId,
        tab.PageId AS PageId,
        ISNULL(pagetab.[Name], tabl.TableName) AS [Name],
        ISNULL(pagetab.isDeleted, tabl.isDeleted) AS isDeleted,
        ISNULL(pagetab.isActive, 1) AS isActive,
        ISNULL(pagetab.ColumnaliasName, tabl.TableAliasName) AS AliasName,
        ISNULL(pagetab.SequenceNo, tabl.SequenceNo) AS SequenceNo,
        COALESCE(pageTab.KPITableId, tabl.TableID) AS TableId,
        ISNULL(pagetab.id, 0) AS id,
        LOWER(typ.TableType) as TableType,
        CASE 
            WHEN typ.TableType = 'Static' THEN 1 
            ELSE 0 
        END AS isStaticTable,
        tabl.TableIdentifier AS TableName
    FROM 
        CLO_MTabDetails tab 
    JOIN 
        CLO_MKPITable tabl ON tab.TabId = tabl.TabId
    JOIN 
        CLO_MTableType typ ON tabl.TableType = typ.TableTypeID
    LEFT JOIN 
        CLO_MappingTableColDetails pageTab ON tabl.TableID = pageTab.KPITableId 
        AND pageTab.CompanyId = @CompanyId 
        AND (pageTab.CLOId = @CloId OR CLOId IS NULL)
    LEFT JOIN 
        CLO_InvestmentCompanyDetails company ON pageTab.CompanyId = company.InvestmentCompanyID
    WHERE 
        tabl.TabId = @TabId
    ORDER BY 
        SequenceNo;
END
GO
IF OBJECT_ID('[dbo].[GetCLOPageConfiguration]', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE [dbo].[GetCLOPageConfiguration]
END
GO
CREATE Procedure [dbo].[GetCLOPageConfiguration] 
@CompanyId int,
@PageId int,
@CLOId int=0
as
Begin
SELECT Distinct
    COALESCE(company.InvestmentCompanyID, 0) AS CompanyId,
    company.InvestmentCompanyName AS CompanyName,
    tab.TabId AS TabId,
    tab.PageId AS PageId,
    tab.tabName AS [Name],
    ISNULL(pagetab.isDelete, tab.isDeleted) AS isDeleted,
    ISNULL(pagetab.isActive, 1) AS isActive,
    ISNULL(pagetab.aliasName, tab.aliasName) AS AliasName,
    ISNULL(pagetab.SequenceNo, tab.SequenceNo) AS SequenceNo,
    ISNULL(pagetab.id, 0) AS id
FROM 
    M_CLOPageDetails p
JOIN 
    CLO_MTabDetails tab ON p.pageId = tab.PageId
LEFT JOIN 
    CLO_MappingTabDetails pageTab ON tab.tabid = pageTab.tabId AND pageTab.CompanyId = @CompanyId AND (pageTab.CLOId = @CloId OR CLOId IS NULL)
LEFT JOIN 
    CLO_InvestmentCompanyDetails company ON pageTab.CompanyId = company.InvestmentCompanyID
WHERE 
    tab.PageId = @PageId and tab.ParentId is null
ORDER BY 
    SequenceNo;
End
GO
IF OBJECT_ID('[dbo].[GetCLOSubPageConfiguration]', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE [dbo].[GetCLOSubPageConfiguration]
END
GO
CREATE Procedure [dbo].[GetCLOSubPageConfiguration]
@CompanyId int,
@TabId int,
@CLOId int=null
as
Begin
SELECT Distinct
    COALESCE(company.InvestmentCompanyID, 0) AS CompanyId,
    company.InvestmentCompanyName AS CompanyName,
    childTab.TabId AS TabId,
    childTab.PageId AS PageId,
    childTab.tabName AS [Name],
    ISNULL(pagetab.isDelete, childTab.isDeleted) AS isDeleted,
    ISNULL(pagetab.isActive, 1) AS isActive,
    ISNULL(pagetab.aliasName, childTab.aliasName) AS AliasName,
    ISNULL(pagetab.SequenceNo, childTab.SequenceNo) AS SequenceNo,
    ISNULL(pagetab.id, 0) AS id,
	ISNULL(childTab.ParentId, 0) AS ParentId,
	ISNULL(pagetab.CLOId, 0) AS CloId
FROM 
    M_CLOPageDetails p
JOIN 
    CLO_MTabDetails tab ON p.pageId = tab.PageId
	inner join CLO_MTabDetails childTab on tab.TabId=childTab.ParentId
LEFT JOIN 
    CLO_MappingTabDetails pageTab ON childTab.TabId = pageTab.tabId AND pageTab.CompanyId = @CompanyId AND (pageTab.CLOId = @CloId OR CLOId IS NULL)
LEFT JOIN 
    CLO_InvestmentCompanyDetails company ON pageTab.CompanyId = company.InvestmentCompanyID
WHERE  
	childTab.ParentId =@TabId
ORDER BY 
    SequenceNo
end
GO
IF (EXISTS (SELECT *  FROM [dbo].[CLO_MKPITable] WHERE [TableID] = 4))
BEGIN
update CLO_MKPITable set TableAliasName='Currency Exposure',TableName='Currency Exposure' where TableID=4
END
GO
IF (EXISTS (SELECT *  FROM [dbo].[CLO_MKPITable] WHERE [TableID] = 6))
BEGIN
update CLO_MKPITable set TableAliasName='Return Composition',TableName='Return Composition' where TableID=6
END
GO
--------------------------END Table-level Page configuration changes-----------------------
---------------------------------------------8159----------------------------
IF NOT EXISTS (
    SELECT * 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'CLO_Aggregate_CLO_Metrics_EU' 
    AND COLUMN_NAME = 'Default_Loss_Cumulative'
) and EXISTS (SELECT * FROM sysobjects WHERE name='CLO_Aggregate_CLO_Metrics_EU'  AND xtype='U')
BEGIN
    ALTER table CLO_Aggregate_CLO_Metrics_EU
    ADD Default_Loss_Cumulative nvarchar(max);
END
GO
IF NOT EXISTS (
    SELECT * 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'CLO_Aggregate_CLO_Metrics_US' 
    AND COLUMN_NAME = 'Default_Loss_Cumulative'
) and EXISTS (SELECT * FROM sysobjects WHERE name='CLO_Aggregate_CLO_Metrics_US'  AND xtype='U')
BEGIN
    ALTER table CLO_Aggregate_CLO_Metrics_US
    ADD Default_Loss_Cumulative nvarchar(max);
END
GO
IF NOT EXISTS (
    SELECT * 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'Key_KPIs_US' 
    AND COLUMN_NAME = 'Default_Loss_Cumulative'
) and EXISTS (SELECT * FROM sysobjects WHERE name='Key_KPIs_US'  AND xtype='U')
BEGIN
    ALTER table Key_KPIs_US
    ADD Default_Loss_Cumulative nvarchar(max);
END
GO
IF NOT EXISTS (
    SELECT * 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'CLO_Key_KPIs_EU' 
    AND COLUMN_NAME = 'Default_Loss_Cumulative'
) and EXISTS (SELECT * FROM sysobjects WHERE name='CLO_Key_KPIs_EU'  AND xtype='U')
BEGIN
    ALTER table CLO_Key_KPIs_EU
    ADD Default_Loss_Cumulative nvarchar(max);
END
GO
IF NOT EXISTS (
    SELECT * 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'CLO_KPI_History_US' 
    AND COLUMN_NAME = 'Default_Loss_Cumulative'
) and EXISTS (SELECT * FROM sysobjects WHERE name='CLO_KPI_History_US'  AND xtype='U')
BEGIN
    ALTER table CLO_KPI_History_US
    ADD Default_Loss_Cumulative nvarchar(max);
END
GO
IF NOT EXISTS (
    SELECT * 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'CLO_KPI_History_EU' 
    AND COLUMN_NAME = 'Default_Loss_Cumulative'
) and EXISTS (SELECT * FROM sysobjects WHERE name='CLO_KPI_History_EU'  AND xtype='U')
BEGIN
    ALTER table CLO_KPI_History_EU
    ADD Default_Loss_Cumulative nvarchar(max);
END
GO
----- RA---------
IF NOT EXISTS (
    SELECT * 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'CLO_Aggregate_CLO_Metrics_EU' 
    AND COLUMN_NAME = 'w1'
) and EXISTS (SELECT * FROM sysobjects WHERE name='CLO_Aggregate_CLO_Metrics_EU'  AND xtype='U')
BEGIN
    ALTER table CLO_Aggregate_CLO_Metrics_EU
    ADD w1 nvarchar(max);
END
GO
IF NOT EXISTS (
    SELECT * 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'CLO_Aggregate_CLO_Metrics_US' 
    AND COLUMN_NAME = 'w1'
) and EXISTS (SELECT * FROM sysobjects WHERE name='CLO_Aggregate_CLO_Metrics_US'  AND xtype='U')
BEGIN
    ALTER table CLO_Aggregate_CLO_Metrics_US
    ADD w1 nvarchar(max);
END
GO
IF NOT EXISTS (
    SELECT * 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'Key_KPIs_US' 
    AND COLUMN_NAME = 'w1'
) and EXISTS (SELECT * FROM sysobjects WHERE name='Key_KPIs_US'  AND xtype='U')
BEGIN
    ALTER table Key_KPIs_US
    ADD w1 nvarchar(max);
END
GO
IF NOT EXISTS (
    SELECT * 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'CLO_Key_KPIs_EU' 
    AND COLUMN_NAME = 'w1'
) and EXISTS (SELECT * FROM sysobjects WHERE name='CLO_Key_KPIs_EU'  AND xtype='U')
BEGIN
    ALTER table CLO_Key_KPIs_EU
    ADD w1 nvarchar(max);
END
GO
IF NOT EXISTS (
    SELECT * 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'CLO_KPI_History_US' 
    AND COLUMN_NAME = 'w1'
) and EXISTS (SELECT * FROM sysobjects WHERE name='CLO_KPI_History_US'  AND xtype='U')
BEGIN
    ALTER table CLO_KPI_History_US
    ADD w1 nvarchar(max);
END
GO
IF NOT EXISTS (
    SELECT * 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'CLO_KPI_History_EU' 
    AND COLUMN_NAME = 'w1'
) and EXISTS (SELECT * FROM sysobjects WHERE name='CLO_KPI_History_EU'  AND xtype='U')
BEGIN
    ALTER table CLO_KPI_History_EU
    ADD w1 nvarchar(max);
END
GO
---Rating---
IF NOT EXISTS (
    SELECT * 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'CLO_Aggregate_CLO_Metrics_EU' 
    AND COLUMN_NAME = 'w2'
) and EXISTS (SELECT * FROM sysobjects WHERE name='CLO_Aggregate_CLO_Metrics_EU'  AND xtype='U')
BEGIN
    ALTER table CLO_Aggregate_CLO_Metrics_EU
    ADD w2 nvarchar(max);
END
GO
IF NOT EXISTS (
    SELECT * 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'CLO_Aggregate_CLO_Metrics_US' 
    AND COLUMN_NAME = 'w2'
) and EXISTS (SELECT * FROM sysobjects WHERE name='CLO_Aggregate_CLO_Metrics_US'  AND xtype='U')
BEGIN
    ALTER table CLO_Aggregate_CLO_Metrics_US
    ADD w2 nvarchar(max);
END
GO
IF NOT EXISTS (
    SELECT * 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'Key_KPIs_US' 
    AND COLUMN_NAME = 'w2'
) and EXISTS (SELECT * FROM sysobjects WHERE name='Key_KPIs_US'  AND xtype='U')
BEGIN
    ALTER table Key_KPIs_US
    ADD w2 nvarchar(max);
END
GO
IF NOT EXISTS (
    SELECT * 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'CLO_Key_KPIs_EU' 
    AND COLUMN_NAME = 'w2'
) and EXISTS (SELECT * FROM sysobjects WHERE name='CLO_Key_KPIs_EU'  AND xtype='U')
BEGIN
    ALTER table CLO_Key_KPIs_EU
    ADD w2 nvarchar(max);
END
GO
IF NOT EXISTS (
    SELECT * 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'CLO_KPI_History_US' 
    AND COLUMN_NAME = 'w2'
) and EXISTS (SELECT * FROM sysobjects WHERE name='CLO_KPI_History_US'  AND xtype='U')
BEGIN
    ALTER table CLO_KPI_History_US
    ADD w2 nvarchar(max);
END
GO
IF NOT EXISTS (
    SELECT * 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'CLO_KPI_History_EU' 
    AND COLUMN_NAME = 'w2'
) and EXISTS (SELECT * FROM sysobjects WHERE name='CLO_KPI_History_EU'  AND xtype='U')
BEGIN
    ALTER table CLO_KPI_History_EU
    ADD w2 nvarchar(max);
END
GO
SET IDENTITY_INSERT CLO_MColumnKPI ON;
------------Default_Loss_Cumulative
IF NOT EXISTS (
    SELECT 1
    FROM CLO_MColumnKPI
    WHERE ColumnKPIId = 236
)
BEGIN
INSERT INTO [dbo].[CLO_MColumnKPI]
(ColumnKPIId,KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
VALUES
(236,7, 
'Default_Loss_Cumulative', 
'Default_Loss_Cumulative', 
4, 
0, 
0, 
NULL, 
0, 
0, 
null, 
2, 
NULL, 
GETUTCDATE(), 1, GETUTCDATE(), 1, 0)
END
GO

IF NOT EXISTS (
    SELECT 1
    FROM CLO_MColumnKPI
    WHERE ColumnKPIId = 237
)
BEGIN
INSERT INTO [dbo].[CLO_MColumnKPI]
(ColumnKPIId,KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
VALUES
(237,8, 
'Default_Loss_Cumulative', 
'Default_Loss_Cumulative', 
4, 
0, 
0, 
NULL, 
0, 
0, 
null, 
2, 
NULL, 
GETUTCDATE(), 1, GETUTCDATE(), 1, 0)
END
GO

IF NOT EXISTS (
    SELECT 1
    FROM CLO_MColumnKPI
    WHERE ColumnKPIId = 238
)
BEGIN
INSERT INTO [dbo].[CLO_MColumnKPI]
(ColumnKPIId,KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
VALUES
(238,10, 
'Default_Loss_Cumulative', 
'Default_Loss_Cumulative', 
4, 
0, 
0, 
NULL, 
0, 
0, 
null, 
2, 
NULL, 
GETUTCDATE(), 1, GETUTCDATE(), 1, 0)
end
GO

IF NOT EXISTS (
    SELECT 1
    FROM CLO_MColumnKPI
    WHERE ColumnKPIId = 239
)
BEGIN
INSERT INTO [dbo].[CLO_MColumnKPI]
(ColumnKPIId,KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
VALUES
(239,11, 
'Default_Loss_Cumulative', 
'Default_Loss_Cumulative', 
4, 
0, 
0, 
NULL, 
0, 
0, 
null, 
2, 
NULL, 
GETUTCDATE(), 1, GETUTCDATE(), 1, 0);
END
GO
IF NOT EXISTS (
    SELECT 1
    FROM CLO_MColumnKPI
    WHERE ColumnKPIId = 240
)
BEGIN
INSERT INTO [dbo].[CLO_MColumnKPI]
(ColumnKPIId,KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
VALUES
(240,12, 
'Default_Loss_Cumulative', 
'Default_Loss_Cumulative', 
4, 
0, 
0, 
NULL, 
0, 
0, 
null, 
2, 
NULL, 
GETUTCDATE(), 1, GETUTCDATE(), 1, 0);
END
GO

IF NOT EXISTS (
    SELECT 1
    FROM CLO_MColumnKPI
    WHERE ColumnKPIId = 241
)
BEGIN
INSERT INTO [dbo].[CLO_MColumnKPI]
(ColumnKPIId,KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
VALUES
(241,13, 
'Default_Loss_Cumulative', 
'Default_Loss_Cumulative', 
4, 
0, 
0, 
NULL, 
0, 
0, 
null, 
2, 
NULL, 
GETUTCDATE(), 1, GETUTCDATE(), 1, 0);
END
GO
------RA
IF NOT EXISTS (
    SELECT 1
    FROM CLO_MColumnKPI
    WHERE ColumnKPIId = 242
)
BEGIN
INSERT INTO [dbo].[CLO_MColumnKPI]
(ColumnKPIId,KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
VALUES
(242,7, 
'w1', 
'RA', 
1, 
0, 
0, 
56, 
0, 
0, 
null, 
0, 
NULL, 
GETUTCDATE(), 1, GETUTCDATE(), 1, 0);
END
GO
IF NOT EXISTS (
    SELECT 1
    FROM CLO_MColumnKPI
    WHERE ColumnKPIId = 243
)
BEGIN
INSERT INTO [dbo].[CLO_MColumnKPI]
(ColumnKPIId,KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
VALUES
(243,8, 
'w1', 
'RA', 
1, 
0, 
0, 
75, 
0, 
0, 
null, 
0, 
NULL, 
GETUTCDATE(), 1, GETUTCDATE(), 1, 0);
END
GO

IF NOT EXISTS (
    SELECT 1
    FROM CLO_MColumnKPI
    WHERE ColumnKPIId = 244
)
BEGIN
INSERT INTO [dbo].[CLO_MColumnKPI]
(ColumnKPIId,KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
VALUES
(244,10, 
'w1', 
'RA', 
1, 
0, 
0, 
106, 
0, 
0, 
null, 
0, 
NULL, 
GETUTCDATE(), 1, GETUTCDATE(), 1, 0);
END
GO

IF NOT EXISTS (
    SELECT 1
    FROM CLO_MColumnKPI
    WHERE ColumnKPIId = 245
)
BEGIN
INSERT INTO [dbo].[CLO_MColumnKPI]
(ColumnKPIId,KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
VALUES
(245,11, 
'w1', 
'RA', 
1, 
0, 
0, 
125, 
0, 
0, 
null, 
0, 
NULL, 
GETUTCDATE(), 1, GETUTCDATE(), 1, 0);
END
GO

IF NOT EXISTS (
    SELECT 1
    FROM CLO_MColumnKPI
    WHERE ColumnKPIId = 246
)
BEGIN
INSERT INTO [dbo].[CLO_MColumnKPI]
(ColumnKPIId,KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
VALUES
(246,12, 
'w1', 
'RA', 
1, 
0, 
0, 
170, 
0, 
0, 
null, 
0, 
NULL, 
GETUTCDATE(), 1, GETUTCDATE(), 1, 0);
END
GO

IF NOT EXISTS (
    SELECT 1
    FROM CLO_MColumnKPI
    WHERE ColumnKPIId = 247
)
BEGIN
INSERT INTO [dbo].[CLO_MColumnKPI]
(ColumnKPIId,KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
VALUES
(247,13, 
'w1', 
'RA', 
1, 
0, 
0, 
189, 
0, 
0, 
null, 
0, 
NULL, 
GETUTCDATE(), 1, GETUTCDATE(), 1, 0);
END
GO

-------------------------Rating
IF NOT EXISTS (
    SELECT 1
    FROM CLO_MColumnKPI
    WHERE ColumnKPIId = 248
)
BEGIN
INSERT INTO [dbo].[CLO_MColumnKPI]
(ColumnKPIId,KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
VALUES
(248,7, 
'w2', 
'Rating', 
2, 
0, 
0, 
56, 
0, 
0, 
null, 
2, 
NULL, 
GETUTCDATE(), 1, GETUTCDATE(), 1, 0);
END
GO

IF NOT EXISTS (
    SELECT 1
    FROM CLO_MColumnKPI
    WHERE ColumnKPIId = 249
)
BEGIN
INSERT INTO [dbo].[CLO_MColumnKPI]
(ColumnKPIId,KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
VALUES
(249,8, 
'w2', 
'Rating',  
2, 
0, 
0, 
75, 
0, 
0, 
null, 
2, 
NULL, 
GETUTCDATE(), 1, GETUTCDATE(), 1, 0);
END
GO

IF NOT EXISTS (
    SELECT 1
    FROM CLO_MColumnKPI
    WHERE ColumnKPIId = 250
)
BEGIN
INSERT INTO [dbo].[CLO_MColumnKPI]
(ColumnKPIId,KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
VALUES
(250,10, 
'w2', 
'Rating', 
2, 
0, 
0, 
106, 
0, 
0, 
null, 
2, 
NULL, 
GETUTCDATE(), 1, GETUTCDATE(), 1, 0);
END
GO

IF NOT EXISTS (
    SELECT 1
    FROM CLO_MColumnKPI
    WHERE ColumnKPIId = 251
)
BEGIN
INSERT INTO [dbo].[CLO_MColumnKPI]
(ColumnKPIId,KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
VALUES
(251,11, 
'w2', 
'Rating',  
2, 
0, 
0, 
125, 
0, 
0, 
null, 
2, 
NULL, 
GETUTCDATE(), 1, GETUTCDATE(), 1, 0);
END
GO

IF NOT EXISTS (
    SELECT 1
    FROM CLO_MColumnKPI
    WHERE ColumnKPIId = 252
)
BEGIN
INSERT INTO [dbo].[CLO_MColumnKPI]
(ColumnKPIId,KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
VALUES
(252,12, 
'w2', 
'Rating', 
2, 
0, 
0, 
170, 
0, 
0, 
null, 
2, 
NULL, 
GETUTCDATE(), 1, GETUTCDATE(), 1, 0);
END
GO

IF NOT EXISTS (
    SELECT 1
    FROM CLO_MColumnKPI
    WHERE ColumnKPIId = 253
)
BEGIN
INSERT INTO [dbo].[CLO_MColumnKPI]
(ColumnKPIId, KPITableID, ColumnName, ColumnAliasName, KPIColumType, IsUniqueIdentifier, 
IsHidden, ParentColumnKPIId, EnableLink, IsStaticTableHeader, CurrencyUnit, 
DecimalUnits, DateFormat, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted)
VALUES
(253,13, 
'w2', 
'Rating', 
2, 
0, 
0, 
189, 
0, 
0, 
null, 
2, 
NULL, 
GETUTCDATE(), 1, GETUTCDATE(), 1, 0);
END
GO

SET IDENTITY_INSERT CLO_MColumnKPI OFF;
---------------------end 8159----------------------------------------------------
-- enable view permission for investment and clo  feature page --

GO
IF (NOT EXISTS (SELECT * 
                 FROM [dbo].[Mapping_FeatureAction]
                 WHERE [FeatureID] = 57 and [ActionID] = 3
                ))
BEGIN
    INSERT [dbo].[Mapping_FeatureAction] ([FeatureID], [ActionID], [IsEnabled], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedFeatureActionMappingID]) VALUES (57, 3, 1, CAST(N'2022-03-10T17:50:04.677' AS DateTime), 1, NULL, NULL, NULL)
END

GO
IF (NOT EXISTS (SELECT * 
                 FROM [dbo].[Mapping_FeatureAction]
                 WHERE [FeatureID] = 58 and [ActionID] = 3
                ))
BEGIN
    INSERT [dbo].[Mapping_FeatureAction] ([FeatureID], [ActionID], [IsEnabled], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedFeatureActionMappingID]) VALUES (58, 3, 1, CAST(N'2022-03-10T17:50:04.677' AS DateTime), 1, NULL, NULL, NULL)
END

GO
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'Status')
BEGIN
CREATE TABLE [dbo].[Status] (
    [Id] UNIQUEIDENTIFIER NOT NULL PRIMARY KEY DEFAULT NEWID(),
    [Name] NVARCHAR(MAX) NOT NULL,
	[State] NVARCHAR(MAX) NOT NULL,
    [CreatedOn] DATETIME NOT NULL DEFAULT GETUTCDATE(),
    [CreatedBy] INT NOT NULL,
    [ModifiedOn] DATETIME NULL,
    [ModifiedBy] INT NULL,
    [IsDeleted] BIT NOT NULL DEFAULT 0
)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Status] WHERE State = 'File Draft before Extraction' AND Name = 'In Progress')
BEGIN
    INSERT INTO [dbo].[Status] ([Id], [State], [Name], [CreatedOn], [CreatedBy], [IsDeleted])
    VALUES (NEWID(), 'File Draft before Extraction', 'In Progress', GETUTCDATE(), 1, 0)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Status] WHERE State = 'Extraction in progress' AND Name = 'In Progress')
BEGIN
    INSERT INTO [dbo].[Status] ([Id], [State], [Name],  [CreatedOn], [CreatedBy], [IsDeleted])
    VALUES (NEWID(), 'Extraction in progress', 'In Progress', GETUTCDATE(), 1, 0)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Status] WHERE State = 'Extraction Completed' AND Name = 'In Progress')
BEGIN
    INSERT INTO [dbo].[Status] ([Id], State, Name, [CreatedOn], [CreatedBy], [IsDeleted])
    VALUES (NEWID(), 'Extraction Completed', 'In Progress', GETUTCDATE(), 1, 0)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Status] WHERE State = 'Extraction Draft' AND Name = 'Active Draft')
BEGIN
    INSERT INTO [dbo].[Status] ([Id], State, Name, [CreatedOn], [CreatedBy], [IsDeleted])
    VALUES (NEWID(), 'Extraction Draft', 'Active Draft', GETUTCDATE(), 1, 0)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Status] WHERE State = 'Data Ingestion in progress' AND Name = 'In Progress')
BEGIN
    INSERT INTO [dbo].[Status] ([Id], State, Name, [CreatedOn], [CreatedBy], [IsDeleted])
    VALUES (NEWID(), 'Data Ingestion in progress', 'In Progress', GETUTCDATE(), 1, 0)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Status] WHERE State = 'Data Ingestion Completed' AND Name = 'Completed')
BEGIN
    INSERT INTO [dbo].[Status] ([Id], State, Name, [CreatedOn], [CreatedBy], [IsDeleted])
    VALUES (NEWID(), 'Data Ingestion Completed', 'Completed', GETUTCDATE(), 1, 0)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Status] WHERE State = 'Data Ingestion Failed' AND Name = 'Failed')
BEGIN
    INSERT INTO [dbo].[Status] ([Id], State, Name, [CreatedOn], [CreatedBy], [IsDeleted])
    VALUES (NEWID(), 'Data Ingestion Failed', 'Failed', GETUTCDATE(), 1, 0)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Status] WHERE State = 'Extraction Failed' AND Name = 'Failed')
BEGIN
    INSERT INTO [dbo].[Status] ([Id], State, Name, [CreatedOn], [CreatedBy], [IsDeleted])
    VALUES (NEWID(), 'Extraction Failed', 'Failed', GETUTCDATE(), 1, 0)
END
GO
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'Jobs')
BEGIN
    CREATE TABLE Jobs (
        Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        JobId UNIQUEIDENTIFIER NOT NULL,
        ParentJobId UNIQUEIDENTIFIER NOT NULL,
        ProcessId UNIQUEIDENTIFIER NOT NULL,
        StatusId UNIQUEIDENTIFIER NOT NULL,
        TenantId UNIQUEIDENTIFIER NOT NULL,
        CreatedOn DATETIME NOT NULL DEFAULT GETUTCDATE(),
        CreatedBy INT NOT NULL,
        ModifiedOn DATETIME NULL,
        ModifiedBy INT NULL,
        IsDeleted BIT NOT NULL DEFAULT 0
    )
END
GO
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'DataIngestionDocuments')
BEGIN
    CREATE TABLE DataIngestionDocuments (
        Id UNIQUEIDENTIFIER PRIMARY KEY NOT NULL,
        ProcessId UNIQUEIDENTIFIER NOT NULL,
		TenantId UNIQUEIDENTIFIER NULL,
        S3Path NVARCHAR(MAX) NOT NULL,
        FileName NVARCHAR(255) NOT NULL,
        Type NVARCHAR(500) NOT NULL,
		Extension NVARCHAR(100) NOT NULL,
        Errors NVARCHAR(MAX) NULL,
        CreatedOn DATETIME NOT NULL DEFAULT GETUTCDATE(),
        CreatedBy INT NOT NULL,
        ModifiedOn DATETIME NULL,
        ModifiedBy INT NULL,
        IsDeleted BIT NOT NULL DEFAULT 0
    )
END
GO
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'DIMappingDocumentsDetails')
BEGIN
CREATE TABLE DIMappingDocumentsDetails (
    DiMappingId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(), 
    ProcessId UNIQUEIDENTIFIER NOT NULL,
    CompanyId INT NOT NULL,
    SourceTypeId INT NOT NULL, 
    FeatureId INT NOT NULL,
	Quarter Varchar(50) NULL,
	Month INT NULL,
	Year INT NOT NULL,
    CreatedOn DATETIME NOT NULL DEFAULT GETUTCDATE(),
    CreatedBy INT NOT NULL,
    ModifiedOn DATETIME NULL,
    ModifiedBy INT NULL,
    IsDeleted BIT NOT NULL DEFAULT 0
);
END
GO
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'DIMappingDocumentsDetails') 
    AND NOT EXISTS (SELECT * FROM sys.columns WHERE name = 'PeriodType' AND object_id = OBJECT_ID('DIMappingDocumentsDetails'))
BEGIN
    ALTER TABLE DIMappingDocumentsDetails
    ADD PeriodType VARCHAR(100) NULL;
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Status] WHERE State = 'Api Failed' AND Name = 'Failed')
BEGIN
    INSERT INTO [dbo].[Status] ([Id], [State], [Name], [CreatedOn], [CreatedBy], [IsDeleted])
    VALUES (NEWID(), 'Api Failed', 'Failed', GETUTCDATE(), 1, 0)
END
GO
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'DIMappingDocumentsDetails') 
    AND NOT EXISTS (SELECT * FROM sys.columns WHERE name = 'FundId' AND object_id = OBJECT_ID('DIMappingDocumentsDetails'))
BEGIN
    ALTER TABLE DIMappingDocumentsDetails
    ADD FundId INT NULL DEFAULT NULL;
END
GO
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'DIMappingDocumentsDetails') 
    AND NOT EXISTS (SELECT * FROM sys.columns WHERE name = 'ExtractionType' AND object_id = OBJECT_ID('DIMappingDocumentsDetails'))
BEGIN
    ALTER TABLE DIMappingDocumentsDetails
    ADD ExtractionType VARCHAR(100) NULL DEFAULT NULL;
END
GO
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'DataIngestionDocuments') 
    AND NOT EXISTS (SELECT * FROM sys.columns WHERE name = 'DocumentTypeId' AND object_id = OBJECT_ID('DataIngestionDocuments'))
BEGIN
ALTER TABLE DataIngestionDocuments
ADD [DocumentTypeId] INT NULL DEFAULT NULL;
END
GO
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'DataIngestionDocuments') 
    AND NOT EXISTS (SELECT * FROM sys.columns WHERE name = 'PeriodType' AND object_id = OBJECT_ID('DataIngestionDocuments'))
BEGIN
ALTER TABLE DataIngestionDocuments
ADD [PeriodType] VARCHAR(50) NULL DEFAULT NULL;
END
GO
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'DataIngestionDocuments') 
    AND NOT EXISTS (SELECT * FROM sys.columns WHERE name = 'Year' AND object_id = OBJECT_ID('DataIngestionDocuments'))
BEGIN
ALTER TABLE DataIngestionDocuments
ADD [Year] INT NULL DEFAULT NULL;
END
GO
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'DataIngestionDocuments') 
    AND NOT EXISTS (SELECT * FROM sys.columns WHERE name = 'Month' AND object_id = OBJECT_ID('DataIngestionDocuments'))
BEGIN
ALTER TABLE DataIngestionDocuments
ADD [Month] VARCHAR(50) NULL DEFAULT NULL;
END
GO
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'DataIngestionDocuments') 
    AND NOT EXISTS (SELECT * FROM sys.columns WHERE name = 'Quarter' AND object_id = OBJECT_ID('DataIngestionDocuments'))
BEGIN
ALTER TABLE DataIngestionDocuments
ADD [Quarter] VARCHAR(50) NULL DEFAULT NULL;
END
GO
IF EXISTS (
    SELECT 1 
    FROM sys.columns c
    JOIN sys.tables t ON c.object_id = t.object_id
    JOIN sys.schemas s ON t.schema_id = s.schema_id
    WHERE t.name = 'DIMappingDocumentsDetails'
    AND c.name = 'Year'
    AND c.is_nullable = 0
)
BEGIN
    ALTER TABLE DIMappingDocumentsDetails 
    ALTER COLUMN Year INT NULL;
END
GO
-- Email Notification Groups Table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='EmailNotificationGroups' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[EmailNotificationGroups](
        [GroupId] INT IDENTITY(1,1) NOT NULL PRIMARY KEY,
        [GroupName] NVARCHAR(100) NOT NULL,
        [CreatedOn] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        [CreatedBy] INT NULL,
        [ModifiedOn] DATETIME2 NULL,
        [ModifiedBy] INT NULL,
        [IsDeleted] BIT NOT NULL DEFAULT 0
    ) ON [PRIMARY];
END
GO

-- Email List Members Table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='EmailListMembers' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[EmailListMembers](
        [MemberId] INT IDENTITY(1,1) NOT NULL PRIMARY KEY,
        [GroupId] INT NOT NULL,
        [Name] NVARCHAR(100) NOT NULL,
        [Email] NVARCHAR(255) NOT NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [CreatedOn] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        [CreatedBy] INT NULL,
        [ModifiedOn] DATETIME2 NULL,
        [ModifiedBy] INT NULL,
        [IsDeleted] BIT NOT NULL DEFAULT 0,
        CONSTRAINT [FK_EmailListMembers_EmailNotificationGroups] FOREIGN KEY ([GroupId]) REFERENCES [dbo].[EmailNotificationGroups]([GroupId]),
        CONSTRAINT [UQ_GroupEmail] UNIQUE ([GroupId], [Email])
    ) ON [PRIMARY];
END
GO

-- Company Email Groups Table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CompanyEmailGroups' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[CompanyEmailGroups](
        [CompanyEmailGroupId] INT IDENTITY(1,1) NOT NULL PRIMARY KEY,
        [GroupId] INT NOT NULL,
        [CompanyId] NVARCHAR(50) NOT NULL,
        [IsSelected] BIT NOT NULL DEFAULT 0,
        [CreatedOn] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        [CreatedBy] INT NULL,
        [ModifiedOn] DATETIME2 NULL,
        [ModifiedBy] INT NULL,
        [IsDeleted] BIT NOT NULL DEFAULT 0,
        CONSTRAINT [FK_CompanyEmailGroups_EmailNotificationGroups] FOREIGN KEY ([GroupId]) REFERENCES [dbo].[EmailNotificationGroups]([GroupId]),
        CONSTRAINT [UQ_GroupCompany] UNIQUE ([GroupId], [CompanyId])
    ) ON [PRIMARY];
END
GO

-- Drop unique constraints from EmailListMembers and CompanyEmailGroups tables
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[UQ_GroupEmail]') AND type = 'UQ')
BEGIN
    ALTER TABLE [dbo].[EmailListMembers] DROP CONSTRAINT [UQ_GroupEmail]
END
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[UQ_GroupCompany]') AND type = 'UQ')
BEGIN
    ALTER TABLE [dbo].[CompanyEmailGroups] DROP CONSTRAINT [UQ_GroupCompany]
END
GO