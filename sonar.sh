if [ -n "$CODEBUILD_BUILD_ID" ]; then
  # Extract branch name from AWS CodeBuild environment variables
  BRANCH_NAME=$(echo $CODEBUILD_WEBHOOK_HEAD_REF | sed 's|refs/heads/||')
else
  # Fallback to git command
  BR<PERSON>CH_NAME=$(git rev-parse --abbrev-ref HEAD)

  # Check if we are in a detached HEAD state
  if [ "$BRANCH_NAME" == "HEAD" ]; then
    # Get the commit hash
    BRANCH_NAME=$(git rev-parse --short HEAD)
  fi
fi


#!/bin/bash
dotnet tool install -g dotnet-sonarscanner
dotnet tool install -g dotnetsay
dotnet tool install -g dotnet-reportgenerator-globaltool
dotnet tool install -g coverlet.console
dotnet tool install dotnet-reportgenerator-globaltool --tool-path tools

dotnet sonarscanner begin /k:"PEC_FOLIOSURE_API" /v:"2.30.0" /d:sonar.host.url="https://sast.beatapps.net"  /d:sonar.language="cs" /d:sonar.exclusions="**/bin/**/*,**/obj/**/*,**/Migrations/**/*,**/DataAccessLayer/**/*,**/*Configuration.cs,tests/**/*,**/Encryption.cs,**/*Encryption.cs,src/API/wwwroot/**/*" /d:sonar.cs.opencover.reportsPaths="CoverageResults/coverage.opencover.xml" /d:sonar.login=$SonarQubeToken /d:sonar.branch.name=$BRANCH_NAME
dotnet clean
dotnet restore
dotnet build
dotnet test --collect:"XPlat Code Coverage" /p:CollectCoverage=true /p:CoverletOutput=../../../CoverageResults/ /p:MergeWith="../../../CoverageResults/coverage.json" /p:CoverletOutputFormat=\"opencover,json\"
dotnet sonarscanner end /d:sonar.login=$SonarQubeToken
