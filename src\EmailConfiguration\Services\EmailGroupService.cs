﻿using DataAccessLayer.Models.EmailNotifications;
using DataAccessLayer.UnitOfWork;
using EmailConfiguration.Interfaces;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EmailNotification.Services
{
    public class EmailGroupService : IEmailGroupService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<EmailGroupService> _logger;

        public EmailGroupService(
            IUnitOfWork unitOfWork, ILogger<EmailGroupService> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<int?> CreateEmailGroupAsync(EmailGroupCreateDto dto, int userId)
        {
            try
            {
                // 1. Create group
                var group = new EmailNotificationGroup
                {
                    GroupName = dto.GroupName,
                    CreatedBy = userId,
                    CreatedOn = DateTime.UtcNow,
                };
                await _unitOfWork.EmailNotificationGroupRepository.AddAsyn(group);
                await _unitOfWork.SaveAsync();

                // 2. Add email members
                foreach (var member in dto.EmailList)
                {
                    var emailMember = new EmailListMember
                    {
                        GroupId = group.GroupId,
                        Name = member.Name,
                        Email = member.Email,
                        IsActive = true,
                        CreatedBy = userId,
                        CreatedOn = DateTime.UtcNow
                    };
                    await _unitOfWork.EmailListMemberRepository.AddAsyn(emailMember);
                }

                // 3. Add company associations
                foreach (var companyId in dto.CompanyIds)
                {
                    var companyGroup = new CompanyEmailGroup
                    {
                        GroupId = group.GroupId,
                        CompanyId = companyId.ToString(),
                        IsSelected = true,
                        CreatedBy = userId,
                        CreatedOn = DateTime.UtcNow
                    };
                    await _unitOfWork.CompanyEmailGroupRepository.AddAsyn(companyGroup);
                }

                await _unitOfWork.SaveAsync();

                return group.GroupId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating email group");
                throw;
            }
        }
        public async Task<List<EmailGroupDto>> GetEmailGroupsWithCountAsync()
        {
            var groups = await _unitOfWork.EmailNotificationGroupRepository.FindAllAsync(x => !x.IsDeleted);
            var groupIds = groups.Select(g => g.GroupId).ToList();
            var members = await _unitOfWork.EmailListMemberRepository.FindAllAsync(m => !m.IsDeleted && groupIds.Contains(m.GroupId));
            var users = _unitOfWork.UserRepository.GetAll().ToList();

            // 1. Get all company associations for these groups
            var allCompanyAssociations = await _unitOfWork.CompanyEmailGroupRepository.FindAllAsync(c => groupIds.Contains(c.GroupId) && !c.IsDeleted && c.IsSelected);
            var allCompanyIdStrings = allCompanyAssociations.Select(c => c.CompanyId).Distinct().ToList();
            var allCompanyIds = allCompanyIdStrings
                .Select(cid => int.TryParse(cid, out var id) ? id : (int?)null)
                .Where(id => id.HasValue)
                .Select(id => id.Value)
                .ToList();

            // 2. Get company details (names) for these IDs
            var companies = await _unitOfWork.PortfolioCompanyDetailRepository.FindAllAsync(pc => allCompanyIds.Contains(pc.PortfolioCompanyId) && !pc.IsDeleted && pc.IsActive == true);
            var companyIdNameDict = companies.ToDictionary(c => c.PortfolioCompanyId.ToString(), c => c.CompanyName);

            // 3. Build the result
            return groups.Select(g =>
            {
                var user = users.FirstOrDefault(x => x.UserId == g.CreatedBy);
                var groupCompanyNames = allCompanyAssociations
                    .Where(ca => ca.GroupId == g.GroupId && !string.IsNullOrEmpty(ca.CompanyId) && companyIdNameDict.ContainsKey(ca.CompanyId))
                    .Select(ca => companyIdNameDict[ca.CompanyId])
                    .Distinct()
                    .ToList();
                return new EmailGroupDto
                {
                    GroupId = g.GroupId,
                    GroupName = g.GroupName,
                    CreatedBy = g.CreatedBy.ToString(),
                    CreatedOn = g.CreatedOn,
                    EmailCount = members.Count(m => m.GroupId == g.GroupId),
                    UploadedBy = user != null ? $"{user.FirstName} {user.LastName}" : string.Empty,
                    CompanyNames = groupCompanyNames
                };
            }).ToList();
        }
        public async Task<List<EmailMemberDto>> GetEmailMembersByGroupIdAsync(int groupId)
        {
            var members = await _unitOfWork.EmailListMemberRepository.FindAllAsync(m => m.GroupId == groupId && !m.IsDeleted);
            return members.Select(m => new EmailMemberDto
            {
                MemberId = m.MemberId,
                Name = m.Name,
                Email = m.Email,
                IsActive = m.IsActive
            }).ToList();
        }

        public async Task<bool> DeleteEmailMemberAsync(int groupId, int memberId, int userId)
        {
            try
            {
                // 1. Check if the group exists and is not deleted
                var group = await _unitOfWork.EmailNotificationGroupRepository.FindFirstAsync(g => g.GroupId == groupId && !g.IsDeleted);
                if (group == null)
                {
                    _logger.LogWarning("Email group with ID {GroupId} not found or already deleted", groupId);
                    return false;
                }

                // 2. Check if the member exists in the group and is not already deleted
                var member = await _unitOfWork.EmailListMemberRepository.FindFirstAsync(m =>
                    m.MemberId == memberId && m.GroupId == groupId && !m.IsDeleted);
                if (member == null)
                {
                    _logger.LogWarning("Email member with ID {MemberId} not found in group {GroupId} or already deleted", memberId, groupId);
                    return false;
                }

                // 3. Soft delete the member
                member.IsDeleted = true;
                member.ModifiedBy = userId;
                member.ModifiedOn = DateTime.UtcNow;
                _unitOfWork.EmailListMemberRepository.Update(member);

                await _unitOfWork.SaveAsync();

                _logger.LogInformation("Successfully deleted email member {MemberId} from group {GroupId}", memberId, groupId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting email member {MemberId} from group {GroupId}", memberId, groupId);
                throw;
            }
        }

        public async Task<(bool Success, List<int> DeletedMemberIds, List<int> NotFoundMemberIds)> DeleteEmailMembersAsync(int groupId, List<int> memberIds, int userId)
        {
            var deletedMemberIds = new List<int>();
            var notFoundMemberIds = new List<int>();

            try
            {
                // 1. Check if the group exists and is not deleted
                var group = await _unitOfWork.EmailNotificationGroupRepository.FindFirstAsync(g => g.GroupId == groupId && !g.IsDeleted);
                if (group == null)
                {
                    _logger.LogWarning("Email group with ID {GroupId} not found or already deleted", groupId);
                    return (false, deletedMemberIds, memberIds); // All member IDs are considered not found if group doesn't exist
                }

                // 2. Validate input
                if (memberIds == null || !memberIds.Any())
                {
                    _logger.LogWarning("No member IDs provided for deletion in group {GroupId}", groupId);
                    await _unitOfWork.SaveAsync(); // Call SaveAsync for consistency even when no changes
                    return (true, deletedMemberIds, notFoundMemberIds); // Success but nothing to delete
                }

                // Remove duplicates and invalid IDs
                var validMemberIds = memberIds.Where(id => id > 0).Distinct().ToList();
                var invalidMemberIds = memberIds.Where(id => id <= 0).ToList();

                if (invalidMemberIds.Any())
                {
                    _logger.LogWarning("Invalid member IDs provided: {InvalidIds}", string.Join(", ", invalidMemberIds));
                }

                // 3. Get all members that exist in the group and are not already deleted
                var existingMembers = await _unitOfWork.EmailListMemberRepository.FindAllAsync(m =>
                    validMemberIds.Contains(m.MemberId) && m.GroupId == groupId && !m.IsDeleted);

                var existingMemberIds = existingMembers.Select(m => m.MemberId).ToList();
                notFoundMemberIds = validMemberIds.Except(existingMemberIds).ToList();

                // 4. Soft delete the existing members
                foreach (var member in existingMembers)
                {
                    member.IsDeleted = true;
                    member.ModifiedBy = userId;
                    member.ModifiedOn = DateTime.UtcNow;
                    _unitOfWork.EmailListMemberRepository.Update(member);
                    deletedMemberIds.Add(member.MemberId);
                }

                await _unitOfWork.SaveAsync();

                _logger.LogInformation("Successfully deleted {DeletedCount} email members from group {GroupId}. Not found: {NotFoundCount}",
                    deletedMemberIds.Count, groupId, notFoundMemberIds.Count);

                return (true, deletedMemberIds, notFoundMemberIds);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting email members from group {GroupId}", groupId);
                throw;
            }
        }

        public async Task<EmailGroupDetailDto> GetEmailGroupDetailAsync(int groupId)
        {
            try
            {
                // 1. Get the email group
                var group = await _unitOfWork.EmailNotificationGroupRepository.FindFirstAsync(g => g.GroupId == groupId && !g.IsDeleted);
                if (group == null)
                {
                    _logger.LogWarning("Email group with ID {GroupId} not found or already deleted", groupId);
                    return null;
                }

                // 2. Get email members
                var members = await _unitOfWork.EmailListMemberRepository.FindAllAsync(m => m.GroupId == groupId && !m.IsDeleted);
                var emailMembers = members.Select(m => new EmailMemberDto
                {
                    MemberId = m.MemberId,
                    Name = m.Name,
                    Email = m.Email,
                    IsActive = m.IsActive
                }).ToList();

                // 3. Get company associations
                var companyAssociations = await _unitOfWork.CompanyEmailGroupRepository.FindAllAsync(c => c.GroupId == groupId && !c.IsDeleted);
                var companyDtos = companyAssociations.Select(c => new CompanyEmailGroupDto
                {
                    CompanyEmailGroupId = c.CompanyEmailGroupId,
                    GroupId = c.GroupId,
                    CompanyId = c.CompanyId,
                    IsSelected = c.IsSelected
                }).ToList();

                // 4. Get user information for created/modified by
                var users = _unitOfWork.UserRepository.GetAll().ToList();
                var createdByUser = users.FirstOrDefault(u => u.UserId == group.CreatedBy);
                var modifiedByUser = group.ModifiedBy.HasValue ? users.FirstOrDefault(u => u.UserId == group.ModifiedBy.Value) : null;

                // 5. Build the detail DTO
                var detailDto = new EmailGroupDetailDto
                {
                    GroupId = group.GroupId,
                    GroupName = group.GroupName,
                    CreatedBy = group.CreatedBy.ToString(),
                    CreatedOn = group.CreatedOn,
                    ModifiedBy = group.ModifiedBy?.ToString(),
                    ModifiedOn = group.ModifiedOn,
                    UploadedBy = createdByUser != null ? $"{createdByUser.FirstName} {createdByUser.LastName}" : string.Empty,
                    EmailMembers = emailMembers,
                    CompanyAssociations = companyDtos
                };

                _logger.LogInformation("Successfully retrieved email group details for group {GroupId}", groupId);
                return detailDto;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving email group details for group {GroupId}", groupId);
                throw;
            }
        }

        public async Task<bool> UpdateEmailGroupAsync(int groupId, EmailGroupUpdateDto dto, int userId)
        {
            try
            {
                // 1. Validate input
                if (dto == null)
                {
                    _logger.LogWarning("Update DTO is null for group {GroupId}", groupId);
                    return false;
                }

                if (string.IsNullOrWhiteSpace(dto.GroupName))
                {
                    _logger.LogWarning("Group name is required for update of group {GroupId}", groupId);
                    return false;
                }

                // 2. Check if group exists and is not deleted
                var group = await _unitOfWork.EmailNotificationGroupRepository.FindFirstAsync(g => g.GroupId == groupId && !g.IsDeleted);
                if (group == null)
                {
                    _logger.LogWarning("Email group with ID {GroupId} not found or already deleted", groupId);
                    return false;
                }

                // 3. Update group name
                group.GroupName = dto.GroupName.Trim();
                group.ModifiedBy = userId;
                group.ModifiedOn = DateTime.UtcNow;
                _unitOfWork.EmailNotificationGroupRepository.Update(group);

                // 4. Smart update email members - Update existing, delete missing, add new
                var existingMembers = await _unitOfWork.EmailListMemberRepository.FindAllAsync(m => m.GroupId == groupId && !m.IsDeleted);
                var inputMembers = dto.EmailMembers ?? [];

                // Create lookup dictionaries for efficient matching
                var existingMembersDict = existingMembers.ToDictionary(m => m.MemberId);
                var inputMembersByEmail = inputMembers
                    .Where(m => !string.IsNullOrWhiteSpace(m.Email))
                    .GroupBy(m => m.Email.Trim().ToLowerInvariant())
                    .ToDictionary(g => g.Key, g => g.First());

                var processedMemberIds = new HashSet<int>();

                // Process input members
                foreach (var memberDto in inputMembers.Where(m => !string.IsNullOrWhiteSpace(m.Email)))
                {
                    var trimmedEmail = memberDto.Email.Trim();

                    if (memberDto.MemberId > 0 && existingMembersDict.TryGetValue(memberDto.MemberId, out var existingMember))
                    {
                        // Update existing member by ID
                        existingMember.Name = memberDto.Name?.Trim() ?? string.Empty;
                        existingMember.Email = trimmedEmail;
                        existingMember.IsActive = memberDto.IsActive;
                        existingMember.ModifiedBy = userId;
                        existingMember.ModifiedOn = DateTime.UtcNow;
                        _unitOfWork.EmailListMemberRepository.Update(existingMember);
                        processedMemberIds.Add(memberDto.MemberId);
                    }
                    else
                    {
                        // Add new member (no ID provided or ID not found)
                        var newMember = new EmailListMember
                        {
                            GroupId = groupId,
                            Name = memberDto.Name?.Trim() ?? string.Empty,
                            Email = trimmedEmail,
                            IsActive = memberDto.IsActive,
                            CreatedBy = userId,
                            CreatedOn = DateTime.UtcNow
                        };
                        await _unitOfWork.EmailListMemberRepository.AddAsyn(newMember);
                    }
                }

                // Soft delete members that are not in the input
                foreach (var existingMember in existingMembers.Where(m => !processedMemberIds.Contains(m.MemberId)))
                {
                    existingMember.IsDeleted = true;
                    existingMember.ModifiedBy = userId;
                    existingMember.ModifiedOn = DateTime.UtcNow;
                    _unitOfWork.EmailListMemberRepository.Update(existingMember);
                }

                // 5. Smart update company associations - Update existing, delete missing, add new
                var existingCompanyAssociations = await _unitOfWork.CompanyEmailGroupRepository.FindAllAsync(c => c.GroupId == groupId && !c.IsDeleted);
                var inputCompanyIds = dto.CompanyIds?.Where(id => id > 0).Select(id => id.ToString()).ToHashSet() ?? [];

                // Create lookup dictionary for existing associations (filter out null CompanyIds)
                var existingAssociationsDict = existingCompanyAssociations
                    .Where(c => !string.IsNullOrEmpty(c.CompanyId))
                    .ToDictionary(c => c.CompanyId);
                var processedCompanyIds = new HashSet<string>();

                // Process input company IDs
                foreach (var companyIdStr in inputCompanyIds)
                {
                    if (existingAssociationsDict.TryGetValue(companyIdStr, out var existingAssociation))
                    {
                        // Update existing association (ensure it's selected and not deleted)
                        existingAssociation.IsSelected = true;
                        existingAssociation.ModifiedBy = userId;
                        existingAssociation.ModifiedOn = DateTime.UtcNow;
                        _unitOfWork.CompanyEmailGroupRepository.Update(existingAssociation);
                        processedCompanyIds.Add(companyIdStr);
                    }
                    else
                    {
                        // Add new company association
                        var newAssociation = new CompanyEmailGroup
                        {
                            GroupId = groupId,
                            CompanyId = companyIdStr,
                            IsSelected = true,
                            CreatedBy = userId,
                            CreatedOn = DateTime.UtcNow
                        };
                        await _unitOfWork.CompanyEmailGroupRepository.AddAsyn(newAssociation);
                        processedCompanyIds.Add(companyIdStr);
                    }
                }

                // Soft delete associations that are not in the input
                foreach (var existingAssociation in existingCompanyAssociations.Where(c => !processedCompanyIds.Contains(c.CompanyId)))
                {
                    existingAssociation.IsDeleted = true;
                    existingAssociation.ModifiedBy = userId;
                    existingAssociation.ModifiedOn = DateTime.UtcNow;
                    _unitOfWork.CompanyEmailGroupRepository.Update(existingAssociation);
                }

                await _unitOfWork.SaveAsync();

                _logger.LogInformation("Successfully updated email group {GroupId} with {MemberCount} members and {CompanyCount} company associations",
                    groupId, dto.EmailMembers?.Count ?? 0, dto.CompanyIds?.Count ?? 0);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating email group {GroupId}", groupId);
                throw;
            }
        }

        public async Task<bool> DeleteEmailGroupAsync(int groupId, int userId)
        {
            try
            {
                // 1. Check if group exists and is not already deleted
                var group = await _unitOfWork.EmailNotificationGroupRepository.FindFirstAsync(g => g.GroupId == groupId && !g.IsDeleted);
                if (group == null)
                {
                    _logger.LogWarning("Email group with ID {GroupId} not found or already deleted", groupId);
                    return false;
                }

                // 2. Check if group is referenced in active email reminders
                var activeReminderReferences = await _unitOfWork.EmailReminderRecipientsRepository
                    .FindAllAsync(r => r.GroupID == groupId && r.IsGroupMember && !r.IsDeleted);

                if (activeReminderReferences.Count > 0)
                {
                    _logger.LogWarning("Cannot delete email group {GroupId} as it is referenced in {Count} active email reminders",
                        groupId, activeReminderReferences.Count);
                    throw new InvalidOperationException($"Cannot delete email group as it is referenced in {activeReminderReferences.Count} active email reminders. Please remove the group from email reminders first.");
                }

                // 3. Soft delete the main group
                group.IsDeleted = true;
                group.ModifiedBy = userId;
                group.ModifiedOn = DateTime.UtcNow;
                _unitOfWork.EmailNotificationGroupRepository.Update(group);

                // 4. Soft delete all email list members for this group
                var members = await _unitOfWork.EmailListMemberRepository.FindAllAsync(m => m.GroupId == groupId && !m.IsDeleted);
                foreach (var member in members)
                {
                    member.IsDeleted = true;
                    member.ModifiedBy = userId;
                    member.ModifiedOn = DateTime.UtcNow;
                    _unitOfWork.EmailListMemberRepository.Update(member);
                }

                // 5. Soft delete all company associations for this group
                var companyAssociations = await _unitOfWork.CompanyEmailGroupRepository.FindAllAsync(c => c.GroupId == groupId && !c.IsDeleted);
                foreach (var association in companyAssociations)
                {
                    association.IsDeleted = true;
                    association.ModifiedBy = userId;
                    association.ModifiedOn = DateTime.UtcNow;
                    _unitOfWork.CompanyEmailGroupRepository.Update(association);
                }

                await _unitOfWork.SaveAsync();

                _logger.LogInformation("Successfully deleted email group {GroupId} with {MemberCount} members and {CompanyCount} company associations",
                    groupId, members.Count, companyAssociations.Count);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting email group {GroupId}", groupId);
                throw;
            }
        }

        /// <summary>
        /// Checks if a given email group name already exists for the specified user,
        /// excluding the current group if updating. The group name is normalized by
        /// trimming whitespace and converting to lowercase for accurate comparison.
        /// </summary>
        /// <param name="groupName">The name of the email group to check.</param>
        /// <param name="groupId">The ID of the group being updated; 0 if creating a new group.</param>        
        /// <returns>True if a duplicate group name exists; otherwise, false.</returns>
        public async Task<bool> CheckDuplicateName(string groupName, int groupId)
        {
            // For create: groupId = 0; For update: groupId > 0 (exclude current group)
            var normalizedGroupName = groupName.Trim().ToLower();

            if (groupId > 0)
            {
                // Updating: check if the group with this ID has the same name
                var group = await _unitOfWork.EmailNotificationGroupRepository
                    .FindFirstAsync(g =>
                        g.GroupId == groupId &&                        
                        !g.IsDeleted);

                if (group != null && group.GroupName.Trim().ToLower() == normalizedGroupName)
                {
                    // The name matches the current group
                    return false;
                }
            }
            else
            {
                // Creating: check if any group by this user has the same name
                var exists = await _unitOfWork.EmailNotificationGroupRepository
                    .FindFirstAsync(g =>                        
                        !g.IsDeleted &&
                        g.GroupName.Trim().ToLower() == normalizedGroupName);

                return exists != null;
            }

            // For update, if the name is different, check for duplicates in other groups
            var duplicate = await _unitOfWork.EmailNotificationGroupRepository
                .FindFirstAsync(g =>                    
                    !g.IsDeleted &&
                    g.GroupName.Trim().ToLower() == normalizedGroupName &&
                    g.GroupId != groupId);

            return duplicate != null;
        }
    }
}