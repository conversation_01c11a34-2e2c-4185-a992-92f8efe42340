﻿using DataAccessLayer.DBModel;
using System.ComponentModel.DataAnnotations;


namespace DataAccessLayer.Models.Fund
{
    public partial class MappingFundSectionKpi : BaseCommonModel
    {
        [Key]
        public int MappingFundSectionKpiId { get; set; }
        public int FundId { get; set; }
        public int KpiId { get; set; }
        public int? ParentKpiId { get; set; }
        public int? DisplayOrder { get; set; }
        public bool? IsHeader { get; set; }
        public int? ModuleId { get; set; }
        public string Formula { get; set; }
        public string FormulaKpiId { get; set; }
        public bool IsExtraction { get; set; }
        public string Synonym { get; set; }
        public string Definition { get; set; }
    }
}
