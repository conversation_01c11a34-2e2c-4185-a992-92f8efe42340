﻿using API.Filters.CustomAuthorization;
using API.Helpers;
using Contract.Account;
using Contract.Documents;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using PortfolioCompany.Interfaces;
using System.Threading.Tasks;

namespace API.Controllers.DataExtraction
{
    [Authorize(Policy = AppSettings.DefaultAuthPolicy)]
    [Route("api")]
    public class DataExtractionController(IDataExtraction dataExtraction, ILogger<DataExtractionController> logger) : ControllerBase
    {
        private readonly ILogger<DataExtractionController> _logger = logger;
        private readonly IDataExtraction _dataExtraction = dataExtraction;
        /// <summary>
        /// Fetches the available data extraction types.
        /// </summary>
        /// <remarks>
        /// This endpoint retrieves a list of data extraction types available in the system.
        /// </remarks>
        /// <returns>A list of <see cref="DocumentsInformationDto"/> representing the data extraction types.</returns>
        [HttpGet("data-extraction-types/{featureId}")]
        public async Task<IActionResult> FetchDataExtractionTypes(int featureId)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            _logger.LogInformation("Fetching data extraction types");
            return Ok(await _dataExtraction.GetDataExtractionTypes(featureId));
        }
        [HttpGet("data-extraction-source-types")]
        public async Task<IActionResult> FetchSourceTypes()
        {
            _logger.LogInformation("Fetching data extraction types");
            return Ok(await _dataExtraction.GetSourceTypes());
        }
        /// <summary>
        /// Adds a new document type for data extraction.
        /// </summary>
        /// <param name="documentType">The document type to be added.</param>
        /// <returns>An <see cref="IActionResult"/> representing the result of the operation.</returns>
        /// <response code="200">Returns the newly added document type.</response>
        /// <response code="400">If the document type is null or invalid.</response>

        [HttpPost]
        [UserFeatureAuthorize((int)Features.RepositoryConfiguration)]
        [Route("add-document-type")]
        public async Task<IActionResult> AddDocumentType([FromBody] DocumentsInformationDto documentType)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var result = await _dataExtraction.AddDocumentTypeAsync(documentType);
            return Ok(result);
        }
    }
}
