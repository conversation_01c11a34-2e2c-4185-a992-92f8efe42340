using System;
using System.Collections.Generic;
using System.Linq;
using DataAccessLayer.Models;
using DataAccessLayer.Models.DataExtraction;
using DataAccessLayer.Models.EmailNotifications;
using DataAccessLayer.DBModel;
using EmailConfiguration.DTOs;

namespace EmailConfiguration.Helpers
{
    /// <summary>
    /// Helper class for common email reminder operations
    /// </summary>
    public static class EmailReminderHelper
    {
        /// <summary>
        /// Parses comma-separated string of IDs into a list of integers
        /// </summary>
        /// <param name="idsString">Comma-separated string of IDs</param>
        /// <returns>List of valid integer IDs</returns>
        public static List<int> ParseCommaSeparatedIds(string idsString)
        {
            if (string.IsNullOrWhiteSpace(idsString))
                return new List<int>();

            return idsString
                .Split(',', StringSplitOptions.RemoveEmptyEntries)
                .Select(id => int.TryParse(id.Trim(), out var result) ? result : 0)
                .Where(id => id > 0)
                .ToList();
        }

        /// <summary>
        /// Converts a list of integers to comma-separated string
        /// </summary>
        /// <param name="ids">List of integer IDs</param>
        /// <returns>Comma-separated string</returns>
        public static string ConvertIdsToCommaSeparatedString(IEnumerable<int> ids)
        {
            if (ids == null)
                return string.Empty;

            return string.Join(",", ids);
        }

        /// <summary>
        /// Maps portfolio companies to EntityDetails list
        /// </summary>
        /// <param name="companies">Portfolio companies</param>
        /// <returns>List of EntityDetails</returns>
        public static List<EntityDetails> MapCompaniesToEntityDetails(IEnumerable<PortfolioCompanyDetails> companies)
        {
            return companies.Select(c => new EntityDetails
            {
                Id = c.PortfolioCompanyId,
                Name = c.CompanyName
            }).ToList();
        }

        /// <summary>
        /// Maps document types to EntityDetails list
        /// </summary>
        /// <param name="documentTypes">Document types</param>
        /// <returns>List of EntityDetails</returns>
        public static List<EntityDetails> MapDocumentTypesToEntityDetails(IEnumerable<DataExtractionTypes> documentTypes)
        {
            return documentTypes.Select(dt => new EntityDetails
            {
                Id = dt.Id,
                Name = dt.DocumentName
            }).ToList();
        }

        /// <summary>
        /// Gets company details for a reminder based on entity IDs string
        /// </summary>
        /// <param name="entityIdsStr">Comma-separated entity IDs</param>
        /// <param name="allCompanies">All available companies</param>
        /// <returns>List of EntityDetails for matching companies</returns>
        public static List<EntityDetails> GetCompanyDetailsForReminder(string entityIdsStr, IEnumerable<PortfolioCompanyDetails> allCompanies)
        {
            var companyIds = ParseCommaSeparatedIds(entityIdsStr);
            return allCompanies
                .Where(c => companyIds.Contains(c.PortfolioCompanyId))
                .Select(c => new EntityDetails
                {
                    Id = c.PortfolioCompanyId,
                    Name = c.CompanyName
                })
                .ToList();
        }

        /// <summary>
        /// Gets document type details for a reminder based on document type IDs string
        /// </summary>
        /// <param name="docTypeIdsStr">Comma-separated document type IDs</param>
        /// <param name="allDocTypes">All available document types</param>
        /// <returns>List of EntityDetails for matching document types</returns>
        public static List<EntityDetails> GetDocumentTypeDetailsForReminder(string docTypeIdsStr, IEnumerable<DataExtractionTypes> allDocTypes)
        {
            var docTypeIds = ParseCommaSeparatedIds(docTypeIdsStr);
            return allDocTypes
                .Where(dt => docTypeIds.Contains(dt.Id))
                .Select(dt => new EntityDetails
                {
                    Id = dt.Id,
                    Name = dt.DocumentName
                })
                .ToList();
        }

        /// <summary>
        /// Calculates reminder dates based on configuration
        /// </summary>
        /// <param name="remainder1Date">First reminder date</param>
        /// <param name="remainder2">Days to add for second reminder</param>
        /// <param name="remainder3">Days to add for third reminder</param>
        /// <param name="remainder4">Days to add for fourth reminder</param>
        /// <param name="remainder5">Days to add for fifth reminder</param>
        /// <param name="totalRemindersPerCycle">Total number of reminders</param>
        /// <returns>List of calculated reminder dates</returns>
        public static List<DateTime> CalculateReminderDates(DateTime remainder1Date, string remainder2, string remainder3,
            string remainder4, string remainder5, int totalRemindersPerCycle)
        {
            if (totalRemindersPerCycle <= 0)
                return new List<DateTime>();

            var reminderDates = new List<DateTime> { remainder1Date };
            var currentDate = remainder1Date;
            var remainderDays = new[] { remainder2, remainder3, remainder4, remainder5 };

            for (int i = 0; i < remainderDays.Length && reminderDates.Count < totalRemindersPerCycle; i++)
            {
                if (!string.IsNullOrEmpty(remainderDays[i]) && int.TryParse(remainderDays[i], out int daysToAdd))
                {
                    currentDate = currentDate.AddDays(daysToAdd);
                    reminderDates.Add(currentDate);
                }
            }

            return reminderDates;
        }

        /// <summary>
        /// Calculates the next cycle start date based on frequency type
        /// </summary>
        /// <param name="currentCycleStartDate">Current cycle start date</param>
        /// <param name="frequencyType">Frequency type (1=Monthly, 2=Quarterly, 3=Yearly)</param>
        /// <returns>Next cycle start date</returns>
        public static DateTime CalculateNextCycleStartDate(DateTime currentCycleStartDate, int frequencyType)
        {
            return frequencyType switch
            {
                1 => currentCycleStartDate.AddMonths(1), // Monthly
                2 => currentCycleStartDate.AddMonths(3), // Quarterly
                3 => currentCycleStartDate.AddYears(1),  // Yearly
                _ => throw new ArgumentException($"Invalid frequency type: {frequencyType}. Valid values are 1 (Monthly), 2 (Quarterly), 3 (Yearly).")
            };
        }

        /// <summary>
        /// Creates next cycle reminder schedules based on configuration
        /// </summary>
        /// <param name="config">Email reminder configuration</param>
        /// <param name="userId">User ID for audit</param>
        /// <returns>List of EmailReminderSchedule entities for the next cycle</returns>
        public static List<EmailReminderSchedule> CreateNextCycleSchedule(EmailReminderConfig config, int userId)
        {
            if (config == null)
                throw new ArgumentNullException(nameof(config));
            

            // Calculate reminder dates for the next cycle
            var reminderDates = CalculateReminderDates(config.Remainder1Date, config.Remainder2,
                config.Remainder3, config.Remainder4, config.Remainder5, config.TotalRemindersPerCycle);

            // Create schedule entries
            var scheduleEntries = new List<EmailReminderSchedule>();
            for (int i = 0; i < config.TotalRemindersPerCycle && i < reminderDates.Count; i++)
            {
                var scheduleEntry = EmailReminderAuditHelper.CreateEmailReminderScheduleWithAudit(
                    config.Id, reminderDates[i], i + 1, userId);
                scheduleEntries.Add(scheduleEntry);
            }

            return scheduleEntries;
        }

        /// <summary>
        /// Extracts all unique IDs from a collection of reminders
        /// </summary>
        /// <param name="reminders">Collection of email reminders</param>
        /// <param name="idSelector">Function to select the ID string from reminder</param>
        /// <returns>List of unique integer IDs</returns>
        public static List<int> ExtractUniqueIdsFromReminders<T>(IEnumerable<T> reminders, Func<T, string> idSelector)
        {
            var allIds = new List<int>();
            foreach (var reminder in reminders)
            {
                var ids = ParseCommaSeparatedIds(idSelector(reminder));
                allIds.AddRange(ids);
            }
            return allIds.Distinct().ToList();
        }

        /// <summary>
        /// Checks if a string represents a case-insensitive match
        /// </summary>
        /// <param name="value">Value to check</param>
        /// <param name="target">Target value to match</param>
        /// <returns>True if values match case-insensitively</returns>
        public static bool IsStringMatch(string value, string target)
        {
            if (value == null || target == null)
                return false;

            return string.Equals(value, target, StringComparison.OrdinalIgnoreCase);
        }
    }
}
