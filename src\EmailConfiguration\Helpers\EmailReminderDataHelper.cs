using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using DataAccessLayer.Models;
using DataAccessLayer.Models.DataExtraction;
using DataAccessLayer.Models.EmailNotifications;
using DataAccessLayer.DBModel;
using DataAccessLayer.UnitOfWork;
using EmailConfiguration.DTOs;
using Microsoft.EntityFrameworkCore;

namespace EmailConfiguration.Helpers
{
    /// <summary>
    /// Helper class for data retrieval operations in email reminders
    /// </summary>
    public static class EmailReminderDataHelper
    {
        /// <summary>
        /// Gets portfolio companies by IDs
        /// </summary>
        /// <param name="unitOfWork">Unit of work instance</param>
        /// <param name="companyIds">List of company IDs</param>
        /// <returns>List of portfolio companies</returns>
        public static async Task<List<PortfolioCompanyDetails>> GetPortfolioCompaniesByIdsAsync(IUnitOfWork unitOfWork, List<int> companyIds)
        {
            if (companyIds == null || companyIds.Count == 0)
                return new List<PortfolioCompanyDetails>();

            return (await unitOfWork.PortfolioCompanyDetailRepository
                .GetManyAsync(pcr => !pcr.IsDeleted && pcr.IsActive == true && companyIds.Contains(pcr.PortfolioCompanyId)))
                .ToList();
        }

        /// <summary>
        /// Gets document types by IDs
        /// </summary>
        /// <param name="unitOfWork">Unit of work instance</param>
        /// <param name="documentTypeIds">List of document type IDs</param>
        /// <returns>List of document types</returns>
        public static async Task<List<DataExtractionTypes>> GetDocumentTypesByIdsAsync(IUnitOfWork unitOfWork, List<int> documentTypeIds)
        {
            if (documentTypeIds == null || documentTypeIds.Count == 0)
                return new List<DataExtractionTypes>();

            return (await unitOfWork.DataExtractionTypesRepository
                .GetManyAsync(dt => documentTypeIds.Contains(dt.Id)))
                .ToList();
        }

        /// <summary>
        /// Gets user information for companies and document types
        /// </summary>
        /// <param name="unitOfWork">Unit of work instance</param>
        /// <param name="companyIds">List of company IDs</param>
        /// <param name="documentTypeIds">List of document type IDs</param>
        /// <param name="portfolioCompanyFeatureId">Portfolio company feature ID</param>
        /// <returns>List of user information</returns>
        public static async Task<List<UserInformation>> GetUserInformationAsync(IUnitOfWork unitOfWork, List<int> companyIds,
            List<int> documentTypeIds, int portfolioCompanyFeatureId)
        {
            if (companyIds == null || companyIds.Count == 0)
                return new List<UserInformation>();

            // Get all active user information records for the specified companies
            var userInfoList = await unitOfWork.UserInfoRepository
                .GetManyAsync(ui => !ui.IsDeleted && ui.IsActive == true && ui.FeatureId == portfolioCompanyFeatureId &&
                    companyIds.Contains((int)ui.EntityID));

            if (documentTypeIds == null || documentTypeIds.Count == 0)
                return userInfoList.ToList();

            // Filter by document types if provided
            var finalUserInformationIDs = await unitOfWork.UserDocumentsRepository
                .GetManyAsync(ud => userInfoList.Any(ui => ui.UserInformationID == ud.UserInformationID) &&
                    documentTypeIds.Contains(ud.DocumentTypeID));

            return userInfoList.Where(x => finalUserInformationIDs.Any(y => y.UserInformationID == x.UserInformationID))
                .ToList();
        }

        /// <summary>
        /// Gets company email groups by company IDs
        /// </summary>
        /// <param name="unitOfWork">Unit of work instance</param>
        /// <param name="companyIds">List of company IDs</param>
        /// <returns>List of company email groups</returns>
        public static List<CompanyEmailGroup> GetCompanyEmailGroups(IUnitOfWork unitOfWork, List<int> companyIds)
        {
            if (companyIds == null || companyIds.Count == 0)
                return new List<CompanyEmailGroup>();

            return unitOfWork.CompanyEmailGroupRepository
                .GetManyQueryable(x => !x.IsDeleted && companyIds.Contains(int.Parse(x.CompanyId)))
                .Include(x => x.Group)
                .ToList();
        }

        /// <summary>
        /// Gets email notification groups by group IDs
        /// </summary>
        /// <param name="unitOfWork">Unit of work instance</param>
        /// <param name="groupIds">List of group IDs</param>
        /// <returns>List of email notification groups</returns>
        public static async Task<List<EmailNotificationGroup>> GetEmailNotificationGroupsAsync(IUnitOfWork unitOfWork, List<int> groupIds)
        {
            if (groupIds == null || groupIds.Count == 0)
                return new List<EmailNotificationGroup>();

            return (await unitOfWork.EmailNotificationGroupRepository
                .GetManyAsync(g => groupIds.Contains(g.GroupId) && !g.IsDeleted))
                .ToList();
        }

        /// <summary>
        /// Gets pending email reminder schedules for a specific date
        /// </summary>
        /// <param name="unitOfWork">Unit of work instance</param>
        /// <param name="targetDate">Target date to check</param>
        /// <returns>List of pending schedules</returns>
        public static async Task<List<EmailReminderSchedule>> GetPendingSchedulesAsync(IUnitOfWork unitOfWork, DateTime targetDate)
        {
            return (await unitOfWork.EmailReminderScheduleRepository
                .GetManyAsync(x => x.ReminderDate.Year == targetDate.Year &&
                    x.ReminderDate.Month == targetDate.Month &&
                    x.ReminderDate.Day == targetDate.Day &&
                    x.Status == 0 && // Pending
                    x.IsActive &&
                    !x.IsDeleted))
                .ToList();
        }

        /// <summary>
        /// Gets email reminder recipients by reminder ID
        /// </summary>
        /// <param name="unitOfWork">Unit of work instance</param>
        /// <param name="reminderId">Reminder ID</param>
        /// <returns>List of email reminder recipients</returns>
        public static async Task<List<EmailReminderRecipients>> GetEmailReminderRecipientsAsync(IUnitOfWork unitOfWork, Guid reminderId)
        {
            return (await unitOfWork.EmailReminderRecipientsRepository
                .GetManyAsync(r => r.ReminderId == reminderId && !r.IsDeleted))
                .ToList();
        }

        /// <summary>
        /// Gets all non-deleted email reminders sorted by modified date
        /// </summary>
        /// <param name="unitOfWork">Unit of work instance</param>
        /// <returns>List of email reminders sorted by modified date descending</returns>
        public static async Task<List<EmailReminder>> GetAllValidEmailRemindersAsync(IUnitOfWork unitOfWork)
        {
            var emailReminders = await unitOfWork.EmailReminderRepository
                .FindAllAsync(r => !r.IsDeleted && !string.IsNullOrWhiteSpace(r.EmailBody) && !string.IsNullOrWhiteSpace(r.Subject));

            return emailReminders
                .OrderByDescending(r => r.ModifiedOn ?? r.CreatedOn)
                .ToList();
        }

        /// <summary>
        /// Gets email reminder schedule entries by config ID
        /// </summary>
        /// <param name="unitOfWork">Unit of work instance</param>
        /// <param name="configId">Config ID</param>
        /// <returns>List of schedule entries</returns>
        public static async Task<List<EmailReminderSchedule>> GetEmailReminderSchedulesByConfigIdAsync(IUnitOfWork unitOfWork, int configId)
        {
            return (await unitOfWork.EmailReminderScheduleRepository
                .GetManyAsync(x => x.ReminderConfigId == configId && !x.IsDeleted && x.IsActive))
                .ToList();
        }

        /// <summary>
        /// Finds email reminder by ID
        /// </summary>
        /// <param name="unitOfWork">Unit of work instance</param>
        /// <param name="reminderId">Reminder ID</param>
        /// <returns>Email reminder or null if not found</returns>
        public static async Task<EmailReminder> FindEmailReminderByIdAsync(IUnitOfWork unitOfWork, Guid reminderId)
        {
            return await unitOfWork.EmailReminderRepository.FindFirstAsync(
                x => x.ReminderId == reminderId && !x.IsDeleted);
        }

        /// <summary>
        /// Finds email reminder config by reminder ID
        /// </summary>
        /// <param name="unitOfWork">Unit of work instance</param>
        /// <param name="reminderId">Reminder ID</param>
        /// <returns>Email reminder config or null if not found</returns>
        public static async Task<EmailReminderConfig> FindEmailReminderConfigByReminderIdAsync(IUnitOfWork unitOfWork, Guid reminderId)
        {
            return await unitOfWork.EmailReminderConfigRepository
                .FindFirstAsync(x => x.ReminderId == reminderId);
        }

        /// <summary>
        /// Finds email reminder config by ID
        /// </summary>
        /// <param name="unitOfWork">Unit of work instance</param>
        /// <param name="configId">Config ID</param>
        /// <returns>Email reminder config or null if not found</returns>
        public static async Task<EmailReminderConfig> FindEmailReminderConfigByIdAsync(IUnitOfWork unitOfWork, int configId)
        {
            return await unitOfWork.EmailReminderConfigRepository
                .FindFirstAsync(x => x.Id == configId && !x.IsDeleted);
        }

        /// <summary>
        /// Finds email reminder schedule by ID
        /// </summary>
        /// <param name="unitOfWork">Unit of work instance</param>
        /// <param name="scheduleId">Schedule ID</param>
        /// <returns>Email reminder schedule or null if not found</returns>
        public static async Task<EmailReminderSchedule> FindEmailReminderScheduleByIdAsync(IUnitOfWork unitOfWork, int scheduleId)
        {
            return await unitOfWork.EmailReminderScheduleRepository
                .FindFirstAsync(x => x.Id == scheduleId && x.IsActive);
        }

        /// <summary>
        /// Gets schedule statistics for a reminder config
        /// </summary>
        /// <param name="unitOfWork">Unit of work instance</param>
        /// <param name="configId">Config ID</param>
        /// <returns>Tuple containing total sent count, last sent date, and next scheduled date</returns>
        public static async Task<(int totalSent,int nonSentandnonPending , DateTime? lastSentDate, DateTime? nextScheduledDate)> GetScheduleStatisticsAsync(IUnitOfWork unitOfWork, int configId)
        {
            var schedules = await GetEmailReminderSchedulesByConfigIdAsync(unitOfWork, configId);

            var sentSchedules = schedules.Where(s => s.Status == ReminderStatus.Sent && s.SentDate.HasValue).ToList();
            var pendingSchedules = schedules.Where(s => s.Status == ReminderStatus.Pending).OrderBy(s => s.ReminderDate).ToList();
            var errorandskippedSchedules = schedules.Where(s => s.Status == ReminderStatus.Failed || s.Status == ReminderStatus.Skipped).ToList();

            var totalSent = sentSchedules.Count;
            var lastSentDate = sentSchedules.Any() ? sentSchedules.Max(s => s.SentDate) : null;
            var nextScheduledDate = pendingSchedules.Any() ? pendingSchedules.First().ReminderDate : (DateTime?)null;
            var errorandskippedcount = errorandskippedSchedules.Count;

            return (totalSent, errorandskippedcount, lastSentDate, nextScheduledDate);
        }

        /// <summary>
        /// Gets pending email reminder schedules by reminder ID
        /// </summary>
        /// <param name="unitOfWork">Unit of work instance</param>
        /// <param name="reminderId">Reminder ID</param>
        /// <returns>List of pending schedules for the reminder</returns>
        public static async Task<List<EmailReminderSchedule>> GetPendingSchedulesByReminderIdAsync(IUnitOfWork unitOfWork, Guid reminderId)
        {
            // First get the config for this reminder
            var config = await FindEmailReminderConfigByReminderIdAsync(unitOfWork, reminderId);
            if (config == null)
                return new List<EmailReminderSchedule>();

            // Get pending schedules for this config
            return (await unitOfWork.EmailReminderScheduleRepository
                .GetManyAsync(x => x.ReminderConfigId == config.Id &&
                    x.Status == 0 && // Pending
                    x.IsActive &&
                    !x.IsDeleted))
                .ToList();
        }

        /// <summary>
        /// Checks if a schedule is the last one in the current cycle
        /// </summary>
        /// <param name="unitOfWork">Unit of work instance</param>
        /// <param name="scheduleId">Schedule ID to check</param>
        /// <returns>True if this is the last schedule in the cycle</returns>
        public static async Task<bool> IsLastScheduleInCycleAsync(IUnitOfWork unitOfWork, int scheduleId)
        {
            var schedule = await FindEmailReminderScheduleByIdAsync(unitOfWork, scheduleId);
            if (schedule == null)
                return false;

            // Get the config to know total reminders per cycle
            var config = await FindEmailReminderConfigByIdAsync(unitOfWork, schedule.ReminderConfigId);
            if (config == null)
                return false;

            // Check if this schedule has the highest occurrence number in the current cycle
            return schedule.ScheduleOccurrence == config.TotalRemindersPerCycle;
        }
    }
}
