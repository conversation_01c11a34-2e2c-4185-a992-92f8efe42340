using AutoMapper;
using Dapper;
using System.Data;
using Microsoft.Extensions.Logging;
using Shared;
using DapperRepository;
using DapperRepository.Constants;
using DataAccessLayer.UnitOfWork;
using DataAccessLayer.Models.Fund;
using System;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using Contract.Fund.Models;
using Contract.KPI;
using Contract.Configuration;
using DataAccessLayer.DBModel;
using Contract.PortfolioCompany.DomainModel.PortfolioCompanyKPI;
using DataAccessLayer.Models;
using Contract.PortfolioCompany;
using Financials.Helpers;
using Utility.Helpers;
using System.Reflection;
using Contract.Funds;
using DocumentFormat.OpenXml.Spreadsheet;
namespace Fund.Services;

public class FundKpiService(
    IUnitOfWork unitOfWork,
    IMapper mapper,
    IDapperGenericRepository dapperGenericRepository,
    ILogger<FundKpiService> logger) : IFundKpiService
{
    private readonly IUnitOfWork _unitOfWork = unitOfWork;
    private readonly IMapper _mapper = mapper;
    private readonly IDapperGenericRepository _dapperGenericRepository = dapperGenericRepository;
    private readonly ILogger<FundKpiService> _logger = logger;

    /// <summary>
    /// Add or Update Fund KPI
    /// </summary>
    /// <param name="kpiModel">Fund KPI Model</param>
    /// <param name="userId">User ID</param>
    /// <returns>Result status code</returns>
    public async Task<int> AddOrUpdateFundKPI(FundKpiModel kpiModel, int userId)
    {
        if (kpiModel.FundSectionKpiId > 0)
        {
            _logger.LogInformation($"Updating Fund KPI with ID: {kpiModel.FundSectionKpiId}");
            await UpdateFundKpiModel(kpiModel, userId);
            return 1;
        }
        else
        {
            if (await _unitOfWork.MFundSectionKpiRepository.ExistsAsyncAny(x => x.Kpi.ToLower() == kpiModel.Kpi.ToLower() && x.ModuleId == kpiModel.ModuleId && !x.IsDeleted))
                return -1;
            else
            {
                kpiModel.CreatedBy = userId;
                await AddFundKPI(kpiModel);
                _logger.LogInformation($"Added new Fund KPI with ID: {kpiModel.FundSectionKpiId}");
                return 1;
            }
        }
    }

    private async Task UpdateFundKpiModel(FundKpiModel kpiModel, int userId)
    {
        _logger.LogInformation($"Updating Fund KPI with ID: {kpiModel.FundSectionKpiId}");
        MFundSectionKpi mFundSectionKpi = await _unitOfWork.MFundSectionKpiRepository.FindFirstAsync(x => x.FundSectionKpiId == kpiModel.FundSectionKpiId);
        mFundSectionKpi.ModifiedBy = userId;
        mFundSectionKpi.ModifiedOn = DateTime.UtcNow;
        mFundSectionKpi.Kpi = kpiModel.Kpi;
        mFundSectionKpi.KpiInfo = kpiModel.KpiInfo;
        mFundSectionKpi.Description = kpiModel.Description;
        mFundSectionKpi.IsBoldKpi = kpiModel.IsBoldKpi;
        mFundSectionKpi.IsHeader = kpiModel.IsHeader;
        mFundSectionKpi.Synonym = kpiModel.Synonym;
        mFundSectionKpi.MethodologyId = (kpiModel.KpiInfo != Constants.KpiInfoText) ? kpiModel.MethodologyId : 0;
        _unitOfWork.MFundSectionKpiRepository.Update(mFundSectionKpi);
        await _unitOfWork.SaveAsync();
        _logger.LogInformation($"Updated Fund KPI with ID: {kpiModel.FundSectionKpiId}");
    }

    private async Task AddFundKPI(FundKpiModel kpiModel)
    {
        _logger.LogInformation($"Adding new Fund KPI");
        MFundSectionKpi mFundSectionKpi = new();
        mFundSectionKpi.CreatedOn = DateTime.UtcNow;
        mFundSectionKpi.ModifiedOn = null;
        mFundSectionKpi.MethodologyId = (kpiModel.KpiInfo != Constants.KpiInfoText) ? kpiModel.MethodologyId : 0;
        mFundSectionKpi.Description = kpiModel.Description;
        mFundSectionKpi.Kpi = kpiModel.Kpi;
        mFundSectionKpi.KpiInfo = kpiModel.KpiInfo;
        mFundSectionKpi.IsBoldKpi = kpiModel.IsBoldKpi;
        mFundSectionKpi.IsHeader = kpiModel.IsHeader;
        mFundSectionKpi.ModuleId = kpiModel.ModuleId;
        mFundSectionKpi.Synonym = kpiModel.Synonym;
        await _unitOfWork.MFundSectionKpiRepository.AddAsyn(mFundSectionKpi);
        await _unitOfWork.SaveAsync();
        _logger.LogInformation($"Added new Fund KPI");
    }

    /// <summary>
    /// Get Fund KPI List by Module ID
    /// </summary>
    /// <param name="moduleId">Module ID</param>
    /// <returns>List of Fund KPIs</returns>
    public async Task<List<FundKpiModel>> GetFundKPIList(int moduleId)
    {
        _logger.LogInformation($"Getting Fund KPI List for Module ID: {moduleId}");
        List<FundKpiModel> fundKpis = []; //View model
        if (moduleId > 0)
        {
            var result = await _unitOfWork.MFundSectionKpiRepository.FindAllAsync(x => !x.IsDeleted && x.ModuleId == moduleId); // Domain model
            var methodologies = await _unitOfWork.M_MethodologyRepository.FindAllAsync(x => !x.IsDeleted);
            result?.ToList().ForEach(item =>
            {
                item.MethodologyName = methodologies.FirstOrDefault(y => y.MethodologyID == item.MethodologyId)?.MethodologyName;
                item.KpiInfo = item.KpiInfo;
                item.KpiInfoType = GetKpiInfoType(item.KpiInfo);
            });

            var fundModel = _mapper.Map(result, fundKpis);//Domain model to View model
            return fundModel;
        }
        else
        {
            return [];
        }
    }

    /// <summary>
    /// Get Unmapped Fund KPIs
    /// </summary>
    /// <param name="fundId">Fund ID</param>
    /// <param name="moduleId">Module ID</param>
    /// <returns>List of unmapped KPI mapping models</returns>
    public async Task<List<Contract.KPI.KpiMappingModel>> GetUnMappedFundKpi(int fundId, int moduleId)
    {
        _logger.LogInformation($"Getting Unmapped Fund KPIs for Fund ID: {fundId}, Module ID: {moduleId}");
        return await _dapperGenericRepository.Query<Contract.KPI.KpiMappingModel>(SqlConstants.QueryByGetFundKpiNotMappedList, new { @FundId = fundId, @ModuleId = moduleId });
    }

    /// <summary>
    /// Get Fund KPI Mapping
    /// </summary>
    /// <param name="fundId">Fund ID</param>
    /// <param name="moduleId">Module ID</param>
    /// <returns>List of KPI mapping models</returns>
    public async Task<List<Contract.KPI.KpiMappingModel>> FundKPIMapping(int fundId, int moduleId)
    {
        _logger.LogInformation($"Getting Fund KPI Mapping for Fund ID: {fundId}, Module ID: {moduleId}");
        var mappedKPIList = await _dapperGenericRepository.Query<MappingFundSectionKpi>(SqlConstants.QueryByGetMappingFundKpi, new { @FundId = fundId, @ModuleId = moduleId });
        var kpiMasterList = await _dapperGenericRepository.Query<MFundSectionKpi>(SqlConstants.QueryByGetFundKpi, new { @ModuleId = moduleId });
        var mappedList = (from mapping in mappedKPIList
                          join master in kpiMasterList on mapping.KpiId equals master.FundSectionKpiId
                          where !master.IsDeleted
                          select new KpiMappingModel() { Id = mapping.KpiId }).Select(x => x.Id).ToList();
        var mappedFundList = mappedKPIList.Where(x => !x.IsDeleted && mappedList.Contains(x.KpiId) && x.FundId == fundId)
            .Select(x => new KpiMappingModel()
            {
                Id = x.KpiId,
                Name = kpiMasterList.Where(y => y.FundSectionKpiId == x.KpiId && !y.IsDeleted).Select(x => x.Kpi).FirstOrDefault(),
                ParentKPIID = x.ParentKpiId,
                DisplayOrder = x.DisplayOrder,
                IsMapped = true,
                IsExtraction = x.IsExtraction,
                Synonym = string.IsNullOrEmpty(x.Synonym) ? kpiMasterList.FirstOrDefault(y => y.FundSectionKpiId == x.KpiId && !y.IsDeleted).Synonym : x.Synonym,
                Definition = x.Definition,
                IsHeader = kpiMasterList?.FirstOrDefault(y => y.FundSectionKpiId == x.KpiId) != null && kpiMasterList.FirstOrDefault(y => y.FundSectionKpiId == x.KpiId).IsHeader,
                IsBoldKPI = kpiMasterList?.FirstOrDefault(y => y.FundSectionKpiId == x.KpiId) != null && kpiMasterList.FirstOrDefault(y => y.FundSectionKpiId == x.KpiId).IsBoldKpi,
                MappingKPIId = x.MappingFundSectionKpiId,
                KpiInfo = kpiMasterList?.Where(y => y.FundSectionKpiId == x.KpiId && !y.IsDeleted).Select(x => x.KpiInfo).FirstOrDefault(),
                Formula = (string.IsNullOrEmpty(x.Formula) || x.Formula == "||||") ? kpiMasterList?.FirstOrDefault(y => y.FundSectionKpiId == x.KpiId).Formula : x.Formula,
            }).ToList();
        var parentList = mappedFundList?.Where(x => x.ParentKPIID == null || x.ParentKPIID == 0).ToList();
        return parentList.Select(x => new KpiMappingModel()
        {
            Id = x.Id,
            Name = x.Name,
            ParentKPIID = x.ParentKPIID,
            DisplayOrder = x.DisplayOrder,
            IsMapped = x.IsMapped,
            IsHeader = x.IsHeader,
            IsBoldKPI = x.IsBoldKPI,
            MappingKPIId = x.MappingKPIId,
            Formula = x.Formula,
            KpiInfo = x.KpiInfo,
            IsExtraction = x.IsExtraction,
            Synonym = string.IsNullOrEmpty(x.Synonym) ? kpiMasterList.FirstOrDefault(y => y.FundSectionKpiId == x.Id && !y.IsDeleted).Synonym : x.Synonym,
            Definition = x.Definition,
            Children = mappedFundList?.Where(y => y.ParentKPIID == x.Id && y.ParentKPIID != null).OrderByDescending(i => i.DisplayOrder.HasValue && x.IsMapped).ThenBy(i => i.DisplayOrder).ToList()
        }).OrderByDescending(i => i.DisplayOrder.HasValue && i.IsMapped).ThenBy(i => i.DisplayOrder).ToList();
    }

    /// <summary>
    /// Create duplicate Fund KPI
    /// </summary>
    /// <param name="duplicateKPI">Duplicate KPI model</param>
    /// <returns>Result status code</returns>
    public async Task<int> CreateFundKpi(DuplicateKpiModel duplicateKPI)
    {
        _logger.LogInformation($"Creating duplicate Fund KPI for ID: {duplicateKPI.Id}");
        DynamicParameters parameters = new();
        parameters.Add(Constants.KpiId, duplicateKPI.Id, DbType.Int32);
        parameters.Add(Constants.UserDetailId, duplicateKPI.UserId, DbType.Int32);
        parameters.Add(Constants.Id, DbType.Int32, direction: ParameterDirection.Output);
        await _dapperGenericRepository.QueryExecuteSpAsync<int>(SqlConstants.QueryBySPCreateDuplicateFundKpi, parameters);
        return parameters.Get<int>(Constants.Id);
    }

    /// <summary>
    /// Update Fund KPI Mapping
    /// </summary>
    /// <param name="fundId">Fund ID</param>
    /// <param name="kPIMappingModels">KPI Mapping models</param>
    /// <param name="userId">User ID</param>
    /// <param name="moduleId">Module ID</param>
    /// <returns>Success status</returns>
    public async Task<bool> UpdateFundKPIMapping(int fundId, List<Contract.KPI.KpiMappingModel> kPIMappingModels, int userId, int moduleId)
    {
        _logger.LogInformation($"Updating Fund KPI Mapping for Fund ID: {fundId}, Module ID: {moduleId}");
        List<MappingFundSectionKpi> mappingKpis = new();
        List<MappingFundSectionKpi> mappingKpisUpdate = new();
        var lastKpis = await _dapperGenericRepository.QueryFirstAsync<MappingFundSectionKpi>(SqlConstants.QueryByFundKpiLast, new { @FundId = fundId });
        var lastDisplayOrder = lastKpis?.DisplayOrder + 1 ?? 1;
        int displayOrder = 1;
        if (lastDisplayOrder != 1)
            displayOrder = lastDisplayOrder;
        foreach (var model in kPIMappingModels)
        {
            if (model.IsMapped && model.MappingKPIId == 0)
            {
                var mappingKpi = new MappingFundSectionKpi()
                {
                    FundId = fundId,
                    KpiId = model.Id,
                    ParentKpiId = null,
                    DisplayOrder = displayOrder,
                    CreatedBy = userId,
                    CreatedOn = DateTime.UtcNow,
                    IsDeleted = false,
                    ModuleId = moduleId,
                    IsExtraction = model.IsExtraction,
                    Synonym = model.Synonym,
                    Definition = model.Definition
                };
                mappingKpis.Add(mappingKpi);
                displayOrder++;
            }
            else if (!model.IsMapped && model.MappingKPIId > 0)
            {
                MappingFundSectionKpi mappingKpi = await _dapperGenericRepository.QueryFirstAsync<MappingFundSectionKpi>(SqlConstants.QueryByMappingFundKpiFirst, new { @mappingId = model.MappingKPIId });
                if (mappingKpi.DisplayOrder != (displayOrder)) mappingKpi.DisplayOrder = displayOrder;
                mappingKpi.IsExtraction = model.IsExtraction;
                mappingKpi.Synonym = model.Synonym;
                mappingKpi.Definition = model.Definition;
                mappingKpi.IsDeleted = true;
                mappingKpi.ModifiedOn = DateTime.UtcNow;
                mappingKpi.ModifiedBy = userId;
                mappingKpisUpdate.Add(mappingKpi);
                displayOrder++;
            }
            else if (model.IsMapped && model.MappingKPIId > 0)
            {
                MappingFundSectionKpi mappingKpi = await _dapperGenericRepository.QueryFirstAsync<MappingFundSectionKpi>(SqlConstants.QueryByMappingFundKpiFirst, new { @mappingId = model.MappingKPIId });
                if (mappingKpi.DisplayOrder != (displayOrder)) mappingKpi.DisplayOrder = displayOrder;
                mappingKpi.IsExtraction = model.IsExtraction;
                mappingKpi.Synonym = model.Synonym;
                mappingKpi.Definition = model.Definition;
                mappingKpi.ModifiedOn = DateTime.Now;
                mappingKpi.ModifiedBy = userId;
                if ((model.Children == null ? 0 : model.Children.Count) == 0)
                    mappingKpi.ParentKpiId = null;
                mappingKpisUpdate.Add(mappingKpi);
                displayOrder++;
            }

            if (model.Children != null && model.Children.Count > 0)
            {
                foreach (var child in model.Children)
                {
                    if (child.IsMapped && child.MappingKPIId == 0)
                    {
                        var mappingKpi = new MappingFundSectionKpi()
                        {
                            FundId = fundId,
                            KpiId = child.Id,
                            ParentKpiId = model.Id,
                            DisplayOrder = displayOrder,
                            CreatedBy = userId,
                            CreatedOn = DateTime.UtcNow,
                            IsDeleted = false,
                            ModuleId = moduleId,
                            IsExtraction = child.IsExtraction,
                            Synonym = child.Synonym,
                            Definition = child.Definition
                        };
                        mappingKpis.Add(mappingKpi);
                        displayOrder++;
                    }
                    else if (child.MappingKPIId > 0)
                    {
                        MappingFundSectionKpi mappingKpi = await _dapperGenericRepository.QueryFirstAsync<MappingFundSectionKpi>(SqlConstants.QueryByMappingFundKpiFirst, new { @mappingId = child.MappingKPIId });
                        if (mappingKpi.DisplayOrder != (displayOrder)) mappingKpi.DisplayOrder = displayOrder;
                        if (!child.IsMapped) mappingKpi.IsDeleted = true;
                        mappingKpi.ParentKpiId = model.Id;
                        mappingKpi.ModifiedOn = DateTime.UtcNow;
                        mappingKpi.ModifiedBy = userId;
                        mappingKpi.IsExtraction = child.IsExtraction;
                        mappingKpi.Synonym = child.Synonym;
                        mappingKpi.Definition = child.Definition;
                        mappingKpisUpdate.Add(mappingKpi);
                        displayOrder++;
                    }
                }
            }
        }
        if (mappingKpis != null && mappingKpis.Count > 0)
        {
            await _unitOfWork.MappingFundSectionKpiRepository.AddBulkAsyn(mappingKpis);
            await _unitOfWork.SaveAsync();
        }
        if (mappingKpisUpdate != null && mappingKpisUpdate.Count > 0)
        {
            _unitOfWork.MappingFundSectionKpiRepository.UpdateBulk(mappingKpisUpdate);
            await _unitOfWork.SaveAsync();
        }
        return true;
    }

    /// <summary>
    /// Delete Fund KPI
    /// </summary>
    /// <param name="kPIModel">KPI Delete model</param>
    /// <returns>Result status code</returns>
    public async Task<int> DeleteFundKPI(DeleteKpiModel kPIModel)
    {
        _logger.LogInformation($"Deleting Fund KPI with ID: {kPIModel.KPIId}");
        return await _dapperGenericRepository.QueryExecuteAsync<int>(SqlConstants.QueryByDeleteFundKpi, new { @FundKpiId = kPIModel.KPIId, @ModuleId = kPIModel.ModuleId });
    }

    /// <summary>
    /// Get KPI Info Type
    /// </summary>
    /// <param name="kpiInfo">KPI Info</param>
    /// <returns>KPI Info Type</returns>
    private static string GetKpiInfoType(string kpiInfo)
    {
        if (kpiInfo == Constants.KpiInfoCurrency)
        {
            return "Currency";
        }
        else if (kpiInfo == Constants.KpiInfoNumber)
        {
            return "Number";
        }
        else
        {
            return kpiInfo ?? "";
        }
    }
     /// <summary>
    /// Copy kpi to Companies
    /// </summary>
    /// <param name="copyToKPIQueryModel">Kpi Details</param>
    /// <returns>If copy successfully then 1 else 0</returns>
    public async Task<int> CopyFundKpiToCompanies(CopyToKpiQueryModel copyToKPIQueryModel)
    {
        _logger.LogInformation($"Copying Fund KPI to Companies for Fund ID: {copyToKPIQueryModel.CompanyId}");
        return await _dapperGenericRepository.QueryExecuteAsync<int>(SqlConstants.QueryByFundKpiCopyToFunds, new
        {
            @FundId = copyToKPIQueryModel.CompanyId,
            @UserId = copyToKPIQueryModel.UserId,
            @FundIds = copyToKPIQueryModel.CompanyIds,
            @ModuleId = copyToKPIQueryModel.ModuleId
        });
    }
    /// <summary>
    /// GetFundKpiValues
    /// </summary>
    /// <param name="filter"></param>
    /// <returns></returns>
    public async Task<PcKPIsResponse> GetFundKpiValues(PcKPIsFilterType filter)
    {
        PcKPIsResponse kpiObj = new();
        KpiAuditlog kpiAuditlog = new();
        filter.HasActualValues = false;
        List<PCKpisDto> kpiValues = await GetPcKPITableData(filter);
        kpiValues = FundKpiHelper.ApplyFilters(filter, kpiValues, []);
        if (kpiValues.Any(x => x.KPIValue != null))
            SetDataByCompId(filter, kpiObj, kpiAuditlog, kpiValues);
        return kpiObj;
    }

    /// <summary>
    /// GetPageConfigSubSectionFields
    /// </summary>
    /// <returns></returns>
    public async Task<List<KpiConfig>> GetPageConfigSubSectionFields()
    {
        var subPageFieldData = await _unitOfWork.SubPageFieldsRepository.FindAllAsync(x => (x.SubPageID == (int)PageConfigurationSubFeature.FundKpis || x.SubPageID == (int)PageConfigurationSubFeature.FundKeyKpis) && x.IsActive);
        if(subPageFieldData == null)
        {
            return null;
        }
        return await GetKpiSectionConfigData(subPageFieldData);
    }
    public async Task<List<KpiListModel>> GetTabList()
    {
        List<int> subPageIds = [(int)PageConfigurationSubFeature.FundKpis, (int)PageConfigurationSubFeature.FundKeyKpis];
        var kpiModules = await _unitOfWork.MFundKpiModulesRepository.FindAllAsync(x => !x.IsDeleted && x.IsActive == true);
        List<M_SubPageFields> m_SubPageFields = await _unitOfWork.SubPageFieldsRepository.FindAllAsync(x => !x.IsDeleted && x.IsActive && subPageIds.Contains(x.SubPageID));
        var result = kpiModules.Join(m_SubPageFields,
                                        e1 => e1.PageConfigFieldName, e2 => e2.Name,
                                        (e1, e2) => new KpiListModel
                                        {
                                            ModuleId = e1.ModuleId,
                                            TabAliasName = e2.AliasName,
                                            IsFinacials = false,
                                            Name = e1.TabName == null ? e1.Name : e1.TabName,
                                            Active = false,
                                            Order = e2.SequenceNo
                                        })
                                        .OrderBy(x => x.Order).ToList();
        return result;
    }
    /// <summary>
    /// SetDataByCompId
    /// </summary>
    /// <param name="filter"></param>
    /// <param name="kpiObj"></param>
    /// <param name="kpiValuesAuditlog"></param>
    /// <param name="kpiValues"></param>
    /// <returns></returns>
    public static void SetDataByCompId(PcKPIsFilterType filter, PcKPIsResponse kpiObj, KpiAuditlog kpiValuesAuditlog, List<PCKpisDto> kpiValues)
    {
        if (kpiValues?.Any(x => x.KPIValue != null) == true)
        {
            kpiObj.Headers = FundKpiHelper.GetTableHeaders(kpiValues, filter);
            FundKpiHelper.CreateDynamicObjects(filter, kpiObj, kpiValues);
        }
    }
    /// <summary>
    /// GetPcKPITableData
    /// </summary>
    /// <param name="filter"></param>
    /// <param name="fromYear"></param>
    /// <param name="toYear"></param>
    /// <returns></returns>
    private async Task<List<PCKpisDto>> GetPcKPITableData(PcKPIsFilterType filter)
    {
        string dataType = GetDataType(filter);
        return await GetFundKpiValues(filter, dataType);
    }
    /// <summary>
    /// GetValueTypeId
    /// </summary>
    /// <param name="valueType"></param>
    /// <returns></returns>
    private int GetValueTypeId(string valueType)
    {
        var value = _unitOfWork.M_ValueTypesRepository.GetFirstOrDefault(x => x.HeaderValue.Equals(valueType?.ToLower(), StringComparison.CurrentCultureIgnoreCase));
        return value?.ValueTypeID ?? 0;
    }
    /// <summary>
    /// GetFundKpiValues
    /// </summary>
    /// <param name="filter"></param>
    /// <param name="dataType"></param>
    /// <returns></returns>
    private async Task<List<PCKpisDto>> GetFundKpiValues(PcKPIsFilterType filter, string dataType)
    {
        return filter?.ValueType?.ToLower() switch
        {
            Constants.Actual or Constants.Budget or Constants.ForeCast or Constants.IC or Constants.IC2 or Constants.IC3 or Constants.IC4 or Constants.IC5
            or Constants.ActualYtd or Constants.BudgetYtd or Constants.ForecastYtd
            or Constants.ActualLtm or Constants.BudgetLtm or Constants.ForecastLtm
            => await _dapperGenericRepository.Query<PCKpisDto>(SqlConstants.QueryBySpFundKpiValuesList, new { filter.FundId, dataType, ValueTypeId = GetValueTypeId(filter.ValueType), filter.ModuleId }),
            _ => []
        };
    }
    /// <summary>
    /// GetDataType
    /// </summary>
    /// <param name="filter"></param>
    /// <returns></returns>
    private static string GetDataType(PcKPIsFilterType filter)
    {
        string dataType = Constants.MonthlyType;
        if (filter.IsQuarterly)
            dataType = Constants.QuarterlyType;
        else if (filter.IsAnnually)
            dataType = Constants.AnnuallyType;
        return dataType;
    }

    /// <summary>
    /// GetKpiSectionConfigData
    /// </summary>
    /// <param name="items"></param>
    /// <returns></returns>
    private async Task<List<KpiConfig>> GetKpiSectionConfigData(List<M_SubPageFields> items)
    {
        var result = new List<KpiConfig>();
        var subPageDetails = await _unitOfWork.SubPageDetailsRepository.FindAllAsync(x => (x.SubPageID == (int)PageConfigurationSubFeature.FundKpis || x.SubPageID == (int)PageConfigurationSubFeature.FundKeyKpis) && x.IsActive);
        foreach (var item in items)
        {
            var subSectionFields = await _unitOfWork.MSubSectionFieldsRepository.FindAllAsync(y => !y.IsDeleted && y.SubPageID == item.SubPageID && y.FieldID == item.FieldID);

            var data = subSectionFields.OrderBy(x => x.SequenceNo).Select(x => new MSubFields
                {
                    AliasName = x.Name,
                    ChartValue = string.IsNullOrEmpty(x.ChartValue) ? new List<string>() : x.ChartValue.Split(',').ToList(),
                    SectionID = x.SectionID,
                    SubPageID = x.SubPageID,
                    FieldID = x.FieldID,
                    SubFieldAliasName = x.AliasName
                }).ToList();

            result.Add(new KpiConfig
            {
                KpiType = item.Name,
                KpiConfigurationData = data?.Where(x => x.ChartValue != null && x.ChartValue.Count > 0).ToList(),
                HasChart = !item.IsChart,
                AliasName = item.AliasName,
                SectionName = subPageDetails.FirstOrDefault(x => x.SubPageID == item.SubPageID).Name
            });
        }

        return result;
    }
}
