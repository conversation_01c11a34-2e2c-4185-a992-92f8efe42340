﻿using API.Controllers;
using API.Helpers;
using AutoMapper;
using Contract.Currency;
using Contract.Deals;
using Contract.Designation;
using Contract.Funds;
using Contract.MasterMapping;
using Contract.Repository;
using Contract.Sector;
using Contract.Utility;
using DapperRepository;
using DataAccessLayer.DBModel;
using DataAccessLayer.GenericRepository;
using DataAccessLayer.Models;
using DataAccessLayer.UnitOfWork;
using DinkToPdf.Contracts;
using Master;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Logging;
using Moq;
using Utility.Services;
using Workflow.Interface;

namespace PortfolioCompany.UnitTest.Tests
{
    public class PCServiceUnitTestFixture
    {
        public Mock<IInjectedParameters> InjectedParameters;
        public Mock<IEncryption> Encryption;
        public Mock<IUnitOfWork> UnitOfWork;
        public Mock<ILogger> Logger;
        public Mock<IMapper> mapper;
        public Mock<PortfolioCompanyService> _portfolioCompanyServiceMock;
        public readonly PortfolioCompanyService _PortfolioCompanyService;
        public readonly Mock<IGenericRepository<PCCompanyKpiMonthlyValue>> _PCCompanyKpiMonthlyValue;
        public readonly Mock<IGenericRepository<M_CompanyKpi>> _M_CompanyKpi;
        public readonly Mock<IGenericRepository<MappingPortfolioCompanyKpi>> _MappingPortfolioCompanyKpi;
        public Mock<IGlobalConfigurations> GlobalConfigurations = new();
        public PortfolioCompanyController controller;
        public Mock<IDapperGenericRepository> dapperGenericRepository;
        public Mock<IDealService> dealService;
        public PCServiceUnitTestFixture()
        {
            InjectedParameters = new Mock<IInjectedParameters>();
            UnitOfWork = new Mock<IUnitOfWork>();
            mapper = new Mock<IMapper> { CallBase = true };
            _PCCompanyKpiMonthlyValue = new Mock<IGenericRepository<PCCompanyKpiMonthlyValue>>();
            UnitOfWork.Setup(s => s.PCCompanyKpiMonthlyValueRepository).Returns(_PCCompanyKpiMonthlyValue.Object);
            _M_CompanyKpi = new Mock<IGenericRepository<M_CompanyKpi>>();
            UnitOfWork.Setup(s => s.M_CompanyKPIRepository).Returns(_M_CompanyKpi.Object);
            _MappingPortfolioCompanyKpi = new Mock<IGenericRepository<MappingPortfolioCompanyKpi>>();
            UnitOfWork.Setup(s => s.PortfolioCompanyKpiMappingRepository).Returns(_MappingPortfolioCompanyKpi.Object);
            Encryption = new Mock<IEncryption>();
            Logger = new Mock<ILogger>();
            dealService = new Mock<IDealService>();
            GlobalConfigurations.Setup(S => S.GetValueByKey("HashKey")).Returns("D,zMM3X45}&QE7FZ)9Z#Lkj");
            InjectedParameters.Setup(S => S.Encryption).Returns(new Encryption(GlobalConfigurations.Object));
            InjectedParameters.Setup(S => S.UnitOfWork).Returns(UnitOfWork.Object);
            InjectedParameters.Setup(S => S.Logger).Returns(Logger.Object);
            InjectedParameters.Setup(S => S.Mapper).Returns(mapper.Object);
            Encryption.Setup(S => S.Encrypt(It.IsAny<string>())).Returns("66F63D87E53A6F638CBDBE6B3B83AEA3");
            dapperGenericRepository = new Mock<IDapperGenericRepository>();
            Mock<IPageDetailsConfigurationService> MockPageDetailsConfigurationService = new();
            _PortfolioCompanyService = new PortfolioCompanyService(dapperGenericRepository.Object, dealService.Object);
            _PortfolioCompanyService.InjectedParameters = InjectedParameters.Object;
            Mock<ISectorService> SectorService = new();
            Mock<ICurrencyService> CurrencyService = new();
            Mock<IDesignationService> DesignationService = new();
            Mock<IMasterMappingService> MasterMappingService = new();
            Mock<IConverter> Converter = new();
            Mock<IWebHostEnvironment> WebHostEnv = new();
            Mock<IFileService> _IFileService = new();
            Mock<IHelperService> helperService = new Mock<IHelperService>();
            Mock<IReportTemplateService> ReportTemplateService = new Mock<IReportTemplateService>();
            Mock<IPageDetailsConfigurationService> mockPageDetailsConfigurationService = new();
            Mock<IFundService> _IFundService = new();
            Mock<IPageFieldValueDraftService> MockPageFieldValueDraftService = new();
            Mock<IFootNote> footnote = new();          
        }
    }
}
