﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EmailConfiguration.Interfaces
{
    public interface IEmailGroupService
    {
        Task<int?> CreateEmailGroupAsync(EmailGroupCreateDto dto, int userId);
        Task<List<EmailGroupDto>> GetEmailGroupsWithCountAsync();

        // For loading members of a group
        Task<List<EmailMemberDto>> GetEmailMembersByGroupIdAsync(int groupId);

        // For deleting email group with soft delete
        Task<bool> DeleteEmailGroupAsync(int groupId, int userId);

        // For deleting a specific email member from a group with soft delete
        Task<bool> DeleteEmailMemberAsync(int groupId, int memberId, int userId);

        // For deleting multiple email members from a group with soft delete
        Task<(bool Success, List<int> DeletedMemberIds, List<int> NotFoundMemberIds)> DeleteEmailMembersAsync(int groupId, List<int> memberIds, int userId);

        // For getting complete email group details for editing
        Task<EmailGroupDetailDto> GetEmailGroupDetailAsync(int groupId);

        // For updating email group information
        Task<bool> UpdateEmailGroupAsync(int groupId, EmailGroupUpdateDto dto, int userId);

        /// <summary>
        /// Checks if a given email group name is a duplicate.
        /// </summary>
        /// <param name="groupName">The name of the email group to check.</param>
        /// <param name="groupId">The ID of the group to check against for duplicates.</param>        
        /// <returns>A task that represents the asynchronous operation. The task result contains a boolean indicating whether the name is a duplicate.</returns>
        Task<bool> CheckDuplicateName(string groupName, int groupId);
    }
}
