using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Diagnostics.CodeAnalysis;
using DataAccessLayer.DBModel;

namespace DataAccessLayer.Models.EmailNotifications
{
    [Table("EmailReminder")]
    [ExcludeFromCodeCoverage]
    public class EmailReminder : BaseCommonModel
    {
        [Key]
        public Guid ReminderId { get; set; }

        [Required]
        public int FeatureID { get; set; }

        [Required]
        [Column(TypeName = "nvarchar(MAX)")]
        public string EntityIDs { get; set; }

        [Required]
        [Column(TypeName = "nvarchar(MAX)")]
        public string DocumentTypeIds { get; set; }

        [MaxLength(500)]
        public string Subject { get; set; }

        [Column(TypeName = "nvarchar(MAX)")]
        public string EmailBody { get; set; }

        [Required]
        public bool IsActive { get; set; } = true;
    }
}
