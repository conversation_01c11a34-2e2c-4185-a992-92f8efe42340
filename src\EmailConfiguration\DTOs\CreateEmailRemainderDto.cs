using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace EmailConfiguration.DTOs
{
    /// <summary>
    /// DTO for creating email reminders
    /// </summary>
    public class CreateEmailRemainderDto
    {
        /// <summary>
        /// The feature ID that this email reminder is associated with
        /// </summary>
        public int FeatureID { get; set; }

        /// <summary>
        /// List of Portfolio Company IDs
        /// </summary>
        [Required]
        [MinLength(1, ErrorMessage = "At least one Portfolio Company ID is required")]
        public List<int> PortfolioCompanyIds { get; set; }

        /// <summary>
        /// List of Document Type IDs
        /// </summary>
        [Required]
        [MinLength(1, ErrorMessage = "At least one Document Type ID is required")]
        public List<int> DocumentTypeIds { get; set; }
    }
}

