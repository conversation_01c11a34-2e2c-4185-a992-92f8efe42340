using API.Helpers;
using Contract.KPI;
using Contract.Utility;
using Moq;
using Xunit;
using Master;
using ESG;
using System.Collections.Generic;

namespace API.Controllers.UnitTests
{
    public class KpiControllerUnitTest
    {
        readonly KpiController KpiController;
        readonly Mock<IKpiService> MockkPIService = new();
        readonly Mock<IEsgKpiService> _esgKpiService=new();
        readonly Mock<IInjectedParameters> MockInjectedParameters = new();
        readonly Mock<IHelperService> MockhelperService = new();
        public readonly Mock<IPageDetailsConfigurationService> IPageDetailsConfigurationService = new();
      
        public KpiControllerUnitTest()
        {
            KpiController = new KpiController( MockkPIService.Object, MockInjectedParameters.Object, MockhelperService.Object,IPageDetailsConfigurationService.Object, _esgKpiService.Object);
        }
        
        [Fact]
        private void ShouldAbleToDeleteEsgKpi()
        {
            DeleteKpiModel esgKpiModel = new DeleteKpiModel()
            {
                KpiType = "Governance",
                ModuleId = 22,
                KPIId = 45
            };
            //Act
            var result = KpiController.DeleteKPI(esgKpiModel);

            //Assert
            Assert.NotNull(result);
        }

        [Fact]
        private void ShouldAbleToDeleteKpi()
        {
            DeleteKpiModel esgKpiModel = new DeleteKpiModel()
            {
                KpiType = "Governance",
                ModuleId = 9,
                KPIId = 45
            };
            //Act
            var result = KpiController.DeleteKPI(esgKpiModel);

            //Assert
            Assert.NotNull(result);
        }

        [Fact]
        public void ShouldAbleToDeleteCustomKpi()
        {
            DeleteKpiModel model = new DeleteKpiModel()
            {
                KpiType = "Kpi1",
                ModuleId = 17,
                KPIId = 45,
                SubPageId = 2
            };
            //Act
            var result = KpiController.DeleteKPI(model);

            //Assert
            Assert.NotNull(result);
        }

        [Fact]
        public void ShouldAbleGetKpiMapping()
        {
            MockInjectedParameters.Setup(x => x.Encryption.Decrypt(It.IsAny<string>())).Returns("1");
            //Act
            var result = KpiController.GetKPIMapping("66F63D87E53A6F638CBDBE6B3B83AEA3", "Custom Table1",17,2);

            //Assert
            Assert.NotNull(result);
        }

        [Fact]
        public void ShouldAblemGetUnMappedKpi()
        {
            MockInjectedParameters.Setup(x => x.Encryption.Decrypt(It.IsAny<string>())).Returns("1");
            //Act
            var result = KpiController.GetUnMappedKpi("66F63D87E53A6F638CBDBE6B3B83AEA3", "Custom Table1", 17, 2);

            //Assert
            Assert.NotNull(result);
        }

        [Fact]
        public void ShouldAbleToGetCompanywiseKPIListForMapping()
        {
            var actual = KpiController.GetCompanywiseKPIListForMapping();
            Assert.NotNull(actual);
        }
        [Fact]
        public void ShouldAbleToGetInvestmentKPI()
        {
            var filter = new PaginationFilter();
            var actual = KpiController.GetInvestmentKPI(filter);
            Assert.NotNull(actual);
        }

        [Fact]
        public void ShouldAbleToGetImpactKPI()
        {
            var filter = new PaginationFilter();
            var actual = KpiController.GetImpactKPI(filter);
            Assert.NotNull(actual);
        }

        [Fact]
        public void ShouldAbleToGetFinancialKPI()
        {
            var filter = new PaginationFilter();
            var actual = KpiController.GetFinancialKPI(filter);
            Assert.NotNull(actual);
        }

        [Fact]
        public void ShouldAbleToGetOperationalKPIs()
        {
            var filter = new PaginationFilter();
            var actual = KpiController.GetOperationalKPIs();
            Assert.NotNull(actual);
        }

        [Fact]
        public void ShouldAbleToAddOrUpdateKPI()
        {
            var kpiModel = new KpiModel();
            var actual = KpiController.AddOrUpdateKPI(kpiModel);
            Assert.NotNull(actual);
        }

        [Fact]
        public void ShouldAbleToGetBalanceSheetKPI()
        {
            var actual = KpiController.GetBalanceSheetKPI();
            Assert.NotNull(actual);
        }

        [Fact]
        public void ShouldAbleToGetProfitLossKPI()
        {
            var actual = KpiController.GetProfitLossKPI();
            Assert.NotNull(actual);
        }

        [Fact]
        public void ShouldAbleToGetCashflowKPI()
        {
            var actual = KpiController.GetCashflowKPI();
            Assert.NotNull(actual);
        }

        [Fact]
        public void GetKPIs()
        {
            var actual = KpiController.GetKPIs("Impact KPI", "6A231FC5CBE67F379A487CC8FF7D51F5",0);
            Assert.NotNull(actual);
        }

        public void GetKPIsImpactKPI()
        {
            var actual = KpiController.GetKPIs("Impact KPI", "0",0);
            Assert.NotNull(actual);
        }

        [Fact]
        public void GetKPIsTradingRecords()
        {
            var actual = KpiController.GetKPIs("Trading Records ", "6A231FC5CBE67F379A487CC8FF7D51F5",0);
            Assert.NotNull(actual);
        }

        [Fact]
        public void GetKPIsCreditKPI()
        {
            var actual = KpiController.GetKPIs("Credit KPI", "6A231FC5CBE67F379A487CC8FF7D51F5",0);
            Assert.NotNull(actual);
        }

        [Fact]
        public void GetKPIsOperationalKPI()
        {
            var actual = KpiController.GetKPIs("Operational KPI", "6A231FC5CBE67F379A487CC8FF7D51F5",0);
            Assert.NotNull(actual);
        }

        [Fact]
        public void GetKPIsInvestmentKPI()
        {
            var actual = KpiController.GetKPIs("Investment KPI", "6A231FC5CBE67F379A487CC8FF7D51F5",0);
            Assert.NotNull(actual);
        }

        [Fact]
        public void GetKPIsCompanyKPI()
        {
            var actual = KpiController.GetKPIs("Company KPI", "6A231FC5CBE67F379A487CC8FF7D51F5",0);
            Assert.NotNull(actual);
        }

        [Fact]
        public void GetKPIsProfitLossKPI()
        {
            var actual = KpiController.GetKPIs("Profit & Loss KPI", "6A231FC5CBE67F379A487CC8FF7D51F5",0);
            Assert.NotNull(actual);
        }

        [Fact]
        public void GetKPIsBalanceSheetKPI()
        {
            var actual = KpiController.GetKPIs("Balance Sheet KPI", "6A231FC5CBE67F379A487CC8FF7D51F5",0);
            Assert.NotNull(actual);
        }

        [Fact]
        public void GetKPIsCashflowKPI()
        {
            var actual = KpiController.GetKPIs("Cashflow KPI", "6A231FC5CBE67F379A487CC8FF7D51F5",0);
            Assert.NotNull(actual);
        }
        [Fact]
        public async void GetAllMappedKPIs_ShouldReturnOkWithResult()
        {
            // Arrange
            var companyIds = "1,2";
            var mappedKPIs = new List<AllKpiMappingModel>
            {
                new AllKpiMappingModel { PortfolioCompanyId = 1, KpiId = 10, KpiName = "KPI 1" },
                new AllKpiMappingModel { PortfolioCompanyId = 2, KpiId = 20, KpiName = "KPI 2" }
            };
            MockkPIService.Setup(s => s.GetAllMappedKPIs(companyIds)).ReturnsAsync(mappedKPIs);

            // Act
            var result = await KpiController.GetAllMappedKPIs(companyIds);

            // Assert
            Assert.NotNull(result);
        }

        [Fact]
        public async void GetAllMappedKPIs_WithEmptyCompanyIds_ShouldReturnBadRequest()
        {
            // Arrange
            var companyIds = string.Empty;

            // Act
            var result = await KpiController.GetAllMappedKPIs(companyIds);

            // Assert
            Assert.NotNull(result);
        }

        [Fact]
        public async void GetAllMappedKPIs_WithNullCompanyIds_ShouldReturnBadRequest()
        {
            // Arrange
            string companyIds = null;

            // Act
            var result = await KpiController.GetAllMappedKPIs(companyIds);

            // Assert
            Assert.NotNull(result);
        }
    }
}