﻿using DataAccessLayer.DBModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DataAccessLayer.Models.Fund
{
    public partial class MFundSectionKpi : BaseCommonModel
    {
        [Key]
        public int FundSectionKpiId { get; set; }
        public int? ModuleId { get; set; }
        [Required]
        public string Kpi { get; set; }
        public string KpiInfo { get; set; }
        public string Description { get; set; }
        public int? MethodologyId { get; set; }
        public bool IsBoldKpi { get; set; }
        public bool IsHeader { get; set; }
        public string Formula { get; set; }
        public string FormulaKpiId { get; set; }
        [NotMapped]
        public string MethodologyName { get; set; }
        [NotMapped]
        public string KpiInfoType { get; set; }
        public string Synonym { get; set; }
    }
}
