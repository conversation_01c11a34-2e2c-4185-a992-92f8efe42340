using System.Diagnostics.CodeAnalysis;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Contract.PortfolioCompany
{
    [ExcludeFromCodeCoverage]
    public class CompanywiseKpiDetails : BaseModel
    {
        public int CompanywiseKPIID { get; set; }
        public int PortfolioCompanyID { get; set; }
        [MaxLength(200)]
        public string KPI { get; set; }
        [MaxLength(20)]
        public string KpiInfo { get; set; }
        public int? ParentKPIID { get; set; }
        public string ParentKPIName { get; set; }
        public string MethodologyName { get; set; }
        [MaxLength(500)]
        public string Description { get; set; }
        public bool IsDeleted { get; set; }
        public bool IsMapped { get; set; }
        public PortfolioCompanyModel PortfolioCompany { get; set; }
        public ParentKpiModel ParentKPI { get; set; }

        [MaxLength(500)]
        public string EncryptedCompanywiseKPIID { get; set; }
        public CompanywiseKpiListModel CompanywiseKPIListModelChild;
        [NotMapped]
        public string KpiInfoType { get; set; }
        public string Formula { get; set; }
        public string FormulaKPIId { get; set; }
        public bool? IsBoldKPI { get; set; }
        public bool? IsHeader { get; set; }
    }
    public class CompanyKpiModel
    {
        public int CompanywiseKPIID { get; set; }
        public int PortfolioCompanyID { get; set; }
        public string KPI { get; set; }
        public string KpiInfo { get; set; }
        public int? ParentKPIID { get; set; }
        public string ParentKPIName { get; set; }
        public string MethodologyName { get; set; }
        public string Description { get; set; }
        public bool IsDeleted { get; set; }
        public bool IsMapped { get; set; }
        public string EncryptedCompanywiseKPIID { get; set; }
        public string KpiInfoType { get; set; }
        public string Formula { get; set; }
        public string FormulaKPIId { get; set; }
        public bool? IsBoldKPI { get; set; }
        public bool? IsHeader { get; set; }
        public int MethodologyID { get; set; }
        public string Synonym { get; set;}
    }
}
