﻿using DapperRepository;
using DapperRepository.Constants;
using DataAccessLayer.Models.DataExtraction;
using DataAccessLayer.Models.DocumentCollection;
using DataAccessLayer.UnitOfWork;
using DocumentCollection.DTOs;
using DocumentCollection.Interfaces;
using DocumentCollection.Models;

namespace DocumentCollection.Services
{
    public class RepositoryConfigService(IUnitOfWork unitOfWork, IDapperGenericRepository dapperGenericRepository) : IRepositoryConfigurationService
    {        
        /// <summary>
        /// Retrieves repository configurations for a list of companies.
        /// </summary>
        /// <param name="companyIds">List of company IDs to retrieve configurations for.</param>
        /// <returns>A response containing a list of document configuration DTOs.</returns>        
        public async Task<ResponseDto<List<DocumentConfigurationDto>>> GetRepositoryConfigurationByCompanies(List<int> entityIds, int featureId)
        {
            var response = new ResponseDto<List<DocumentConfigurationDto>> { IsSuccess = true };
            try
            {
                IEnumerable<DocCollectionFrequencyConfig> allconfigs = await unitOfWork.DocCollectionConfigRepository.FindAllAsync(c => !c.IsDeleted && c.FeatureId == featureId && entityIds.Contains(c.EntityId));

                if (!allconfigs.Any())
                {
                    response.Data = new List<DocumentConfigurationDto>();
                    return response;
                }
                var hasCompanyWithNoConfig = HasCompanyWithoutConfiguration(entityIds, allconfigs);
                if (hasCompanyWithNoConfig)
                {
                    response.IsSuccess = false;
                    response.Message = "Company having different configuration";
                    response.Data = null;
                    return response;
                }
                List<DocumentConfigurationDto> documentConfigurationDtos = GenerateDocumentConfigDtoList(allconfigs);
                response.Data = documentConfigurationDtos;
                if (entityIds.Count == 1)
                {
                    response.IsSuccess = true;
                    response.Data = documentConfigurationDtos;
                    return response;
                }
                if (response.Data.Count > 0)
                {
                    if (!ValidateConfigurations(allconfigs, response))
                    {
                        response.Data = null;
                        return response;
                    }
                }
            }
            catch (Exception ex)
            {
                response.IsSuccess = false;
                response.Message = $"Failed to get document configurations: {ex.Message}";
            }
            return response;
        }

        private static List<DocumentConfigurationDto> GenerateDocumentConfigDtoList(IEnumerable<DocCollectionFrequencyConfig> allconfigs)
        {
            return allconfigs
                .GroupBy(d => d.DocTypeID)
                .Select(g => new DocumentConfigurationDto
                {
                    DoctypeID = g.Key,
                    AnnualConfig = ToRangeConfigDto(g.FirstOrDefault(d => d.FrequencyId == (int)FolderFrequencyType.Anual)),
                    QuarterConfig = ToRangeConfigDto(g.FirstOrDefault(d => d.FrequencyId == (int)FolderFrequencyType.Quarterly)),
                    MonthConfig = ToRangeConfigDto(g.FirstOrDefault(d => d.FrequencyId == (int)FolderFrequencyType.Monthly))
                }).ToList();
        }

        /// <summary>
        /// Checks if there are any company IDs without configurations in the provided list.
        /// </summary>
        /// <param name="companyIds">List of company IDs to check.</param>
        /// <param name="allconfigs">List of existing configurations.</param>
        /// <returns>True if any company ID does not have a configuration, otherwise false.</returns>
        public bool HasCompanyWithoutConfiguration(List<int> entityIds, IEnumerable<DocCollectionFrequencyConfig> allconfigs)
        {
            var configuredCompanyIds = allconfigs.Select(c => c.EntityId).Distinct();
            return entityIds.Any(entityId => !configuredCompanyIds.Contains(entityId));
        }

        private bool ValidateConfigurations(IEnumerable<DocCollectionFrequencyConfig> allconfigs, ResponseDto<List<DocumentConfigurationDto>> response)
        {
            if (!ValidateFrequencyConfigs(allconfigs, FolderFrequencyType.Anual, response))
            {
                return false;
            }

            if (!ValidateFrequencyConfigs(allconfigs, FolderFrequencyType.Quarterly, response))
            {
                return false;
            }

            if (!ValidateFrequencyConfigs(allconfigs, FolderFrequencyType.Monthly, response))
            {
                return false;
            }

            return true;
        }

        private bool ValidateFrequencyConfigs(IEnumerable<DocCollectionFrequencyConfig> allconfigs, FolderFrequencyType frequencyType, ResponseDto<List<DocumentConfigurationDto>> response)
        {
            var groupedConfigs = allconfigs
            .Where(d => d.FrequencyId == (int)frequencyType)
            .GroupBy(c => c.EntityId)
            .SelectMany(entityGroup => entityGroup.Select(c => new { c.From, c.To }))
            .Distinct()
            .ToList();

            if (groupedConfigs.Count > 1)
            {
                response.IsSuccess = false;
                response.Message = "Company having different configuration";
                return false;
            }

            return true;
        }
        /// <summary>
        /// Retrieves repository configurations for a single company.
        /// </summary>
        /// <param name="companyid">The ID of the company to retrieve configurations for.</param>
        /// <returns>A response containing a list of document configuration DTOs.</returns>
        public async Task<ResponseDto<List<DocumentConfigurationDto>>> GetRepositoryConfigurationByCompany(int entityId, int featureId)
        {
            var response = new ResponseDto<List<DocumentConfigurationDto>> { IsSuccess = true };
            try
            {
                var allconfigs = await unitOfWork.DocCollectionConfigRepository.FindAllAsync(c => !c.IsDeleted && c.EntityId == entityId && c.FeatureId == featureId);

                if (!allconfigs.Any())
                {
                    response.Data = new List<DocumentConfigurationDto>();
                    return response;
                }

                List<DocumentConfigurationDto> documentConfigurationDtos =
                allconfigs.GroupBy(d => d.DocTypeID).Select(g => new DocumentConfigurationDto
                {
                    DoctypeID = g.Key,
                    AnnualConfig = ToRangeConfigDto(g.FirstOrDefault(d => d.FrequencyId == (int)FolderFrequencyType.Anual)),
                    QuarterConfig = ToRangeConfigDto(g.FirstOrDefault(d => d.FrequencyId == (int)FolderFrequencyType.Quarterly)),
                    MonthConfig = ToRangeConfigDto(g.FirstOrDefault(d => d.FrequencyId == (int)FolderFrequencyType.Monthly))
                }).ToList();
                response.Data = documentConfigurationDtos;
            }
            catch (Exception ex)
            {
                response.IsSuccess = false;
                response.Message = $"Failed to get document configurations: {ex.Message}";
            }

            return response;
        }

        /// <summary>
        /// Updates repository configurations for a company.
        /// </summary>
        /// <param name="configurations">The configuration model containing updates.</param>
        /// <param name="userId">The ID of the user performing the update.</param>
        /// <returns>A response indicating the success or failure of the operation.</returns>
        public async Task<ResponseDto<List<DocumentConfigurationDto>>> UpdateRepositoryConfigurations(DocumentConfigurationModel configurations, int userId)
        {
            try
            {

                var response = new ResponseDto<List<DocumentConfigurationDto>> { IsSuccess = true , Message = "Selected Company configured successfully" };

                // Get existing configurations for the company
                var existingConfigs = await unitOfWork.DocCollectionConfigRepository.FindAllAsync(c => c.IsDeleted == false &&
                    configurations.Ids.Contains(c.EntityId));


                // Convert model to entity objects
                var configEntities = ConvertToDocFrequencyConfigs(configurations);

                var configsToUpdate = new List<DocCollectionFrequencyConfig>();
                var configsToInsert = new List<DocCollectionFrequencyConfig>();

                foreach (var config in configEntities)
                {
                    // Find if this configuration already exists
                    var existing = existingConfigs.FirstOrDefault(c =>
                        c.EntityId == config.EntityId &&
                        c.DocTypeID == config.DocTypeID &&
                        c.FrequencyId == config.FrequencyId);

                    if (existing != null)
                    {
                        // Update existing
                        ModifyExistingConfig(userId, configsToUpdate, config, existing);
                    }
                    else
                    {
                        // Set creation metadata
                        AddNewConfig(userId, configsToInsert, config);
                    }
                }
                DeleteOldConfig(existingConfigs, configEntities);
                // Perform database operations
                if (configsToUpdate.Any())
                {
                    unitOfWork.DocCollectionConfigRepository.UpdateBulk(configsToUpdate);
                }

                if (configsToInsert.Any())
                {
                    await unitOfWork.DocCollectionConfigRepository.AddBulkAsyn(configsToInsert);
                }

                await unitOfWork.SaveAsync();

                return response;
            }
            catch (Exception ex)
            {
                return new ResponseDto<List<DocumentConfigurationDto>>
                {
                    IsSuccess = false,
                    Message = $"Failed to update configurations: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// Deletes old configurations that are no longer valid.
        /// </summary>
        /// <param name="existingConfigs">The list of existing configurations.</param>
        /// <param name="configEntities">The list of new configuration entities.</param>
        private void DeleteOldConfig(List<DocCollectionFrequencyConfig> existingConfigs, List<DocCollectionFrequencyConfig> configEntities)
        {
            var configsToDelete = existingConfigs
                .Where(existing => !configEntities.Any(config =>
                    config.EntityId == existing.EntityId &&
                    config.DocTypeID == existing.DocTypeID &&
                    config.FrequencyId == existing.FrequencyId))
                .ToList();
            foreach (var item in configsToDelete)
            {
                item.IsDeleted = true;
            }

            if (configsToDelete.Any())
            {
                unitOfWork.DocCollectionConfigRepository.UpdateBulk(configsToDelete);
            }
        }

        /// <summary>
        /// Adds a new configuration to the list of configurations to insert.
        /// </summary>
        /// <param name="userId">The ID of the user creating the configuration.</param>
        /// <param name="configsToInsert">The list of configurations to insert.</param>
        /// <param name="config">The configuration to add.</param>
        private static void AddNewConfig(int userId, List<DocCollectionFrequencyConfig> configsToInsert, DocCollectionFrequencyConfig config)
        {
            config.CreatedOn = DateTime.UtcNow;
            config.CreatedBy = userId;
            config.IsActive = true;
            config.IsDeleted = false;
            configsToInsert.Add(config);
        }

        /// <summary>
        /// Modifies an existing configuration with new data.
        /// </summary>
        /// <param name="userId">The ID of the user modifying the configuration.</param>
        /// <param name="configsToUpdate">The list of configurations to update.</param>
        /// <param name="config">The new configuration data.</param>
        /// <param name="existing">The existing configuration to modify.</param>
        private static void ModifyExistingConfig(int userId, List<DocCollectionFrequencyConfig> configsToUpdate, DocCollectionFrequencyConfig config, DocCollectionFrequencyConfig? existing)
        {
            existing.From = config.From;
            existing.To = config.To;
            existing.ModifiedOn = DateTime.UtcNow;
            existing.ModifiedBy = userId;
            configsToUpdate.Add(existing);
        }

        /// <summary>
        /// Converts a configuration entity to a range configuration DTO.
        /// </summary>
        /// <param name="config">The configuration entity to convert.</param>
        /// <returns>A range configuration DTO.</returns>
        private static RangeConfigDto? ToRangeConfigDto(DocCollectionFrequencyConfig? config)
        {
            if (config == null)
            {
                return null;
            }
            return new RangeConfigDto
            {
                From = config.From,
                To = config.To,
            };
        }

        /// <summary>
        /// Converts a configuration model to a list of configuration entities.
        /// </summary>
        /// <param name="model">The configuration model to convert.</param>
        /// <returns>A list of configuration entities.</returns>
        private List<DocCollectionFrequencyConfig> ConvertToDocFrequencyConfigs(DocumentConfigurationModel model)
        {
            var result = new List<DocCollectionFrequencyConfig>();
            foreach (var companyid in model.Ids)
            {
                foreach (var docConfig in model.Configuration)
                {
                    // Add Annual configuration if exists
                    AddAnnualFrequencyConfig(result, companyid, docConfig, model.FeatureId);
                    // Add Quarterly configuration if exists
                    AddQuarterlyFrequencyConfig(result, companyid, docConfig, model.FeatureId);
                    // Add Monthly configuration if exists
                    AddMonthlyFrequencyConfig(result, companyid, docConfig, model.FeatureId);
                    // Add None Type frequency configuration if exists
                    AddNoneFrequencyConfig(result, companyid, docConfig, model.FeatureId);
                }
            }

            return result;
        }

        /// <summary>
        /// Adds a configuration with no frequency type to the list of configurations.
        /// </summary>
        /// <param name="result">The list of configurations.</param>
        /// <param name="companyid">The ID of the company.</param>
        /// <param name="docConfig">The document configuration model.</param>
        private static void AddNoneFrequencyConfig(List<DocCollectionFrequencyConfig> result, int companyid, FolderFrequencyConfigModel docConfig, int featureId)
        {
            if (docConfig.AnnualConfig == null && docConfig.QuarterConfig == null && docConfig.MonthConfig == null)
            {
                result.Add(new DocCollectionFrequencyConfig
                {
                    FeatureId = featureId, 
                    EntityId = companyid,
                    DocTypeID = docConfig.DoctypeID,
                    FrequencyId = (int)FolderFrequencyType.None,

                });

            }
        }

        /// <summary>
        /// Adds a monthly frequency configuration to the list of configurations.
        /// </summary>
        /// <param name="result">The list of configurations.</param>
        /// <param name="companyid">The ID of the company.</param>
        /// <param name="docConfig">The document configuration model.</param>
        private static void AddMonthlyFrequencyConfig(List<DocCollectionFrequencyConfig> result, int companyid, FolderFrequencyConfigModel docConfig, int featureId)
        {
            if (docConfig.MonthConfig != null)
            {
                result.Add(new DocCollectionFrequencyConfig
                {
                    FeatureId = featureId, 
                    EntityId = companyid,
                    DocTypeID = docConfig.DoctypeID,
                    FrequencyId = (int)FolderFrequencyType.Monthly,
                    From = docConfig.MonthConfig.From,
                    To = docConfig.MonthConfig.To
                });
            }
        }

        /// <summary>
        /// Adds a quarterly frequency configuration to the list of configurations.
        /// </summary>
        /// <param name="result">The list of configurations.</param>
        /// <param name="companyid">The ID of the company.</param>
        /// <param name="docConfig">The document configuration model.</param>
        private static void AddQuarterlyFrequencyConfig(List<DocCollectionFrequencyConfig> result, int companyid, FolderFrequencyConfigModel docConfig, int featureId)
        {
            if (docConfig.QuarterConfig != null)
            {
                result.Add(new DocCollectionFrequencyConfig
                {
                    FeatureId = featureId, 
                    EntityId = companyid,
                    DocTypeID = docConfig.DoctypeID,
                    FrequencyId = (int)FolderFrequencyType.Quarterly,
                    From = docConfig.QuarterConfig.From,
                    To = docConfig.QuarterConfig.To
                });
            }
        }

        /// <summary>
        /// Adds an annual frequency configuration to the list of configurations.
        /// </summary>
        /// <param name="result">The list of configurations.</param>
        /// <param name="companyid">The ID of the company.</param>
        /// <param name="docConfig">The document configuration model.</param>
        private static void AddAnnualFrequencyConfig(List<DocCollectionFrequencyConfig> result, int companyid, FolderFrequencyConfigModel docConfig, int featureId)
        {
            if (docConfig.AnnualConfig != null)
            {
                result.Add(new DocCollectionFrequencyConfig
                {
                    FeatureId = featureId, 
                    EntityId = companyid,
                    DocTypeID = docConfig.DoctypeID,
                    FrequencyId = (int)FolderFrequencyType.Anual,
                    From = docConfig.AnnualConfig.From,
                    To = docConfig.AnnualConfig.To
                });
            }
        }

        /// <summary>
        /// Retrieves repository configuration data for a single company.
        /// </summary>
        /// <param name="CompanyId">The ID of the company to retrieve data for.</param>
        /// <returns>A list of document configuration DTOs.</returns>
        public async Task<List<DocumentConfigurationDto>> GetRepositoryConfigurationDataByCompany(int CompanyId)
        {
            var allconfigs = await unitOfWork.DocCollectionConfigRepository.FindAllAsync(c => !c.IsDeleted && c.EntityId == CompanyId);
            var allDocTypes = await unitOfWork.DataExtractionTypesRepository.FindAllAsync(x => !x.IsDeleted);
            if (!allconfigs.Any() && !allDocTypes.Any())
            {
                return [];
            }
            List<DocumentConfigurationDto> documentConfigurationDtos =
                allconfigs.GroupBy(d => d.DocTypeID).Select(g => new DocumentConfigurationDto
                {
                    DoctypeID = g.Key,
                    DoctypeName = allDocTypes.FirstOrDefault(x => x.Id == g.Key)?.DocumentName ?? string.Empty,
                    AnnualConfig = ToRangeConfigDto(g.FirstOrDefault(d => d.FrequencyId == (int)FolderFrequencyType.Anual)),
                    QuarterConfig = ToRangeConfigDto(g.FirstOrDefault(d => d.FrequencyId == (int)FolderFrequencyType.Quarterly)),
                    MonthConfig = ToRangeConfigDto(g.FirstOrDefault(d => d.FrequencyId == (int)FolderFrequencyType.Monthly)!)
                }).ToList();
            return documentConfigurationDtos;
        }

        /// <summary>
        /// Retrieves a list of portfolio companies for a user.
        /// </summary>
        /// <param name="userId">The ID of the user to retrieve companies for.</param>
        /// <returns>A list of portfolio company models.</returns>
        public async Task<List<PortfolioCompanyModel>> GetPortfolioCompaniesList(int userId)
        {
            var companies = await dapperGenericRepository.Query<PortfolioCompanyModel>(SqlConstants.QueryPCMappingByUserID, new { userId = userId });

            var distinctCompanies = companies.GroupBy(c => c.PortfolioCompanyID).Select(g => g.First()).ToList();

            return distinctCompanies;

        }
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<List<DocumentTypes>> GetDocumentTypes(int featureId)
        {
            List<DataExtractionTypes> documentTypes = await unitOfWork.DataExtractionTypesRepository.FindAllAsync(x => !x.IsDeleted && x.FeatureId == featureId);
            return documentTypes.Select(d => new DocumentTypes
            {
                Id = d.Id,
                DocumentTypeName = d.DocumentName,
            }).ToList();
        }

        /// <summary>
        /// Retrieves a list of funds associated with a user.
        /// </summary>
        /// <param name="userId">The ID of the user to retrieve funds for.</param>
        /// <returns>A list of distinct fund data models.</returns>
        public async Task<List<FundDataModel>> GetFundList(int userId)
        {
            var funds = await dapperGenericRepository.Query<FundDataModel>(SqlConstants.QueryByGetAllFundsByUserID, new { userId });

            var distinctFunds = funds.GroupBy(c => c.FundID).Select(g => g.First()).ToList();

            return distinctFunds;
        }
    }
}
