﻿using System;
using System.Collections.Generic;

public class EmailGroupCreateDto
{
    public string GroupName { get; set; }
    public List<EmailMemberDto> EmailList { get; set; }
    public List<int> CompanyIds { get; set; }
}

public class EmailGroupDto
{
    public int GroupId { get; set; }
    public string GroupName { get; set; }
    public string CreatedBy { get; set; }
    public DateTime CreatedOn { get; set; }
    public int EmailCount { get; set; }
    public string UploadedBy { get; set; }
    public List<string> CompanyNames { get; set; } = new List<string>();
}
public class EmailMemberDto
{
    public int MemberId { get; set; }
    public string Name { get; set; }
    public string Email { get; set; }
    public bool IsActive { get; set; }
}

public class DeleteEmailMembersDto
{
    public List<int> MemberIds { get; set; } = new List<int>();
}

public class EmailGroupDetailDto
{
    public int GroupId { get; set; }
    public string GroupName { get; set; }
    public string CreatedBy { get; set; }
    public DateTime CreatedOn { get; set; }
    public string ModifiedBy { get; set; }
    public DateTime? ModifiedOn { get; set; }
    public string UploadedBy { get; set; }
    public List<EmailMemberDto> EmailMembers { get; set; } = new List<EmailMemberDto>();
    public List<CompanyEmailGroupDto> CompanyAssociations { get; set; } = new List<CompanyEmailGroupDto>();
}

public class EmailGroupUpdateDto
{
    public string GroupName { get; set; }
    public List<EmailMemberDto> EmailMembers { get; set; } = new List<EmailMemberDto>();
    public List<int> CompanyIds { get; set; } = new List<int>();
}

public class CompanyEmailGroupDto
{
    public int CompanyEmailGroupId { get; set; }
    public int GroupId { get; set; }
    public string CompanyId { get; set; }
    public bool IsSelected { get; set; }
}