﻿using System.ComponentModel;

namespace Audit.Enums;
public enum KpiModuleType
{
    [Description("TradingRecords")]
    TradingRecords = 1,
    [Description("CreditKPI")]
    CreditKPI = 2,
    [Description("Operational")]
    Operational = 3,
    [Description("Investment")]
    Investment = 4,
    [Description("Company")]
    Company = 5,
    [Description("Impact")]
    Impact = 6,
    [Description("ProfitAndLoss")]
    ProfitAndLoss = 7,
    [Description("BalanceSheet")]
    BalanceSheet = 8,
    [Description("CashFlow")]
    CashFlow = 9,
    [Description("ESG")]
    ESG = 10,
    CapTable1 = 11,
    CapTable2 = 12,
    CapTable3 = 13,
    CapTable4 = 14,
    CapTable5 = 15,
    MonthlyReport = 16,
    CustomTable1 = 17,
    CustomTable2 = 18,
    CustomTable3 = 19,
    CustomTable4 = 20,
    OtherKPI1 = 21,
    OtherKPI2 = 22,
    OtherKPI3 = 23,
    OtherKPI4 = 24,
    OtherKPI5 = 25,
    OtherKPI6 = 26,
    OtherKPI7 = 27,
    OtherKPI8 = 28,
    OtherKPI9 = 29,
    OtherKPI10 = 30,
    CapTable6 = 31,
    CapTable7 = 32,
    CapTable8 = 33,
    CapTable9 = 34,
    CapTable10 = 35,
    OtherCapTable1 = 36,
    OtherCapTable2 = 37,
    OtherCapTable3 = 38,
    OtherCapTable4 = 39,
    OtherCapTable5 = 40,
    OtherCapTable6 = 41,
    OtherCapTable7 = 42,
    OtherCapTable8 = 43,
    OtherCapTable9 = 44,
    OtherCapTable10 = 45
}
