# Get the current branch name
$<PERSON><PERSON>CH_NAME = (git rev-parse --abbrev-ref HEAD).Trim()

Write-Host "Branch Name: $BRANCH_NAME"

dotnet sonarscanner begin /k:"PEC_FOLIOSURE_API" /v:"2.30.0" /d:sonar.host.url="https://sast.beatapps.net" /d:sonar.language="cs" /d:sonar.sources="/src" /d:sonar.tests="/tests" /d:sonar.exclusions="**/bin/**/*,**/obj/**/*,**/Migrations/**/*,**/DataAccessLayer/**/*,**/*Configuration.cs,tests/**/*,Reports/**/*,code-coverage/**/*,**/ReportDownloadMonthlyTemplate.UnitTest/**/*,src/API/wwwroot/**/*" /d:sonar.cs.opencover.reportsPaths="/code-coverage/coverage.opencover.xml" /d:sonar.branch.name="$<PERSON>ANCH_NAME" /d:sonar.token=$env:SONAR_LOGIN
dotnet build 
./coverage.ps1 
dotnet sonarscanner end /d:sonar.token=$env:SONAR_LOGIN