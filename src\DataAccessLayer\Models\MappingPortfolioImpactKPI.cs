using DataAccessLayer.DBModel;

namespace DataAccessLayer.Models {
    public partial class MappingPortfolioImpactKpi : BaseModel {

        public int MappingPortfolioImpactKpiId { get; set; }
        public int KpiTypeId { get; set; }
        public int PortfolioCompanyId { get; set; }
        public int KpiId { get; set; }
        public string Description { get; set; }
        public bool IsDeleted { get; set; }
        public string EncryptedMappingKpiId { get; set; }
        public bool IsExtraction { get; set; }
        public string Synonym { get; set; }
        public string Definition { get; set; }
    }
}