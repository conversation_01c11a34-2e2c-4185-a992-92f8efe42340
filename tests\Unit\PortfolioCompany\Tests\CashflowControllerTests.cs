using API.Helpers;
using AutoMapper;
using CashFlow;
using CashFlow.Models;
using Contract.CashFlow;
using Contract.Configuration;
using Contract.Deals;
using Contract.Funds;
using Contract.Utility;
using DapperRepository;
using DataAccessLayer.DBModel;
using DataAccessLayer.Models;
using DataAccessLayer.Models.Workflow;
using DataAccessLayer.UnitOfWork;
using Deals;
using System.Data;
using Master;
using Master.DtoProfiles;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Primitives;
using Moq;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using OfficeOpenXml;
using PortfolioCompany;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Text.Json;
using System.Threading.Tasks;
using Utility.Services;
using Xunit;
using Repository;
using Exports.Helpers;
using Shared;

namespace API.Controllers.UnitTests
{
    public class CashflowControllerUnitTest
    {
        private readonly CashflowController CashflowController;
        public Mock<IInjectedParameters> InjectedParameters;
        private readonly Mock<ILogger<CashflowController>> MockexceptionHandling = new();
        private readonly Mock<IHelperService> MockhelperService = new();
        private readonly Mock<IFundService> mockFundService = new();
        private readonly CashFlowService flowService;
        public Mock<IEncryption> Encryption;
        public Mock<IUnitOfWork> UnitOfWork;
        public Mock<Microsoft.Extensions.Logging.ILogger> Logger;
        public Mock<IMemoryCacher> memoryCacher;
        public Mock<IGlobalConfigurations> globalConfig;
        public Mock<IHelperService> helperService;
        public readonly PageDetailsConfigurationService pageConfig;
        public readonly Mock<IMemoryCacher> mockmemoryCacher;
        public readonly Mock<ILogger<IPageDetailsConfigurationService>> mocklogger;
        public readonly PortfolioCompanyService portfolioCompanyService;
        public readonly Mock<IDapperGenericRepository> dapperGenericRepository;
        public readonly DealService dealService;
        public readonly IMock<IFundService> fundservice;
        public readonly Mock<IS3FileUploadService> Is3FileUpload;
        public CashflowControllerUnitTest()
        {
            Is3FileUpload = new Mock<IS3FileUploadService>();
            InjectedParameters = new Mock<IInjectedParameters>();
            UnitOfWork = new Mock<IUnitOfWork>();
            Encryption = new Mock<IEncryption>();
            Logger = new Mock<Microsoft.Extensions.Logging.ILogger>();
            memoryCacher = new Mock<IMemoryCacher>();
            globalConfig = new Mock<IGlobalConfigurations>();
            helperService = new Mock<IHelperService>();
            mockmemoryCacher = new Mock<IMemoryCacher>();
            var config = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile(new MasterMappingProfile());
                cfg.AddProfile(new PageSettingsProfiles());
            });
            var mapper = config.CreateMapper();
            dapperGenericRepository = new Mock<IDapperGenericRepository>();
            fundservice = new Mock<IFundService>();
            mocklogger = new Mock<ILogger<IPageDetailsConfigurationService>>();
            pageConfig = new(UnitOfWork.Object, mapper, mocklogger.Object, globalConfig.Object, mockmemoryCacher.Object);
            globalConfig.Setup(S => S.GetValueByKey("HashKey")).Returns("D,zMM3X45}&QE7FZ)9Z#Lkj");
            InjectedParameters.Setup(S => S.Encryption).Returns(new Encryption(globalConfig.Object));
            InjectedParameters.Setup(S => S.UnitOfWork).Returns(UnitOfWork.Object);
            InjectedParameters.Setup(S => S.Logger).Returns(Logger.Object);
            Encryption.Setup(S => S.Encrypt(It.IsAny<string>())).Returns("66F63D87E53A6F638CBDBE6B3B83AEA3");
            flowService = new CashFlowService();
            flowService.InjectedParameters = InjectedParameters.Object;
            portfolioCompanyService = new PortfolioCompanyService(dapperGenericRepository.Object,dealService);
            portfolioCompanyService.InjectedParameters = InjectedParameters.Object;
            var mockDealServiceLogger = new Mock<ILogger<DealService>>();
            dealService = new DealService(pageConfig, dapperGenericRepository.Object, fundservice.Object, UnitOfWork.Object, Encryption.Object, globalConfig.Object, mockDealServiceLogger.Object);
            CashflowController = new CashflowController(flowService, portfolioCompanyService, dealService, InjectedParameters.Object, MockexceptionHandling.Object, MockhelperService.Object, mockFundService.Object, pageConfig, Is3FileUpload.Object);
            LoadTranasctionTypes();
        }

        [Fact]
        public void Should_Return_Cashflow_Files_List()
        {
            // Arrange
            var filter = new CashFlowFilter();
            filter.EncryptedCashflowFileID = "66F63D87E53A6F638CBDBE6B3B83AEA3";
            InjectedParameters.Setup(x => x.Encryption.Decrypt(It.IsAny<string>())).Returns("1");
            var cashFlowFileLogs = new List<CashFlowFileLogs>();
            cashFlowFileLogs.Add(new CashFlowFileLogs
            {
                CashFlowFileId = 1,
                FundId = 1,
                FileName = "CashFlowFile1.xlsx",
                UniqueFileId = "1234567890",
                CurrencyId = 1,
                CreatedOn = new DateTime(2023, 01, 01),
                CreatedBy = 1,
                IsDeleted = false,
                IsActive = true,
                EncryptedCashFlowFileId = null
            });
            cashFlowFileLogs.Add(new CashFlowFileLogs
            {
                CashFlowFileId = 2,
                FundId = 2,
                FileName = "CashFlowFile2.xlsx",
                UniqueFileId = "9876543210",
                CurrencyId = 2,
                CreatedOn = new DateTime(2023, 02, 01),
                CreatedBy = 2,
                IsDeleted = false,
                IsActive = true,
                EncryptedCashFlowFileId = null
            });
            InjectedParameters.Setup(x => x.UnitOfWork.CashflowRepository.GetManyQueryable(It.IsAny<Func<CashFlowFileLogs, bool>>())).Returns(cashFlowFileLogs.AsQueryable());
            var result = CashflowController.GetCashFlowFiles(filter);
            Assert.NotNull(result);
        }

        [Fact]
        public void Should_Return_Cashflow_Files_List_With_Encript()
        {
            // Arrange
            var filter = new CashFlowFilter();
            filter.EncryptedCashflowFileID = "66F63D87E53A6F638CBDBE6B3B83AEA3";
            InjectedParameters.Setup(x => x.Encryption.Decrypt(It.IsAny<string>())).Returns("1");
            var cashFlowFileLogs = new List<CashFlowFileLogs>();
            cashFlowFileLogs.Add(new CashFlowFileLogs
            {
                CashFlowFileId = 1,
                FundId = 1,
                FileName = "CashFlowFile1.xlsx",
                UniqueFileId = "1234567890",
                CurrencyId = 1,
                CreatedOn = new DateTime(2023, 01, 01),
                CreatedBy = 1,
                IsDeleted = false,
                IsActive = true,
                EncryptedCashFlowFileId = "66F63D87E53A6F638CBDBE6B3B83AEA3"
            });
            cashFlowFileLogs.Add(new CashFlowFileLogs
            {
                CashFlowFileId = 2,
                FundId = 2,
                FileName = "CashFlowFile2.xlsx",
                UniqueFileId = "9876543210",
                CurrencyId = 2,
                CreatedOn = new DateTime(2023, 02, 01),
                CreatedBy = 2,
                IsDeleted = false,
                IsActive = true,
                EncryptedCashFlowFileId = "66F63D87E53A6F638CBDBE6B3B83AEA3"
            });
            InjectedParameters.Setup(x => x.UnitOfWork.CashflowRepository.GetManyQueryable(It.IsAny<Func<CashFlowFileLogs, bool>>())).Returns(cashFlowFileLogs.AsQueryable());
            var result = CashflowController.GetCashFlowFiles(filter);
            Assert.NotNull(result);
        }

        [Fact]
        public void Should_Return_Cashflow_Files_List_With_Encript_AppliedFilter()
        {
            // Arrange
            var filter = new CashFlowFilter();
            filter.EncryptedCashflowFileID = "66F63D87E53A6F638CBDBE6B3B83AEA3";
            filter.PaginationFilter = new PaginationFilter
            {
                FilterWithoutPaging = false,
                First = 1,
                Rows = 2,
                MultiSortMeta = new List<SortMeta>
            {
                new SortMeta { Field = "CashFlowFileId"  },
                new SortMeta { Field = "FileName"}
            },
                GlobalFilter = "CashFlowFile1.xlsx"
            };
            InjectedParameters.Setup(x => x.Encryption.Decrypt(It.IsAny<string>())).Returns("1");
            var cashFlowFileLogs = new List<CashFlowFileLogs>();
            cashFlowFileLogs.Add(new CashFlowFileLogs
            {
                CashFlowFileId = 1,
                FundId = 1,
                FileName = "CashFlowFile1.xlsx",
                UniqueFileId = "1234567890",
                CurrencyId = 1,
                CreatedOn = new DateTime(2023, 01, 01),
                CreatedBy = 1,
                IsDeleted = false,
                IsActive = true,
                EncryptedCashFlowFileId = "66F63D87E53A6F638CBDBE6B3B83AEA3"
            });
            cashFlowFileLogs.Add(new CashFlowFileLogs
            {
                CashFlowFileId = 2,
                FundId = 2,
                FileName = "CashFlowFile2.xlsx",
                UniqueFileId = "9876543210",
                CurrencyId = 2,
                CreatedOn = new DateTime(2023, 02, 01),
                CreatedBy = 2,
                IsDeleted = false,
                IsActive = true,
                EncryptedCashFlowFileId = "66F63D87E53A6F638CBDBE6B3B83AEA3"
            });
            InjectedParameters.Setup(x => x.UnitOfWork.CashflowRepository.GetManyQueryable(It.IsAny<Func<CashFlowFileLogs, bool>>())).Returns(cashFlowFileLogs.AsQueryable());
            var result = CashflowController.GetCashFlowFiles(filter);
            Assert.NotNull(result);
        }

        [Fact]
        public void Should_Return_Currency_Code_When_Reporting_Currency_Id_Is_Found()
        {
            var reportingCurrencyId = 1;
            var currencyCode = "USD";
            InjectedParameters.Setup(x => x.UnitOfWork.M_CurrencyRepository.GetQueryable())
                .Returns(new List<M_Currency> { new M_Currency { CurrencyId = reportingCurrencyId, CurrencyCode = currencyCode } }.AsQueryable());
            var result = flowService.GetCurrency(reportingCurrencyId);
            // Assert
            Assert.Equal(currencyCode, result);
        }

        [Fact]
        public void Should_Return_Null_When_Reporting_Currency_Id_Is_Not_Found()
        {
            var reportingCurrencyId = 1;
            InjectedParameters.Setup(x => x.UnitOfWork.M_CurrencyRepository.GetQueryable())
                .Returns(new List<M_Currency>().AsQueryable());
            var result = flowService.GetCurrency(reportingCurrencyId);
            Assert.Null(result);
        }

        [Fact]
        public void Should_Return_Cashflow_Details_When_Cashflow_File_Id_Is_Found_WhenPageName_NotUpload()
        {
            CashFlowFileModelMock fileModelMock = new CashFlowFileModelMock();
            // Arrange
            var cashFlowFileId = 1;
            var reportingCurrencyId = 1;
            var currencyCode = "USD";
            InjectedParameters.Setup(x => x.UnitOfWork.M_CurrencyRepository.GetQueryable())
                .Returns(new List<M_Currency> { new M_Currency { CurrencyId = reportingCurrencyId, CurrencyCode = currencyCode } }.AsQueryable());
            var cashFlow = fileModelMock.GenerateMockData();
            var portfolioCompanyDetails = new List<PortfolioCompanyDetails>
{
    new PortfolioCompanyDetails
    {
        PortfolioCompanyId = 1,
        CompanyName = "Portfolio Company 1",
        Website = "www.portfoliocompany1.com",
        BussinessDescription = "This is the business description for Portfolio Company 1.",
        SectorId = 1,
        SubSectorId = 1,
        ReportingCurrencyId = 1,
        Status = "Active",
        StockExchangeTicker = "PC1",
        HeadquarterId = 1,
        IsDeleted = false,
        IsActive = true,
        EncryptedPortfolioCompanyId = "1234567890",
        FinancialYearEnd = "12/31",
        ImagePath = "/path/to/image.png",
        MasterCompanyName = "Master Company 1",
        GroupId = 1,
        CashflowCalculationDetails = new List<CashflowCalculationDetails>(),
        CashFlowCompanyWiseData = new List<CashFlowCompanyWiseData>(),
        DealDetails = new List<DealDetails>(),
        Sector = new M_Sector {  },
        SubSector = new M_SubSector {},
        ReportingCurrency = new M_Currency { CurrencyId = 1, CurrencyCode = "USD" },
        MappingPCEmployee = new List<Mapping_PCEmployee>(),
        MappingPCGeographicLocation = new List<Mapping_PCGeographicLocation>(),
        PortfolioCompanyProfitabilityDetails = new List<PortfolioCompanyProfitabilityDetails>(),
        PortfolioCompanyOperationalKPIQuarters = new List<PortfolioCompanyOperationalKPIQuarter>(),
        PCCompanyKpimonthlyValue = new List<PCCompanyKpiMonthlyValue>(),
        PCInvestmentKpiQuarterlyValue = new List<PCInvestmentKpiQuarterlyValue>(),
        PCImpactKpiQuarterlyValue = new List<PcImpactKpiQuarterlyValue>(),
        CommentaryDetails = new List<CommentaryDetails>(),
        Mapping_ImpactKPI_Order = new List<Mapping_ImpactKPI_Order>(),
        PortfolioCompanyOperationalKPIQuartersDraft = new List<PortfolioCompanyOperationalKPIQuartersDraft>(),
    },
    new PortfolioCompanyDetails
    {
        PortfolioCompanyId = 2,
        CompanyName = "Portfolio Company 2",
        Website = "www.portfoliocompany2.com",
        BussinessDescription = "This is the business description for Portfolio Company 2.",
        SectorId = 2,
        SubSectorId = 2,
        ReportingCurrencyId = 2,
        Status = "Active",
        StockExchangeTicker = "PC2",
        HeadquarterId = 2,
        IsDeleted = false,
        IsActive = true,
        EncryptedPortfolioCompanyId = "9876543210",
        FinancialYearEnd = "12/31",
        ImagePath = "/path/to/image2.png",
        MasterCompanyName = "Master Company 2",
        GroupId = 2,
        CashflowCalculationDetails = new List<CashflowCalculationDetails>(),
        CashFlowCompanyWiseData = new List<CashFlowCompanyWiseData>(),
        DealDetails = new List<DealDetails>(),
        Sector = new M_Sector { },
        SubSector = new M_SubSector {},
        ReportingCurrency = new M_Currency { CurrencyId = 2, CurrencyCode = "EUR" },
        MappingPCEmployee = new List<Mapping_PCEmployee>(),
        MappingPCGeographicLocation = new List<Mapping_PCGeographicLocation>(),
        PortfolioCompanyProfitabilityDetails = new List<PortfolioCompanyProfitabilityDetails>()
    } };
            InjectedParameters.Setup(x => x.UnitOfWork.PortfolioCompanyDetailRepository.GetQueryable()).Returns(portfolioCompanyDetails.AsQueryable());
            InjectedParameters.Setup(x => x.UnitOfWork.CashflowRepository.GetFirstOrDefault(It.IsAny<Func<CashFlowFileLogs, bool>>()))
                .Returns(cashFlow);
            InjectedParameters.Setup(x => x.Encryption.Decrypt(It.IsAny<string>())).Returns("1");
            var response = CashflowController.GetCashflow(new CashFlowGetByIdFilter { CashFlowId = "66F63D87E53A6F638CBDBE6B3B83AEA3" });
            Assert.NotNull(response);
        }

        [Fact]
        public void Should_Return_Cashflow_Details_When_Cashflow_File_Id_Is_Found_WhenPageName_Upload()
        {
            CashFlowFileModelMock fileModelMock = new CashFlowFileModelMock();
            // Arrange
            var cashFlowFileId = 1;
            var reportingCurrencyId = 1;
            var currencyCode = "USD";
            InjectedParameters.Setup(x => x.UnitOfWork.M_CurrencyRepository.GetQueryable())
                .Returns(new List<M_Currency> { new M_Currency { CurrencyId = reportingCurrencyId, CurrencyCode = currencyCode } }.AsQueryable());
            var cashFlow = fileModelMock.GenerateMockData();
            var portfolioCompanyDetails = new List<PortfolioCompanyDetails>
{
    new PortfolioCompanyDetails
    {
        PortfolioCompanyId = 1,
        CompanyName = "Portfolio Company 1",
        Website = "www.portfoliocompany1.com",
        BussinessDescription = "This is the business description for Portfolio Company 1.",
        SectorId = 1,
        SubSectorId = 1,
        ReportingCurrencyId = 1,
        Status = "Active",
        StockExchangeTicker = "PC1",
        HeadquarterId = 1,
        IsDeleted = false,
        IsActive = true,
        EncryptedPortfolioCompanyId = "1234567890",
        FinancialYearEnd = "12/31",
        ImagePath = "/path/to/image.png",
        MasterCompanyName = "Master Company 1",
        GroupId = 1,
        CashflowCalculationDetails = new List<CashflowCalculationDetails>(),
        CashFlowCompanyWiseData = new List<CashFlowCompanyWiseData>(),
        DealDetails = new List<DealDetails>(),
        Sector = new M_Sector {  },
        SubSector = new M_SubSector {},
        ReportingCurrency = new M_Currency { CurrencyId = 1, CurrencyCode = "USD" },
        MappingPCEmployee = new List<Mapping_PCEmployee>(),
        MappingPCGeographicLocation = new List<Mapping_PCGeographicLocation>(),
        PortfolioCompanyProfitabilityDetails = new List<PortfolioCompanyProfitabilityDetails>(),
        PortfolioCompanyOperationalKPIQuarters = new List<PortfolioCompanyOperationalKPIQuarter>(),
        PCCompanyKpimonthlyValue = new List<PCCompanyKpiMonthlyValue>(),
        PCInvestmentKpiQuarterlyValue = new List<PCInvestmentKpiQuarterlyValue>(),
        PCImpactKpiQuarterlyValue = new List<PcImpactKpiQuarterlyValue>(),
        CommentaryDetails = new List<CommentaryDetails>(),
        Mapping_ImpactKPI_Order = new List<Mapping_ImpactKPI_Order>(),
        PortfolioCompanyOperationalKPIQuartersDraft = new List<PortfolioCompanyOperationalKPIQuartersDraft>(),
    },
    new PortfolioCompanyDetails
    {
        PortfolioCompanyId = 2,
        CompanyName = "Portfolio Company 2",
        Website = "www.portfoliocompany2.com",
        BussinessDescription = "This is the business description for Portfolio Company 2.",
        SectorId = 2,
        SubSectorId = 2,
        ReportingCurrencyId = 2,
        Status = "Active",
        StockExchangeTicker = "PC2",
        HeadquarterId = 2,
        IsDeleted = false,
        IsActive = true,
        EncryptedPortfolioCompanyId = "9876543210",
        FinancialYearEnd = "12/31",
        ImagePath = "/path/to/image2.png",
        MasterCompanyName = "Master Company 2",
        GroupId = 2,
        CashflowCalculationDetails = new List<CashflowCalculationDetails>(),
        CashFlowCompanyWiseData = new List<CashFlowCompanyWiseData>(),
        DealDetails = new List<DealDetails>(),
        Sector = new M_Sector { },
        SubSector = new M_SubSector {},
        ReportingCurrency = new M_Currency { CurrencyId = 2, CurrencyCode = "EUR" },
        MappingPCEmployee = new List<Mapping_PCEmployee>(),
        MappingPCGeographicLocation = new List<Mapping_PCGeographicLocation>(),
        PortfolioCompanyProfitabilityDetails = new List<PortfolioCompanyProfitabilityDetails>()
    } };
            InjectedParameters.Setup(x => x.UnitOfWork.PortfolioCompanyDetailRepository.GetQueryable()).Returns(portfolioCompanyDetails.AsQueryable());
            InjectedParameters.Setup(x => x.UnitOfWork.CashflowRepository.GetFirstOrDefault(It.IsAny<Func<CashFlowFileLogs, bool>>()))
                .Returns(cashFlow);
            InjectedParameters.Setup(x => x.Encryption.Decrypt(It.IsAny<string>())).Returns("1");
            var response = CashflowController.GetCashflow(new CashFlowGetByIdFilter { CashFlowId = "66F63D87E53A6F638CBDBE6B3B83AEA3", PageName = "Upload" });
            Assert.NotNull(response);
        }

        [Fact]
        public void AddCashflowData_ShouldInsertCashflowData()
        {
            CashFlowFileModel fileModel = new CashFlowFileModel
            {
                FileName = "TestFile.csv",
                Fund = new FundModel { FundID = 1 },
                CashFlowCalculatedFundData = new CashFlowCalculatedFundData { },
                CashflowCalculationDetails = new List<CashflowCalculationModel> {  new CashflowCalculationModel { CapitalInvested = 1000, RealizedValue = 2000, UnrealizedValue = 3000, TotalValue = 6000, IRR = 100, Multiple = 2, PortfolioCompanyId = 1 },
                    new CashflowCalculationModel { CapitalInvested = 2000, RealizedValue = 3000, UnrealizedValue = 4000, TotalValue = 9000, IRR = 150, Multiple = 3, PortfolioCompanyId = 2 }},
                CashflowDateWiseSummary = new List<CashflowDateWiseSummaryModel> { new CashflowDateWiseSummaryModel
                    {
                        TransactionDate = DateTime.Now,
                        TotalRealizeValue = 1000,
                        TotalUnrealizeValue = 2000,
                        TotalFundFees = 3000,
                        TotalRealizeUnrealizeAndFundFeesValue = 6000,
                        TransactionType = "Investment",
                        CashFlowCompanyList = new List<CashFlowComapnyWiseModel>
                        {
                            new CashFlowComapnyWiseModel { IsRealizedValue = true, PortfolioCompanyId = 1, TransactionValue = 1000 },
                            new CashFlowComapnyWiseModel { IsRealizedValue = false, PortfolioCompanyId = 2, TransactionValue = 2000 }
                        }
                    } },
                TotalCalculationDetails = new List<CashflowTotalCalculationModel> { new CashflowTotalCalculationModel { TotalCapitalInvested = 10000, TotalRealizedValue = 20000, TotalUnrealizedValue = 30000, TotalofTotalValue = 60000, TotalIRR = 100, TotalMultiple = 2 } },
            };
            InjectedParameters.Setup(x => x.UnitOfWork.CashflowRepository.Insert(null));
            CashFlowFileModel result = flowService.AddCashflowData(fileModel);
            Assert.IsType<CashFlowFileModel>(result);
        }

        [Fact]
        public void ExportCashflowData_WithValidFilters_ReturnsFileResult()
        {
            dynamic filters = CreateMockFilters();
            // Mock the injected parameters if needed
            InjectedParameters.Setup(x => x.GlobalConfigurations.ExportFileUploadPath).Returns("/exported-files/");
            // Mock the ExcelExport.ExportToExcelCashflow method
            JsonDocument jsonDocument = JsonDocument.Parse(filters);
            // Get the root element of the JSON document.
            JsonElement rootElement = jsonDocument.RootElement;
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
            Assert.Throws<ArgumentException>(() => CashflowController.ExportCashflowData(rootElement));
        }

        [Fact]
        public void ShouldReturnFileResultWhenFilterSelectionIsFundCashflowFundCurrency()
        {
            dynamic filters = CreateMockFilters("FundCashflow Fund Currency");
            // Mock the injected parameters if needed
            InjectedParameters.Setup(x => x.GlobalConfigurations.ExportFileUploadPath).Returns("/exported-files/");
            // Mock the ExcelExport.ExportToExcelCashflow method
            JsonDocument jsonDocument = JsonDocument.Parse(filters);
            // Get the root element of the JSON document.
            JsonElement rootElement = jsonDocument.RootElement;
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
            var response = CashflowController.ExportCashflowCommonData(rootElement);
            Assert.IsType<string>(response.FileDownloadName);
        }

        [Fact]
        public void ShouldReturnFileResultWhenFilterSelectionIsFundCashflowReportingCurrency()
        {
            dynamic filters = CreateMockFilters("FundCashflow Reporting Currency");
            // Mock the injected parameters if needed
            InjectedParameters.Setup(x => x.GlobalConfigurations.ExportFileUploadPath).Returns("/exported-files/");
            // Mock the ExcelExport.ExportToExcelCashflow method
            JsonDocument jsonDocument = JsonDocument.Parse(filters);
            // Get the root element of the JSON document.
            JsonElement rootElement = jsonDocument.RootElement;
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
            var response = CashflowController.ExportCashflowCommonData(rootElement);
            Assert.IsType<string>(response.FileDownloadName);
        }

        [Fact]
        public void ShouldReturnFileResultWhenFilterSelectionIsFundPerformanceFundCurrency()
        {
            dynamic filters = CreateMockFilters("FundPerformance Fund Currency");
            // Mock the injected parameters if needed
            InjectedParameters.Setup(x => x.GlobalConfigurations.ExportFileUploadPath).Returns("/exported-files/");
            // Mock the ExcelExport.ExportToExcelCashflow method
            JsonDocument jsonDocument = JsonDocument.Parse(filters);
            // Get the root element of the JSON document.
            JsonElement rootElement = jsonDocument.RootElement;
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
            var response = CashflowController.ExportCashflowCommonData(rootElement);
            Assert.IsType<string>(response.FileDownloadName);
        }

        [Fact]
        public void ShouldReturnFileResultWhenFilterSelectionIsFundPerformanceReportingCurrency()
        {
            dynamic filters = CreateMockFilters("FundPerformance Reporting Currency");
            // Mock the injected parameters if needed
            InjectedParameters.Setup(x => x.GlobalConfigurations.ExportFileUploadPath).Returns("/exported-files/");
            // Mock the ExcelExport.ExportToExcelCashflow method
            JsonDocument jsonDocument = JsonDocument.Parse(filters);
            // Get the root element of the JSON document.
            JsonElement rootElement = jsonDocument.RootElement;
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
            var response = CashflowController.ExportCashflowCommonData(rootElement);
            Assert.IsType<string>(response.FileDownloadName);
        }
        [Fact]
        public void ExportCashFlowShouldReturnFileResultWhenFilterSelectionIsFundPerformanceFundCurrency()
        {
            var filters = new
            {
                Selection = "FundPerformance Fund Currency",
                FundName = "Test Fund",
                Currency = "USD",
                ExportCol = JArray.Parse("[{\"header\":\"CapitalInvested\",\"field\":\"capitalInvested\"},{\"header\":\"RealizedValue\",\"field\":\"realizedValue\"},{\"header\":\"UnrealizedValue\",\"field\":\"unrealizedValue\"},{\"header\":\"TotalValue\",\"field\":\"totalValue\"},{\"header\":\"GrossIRR\",\"field\":\"IRR\"},{\"header\":\"GrossTVPI\",\"field\":\"TVPI\"}]"),
                ExportVal = JArray.Parse("[{\"Date\":\"04/22/2021\",\"isRealizedValue\":true,\"portfolioCompanyId\":239,\"value\":-8554500,\"name\":\"CashFlow1\",\"CapitalInvested\":\"8,554,500\",\"RealizedValue\":\"4,875,947\",\"UnrealizedValue\":\"543,217\",\"TotalValue\":\"5,419,164\",\"IRR\":\"-68.2%\",\"TVPI\":\"0.6\",\"currency\":\"DZD\",\"FundCurrency\":\"ADF\",\"capitalInvested\":\"8,554,500\",\"realizedValue\":\"4,875,947\",\"unrealizedValue\":\"543,217\",\"totalValue\":\"5,419,164\"},{\"capitalInvested\":\"8,554,500\",\"realizedValue\":\"4,875,947\",\"unrealizedValue\":\"543,217\",\"IRR\":\"-68.2%\",\"TVPI\":\"0.6\",\"totalValue\":\"5,419,164\",\"name\":\"TotalRealized\"},{\"capitalInvested\":\"8,554,500\",\"realizedValue\":\"4,875,947\",\"unrealizedValue\":\"543,217\",\"IRR\":\"-68.2%\",\"TVPI\":\"0.6\",\"totalValue\":\"5,419,164\",\"isExpense\":true,\"isTotal\":true,\"name\":\"Total\"}]")
            };
            var response = ExportCashflow.FundPerformanceFundCurrency(FilterData(filters));
            Assert.IsType<DataTable>(response);
        }
        [Fact]
        public void ExportCashFlowShouldReturnFileResultWhenFilterSelectionIsFundCashflowFundCurrency()
        {
            var filters = new
            {
                Selection = "FundCashflow Fund Currency",
                FundName = "Test Fund",
                Currency = "USD",
                ExportCol = JArray.Parse("[{\"header\":\"CompanyName\",\"field\":\"name\"},{\"header\":\"TransactionDate\",\"field\":\"Date\"},{\"header\":\"TransactionType\",\"field\":\"TransactionType\"},{\"header\":\"TransactionValue\",\"field\":\"value\"}]"),
                ExportVal = JArray.Parse("[{\"Date\":\"04/22/2021\",\"TransactionType\":\"InitialInvestment\",\"isRealizedValue\":true,\"portfolioCompanyId\":239,\"value\":-8554500,\"name\":\"CashFlow1\"},{\"Date\":\"05/18/2021\",\"TransactionType\":\"Distributions(Net)\",\"isRealizedValue\":true,\"portfolioCompanyId\":239,\"value\":1206320,\"name\":\"CashFlow1\"},{\"Date\":\"06/18/2021\",\"TransactionType\":\"Distribution\",\"isRealizedValue\":true,\"portfolioCompanyId\":239,\"value\":1033107,\"name\":\"CashFlow1\"},{\"Date\":\"06/21/2021\",\"TransactionType\":\"Distribution\",\"isRealizedValue\":true,\"portfolioCompanyId\":239,\"value\":2636520,\"name\":\"CashFlow1\"},{\"Date\":\"09/30/2022\",\"TransactionType\":\"FairMarketValue\",\"isRealizedValue\":true,\"portfolioCompanyId\":239,\"value\":543217,\"name\":\"CashFlow1\"}]")
            };
            Task<List<SubPageFieldModel>> pageFields = TransactionTypes();
            var response = ExportCashflow.FundCashflowFundReportingCurrency(FilterData(filters), pageFields);
            Assert.IsType<DataTable>(response);
        }
        [Fact]
        public void ExportCashFlowShouldReturnFileResultWhenFilterSelectionIsFundCashflowReportingCurrency()
        {
            var filters = new
            {
                Selection = "FundCashflow Reporting Currency",
                FundName = "Test Fund",
                Currency = "USD",
                ExportCol = JArray.Parse("[{\"header\":\"CompanyName\",\"field\":\"name\"},{\"header\":\"ReportingCurrency\",\"field\":\"currency\"},{\"header\":\"TransactionDate\",\"field\":\"date\"},{\"header\":\"TransactionType\",\"field\":\"transactionType\"},{\"header\":\"TransactionValue\",\"field\":\"value\"}]"),
                ExportVal = JArray.Parse("[{\"name\":\"CashFlow1\",\"portfolioCompanyId\":239,\"currency\":\"DZD\",\"fundCurrency\":\"ADF\",\"value\":-8554500,\"date\":\"04/22/2021\",\"transactionType\":\"InitialInvestment\",\"isRealizedValue\":true},{\"name\":\"CashFlow1\",\"portfolioCompanyId\":239,\"currency\":\"DZD\",\"fundCurrency\":\"ADF\",\"value\":1206320,\"date\":\"05/18/2021\",\"transactionType\":\"Distributions(Net)\",\"isRealizedValue\":true},{\"name\":\"CashFlow1\",\"portfolioCompanyId\":239,\"currency\":\"DZD\",\"fundCurrency\":\"ADF\",\"value\":1033107,\"date\":\"06/18/2021\",\"transactionType\":\"Distribution\",\"isRealizedValue\":true},{\"name\":\"CashFlow1\",\"portfolioCompanyId\":239,\"currency\":\"DZD\",\"fundCurrency\":\"ADF\",\"value\":2636520,\"date\":\"06/21/2021\",\"transactionType\":\"Distribution\",\"isRealizedValue\":true},{\"name\":\"CashFlow1\",\"portfolioCompanyId\":239,\"currency\":\"DZD\",\"fundCurrency\":\"ADF\",\"value\":543217,\"date\":\"09/30/2022\",\"transactionType\":\"FairMarketValue\",\"isRealizedValue\":true}]")
            };
            Task<List<SubPageFieldModel>> pageFields = TransactionTypes();
            var response = ExportCashflow.FundCashflowFundReportingCurrency(FilterData(filters), pageFields);
            Assert.IsType<DataTable>(response);
        }
        [Fact]
        public void GetCashflowFileDownloadPath_ReturnsExpectedPath()
        {
            FileUploadModel fileUploadDetails = new FileUploadModel()
            {
                UniqueFileId = "1234567890",
                FileName = "cashflow.csv"
            };
            string actualPath = ExportCashflow.GetCashflowFileDownloadPath(fileUploadDetails);
            Assert.IsType<string>(actualPath);
        }

        [Fact]
        public void CashflowUploadFilePath_ReturnsExpectedPath()
        {
            string fileName = "cashflow.csv";
            string guid = "1234567890";
            string actualPath = ExportCashflow.CashflowUploadFilePath(fileName, guid);
            Assert.IsType<string>(actualPath);
        }

        [Fact]
        public void CreateCashflowTempFolder_CreatesTempFolderIfNotExists()
        {
            string guid = "1234567890";
            string directoryExists=ExcelFileHelper.CreateTempFolderWithGuid(guid,Constants.S3CashFlowFolder);
            Assert.IsType<string>(directoryExists);
        }
        private static async Task<List<SubPageFieldModel>> TransactionTypes()
        {
            return new() {
                new SubPageFieldModel { Id = 1, IsCustom = false, IsActive = true, IsDeleted = false, IsListData = false, ShowOnList = false, SequenceNo = 1, DisplayName = "Distribution Copy", Name = "Distribution", IsMandatory = false, DataTypeId = 0, SubPageID = 21, PagePath = "",  CreatedBy = 1, CreatedOn = DateTime.Now, ModifiedBy = 1, ModifiedOn = DateTime.Now },
                new SubPageFieldModel { Id = 2, IsCustom = false, IsActive = false, IsDeleted = false, IsListData = false, ShowOnList = false, SequenceNo = 2, DisplayName = "Distributions (Net) Copy", Name = "Distributions (Net)", IsMandatory = false, DataTypeId = 0, SubPageID = 21, PagePath = "", CreatedBy = 1, CreatedOn = DateTime.Now, ModifiedBy = 1, ModifiedOn = DateTime.Now },
                new SubPageFieldModel { Id = 3, IsCustom = false, IsActive = true, IsDeleted = false, IsListData = false, ShowOnList = false, SequenceNo = 2, DisplayName = "Drawdowns Copy", Name = "Drawdowns", IsMandatory = false, DataTypeId = 0, SubPageID = 21, PagePath = "",CreatedBy = 1, CreatedOn = DateTime.Now, ModifiedBy = 1, ModifiedOn = DateTime.Now },
                new SubPageFieldModel { Id = 4, IsCustom = false, IsActive = true, IsDeleted = false, IsListData = false, ShowOnList = false, SequenceNo = 1, DisplayName = "Fair Market Value Copy", Name = "Fair Market Value", IsMandatory = false, DataTypeId = 0, SubPageID = 21, PagePath = "", CreatedBy = 1, CreatedOn = DateTime.Now, ModifiedBy = 1, ModifiedOn = DateTime.Now },
                new SubPageFieldModel { Id = 5, IsCustom = false, IsActive = false, IsDeleted = false, IsListData = false, ShowOnList = false, SequenceNo = 2, DisplayName = "Follow-on Investment Copy", Name = "Follow-on Investment", IsMandatory = false, DataTypeId = 0, SubPageID = 21, PagePath = "",CreatedBy = 1, CreatedOn = DateTime.Now, ModifiedBy = 1, ModifiedOn = DateTime.Now },
                new SubPageFieldModel { Id = 6, IsCustom = false, IsActive = true, IsDeleted = false, IsListData = false, ShowOnList = false, SequenceNo = 2, DisplayName = "Initial Investment Copy", Name = "Initial Investment", IsMandatory = false, DataTypeId = 0, SubPageID = 21, PagePath = "",CreatedBy = 1, CreatedOn = DateTime.Now, ModifiedBy = 1, ModifiedOn = DateTime.Now },
                new SubPageFieldModel { Id = 7, IsCustom = false, IsActive = false, IsDeleted = false, IsListData = false, ShowOnList = false, SequenceNo = 2, DisplayName = "NAV Copy", Name = "NAV", IsMandatory = false, DataTypeId = 0, SubPageID = 21, PagePath = "", CreatedBy = 1, CreatedOn = DateTime.Now, ModifiedBy = 1, ModifiedOn = DateTime.Now },
                new SubPageFieldModel { Id = 8, IsCustom = false, IsActive = true, IsDeleted = false, IsListData = false, ShowOnList = false, SequenceNo = 2, DisplayName = "Fees Copy", Name = "Fees", IsMandatory = false, DataTypeId = 0, SubPageID = 21, PagePath = "", CreatedBy = 1, CreatedOn = DateTime.Now, ModifiedBy = 1, ModifiedOn = DateTime.Now },
            };
        }
        private ExportFilter FilterData(object filters)
        {
            dynamic filter = JsonConvert.SerializeObject(filters);
            JsonDocument jsonDocument = JsonDocument.Parse(filter);
            JsonElement rootElement = jsonDocument.RootElement;
            return JsonConvert.DeserializeObject<ExportFilter>(rootElement.GetRawText());
        }
        private dynamic CreateMockFilters(string selection = "Test")
        {
            var filters = new
            {
                Selection = selection,
                FundName = "Test Fund",
                Currency = "USD",
                ExportCol = JArray.Parse("[{\"header\":\"Capital Invested\",\"field\":\"capitalInvested\"},{\"header\":\"Realized Value\",\"field\":\"realizedValue\"},{\"header\":\"Unrealized Value\",\"field\":\"unrealizedValue\"},{\"header\":\"Total Value\",\"field\":\"totalValue\"},{\"header\":\"Gross IRR\",\"field\":\"IRR\"},{\"header\":\"Gross TVPI\",\"field\":\"TVPI\"}]"),
                ExportVal = JArray.Parse("[{\"Date\":\"04/22/2020\",\"isRealizedValue\":true,\"portfolioCompanyId\":5916,\"value\":-8554500,\"name\":\"Fund cashflow R\",\"Capital Invested\":\"13,784,500\",\"Realized Value\":\"3,272,534\",\"Unrealized Value\":\"85,020\",\"Total Value\":\"3,357,554\",\"IRR\":\"-96.9%\",\"TVPI\":\"0.2\",\"currency\":\"USD\",\"FundCurrency\":\"USD\",\"capitalInvested\":\"13,784,500\",\"realizedValue\":\"3,272,534\",\"unrealizedValue\":\"85,020\",\"totalValue\":\"3,357,554\"},{\"Date\":\"04/30/2020\",\"value\":-2846211,\"name\":\"Fund Cashflow UNR\",\"Capital Invested\":\"2,846,211\",\"Realized Value\":\"465,772\",\"Unrealized Value\":\"1,052,000\",\"Total Value\":\"1,517,772\",\"IRR\":\"-42.2%\",\"TVPI\":\"0.5\",\"currency\":\"USD\",\"FundCurrency\":\"USD\",\"capitalInvested\":\"2,846,211\",\"realizedValue\":\"465,772\",\"unrealizedValue\":\"1,052,000\",\"totalValue\":\"1,517,772\"},{\"capitalInvested\":\"13,784,500\",\"realizedValue\":\"3,272,534\",\"unrealizedValue\":\"85,020\",\"IRR\":\"-96.9%\",\"TVPI\":\"0.2\",\"totalValue\":\"3,357,554\",\"name\":\"Total Realized\"},{\"capitalInvested\":\"2,846,211\",\"realizedValue\":\"465,772\",\"unrealizedValue\":\"1,052,000\",\"IRR\":\"-42.2%\",\"TVPI\":\"0.5\",\"totalValue\":\"1,517,772\",\"name\":\"Total Unrealized\"},{\"capitalInvested\":\"16,630,711\",\"realizedValue\":\"3,738,306\",\"unrealizedValue\":\"1,137,020\",\"IRR\":\"-82.1%\",\"TVPI\":\"0.3\",\"totalValue\":\"4,875,326\",\"isExpense\":true,\"isTotal\":true,\"name\":\"Total\"}]")
            };
            return JsonConvert.SerializeObject(filters);
        }

        [Fact]
        public async Task ShouldReturnOkWhenCashflowDataIsSavedAsync()
        {
            CashFlowFileModelMock mock = new();
            var model = mock.GenerateSaveMockData();
            InjectedParameters.Setup(x => x.UnitOfWork.CashflowRepository.Get(It.IsAny<Func<CashFlowFileLogs, bool>>())).Returns(new CashFlowFileLogs
            {
                CashFlowFileId = 1,
                FundId = 1,
                FileName = "CashFlowFile1.xlsx",
                UniqueFileId = "1234567890",
                CurrencyId = 1,
                CreatedOn = new DateTime(2023, 01, 01),
                CreatedBy = 1,
                IsDeleted = false,
                IsActive = true,
                EncryptedCashFlowFileId = null
            });
            InjectedParameters.Setup(x => x.Encryption.Decrypt(It.IsAny<string>())).Returns("1");
            InjectedParameters.Setup(x => x.UnitOfWork.PortfolioCompanyFundHoldingDetailRepository.GetFirstOrDefault(It.IsAny<Func<PortfolioCompanyFundHoldingDetails, bool>>())).Returns((new PortfolioCompanyFundHoldingDetails
            {
                PortfolioCompanyFundHoldingId = 3,
                DealId = 1,
                Quarter = "Q1",
                Year = 2023,
                InvestementDate = new DateTime(2023, 1, 1),
                ValuationDate = new DateTime(2023, 3, 31),
                InvestmentCost = 100000,
                RealizedValue = 50000,
                UnrealizedValue = 50000,
                TotalValue = 100000,
                GrossMultiple = 2,
                GrossIrr = 2,
                FundHoldingStatusId = 1,
                IsDeleted = false,
                IsActive = true,
                Dpi = 1,
                Rvpi = 0,
                EncryptedPortfolioCompanyFundHoldingId = "1234567890",
            }));
            var result = await CashflowController.SaveCashFlowData(model);
            Assert.IsType<JsonResult>(result);
        }

        [Fact]
        public async Task ShouldReturnOkStatusCodeWhenCashflowTransactionTypesListIsFound()
        {
            List<M_SubPageFields> m_SubPageFields = new() { new M_SubPageFields { FieldID = 1, IsCustom = false, IsActive = true, IsDeleted = false, IsListData = false, ShowOnList = false, SequenceNo = 1, AliasName = "Filed Name", Name = "Name", IsMandatory = false, DataTypeId = 0, SubPageID = 21, PagePath = "", EncryptedSubPageID = "s", CreatedBy = 1, CreatedOn = DateTime.Now, ModifiedBy = 1, ModifiedOn = DateTime.Now }, new M_SubPageFields { FieldID = 2, IsCustom = false, IsActive = false, IsDeleted = false, IsListData = false, ShowOnList = false, SequenceNo = 2, AliasName = "2Filed Name", Name = "Customfield", IsMandatory = false, DataTypeId = 0, SubPageID = 21, PagePath = "", EncryptedSubPageID = "s", CreatedBy = 1, CreatedOn = DateTime.Now, ModifiedBy = 1, ModifiedOn = DateTime.Now }, new M_SubPageFields { FieldID = 2, IsCustom = false, IsActive = true, IsDeleted = false, IsListData = false, ShowOnList = false, SequenceNo = 2, AliasName = "2Filed Name", Name = "Customfield", IsMandatory = false, DataTypeId = 0, SubPageID = 21, PagePath = "", EncryptedSubPageID = "s", CreatedBy = 1, CreatedOn = DateTime.Now, ModifiedBy = 1, ModifiedOn = DateTime.Now } };
            InjectedParameters.Setup(x => x.UnitOfWork.SubPageFieldsRepository.FindAllAsync(It.IsAny<Expression<Func<M_SubPageFields, bool>>>())).Returns(Task.FromResult(m_SubPageFields));
            var result = await CashflowController.GetCashflowTransactionTypesList();
            var okResult = result as ObjectResult;
            Assert.IsType<List<SubPageFieldModel>>(okResult?.Value);
        }

        [Fact]
        public async Task UploadCashflow_NotValidObject_ReturnsSuccess()
        {
            string folderName = Path.Combine("Mocks");
            string webRootPath = Path.Combine(Directory.GetCurrentDirectory(), folderName);
            string fileNameWithPath = Path.Combine(webRootPath, "Fund Cashflow.xlsx");
            InjectedParameters.Setup(x => x.GlobalConfigurations.ExportFileUploadPath).Returns("/exported-files/");
            this.CashflowController.ControllerContext = RequestWithFile(fileNameWithPath, null);
            var result = await CashflowController.UploadCashflow() as OkObjectResult;
            Assert.IsType<CashflowUploadDetails>(result.Value);
        }

        [Fact]
        public async Task UploadCashflow_ValidFile_ReturnsSuccess()
        {
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
            string folderName = Path.Combine("Mocks");
            string webRootPath = Path.Combine(Directory.GetCurrentDirectory(), folderName);
            string fileNameWithPath = Path.Combine(webRootPath, "Fund Cashflow.xlsx");
            var dynamicObject = new Dictionary<string, StringValues>()
                {
                    {"FundId", "1"},
                    {"CompanyId", "1"}
                };
            InjectedParameters.Setup(x => x.GlobalConfigurations.ExportFileUploadPath).Returns("/exported-files/");
            this.CashflowController.ControllerContext = RequestWithFile(fileNameWithPath, dynamicObject);
            InjectedParameters.Setup(x => x.UnitOfWork.CashflowRepository.Insert(null));
            LoadAllPortfolioCompanies();
            LoadTranasctionTypes();
            LoadDeals();
            var result = await CashflowController.UploadCashflow() as OkObjectResult;
            Assert.Equal(null, result);
        }

        private ControllerContext RequestWithFile(string fileNameWithPath, Dictionary<string, StringValues> dynamicObject)
        {
            var httpContext = new DefaultHttpContext();
            var file = SetUpTestingFile(fileNameWithPath);
            httpContext.Request.Form = new FormCollection(dynamicObject, new FormFileCollection { file });
            var actx = new ActionContext(httpContext, new Microsoft.AspNetCore.Routing.RouteData(), new ControllerActionDescriptor());
            return new ControllerContext(actx);
        }

        private IFormFile SetUpTestingFile(string fileName)
        {
            var byteArray = File.ReadAllBytes(fileName);
            var ms = new MemoryStream(byteArray);
            var file = new FormFile(ms, 0, ms.Length, null, Path.GetFileName(fileName))
            {
                Headers = new HeaderDictionary(),
                ContentDisposition = $"form-data; name=\"formFile\"; filename=\"{Path.GetFileName(fileName)}\"",
                ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            };
            ms.Position = 0;
            return file;
            //Use your memorystream
        }

        private void LoadDeals()
        {
            dapperGenericRepository.Setup(x => x.Query<DealModel>(It.IsAny<string>(), It.IsAny<object>())).Returns(Task.FromResult(new List<DealModel> { new DealModel { DealID=1,PortfolioCompanyID=1,FundID=1 },
                new DealModel { DealID = 2, PortfolioCompanyID = 2, FundID = 1 },
            new DealModel { DealID=3,PortfolioCompanyID=3,FundID=1 },
            new DealModel { DealID=4,PortfolioCompanyID=4,FundID=1 }
            }));
        }

        private void LoadAllPortfolioCompanies()
        {
            var portfolioCompanyDetails = new List<PortfolioCompanyDetails>
            {
            new PortfolioCompanyDetails
    {
        PortfolioCompanyId = 1,
        CompanyName = "Fund Cash Flow 1",
        Website = "www.portfoliocompany1.com",
        BussinessDescription = "This is the business description for Portfolio Company 1.",
        SectorId = 1,
        SubSectorId = 1,
        ReportingCurrencyId = 1,
        Status = "Active",
        StockExchangeTicker = "PC1",
        HeadquarterId = 1,
        IsDeleted = false,
        IsActive = true,
        EncryptedPortfolioCompanyId = "1234567890",
        FinancialYearEnd = "12/31",
        ImagePath = "/path/to/image.png",
        MasterCompanyName = "Master Company 1",
        GroupId = 1,
        CashflowCalculationDetails = new List<CashflowCalculationDetails>(),
        CashFlowCompanyWiseData = new List<CashFlowCompanyWiseData>(),
        DealDetails = new List<DealDetails>(),
        Sector = new M_Sector {  },
        SubSector = new M_SubSector {},
        ReportingCurrency = new M_Currency { CurrencyId = 1, CurrencyCode = "USD" },
        MappingPCEmployee = new List<Mapping_PCEmployee>(),
        MappingPCGeographicLocation = new List<Mapping_PCGeographicLocation>(),
        PortfolioCompanyProfitabilityDetails = new List<PortfolioCompanyProfitabilityDetails>(),
        PortfolioCompanyOperationalKPIQuarters = new List<PortfolioCompanyOperationalKPIQuarter>(),
        PCCompanyKpimonthlyValue = new List<PCCompanyKpiMonthlyValue>(),
        PCInvestmentKpiQuarterlyValue = new List<PCInvestmentKpiQuarterlyValue>(),
        PCImpactKpiQuarterlyValue = new List<PcImpactKpiQuarterlyValue>(),
        CommentaryDetails = new List<CommentaryDetails>(),
        Mapping_ImpactKPI_Order = new List<Mapping_ImpactKPI_Order>(),
        PortfolioCompanyOperationalKPIQuartersDraft = new List<PortfolioCompanyOperationalKPIQuartersDraft>(),
    },
            new PortfolioCompanyDetails
    {
        PortfolioCompanyId = 2,
        CompanyName = "Fund Cash Flow 2",
        Website = "www.portfoliocompany2.com",
        BussinessDescription = "This is the business description for Portfolio Company 2.",
        SectorId = 2,
        SubSectorId = 2,
        ReportingCurrencyId = 2,
        Status = "Active",
        StockExchangeTicker = "PC2",
        HeadquarterId = 2,
        IsDeleted = false,
        IsActive = true,
        EncryptedPortfolioCompanyId = "9876543210",
        FinancialYearEnd = "12/31",
        ImagePath = "/path/to/image2.png",
        MasterCompanyName = "Master Company 2",
        GroupId = 2,
        CashflowCalculationDetails = new List<CashflowCalculationDetails>(),
        CashFlowCompanyWiseData = new List<CashFlowCompanyWiseData>(),
        DealDetails = new List<DealDetails>(),
        Sector = new M_Sector { },
        SubSector = new M_SubSector {},
        ReportingCurrency = new M_Currency { CurrencyId = 2, CurrencyCode = "EUR" },
        MappingPCEmployee = new List<Mapping_PCEmployee>(),
        MappingPCGeographicLocation = new List<Mapping_PCGeographicLocation>(),
        PortfolioCompanyProfitabilityDetails = new List<PortfolioCompanyProfitabilityDetails>()
    },
            new PortfolioCompanyDetails
    {
        PortfolioCompanyId = 3,
        CompanyName = "Fund Cash Flow 3",
        Website = "www.portfoliocompany1.com",
        BussinessDescription = "This is the business description for Portfolio Company 1.",
        SectorId = 1,
        SubSectorId = 1,
        ReportingCurrencyId = 1,
        Status = "Active",
        StockExchangeTicker = "PC1",
        HeadquarterId = 1,
        IsDeleted = false,
        IsActive = true,
        EncryptedPortfolioCompanyId = "1234567890",
        FinancialYearEnd = "12/31",
        ImagePath = "/path/to/image.png",
        MasterCompanyName = "Master Company 1",
        GroupId = 1,
        CashflowCalculationDetails = new List<CashflowCalculationDetails>(),
        CashFlowCompanyWiseData = new List<CashFlowCompanyWiseData>(),
        DealDetails = new List<DealDetails>(),
        Sector = new M_Sector {  },
        SubSector = new M_SubSector {},
        ReportingCurrency = new M_Currency { CurrencyId = 1, CurrencyCode = "USD" },
        MappingPCEmployee = new List<Mapping_PCEmployee>(),
        MappingPCGeographicLocation = new List<Mapping_PCGeographicLocation>(),
        PortfolioCompanyProfitabilityDetails = new List<PortfolioCompanyProfitabilityDetails>(),
        PortfolioCompanyOperationalKPIQuarters = new List<PortfolioCompanyOperationalKPIQuarter>(),
        PCCompanyKpimonthlyValue = new List<PCCompanyKpiMonthlyValue>(),
        PCInvestmentKpiQuarterlyValue = new List<PCInvestmentKpiQuarterlyValue>(),
        PCImpactKpiQuarterlyValue = new List<PcImpactKpiQuarterlyValue>(),
        CommentaryDetails = new List<CommentaryDetails>(),
        Mapping_ImpactKPI_Order = new List<Mapping_ImpactKPI_Order>(),
        PortfolioCompanyOperationalKPIQuartersDraft = new List<PortfolioCompanyOperationalKPIQuartersDraft>(),
    },
            new PortfolioCompanyDetails
    {
        PortfolioCompanyId = 4,
        CompanyName = "Fund Cash Flow 4",
        Website = "www.portfoliocompany2.com",
        BussinessDescription = "This is the business description for Portfolio Company 2.",
        SectorId = 2,
        SubSectorId = 2,
        ReportingCurrencyId = 2,
        Status = "Active",
        StockExchangeTicker = "PC2",
        HeadquarterId = 2,
        IsDeleted = false,
        IsActive = true,
        EncryptedPortfolioCompanyId = "9876543210",
        FinancialYearEnd = "12/31",
        ImagePath = "/path/to/image2.png",
        MasterCompanyName = "Master Company 2",
        GroupId = 2,
        CashflowCalculationDetails = new List<CashflowCalculationDetails>(),
        CashFlowCompanyWiseData = new List<CashFlowCompanyWiseData>(),
        DealDetails = new List<DealDetails>(),
        Sector = new M_Sector { },
        SubSector = new M_SubSector {},
        ReportingCurrency = new M_Currency { CurrencyId = 2, CurrencyCode = "EUR" },
        MappingPCEmployee = new List<Mapping_PCEmployee>(),
        MappingPCGeographicLocation = new List<Mapping_PCGeographicLocation>(),
        PortfolioCompanyProfitabilityDetails = new List<PortfolioCompanyProfitabilityDetails>()
    },
            };
            InjectedParameters.Setup(x => x.UnitOfWork.PortfolioCompanyDetailRepository.GetManyQueryable(It.IsAny<Func<PortfolioCompanyDetails, bool>>())).Returns(portfolioCompanyDetails.AsQueryable());
        }

        private void LoadTranasctionTypes()
        {
            List<M_SubPageFields> m_SubPageFields = new() {
                new M_SubPageFields { FieldID = 1, IsCustom = false, IsActive = true, IsDeleted = false, IsListData = false, ShowOnList = false, SequenceNo = 1, AliasName = "Distribution Copy", Name = "Distribution", IsMandatory = false, DataTypeId = 0, SubPageID = 21, PagePath = "", EncryptedSubPageID = "s", CreatedBy = 1, CreatedOn = DateTime.Now, ModifiedBy = 1, ModifiedOn = DateTime.Now },
                new M_SubPageFields { FieldID = 2, IsCustom = false, IsActive = false, IsDeleted = false, IsListData = false, ShowOnList = false, SequenceNo = 2, AliasName = "Distributions (Net) Copy", Name = "Distributions (Net)", IsMandatory = false, DataTypeId = 0, SubPageID = 21, PagePath = "", EncryptedSubPageID = "s", CreatedBy = 1, CreatedOn = DateTime.Now, ModifiedBy = 1, ModifiedOn = DateTime.Now },
                new M_SubPageFields { FieldID = 3, IsCustom = false, IsActive = true, IsDeleted = false, IsListData = false, ShowOnList = false, SequenceNo = 2, AliasName = "Drawdowns Copy", Name = "Drawdowns", IsMandatory = false, DataTypeId = 0, SubPageID = 21, PagePath = "", EncryptedSubPageID = "s", CreatedBy = 1, CreatedOn = DateTime.Now, ModifiedBy = 1, ModifiedOn = DateTime.Now },
                 new M_SubPageFields { FieldID = 4, IsCustom = false, IsActive = true, IsDeleted = false, IsListData = false, ShowOnList = false, SequenceNo = 1, AliasName = "Fair Market Value Copy", Name = "Fair Market Value", IsMandatory = false, DataTypeId = 0, SubPageID = 21, PagePath = "", EncryptedSubPageID = "s", CreatedBy = 1, CreatedOn = DateTime.Now, ModifiedBy = 1, ModifiedOn = DateTime.Now },
                new M_SubPageFields { FieldID = 5, IsCustom = false, IsActive = false, IsDeleted = false, IsListData = false, ShowOnList = false, SequenceNo = 2, AliasName = "Follow-on Investment Copy", Name = "Follow-on Investment", IsMandatory = false, DataTypeId = 0, SubPageID = 21, PagePath = "", EncryptedSubPageID = "s", CreatedBy = 1, CreatedOn = DateTime.Now, ModifiedBy = 1, ModifiedOn = DateTime.Now },
                new M_SubPageFields { FieldID = 6, IsCustom = false, IsActive = true, IsDeleted = false, IsListData = false, ShowOnList = false, SequenceNo = 2, AliasName = "Initial Investment Copy", Name = "Initial Investment", IsMandatory = false, DataTypeId = 0, SubPageID = 21, PagePath = "", EncryptedSubPageID = "s", CreatedBy = 1, CreatedOn = DateTime.Now, ModifiedBy = 1, ModifiedOn = DateTime.Now },
                 new M_SubPageFields { FieldID = 7, IsCustom = false, IsActive = false, IsDeleted = false, IsListData = false, ShowOnList = false, SequenceNo = 2, AliasName = "NAV Copy", Name = "NAV", IsMandatory = false, DataTypeId = 0, SubPageID = 21, PagePath = "", EncryptedSubPageID = "s", CreatedBy = 1, CreatedOn = DateTime.Now, ModifiedBy = 1, ModifiedOn = DateTime.Now },
                new M_SubPageFields { FieldID = 8, IsCustom = false, IsActive = true, IsDeleted = false, IsListData = false, ShowOnList = false, SequenceNo = 2, AliasName = "Fees Copy", Name = "Fees", IsMandatory = false, DataTypeId = 0, SubPageID = 21, PagePath = "", EncryptedSubPageID = "s", CreatedBy = 1, CreatedOn = DateTime.Now, ModifiedBy = 1, ModifiedOn = DateTime.Now },
            };
            InjectedParameters.Setup(x => x.UnitOfWork.SubPageFieldsRepository.FindAllAsync(It.IsAny<Expression<Func<M_SubPageFields, bool>>>())).Returns(Task.FromResult(m_SubPageFields));
        }

        [Fact]
        public void SaveCashflowData_ShouldReturnErrorIfCashflowIdDoesNotExist()
        {
            // Arrange
            string cashFlowId = "1234567891";
            int result = flowService.SaveCashflowData(cashFlowId);
            Assert.Equal(-1, result);
        }

        [Fact]
        public void ShouldAbleToUploadCashflow()
        {
            var actual = CashflowController.UploadCashflow();
            Assert.NotNull(actual);
        }
        [Fact]
        public void ShouldAbleToGetFxRatesByCurrency()
        {
            var cashflowConversionModels = new List<CashflowConversionModel>();
            var actual = CashflowController.GetFxRatesByCurrency(cashflowConversionModels);
            Assert.NotNull(actual);
        }

        [Fact]
        public void ShouldAbleToGetReportngCurrencyValuesForFundPerformance()
        {
            var fundPerformanceData = new FundPerformanceData();
            var actual = CashflowController.GetReportngCurrencyValuesForFundPerformance(fundPerformanceData);
            Assert.NotNull(actual);
        }

        [Fact]
        public void ShouldAbleToSaveCashFlowData()
        {
            var model = new CashFlowFileModel();
            var actual = CashflowController.SaveCashFlowData(model);
            Assert.NotNull(actual);
        }
    }

    public class CashFlowFileModelMock
    {
        public string FileName { get; set; }
        public int CashflowFileId { get; set; }
        public FundModel Fund { get; set; }
        public M_Currency Currency { get; set; }
        public List<CashflowDateWiseSummaryModel> CashflowDateWiseSummary { get; set; }
        public List<CashflowCalculationModel> CashflowCalculationDetails { get; set; }
        public List<CashflowTotalCalculationModel> TotalCalculationDetails { get; set; }

        public CashFlowFileLogs GenerateMockData()
        {
            var cashFlowFileModel = new CashFlowFileLogs
            {
                FileName = "CashFlowFile.csv",
                CashFlowFileId = 1,
                FundId = 0,
                CurrencyId = 0,
                FundDetail = new DataAccessLayer.DBModel.FundDetails
                {
                    FundId = 1,
                    FundName = "Fund Name",
                    CurrencyId = 1
                },
                CashflowCurrency = new M_Currency
                {
                    CurrencyId = 1,
                    Currency = "USD",
                    CurrencyCode = "USD"
                },
                CashFlowDateWiseSummaries = new List<CashFlowDateWiseSummary>
                {
                    new CashFlowDateWiseSummary
                    {
                        TransactionDate = new DateTime(2023, 1, 1),
                        TotalRealizeValue = 1000,
                        TotalUnrealizeValue = 2000,
                        TotalFundFees = 3000,
                        TotalRealizeUnrealizeValue = 4000,
                        TotalRealizeUnrealizeAndFundFeesValue = 5000,
                        TransactionType = "Investment",
                        CashFlowCompanyWiseData = new List<CashFlowCompanyWiseData>
                        {
                            new CashFlowCompanyWiseData
                            {
                                CashFlowCompanyWiseDataId = 1,
                                IsRealizedValue = true,
                                PortfolioCompanyId = 1,
                                TransactionType = "Investment",
                                TransactionValue = 1000,
                                PortfolioCompanyDetail = new PortfolioCompanyDetails { CompanyName = "Name1", PortfolioCompanyId = 1, ReportingCurrencyId = 1 }
                            },
                            new CashFlowCompanyWiseData
                            {
                                CashFlowCompanyWiseDataId = 2,
                                IsRealizedValue = false,
                                PortfolioCompanyId = 2,
                                TransactionType = "Investment",
                                TransactionValue = 2000,
                                PortfolioCompanyDetail = new PortfolioCompanyDetails { CompanyName = "Name2", PortfolioCompanyId = 2, ReportingCurrencyId = 1 }
                            }
                        }
                    },
                    new CashFlowDateWiseSummary
                    {
                        TransactionDate = new DateTime(2023, 2, 1),
                        TotalRealizeValue = 5000,
                        TotalUnrealizeValue = 6000,
                        TotalFundFees = 7000,
                        TotalRealizeUnrealizeValue = 8000,
                        TotalRealizeUnrealizeAndFundFeesValue = 9000,
                        TransactionType = "Exit",
                        CashFlowCompanyWiseData = new List<CashFlowCompanyWiseData>
                        {
                            new CashFlowCompanyWiseData
                            {
                                CashFlowCompanyWiseDataId = 3,
                                IsRealizedValue = true,
                                PortfolioCompanyId = 3,
                                TransactionType = "Exit",
                                TransactionValue = 5000,
                                PortfolioCompanyDetail = new PortfolioCompanyDetails { CompanyName = "Name1", PortfolioCompanyId = 3, ReportingCurrencyId = 1 }
                            },
                            new CashFlowCompanyWiseData
                            {
                                CashFlowCompanyWiseDataId = 4,
                                IsRealizedValue = false,
                                PortfolioCompanyId = 4,
                                TransactionType = "Exit",TransactionValue = 6000,
                                PortfolioCompanyDetail = new PortfolioCompanyDetails { CompanyName = "Name2", PortfolioCompanyId = 4, ReportingCurrencyId = 1 }
                            }
                        }
                    }
                },
                CashflowCalculationDetails = new List<CashflowCalculationDetails>
                {
                    new CashflowCalculationDetails
                    {
                        CapitalInvested = 10000,
                        RealizedValue = 15000,
                        UnrealizedValue = 20000,
                        TotalValue = 35000,
                        IRR = 1,
                        Multiple = 2,
                        PortfolioCompanyId = 1,
                        IsRealizedValue = true,
                        PortfolioCompanyDetail=new PortfolioCompanyDetails{ CompanyName="Name1",PortfolioCompanyId=1,ReportingCurrencyId=1 }
                    },
                    new CashflowCalculationDetails
                    {
                        CapitalInvested = 20000,
                        RealizedValue = 30000,
                        UnrealizedValue = 40000,
                        TotalValue = 90000,
                        IRR = 2,
                        Multiple = 3,
                        PortfolioCompanyId = 2,
                        IsRealizedValue = false,
                        PortfolioCompanyDetail=new PortfolioCompanyDetails{ CompanyName="Name2",PortfolioCompanyId=2,ReportingCurrencyId=1 }
                    }
                },
                CashflowTotalCalculationDetails = new List<CashflowTotalCalculationDetails>
                {
                    new CashflowTotalCalculationDetails
                    {
                        TotalCapitalInvested = 30000,
                        TotalRealizedValue = 45000,
                        TotalUnrealizedValue = 60000,
                        TotalofTotalValue = 105000,
                        TotalIRR = 15,
                        TotalMultiple = 5,
                        CalculationType = "Total"
                    }
                }
            };
            return cashFlowFileModel;
        }

        public CashFlowFileModel GenerateSaveMockData()
        {
            var cashFlowFileModel = new CashFlowFileModel
            {
                FileName = "CashFlowFile.csv",
                CashflowFileId = 1,
                CashFlowCalculatedFundData = new CashFlowCalculatedFundData { Year = 2022, Quarter = "Q1", FundId = 1, GrossIrr = 10, NetIrr = 10, NetTvpi = 10 },
                Fund = new Contract.Funds.FundModel
                {
                    FundID = 1,
                    FundName = "Fund Name",
                    CurrencyID = 1
                },
                Currency = new Contract.Currency.CurrencyModel
                {
                    CurrencyID = 1,
                    Currency = "USD",
                    CurrencyCode = "USD"
                },
                CashflowDateWiseSummary = new List<Contract.CashFlow.CashflowDateWiseSummaryModel>
                {
                    new CashflowDateWiseSummaryModel
                    {
                        TransactionDate = new DateTime(2023, 1, 1),
                        TotalRealizeValue = 1000,
                        TotalUnrealizeValue = 2000,
                        TotalFundFees = 3000,
                        TotalRealizeUnrealizeValue = 4000,
                        TotalRealizeUnrealizeAndFundFeesValue = 5000,
                        TransactionType = "Investment",
                        CashFlowCompanyList = new List<CashFlowComapnyWiseModel>
                        {
                            new CashFlowComapnyWiseModel
                            {
                                CashFlowCompanyWiseDataID = 1,
                                IsRealizedValue = true,
                                PortfolioCompanyId = 1,
                                TransactionType = "Investment",
                                TransactionValue = 1000,
                                PortfolioCompanyName = "Name1"
                            },
                            new CashFlowComapnyWiseModel
                            {
                                CashFlowCompanyWiseDataID = 2,
                                IsRealizedValue = false,
                                PortfolioCompanyId = 2,
                                TransactionType = "Investment",
                                TransactionValue = 2000,
                                PortfolioCompanyName = "Name2"
                            }
                        }
                    },
                    new CashflowDateWiseSummaryModel
                    {
                        TransactionDate = new DateTime(2023, 2, 1),
                        TotalRealizeValue = 5000,
                        TotalUnrealizeValue = 6000,
                        TotalFundFees = 7000,
                        TotalRealizeUnrealizeValue = 8000,
                        TotalRealizeUnrealizeAndFundFeesValue = 9000,
                        TransactionType = "Exit",
                        CashFlowCompanyList = new List<CashFlowComapnyWiseModel>
                        {
                            new CashFlowComapnyWiseModel
                            {
                                CashFlowCompanyWiseDataID = 3,
                                IsRealizedValue = true,
                                PortfolioCompanyId = 3,
                                TransactionType = "Exit",
                                TransactionValue = 5000,
                                PortfolioCompanyName = "Name1"
                            },
                            new CashFlowComapnyWiseModel
                            {
                                CashFlowCompanyWiseDataID = 4,
                                IsRealizedValue = false,
                                PortfolioCompanyId = 4,
                                TransactionType = "Exit", TransactionValue = 6000,
                                PortfolioCompanyName = "Name2"
                            }
                        }
                    }
                },
                CashflowCalculationDetails = new List<Contract.CashFlow.CashflowCalculationModel>
                {
                    new CashflowCalculationModel
                    {
                        CapitalInvested = 10000,
                        RealizedValue = 15000,
                        UnrealizedValue = 20000,
                        TotalValue = 35000,
                        IRR = 1,
                        Multiple = 2,
                        PortfolioCompanyId = 1,
                        IsRealizedValue = true,
                        DealId = 2,
                        ExistingFundHoldingId = 3,
                        CashflowFileId = 1,
                        PortfolioCompanyName="Name 1",
                        InvestmentDate = DateTime.Now,
                        Quarter="Q1",
                        QuarterYear=DateTime.Now.Year
                    }
                },
                TotalCalculationDetails = new List<Contract.CashFlow.CashflowTotalCalculationModel>
                {
                    new CashflowTotalCalculationModel
                    {
                        TotalCapitalInvested = 30000,
                        TotalRealizedValue = 45000,
                        TotalUnrealizedValue = 60000,
                        TotalofTotalValue = 105000,
                        TotalIRR = 15,
                        TotalMultiple = 5,
                        CalculationType = "Total"
                    }
                }
            };
            return cashFlowFileModel;
        }
    }
}