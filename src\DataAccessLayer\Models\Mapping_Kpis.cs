using DataAccessLayer.DBModel;
using System.ComponentModel.DataAnnotations;
namespace DataAccessLayer.Models
{
    public partial class Mapping_Kpis : BaseModel
    {
        [Key]
        public int Mapping_KpisID { get; set; }
        public int? KPITypeID { get; set; }
        public int? PortfolioCompanyID { get; set; }
        public int? ModuleID { get; set; }
        public int KpiID { get; set; }
        public int? DisplayOrder { get; set; }
        public int? ParentKPIID { get; set; }
        public bool IsDeleted { get; set; }
        public bool IsHeader { get; set; }
        public string Formula { get; set; }
        public string FormulaKPIId { get; set; }
        public bool IsExtraction { get; set; }
        public string Synonym { get; set; }
        public string Definition { get; set; }


    }
}
