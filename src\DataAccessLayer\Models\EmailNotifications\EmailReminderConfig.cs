using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Diagnostics.CodeAnalysis;
using DataAccessLayer.DBModel;

namespace DataAccessLayer.Models.EmailNotifications
{
    [Table("EmailReminderConfig")]
    [ExcludeFromCodeCoverage]
    public class EmailReminderConfig : BaseCommonModel
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Required]
        public Guid ReminderId { get; set; }

        [Required]
        public int FrequencyType { get; set; } // 1=Monthly, 2=Quarterly, 3=Yearly

        [Required]
        public int TotalRemindersPerCycle { get; set; }

        [Required]
        public DateTime Remainder1Date { get; set; }

        [MaxLength(255)]
        public string Remainder2 { get; set; }

        [MaxLength(255)]
        public string Remainder3 { get; set; }

        [MaxLength(255)]
        public string Remainder4 { get; set; }

        [MaxLength(255)]
        public string Remainder5 { get; set; }
    }
}
