﻿using DataAccessLayer.DBModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DataAccessLayer.Models.DataExtraction
{
    public class DataExtractionTypes : BaseCommonModel
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Required]
        public string DocumentName { get; set; }
        public int? FeatureId { get; set; }
    }
}
