using EmailConfiguration.Helpers;
using EmailConfiguration.DTOs;
using DataAccessLayer.Models.EmailNotifications;
using Xunit;

namespace EmailConfiguration.UnitTest.Helpers
{
    /// <summary>
    /// Basic unit tests for EmailReminderHelper that compile and run successfully
    /// </summary>
    public class EmailReminderHelperBasicTests
    {
        [Fact]
        public void ParseCommaSeparatedIds_ValidString_ReturnsCorrectIds()
        {
            // Arrange
            var idsString = "1,2,3,4,5";

            // Act
            var result = EmailReminderHelper.ParseCommaSeparatedIds(idsString);

            // Assert
            Assert.Equal(5, result.Count);
            Assert.Equal(new List<int> { 1, 2, 3, 4, 5 }, result);
        }

        [Fact]
        public void ParseCommaSeparatedIds_StringWithSpaces_ReturnsCorrectIds()
        {
            // Arrange
            var idsString = " 1 , 2 , 3 , 4 , 5 ";

            // Act
            var result = EmailReminderHelper.ParseCommaSeparatedIds(idsString);

            // Assert
            Assert.Equal(5, result.Count);
            Assert.Equal(new List<int> { 1, 2, 3, 4, 5 }, result);
        }

        [Fact]
        public void ParseCommaSeparatedIds_StringWithInvalidIds_ReturnsOnlyValidIds()
        {
            // Arrange
            var idsString = "1,abc,3,0,-1,5";

            // Act
            var result = EmailReminderHelper.ParseCommaSeparatedIds(idsString);

            // Assert
            Assert.Equal(3, result.Count);
            Assert.Equal(new List<int> { 1, 3, 5 }, result);
        }

        [Fact]
        public void ParseCommaSeparatedIds_EmptyString_ReturnsEmptyList()
        {
            // Arrange
            var idsString = "";

            // Act
            var result = EmailReminderHelper.ParseCommaSeparatedIds(idsString);

            // Assert
            Assert.Empty(result);
        }

        [Fact]
        public void ParseCommaSeparatedIds_NullString_ReturnsEmptyList()
        {
            // Arrange
            string? idsString = null;

            // Act
            var result = EmailReminderHelper.ParseCommaSeparatedIds(idsString);

            // Assert
            Assert.Empty(result);
        }

        [Fact]
        public void ConvertIdsToCommaSeparatedString_ValidIds_ReturnsCorrectString()
        {
            // Arrange
            var ids = new List<int> { 1, 2, 3, 4, 5 };

            // Act
            var result = EmailReminderHelper.ConvertIdsToCommaSeparatedString(ids);

            // Assert
            Assert.Equal("1,2,3,4,5", result);
        }

        [Fact]
        public void ConvertIdsToCommaSeparatedString_EmptyList_ReturnsEmptyString()
        {
            // Arrange
            var ids = new List<int>();

            // Act
            var result = EmailReminderHelper.ConvertIdsToCommaSeparatedString(ids);

            // Assert
            Assert.Equal("", result);
        }

        [Fact]
        public void ConvertIdsToCommaSeparatedString_NullList_ReturnsEmptyString()
        {
            // Arrange
            List<int>? ids = null;

            // Act
            var result = EmailReminderHelper.ConvertIdsToCommaSeparatedString(ids);

            // Assert
            Assert.Equal("", result);
        }

        [Fact]
        public void CalculateReminderDates_ValidData_ReturnsCorrectDates()
        {
            // Arrange
            var remainder1Date = new DateTime(2024, 1, 1);
            var remainder2 = "5";
            var remainder3 = "10";
            var remainder4 = "15";
            var remainder5 = "20";
            var totalRemindersPerCycle = 5;

            // Act
            var result = EmailReminderHelper.CalculateReminderDates(remainder1Date, remainder2, remainder3, remainder4, remainder5, totalRemindersPerCycle);

            // Assert
            Assert.Equal(5, result.Count);
            Assert.Equal(new DateTime(2024, 1, 1), result[0]);
            Assert.Equal(new DateTime(2024, 1, 6), result[1]);
            Assert.Equal(new DateTime(2024, 1, 16), result[2]);
            Assert.Equal(new DateTime(2024, 1, 31), result[3]);
            Assert.Equal(new DateTime(2024, 2, 20), result[4]);
        }

        [Fact]
        public void CalculateReminderDates_LimitedByTotalReminders_ReturnsCorrectCount()
        {
            // Arrange
            var remainder1Date = new DateTime(2024, 1, 1);
            var remainder2 = "5";
            var remainder3 = "10";
            var remainder4 = "15";
            var remainder5 = "20";
            var totalRemindersPerCycle = 3;

            // Act
            var result = EmailReminderHelper.CalculateReminderDates(remainder1Date, remainder2, remainder3, remainder4, remainder5, totalRemindersPerCycle);

            // Assert
            Assert.Equal(3, result.Count);
            Assert.Equal(new DateTime(2024, 1, 1), result[0]);
            Assert.Equal(new DateTime(2024, 1, 6), result[1]);
            Assert.Equal(new DateTime(2024, 1, 16), result[2]);
        }

        [Fact]
        public void CalculateReminderDates_ZeroTotalReminders_ReturnsEmptyList()
        {
            // Arrange
            var remainder1Date = new DateTime(2024, 1, 1);
            var remainder2 = "5";
            var remainder3 = "10";
            var remainder4 = "15";
            var remainder5 = "20";
            var totalRemindersPerCycle = 0;

            // Act
            var result = EmailReminderHelper.CalculateReminderDates(remainder1Date, remainder2, remainder3, remainder4, remainder5, totalRemindersPerCycle);

            // Assert
            Assert.Empty(result);
        }

        [Theory]
        [InlineData(1, 2024, 1, 1, 2024, 2, 1)] // Monthly: +1 month
        [InlineData(2, 2024, 1, 1, 2024, 4, 1)] // Quarterly: +3 months
        [InlineData(3, 2024, 1, 1, 2025, 1, 1)] // Yearly: +1 year
        public void CalculateNextCycleStartDate_ValidFrequencyTypes_ReturnsCorrectDate(
            int frequencyType, int currentYear, int currentMonth, int currentDay,
            int expectedYear, int expectedMonth, int expectedDay)
        {
            // Arrange
            var currentDate = new DateTime(currentYear, currentMonth, currentDay);
            var expectedDate = new DateTime(expectedYear, expectedMonth, expectedDay);

            // Act
            var result = EmailReminderHelper.CalculateNextCycleStartDate(currentDate, frequencyType);

            // Assert
            Assert.Equal(expectedDate, result);
        }

        [Fact]
        public void CalculateNextCycleStartDate_InvalidFrequencyType_ThrowsArgumentException()
        {
            // Arrange
            var currentDate = new DateTime(2024, 1, 1);
            var invalidFrequencyType = 4;

            // Act & Assert
            var exception = Assert.Throws<ArgumentException>(() =>
                EmailReminderHelper.CalculateNextCycleStartDate(currentDate, invalidFrequencyType));

            Assert.Contains("Invalid frequency type: 4", exception.Message);
        }

        [Fact]
        public void CreateNextCycleSchedule_ValidConfig_ReturnsCorrectSchedules()
        {
            // Arrange
            var config = new EmailReminderConfig
            {
                Id = 1,
                ReminderId = Guid.NewGuid(),
                FrequencyType = 1, // Monthly
                TotalRemindersPerCycle = 3,
                Remainder1Date = new DateTime(2024, 2, 1),
                Remainder2 = "5",
                Remainder3 = "10",
                Remainder4 = null,
                Remainder5 = null
            };
            var userId = 123;

            // Act
            var result = EmailReminderHelper.CreateNextCycleSchedule(config, userId);

            // Assert
            Assert.Equal(3, result.Count);
            Assert.Equal(new DateTime(2024, 2, 1), result[0].ReminderDate); // Next cycle start
            Assert.Equal(new DateTime(2024, 2, 6), result[1].ReminderDate); // +5 days
            Assert.Equal(new DateTime(2024, 2, 16), result[2].ReminderDate); // +10 days
            Assert.All(result, schedule => Assert.Equal(1, schedule.ReminderConfigId));
            Assert.All(result, schedule => Assert.Equal(ReminderStatus.Pending, schedule.Status)); // Pending
            Assert.All(result, schedule => Assert.True(schedule.IsActive));
        }

        [Fact]
        public void CreateNextCycleSchedule_QuarterlyFrequency_ReturnsCorrectStartDate()
        {
            // Arrange
            var config = new EmailReminderConfig
            {
                Id = 2,
                ReminderId = Guid.NewGuid(),
                FrequencyType = 2, // Quarterly
                TotalRemindersPerCycle = 2,
                Remainder1Date = new DateTime(2024, 4, 1),
                Remainder2 = "7",
                Remainder3 = null,
                Remainder4 = null,
                Remainder5 = null
            };
            var userId = 123;

            // Act
            var result = EmailReminderHelper.CreateNextCycleSchedule(config, userId);

            // Assert
            Assert.Equal(2, result.Count);
            Assert.Equal(new DateTime(2024, 4, 1), result[0].ReminderDate); // +3 months
            Assert.Equal(new DateTime(2024, 4, 8), result[1].ReminderDate); // +7 days
        }

        [Fact]
        public void CreateNextCycleSchedule_YearlyFrequency_ReturnsCorrectStartDate()
        {
            // Arrange
            var config = new EmailReminderConfig
            {
                Id = 3,
                ReminderId = Guid.NewGuid(),
                FrequencyType = 3, // Yearly
                TotalRemindersPerCycle = 1,
                Remainder1Date = new DateTime(2025, 1, 1),
                Remainder2 = null,
                Remainder3 = null,
                Remainder4 = null,
                Remainder5 = null
            };
            var userId = 123;

            // Act
            var result = EmailReminderHelper.CreateNextCycleSchedule(config, userId);

            // Assert
            Assert.Single(result);
            Assert.Equal(new DateTime(2025, 1, 1), result[0].ReminderDate); // +1 year
        }

        [Fact]
        public void CreateNextCycleSchedule_NullConfig_ThrowsArgumentNullException()
        {
            // Arrange
            EmailReminderConfig? config = null;
            var userId = 123;

            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => EmailReminderHelper.CreateNextCycleSchedule(config, userId));
        }

        [Fact]
        public void ExtractUniqueIdsFromReminders_ValidData_ReturnsUniqueIds()
        {
            // Arrange
            var reminders = new List<TestReminder>
            {
                new TestReminder { EntityIds = "1,2,3" },
                new TestReminder { EntityIds = "2,3,4" },
                new TestReminder { EntityIds = "3,4,5" }
            };

            // Act
            var result = EmailReminderHelper.ExtractUniqueIdsFromReminders(reminders, r => r.EntityIds);

            // Assert
            Assert.Equal(5, result.Count);
            Assert.Equal(new List<int> { 1, 2, 3, 4, 5 }, result.OrderBy(x => x).ToList());
        }

        [Fact]
        public void ExtractUniqueIdsFromReminders_EmptyList_ReturnsEmptyList()
        {
            // Arrange
            var reminders = new List<TestReminder>();

            // Act
            var result = EmailReminderHelper.ExtractUniqueIdsFromReminders(reminders, r => r.EntityIds);

            // Assert
            Assert.Empty(result);
        }

        [Fact]
        public void IsStringMatch_CaseInsensitiveMatch_ReturnsTrue()
        {
            // Arrange
            var value = "Test";
            var target = "test";

            // Act
            var result = EmailReminderHelper.IsStringMatch(value, target);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public void IsStringMatch_ExactMatch_ReturnsTrue()
        {
            // Arrange
            var value = "test";
            var target = "test";

            // Act
            var result = EmailReminderHelper.IsStringMatch(value, target);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public void IsStringMatch_NoMatch_ReturnsFalse()
        {
            // Arrange
            var value = "test";
            var target = "different";

            // Act
            var result = EmailReminderHelper.IsStringMatch(value, target);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void IsStringMatch_NullValues_ReturnsFalse()
        {
            // Arrange
            string? value = null;
            string? target = null;

            // Act
            var result = EmailReminderHelper.IsStringMatch(value, target);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void IsStringMatch_OneNullValue_ReturnsFalse()
        {
            // Arrange
            string? value = null;
            var target = "test";

            // Act
            var result = EmailReminderHelper.IsStringMatch(value, target);

            // Assert
            Assert.False(result);
        }

        private class TestReminder
        {
            public string EntityIds { get; set; } = string.Empty;
        }
    }
}
