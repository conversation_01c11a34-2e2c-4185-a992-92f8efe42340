﻿using Contract.AccountType;
using Contract.Configuration;
using Contract.Currency;
using Contract.Deals;
using Contract.Filters;
using Contract.Firm;
using Contract.Investor;
using Contract.MasterMapping;
using Contract.PortfolioCompany;
using Contract.Sector;
using Contract.Strategy;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;

namespace Contract.Funds
{
    [ExcludeFromCodeCoverage]
    public class FundModel
    {
        public int FundID { get; set; }
        [MaxLength(250)]
        public string FundName { get; set; }
        public int? StrategyID { get; set; }
        public string StrategyDescription { get; set; }
        public int? SectorID { get; set; }
        public int? RegionID { get; set; }
        public int? CountryID { get; set; }
        public int? StateID { get; set; }
        public int? CityID { get; set; }
        public string VintageYear { get; set; }
        public int? AccountTypeID { get; set; }
        public int? CurrencyID { get; set; }
        public decimal? TargetCommitment { get; set; }
        public decimal? MaximumCommitment { get; set; }
        public decimal? FundSize { get; set; }
        public decimal? GPCommitment { get; set; }
        public decimal? PreferredReturnPercent { get; set; }
        public decimal? CarriedInterestPercent { get; set; }
        public decimal? GPCatchupPercent { get; set; }
        public decimal? ManagementFee { get; set; }
        public decimal? ManagementFeeOffset { get; set; }
        public string FundTerm { get; set; }
        public string MaximumExtensionToFundTerm { get; set; }
        public DateTime? FundClosingDate { get; set; }
        public decimal? OrgExpenses { get; set; }
        public string Clawback { get; set; }

        public string TargetCommitment_Comment { get; set; }
        public string MaximumCommitment_Comment { get; set; }
        public string FundSize_Comment { get; set; }
        public string GPCommitment_Comment { get; set; }
        public string PreferredReturnPercent_Comment { get; set; }
        public string CarriedInterestPercent_Comment { get; set; }
        public string GPCatchupPercent_Comment { get; set; }
        public string ManagementFee_Comment { get; set; }
        public string ManagementFeeOffset_Comment { get; set; }
        public string FundTerm_Comment { get; set; }
        public string MaximumExtensionToFundTerm_Comment { get; set; }
        public string FundClosingDate_Comment { get; set; }
        public string OrgExpenses_Comment { get; set; }
        public string Clawback_Comment { get; set; }
        public string VintageYear_Comment { get; set; }
        public DateTime CreatedOn { get; set; }
        public int CreatedBy { get; set; }
        public DateTime? ModifiedOn { get; set; }
        public int? ModifiedBy { get; set; }
        public bool IsDeleted { get; set; }
        public bool IsActive { get; set; }
        public LocationModel GeographyDetail { get; set; }
        public SectorModel SectorDetail { get; set; }
        public StrategyModel StrategyDetail { get; set; }
        public AccountTypeModel AccountTypeDetail { get; set; }
        public CurrencyModel CurrencyDetail { get; set; }
        [SwaggerExclude]
        public FirmModel FirmDetail { get; set; }
        [MaxLength(500)]
        public string EncryptedFundId { get; set; }
        [SwaggerExclude]
        public List<FundTrackRecordModel> FundTrackRecordList { get; set; }
        public List<DealModel> DealList { get; set; }
        public List<FundInvestorListModel> investorListData { get; set; }
        public List<PageFieldValueModel> CustomFieldValueList { get; set; }
    }
    [ExcludeFromCodeCoverage]
    public class FundUploadModel
    {
        public int FundID { get; set; }
        public string FundName { get; set; }
        public string EncryptedFundId { get; set; }
        public int StrategyID { get; set; }
    }

    public class FundNameIdList
    {
        public List<FundUploadModel> FundList
        {
            get;
            set;
        }
    }
    [ExcludeFromCodeCoverage]
    public class FundPCModel
    {
        public string FundName { get; set; }
    }
    [ExcludeFromCodeCoverage]
    public class FundQueryModel
    {
        public int FundID { get; set; }
        public string FundName { get; set; }
        public string EncryptedFundId { get; set; }
        public int? SectorID { get; set; }
        public string VintageYear { get; set; }
        public SectorQueryModel SectorDetail { get; set; }
    }
    [ExcludeFromCodeCoverage]
    public class FundsUploadModel
    {
        public int? FundID { get; set; }
        public int? StrategyID { get; set; }
        public string StrategyDescription { get; set; }
        public int? SectorID { get; set; }
        public int? RegionID { get; set; }
        public int? CountryID { get; set; }
        public int? StateID { get; set; }
        public int? CityID { get; set; }
        public string VintageYear { get; set; }
        public int? AccountTypeID { get; set; }
        public int? CurrencyID { get; set; }
        public decimal? TargetCommitment { get; set; }
        public decimal? MaximumCommitment { get; set; }
        public decimal? FundSize { get; set; }
        public decimal? GPCommitment { get; set; }
        public decimal? PreferredReturnPercent { get; set; }
        public decimal? CarriedInterestPercent { get; set; }
        public decimal? GPCatchupPercent { get; set; }
        public decimal? ManagementFee { get; set; }
        public decimal? ManagementFeeOffset { get; set; }
        public string FundTerm { get; set; }
        public string MaximumExtensionToFundTerm { get; set; }
        public DateTime? FundClosingDate { get; set; }
        public decimal? OrgExpenses { get; set; }
        public string Clawback { get; set; }

        public string TargetCommitment_Comment { get; set; }
        public string MaximumCommitment_Comment { get; set; }
        public string FundSize_Comment { get; set; }
        public string GPCommitment_Comment { get; set; }
        public string PreferredReturnPercent_Comment { get; set; }
        public string CarriedInterestPercent_Comment { get; set; }
        public string GPCatchupPercent_Comment { get; set; }
        public string ManagementFee_Comment { get; set; }
        public string ManagementFeeOffset_Comment { get; set; }
        public string FundTerm_Comment { get; set; }
        public string MaximumExtensionToFundTerm_Comment { get; set; }
        public string FundClosingDate_Comment { get; set; }
        public string OrgExpenses_Comment { get; set; }
        public string Clawback_Comment { get; set; }
        public string VintageYear_Comment { get; set; }
        public int? FirmID { get; set; }
        public string Quarter { get; set; }
        public int Year { get; set; }
        public int? TotalNumberOfInvestments { get; set; }
        public decimal? RealizedInvestments { get; set; }
        public decimal? UnRealizedInvestments { get; set; }
        public decimal? TotalInvestedCost { get; set; }
        public decimal? TotalRealizedValue { get; set; }
        public decimal? TotalUnRealizedValue { get; set; }
        public decimal? TotalValue { get; set; }
        public decimal? GrossMultiple { get; set; }
        public decimal? NetMultiple { get; set; }
        public decimal? GrossIRR { get; set; }
        public decimal? NetIRR { get; set; }
        public decimal? DPI { get; set; }
        public decimal? RVPI { get; set; }
    }
    [ExcludeFromCodeCoverage]
    public class FundsQueryModel
    {
        public int? FundID { get; set; }
        public int? StrategyID { get; set; }
        public string StrategyDescription { get; set; }
        public int? SectorID { get; set; }
        public int? RegionID { get; set; }
        public int? CountryID { get; set; }
        public int? StateID { get; set; }
        public int? CityID { get; set; }
        public string VintageYear { get; set; }
        public int? AccountTypeID { get; set; }
        public int? CurrencyID { get; set; }
        public decimal? TargetCommitment { get; set; }
        public decimal? MaximumCommitment { get; set; }
        public decimal? FundSize { get; set; }
        public decimal? GPCommitment { get; set; }
        public decimal? PreferredReturnPercent { get; set; }
        public decimal? CarriedInterestPercent { get; set; }
        public decimal? GPCatchupPercent { get; set; }
        public decimal? ManagementFee { get; set; }
        public decimal? ManagementFeeOffset { get; set; }
        public string FundTerm { get; set; }
        public string MaximumExtensionToFundTerm { get; set; }
        public DateTime? FundClosingDate { get; set; }
        public decimal? OrgExpenses { get; set; }
        public string Clawback { get; set; }
        public string FundName { get; set; }
        public string Sector { get; set; }
        public string Strategy { get; set; }
        public string AccountType { get; set; }
        public string Currency { get; set; }
        public string CurrencyCode { get; set; }
        public string EncryptedFundId { get; set; }
        public string FirmName { get; set; }
        public string EncryptedFirmID { get; set; }
        public string Website { get; set; }
        public string BusinessDescription { get; set; }
        public int? FirmID { get; set; }
    }
    public class FundNameModel
    {
        public string FundName { get; set; }
        public int FundId { get; set; }
        public string EncryptedFundId { get; set; }
    }
    public class FundIngestionFilter
    {
        public int FundDetailsId { get; set; }
        public int? FundIngestionId { get; set; }
        public string EncryptedFundDetailsID { get; set; }
        public string EncryptedFundIngestionId { get; set; }
        public int Year { get; set; }
        public string Quarter { get; set; }
    }
}
