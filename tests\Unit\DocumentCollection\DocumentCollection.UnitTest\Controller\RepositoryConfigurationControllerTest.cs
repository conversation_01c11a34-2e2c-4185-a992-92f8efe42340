using API.Controllers.DocumentCollection;
using API.Helpers;
using DocumentCollection.DTOs;
using DocumentCollection.Interfaces;
using DocumentCollection.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;

namespace Controller
{
    public class RepositoryConfigurationControllerTest
    {
        private readonly Mock<IRepositoryConfigurationService> _mockService;
        private readonly Mock<ILogger<RepositoryConfigurationController>> _mockLogger;
        private readonly Mock<IHelperService> _mockHelperService;
        private readonly RepositoryConfigurationController _controller;

        public RepositoryConfigurationControllerTest()
        {
            _mockService = new Mock<IRepositoryConfigurationService>();
            _mockLogger = new Mock<ILogger<RepositoryConfigurationController>>();
            _mockHelperService = new Mock<IHelperService>();
            _controller = new RepositoryConfigurationController(_mockService.Object, _mockLogger.Object, _mockHelperService.Object);
        }

        [Fact]
        public async Task GetRepositoryFreqencyConfig_ValidCompanyId_ReturnsOkResult()
        {
            // Arrange
            int entityId = 1, featureId = 13;
            var responseDto = new ResponseDto<List<DocumentConfigurationDto>> { IsSuccess = true, Data = new List<DocumentConfigurationDto>() };
            _mockService.Setup(s => s.GetRepositoryConfigurationByCompany(entityId, featureId)).ReturnsAsync(responseDto);

            // Act
            var result = await _controller.GetRepositoryFrequencyConfig(entityId, featureId);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.Equal(responseDto, okResult.Value);
        }

        [Fact]
        public async Task GetRepositoryFreqencyConfig_InvalidCompanyId_ReturnsBadRequest()
        {
            // Arrange
            int companyId = 0, featureId = 14;

            // Act
            var result = await _controller.GetRepositoryFrequencyConfig(companyId, featureId);

            // Assert
            Assert.IsType<BadRequestResult>(result);
        }

        [Fact]
        public async Task AddorUpdateRepositoryFreqencyConfig_ValidConfigModel_ReturnsOkResult()
        {
            // Arrange
            var configModel = new DocumentConfigurationModel
            {
                Ids = new List<int> { 1 },
                Configuration = new List<FolderFrequencyConfigModel>()
            };
            var responseDto = new ResponseDto<List<DocumentConfigurationDto>> { IsSuccess = true };
            _mockService.Setup(s => s.UpdateRepositoryConfigurations(configModel, It.IsAny<int>())).ReturnsAsync(responseDto);

            // Act
            var result = await _controller.AddorUpdateRepositoryFreqencyConfig(configModel);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.Equal(responseDto, okResult.Value);
        }

        [Fact]
        public async Task AddorUpdateRepositoryFreqencyConfig_InvalidConfigModel_ReturnsBadRequest()
        {
            // Arrange
            DocumentConfigurationModel? configModel = null;

            // Act
            var result = await _controller.AddorUpdateRepositoryFreqencyConfig(configModel);

            // Assert
            Assert.IsType<BadRequestResult>(result);
        }


        [Fact]
        public async Task GetRepositoryFreqencyConfigByCompanies_InvalidCompanyIds_ReturnsBadRequest()
        {
            // Arrange
            int[]? entityIds = null;
            int featureId = 13;

            // Act
            var result = await _controller.GetRepositoryFreqencyConfigByCompanies(featureId, entityIds);

            // Assert
            Assert.IsType<BadRequestResult>(result);
        }

        [Fact]
        public async Task GetPortfolioCompanyList_ShouldReturnOkResult()
        {
            // Arrange
            var userId = 1;
            var companies = new List<PortfolioCompanyModel>
            {
                new PortfolioCompanyModel { PortfolioCompanyID = 1, CompanyName = "Company A" },
                new PortfolioCompanyModel { PortfolioCompanyID = 2, CompanyName = "Company B" }
            };

            _mockHelperService.Setup(helper => helper.GetCurrentUserId(It.IsAny<System.Security.Claims.ClaimsPrincipal>())).Returns(userId);
            _mockService.Setup(service => service.GetPortfolioCompaniesList(userId)).ReturnsAsync(companies);

            // Act
            var result = await _controller.GetPortfolioCompanyList();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnedCompanies = Assert.IsType<List<PortfolioCompanyModel>>(okResult.Value);
            Assert.Equal(2, returnedCompanies.Count);
            Assert.Contains(returnedCompanies, c => c.PortfolioCompanyID == 1 && c.CompanyName == "Company A");
            Assert.Contains(returnedCompanies, c => c.PortfolioCompanyID == 2 && c.CompanyName == "Company B");
        }

        [Fact]
        public async Task GetFundList_ShouldReturnOkResult()
        {
            // Arrange
            var userId = 1;
            var funds = new List<FundDataModel>
            {
                new FundDataModel { FundID = 1, FundName = "Fund A" },
                new FundDataModel { FundID = 2, FundName = "Fund B" }
            };
            _mockHelperService.Setup(helper => helper.GetCurrentUserId(It.IsAny<System.Security.Claims.ClaimsPrincipal>())).Returns(userId);
            _mockService.Setup(service => service.GetFundList(userId)).ReturnsAsync(funds);

            // Act
            var result = await _controller.GetFundList();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnedFunds = Assert.IsType<List<FundDataModel>>(okResult.Value);
            Assert.Equal(2, returnedFunds.Count);
            Assert.Contains(returnedFunds, f => f.FundID == 1 && f.FundName == "Fund A");
            Assert.Contains(returnedFunds, f => f.FundID == 2 && f.FundName == "Fund B");
        }

        [Fact]
        public async Task GetFundList_ShouldReturnOkResult_WithEmptyList()
        {
            // Arrange
            var userId = 1;
            var funds = new List<FundDataModel>();
            _mockHelperService.Setup(helper => helper.GetCurrentUserId(It.IsAny<System.Security.Claims.ClaimsPrincipal>())).Returns(userId);
            _mockService.Setup(service => service.GetFundList(userId)).ReturnsAsync(funds);

            // Act
            var result = await _controller.GetFundList();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnedFunds = Assert.IsType<List<FundDataModel>>(okResult.Value);
            Assert.Empty(returnedFunds);
        }
    }
}