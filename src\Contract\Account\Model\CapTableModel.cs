using System.ComponentModel.DataAnnotations;

namespace Contract.Account
{
    public class CapTableModel
    {
        public int KpiId { get; set; }
        [Required]
        public int ModuleId { get; set; }
        [Required]
        public string Kpi { get; set; }
        [Required]
        public string KpiInfo { get; set; }
        public string KpiInfoType { get; set; }
        public string Description { get; set; }
        public int MethodologyID { get; set; }
        [Required]
        public int KpiTypeId { get; set; } = 0;
        public bool IsBoldKpi { get; set; } = false;
        public bool IsHeader { get; set; } = false;
        public string Formula { get; set; }
        public string FormulaKPIId { get; set; }
        public string MethodologyName { get; set; }
        public string KpiType { get; set; }
        public bool IsOverrule { get; set; } = false;
        public string Synonym { get; set; }
    }
    public class KpiTypeModel
    {
        public int KpiTypeId { get; set; } = 0;
        public string KpiType { get; set; }
    }
}