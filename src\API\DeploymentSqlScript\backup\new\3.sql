IF NOT EXISTS(Select * from M_Features Where Feature='Repository Configuration' )
BEGIN
INSERT [dbo].[M_Features] ([FeatureID], [Feature], [ParentID], [Description], [IsActive], [IsDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedFeatureID], [SequenceNo], [AliasName], [FeaturePath])
VALUES (61, N'Repository Configuration', 22, NULL, 1, 0,  GETDATE(), 3, NULL, NULL, NULL, 36, N'Repository Configuration', N'/repository-configuration')
END
GO
IF (NOT EXISTS (SELECT *
                 FROM [dbo].[Mapping_FeatureAction]
                 WHERE [FeatureID] = 61
                ))
BEGIN
    INSERT [dbo].[Mapping_FeatureAction] ([FeatureID], [ActionID], [IsEnabled], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedFeatureActionMappingID]) VALUES (61, 1, 1,  GETDATE(), 1, NULL, NULL, NULL)
    INSERT [dbo].[Mapping_FeatureAction] ([FeatureID], [ActionID], [IsEnabled], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedFeatureActionMappingID]) VALUES (61, 2, 1,  GETDATE(), 1, NULL, NULL, NULL)
    INSERT [dbo].[Mapping_FeatureAction] ([FeatureID], [ActionID], [IsEnabled], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedFeatureActionMappingID]) VALUES (61, 3, 1,  GETDATE(), 1, NULL, NULL, NULL)
	INSERT [dbo].[Mapping_FeatureAction] ([FeatureID], [ActionID], [IsEnabled], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedFeatureActionMappingID]) VALUES (61, 4, 1,  GETDATE(), 1, NULL, NULL, NULL)
    INSERT [dbo].[Mapping_FeatureAction] ([FeatureID], [ActionID], [IsEnabled], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedFeatureActionMappingID]) VALUES (61, 5, 1,  GETDATE(), 1, NULL, NULL, NULL)

END
GO
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='MDocFrequencyTypes' AND xtype='U')
BEGIN
CREATE TABLE MDocFrequencyTypes
(
    ID INT PRIMARY KEY,
    Name VARCHAR(50) NOT NULL,
    CreatedDate DATETIME DEFAULT GETDATE(),
    CreatedBy INT NULL,
    ModifiedDate DATETIME NULL,
    ModifiedBy INT NULL,
    IsActive BIT DEFAULT 1,
    IsDeleted BIT DEFAULT 0
);
End
GO
IF NOT EXISTS (SELECT * FROM MDocFrequencyTypes WHERE  Name IN('None','Annual','Quarter','Month'))
BEGIN
INSERT INTO MDocFrequencyTypes (ID, Name) VALUES
(1, 'None'),
(2, 'Annual'),
(3, 'Quarter'),
(4, 'Month');

End
GO
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='DocFrequencyConfiguration' AND xtype='U')
BEGIN
CREATE TABLE DocFrequencyConfiguration (
    ID INT PRIMARY KEY IDENTITY(1,1), -- Assuming SQL Server; adjust for other databases
    FeatureID INT NOT NULL,
    EntityID INT NOT NULL, -- Consider INT to store Feature specific Entity ID ex : PortfolioCompanyID, FundId etc.
    DocTypeID INT NOT NULL,
    FrequencyType INT NULL,
    [From] VARCHAR(50),
    [To] VARCHAR(50),
    [CreatedOn] datetime DEFAULT GETDATE(),
	[CreatedBy] [int] NOT NULL,
	[ModifiedOn] [datetime] NULL,
	[ModifiedBy] [int] NULL,
    IsActive BIT DEFAULT 1,
    IsDeleted BIT DEFAULT 0
);
End
GO

IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'RepositoryDocumentMappingDetails')
BEGIN
CREATE TABLE RepositoryDocumentMappingDetails (
    [ID] UNIQUEIDENTIFIER PRIMARY KEY NOT NULL,
    [FeatureId] INT NOT NULL,
    [EntityId] INT NOT NULL,
    [DocTypeID] INT NOT NULL,
	[Quarter] Varchar(50) NULL,
	[Month] Varchar(50) NULL,
	[Year] INT NULL,
    [CreatedOn] DATETIME NOT NULL DEFAULT GETUTCDATE(),
    [CreatedBy] INT NOT NULL,
    [ModifiedOn] DATETIME NULL,
    [ModifiedBy] INT NULL,
    [IsDeleted] BIT NOT NULL DEFAULT 0
);
END

GO
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'DocumentCollectionStore')
BEGIN
    CREATE TABLE DocumentCollectionStore (
        [ID] UNIQUEIDENTIFIER PRIMARY KEY NOT NULL,
        [SourceTypeId] INT NOT NULL, -- local 1 , external resource enum numbers will be added later here
        [S3Path] NVARCHAR(MAX) NOT NULL,
        [FileName] NVARCHAR(255) NOT NULL,
        [Type] NVARCHAR(100) NOT NULL,
        [FolderMappingId] UNIQUEIDENTIFIER NOT NULL,
        [CreatedOn] DATETIME NOT NULL DEFAULT GETUTCDATE(),
        [CreatedBy] INT NOT NULL,
        [ModifiedOn] DATETIME NULL,
        [ModifiedBy] INT NULL,
        [IsDeleted] BIT NOT NULL DEFAULT 0
    )
END
GO
IF NOT EXISTS (SELECT * FROM M_SubFeature WHERE SubFeature='Documents')
BEGIN
	INSERT [dbo].[M_SubFeature] ([SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
	[ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName])
	VALUES (69, N'Documents', 14, NULL, 1, 0, getdate(),49, NULL, NULL, NULL, 0, 0, N'Documents')
END
GO
IF NOT EXISTS(SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 69)
BEGIN
BEGIN
INSERT INTO [dbo].[Mapping_SubFeatureAction]([SubFeatureID],[ActionID],[IsDeleted],[CreatedOn],[CreatedBy])
VALUES(69,2,0,GETUTCDATE(),1)
END
BEGIN
INSERT INTO [dbo].[Mapping_SubFeatureAction]([SubFeatureID],[ActionID],[IsDeleted],[CreatedOn],[CreatedBy])
VALUES(69,3,0,GETUTCDATE(),1)
END
END
GO
IF (NOT EXISTS (SELECT *
                 FROM [dbo].[M_SubPageDetails]
				 where Name='Documents'
                ))

BEGIN
INSERT [dbo].[M_SubPageDetails] ([Name], [AliasName], [PageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [IsDynamicFieldSupported])
VALUES (N'Documents', N'Documents', 1, NULL, 1, 0, getdate(), 1, NULL, NULL, NULL, 11, NULL, 0, 0)
END
GO
GO
--===========END========================================================================================

IF OBJECT_ID('[dbo].[SpGetSubFeatureListByPageconfig]', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE [dbo].[SpGetSubFeatureListByPageconfig]
END
GO
CREATE PROCEDURE [dbo].[SpGetSubFeatureListByPageconfig] ( @FeatureId INT ) AS
BEGIN   IF (@FeatureId = 14)
BEGIN
SELECT       f.SubFeatureID,      f.SubFeature As SubFeatureName, COALESCE(s.AliasName, sf.AliasName) AS SubFeatureAliasName,
 f.isDeleted AS IsDeleted, f.ParentFeatureID AS ParentFeatureId FROM M_SubFeature f
 LEFT JOIN M_SubPageDetails s ON f.PageConfigName = s.Name AND s.SubPageID IN (1, 4,44) AND s.isDeleted = 0
 LEFT JOIN M_SubPageFields sf ON f.PageConfigName = sf.Name AND sf.SubPageID IN (2, 3, 38, 41) AND sf.isDeleted = 0
 WHERE       (s.SubPageID IS NOT NULL OR sf.SubPageID IS NOT NULL)
END

 ELSE IF(@FeatureId = 13)
 BEGIN
 SELECT       f.SubFeatureID,      f.SubFeature As SubFeatureName,
   COALESCE(s.AliasName, s.AliasName) AS SubFeatureAliasName, f.isDeleted AS IsDeleted, ParentFeatureId
   FROM       M_SubFeature f     LEFT JOIN M_SubPageDetails s ON f.PageConfigName = s.Name AND s.SubPageID IN (7,8,9,15,12) AND s.isDeleted = 0
   WHERE s.SubPageID IS NOT NULL and ParentFeatureID = 13
   END
 ELSE IF(@FeatureId = 15)
 BEGIN
 SELECT f.SubFeatureID, f.SubFeature As SubFeatureName, COALESCE(s.AliasName, s.AliasName) AS SubFeatureAliasName, f.isDeleted AS IsDeleted,
 f.ParentFeatureID AS ParentFeatureId FROM       M_SubFeature f
 LEFT JOIN M_SubPageDetails s ON f.PageConfigName = s.Name AND s.SubPageID IN (5, 6) AND s.isDeleted = 0
 WHERE s.SubPageID IS NOT NULL
 END
 ELSE IF(@FeatureId = 50)
 BEGIN
 SELECT  f.SubFeatureID, f.SubFeature As SubFeatureName, COALESCE(s.AliasName, sf.AliasName) AS SubFeatureAliasName, f.isDeleted AS IsDeleted,
 f.ParentFeatureID AS ParentFeatureId     FROM       M_SubFeature f
 LEFT JOIN M_SubPageDetails s ON f.PageConfigName = s.Name AND s.SubPageID IN (19) AND s.isDeleted = 0
 LEFT JOIN M_SubPageFields sf ON f.PageConfigName = sf.Name AND sf.SubPageID IN (19) AND sf.isDeleted = 0
 WHERE       (s.SubPageID IS NOT NULL OR sf.SubPageID IS NOT NULL) and  ParentFeatureID = 50
 END
 ELSE IF(@FeatureId = 42)
 BEGIN
 SELECT f.SubFeatureID, f.SubFeature As SubFeatureName, COALESCE(s.AliasName, s.AliasName) AS SubFeatureAliasName, f.isDeleted AS IsDeleted,
 f.ParentFeatureID AS ParentFeatureId FROM   M_SubFeature f
 LEFT JOIN M_SubPageDetails s ON f.PageConfigName = s.Name AND s.SubPageID IN (13,14,15,16,17,18) AND s.isDeleted = 0
 WHERE s.SubPageID IS NOT NULL  AND s.SubPageID IN (13,14,15,16,17,18) AND ParentFeatureID = 42

UNION ALL

SELECT    f.SubFeatureID,      f.SubFeature As SubFeatureName,COALESCE(f.SubFeature, f.SubFeature) AS SubFeatureAliasName,f.isDeleted AS IsDeleted,f.ParentFeatureID AS ParentFeatureId     FROM M_SubFeature f
WHERE ParentFeatureID = 42 AND F.isDeleted=0 AND PageConfigName IS NULL
END
ELSE IF(@FeatureId = 19)
BEGIN
SELECT       SubFeatureID,      SubFeature As SubFeatureName, COALESCE(PageConfigName, PageConfigName) AS SubFeatureAliasName,isDeleted AS IsDeleted,
ParentFeatureID AS ParentFeatureId  FROM  M_SubFeature
WHERE  SubFeatureID  BETWEEN 33 AND 48
END
ELSE IF (@FeatureId = 57)
		BEGIN
			 SELECT
					SubFeatureID,
					SubFeature As SubFeatureName,
					COALESCE(PageConfigName, PageConfigName) AS SubFeatureAliasName,
					isDeleted AS IsDeleted,
					ParentFeatureID AS ParentFeatureId
				FROM
					M_SubFeature
				WHERE  SubFeatureID  BETWEEN 71 AND 81
		END
		ELSE IF (@FeatureId = 58)
		BEGIN
			 SELECT
					SubFeatureID,
					SubFeature As SubFeatureName,
					COALESCE(PageConfigName, PageConfigName) AS SubFeatureAliasName,
					isDeleted AS IsDeleted,
					ParentFeatureID AS ParentFeatureId
				FROM
					M_SubFeature
				WHERE  SubFeatureID  BETWEEN 82 AND 92
		END
END
GO

-- Columns for SubPageID
DECLARE @SubPageID INT = (SELECT top 1 SubPageID FROM [dbo].[M_SubPageDetails] where Name='Documents');
IF (EXISTS (SELECT top 1 SubPageID
                 FROM [dbo].[M_SubPageDetails]
				 where SubPageID=@SubPageID
                ))
BEGIN
IF NOT EXISTS (SELECT top 1 FieldID FROM M_SubPageFields WHERE Name='All Folders')
BEGIN
INSERT [dbo].[M_SubPageFields] ( [Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom],[ShowOnList])
VALUES ( N'All Folders', N'All Folders', @SubPageID, NULL, 1, 0, getdate(), 1, NULL, NULL, NULL, 1, NULL, 0,0)
END
IF NOT EXISTS (SELECT top 1 FieldID FROM M_SubPageFields WHERE Name='Documents List')
BEGIN
INSERT [dbo].[M_SubPageFields] ( [Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom],[ShowOnList])
VALUES ( N'Documents List', N'Documents List', @SubPageID, NULL, 1, 0, getdate(), 1, NULL, NULL, NULL, 1, NULL, 0,0)
END
END
GO
IF (EXISTS (SELECT top 1 SubPageID
                 FROM [dbo].[M_SubPageDetails]
				 where Name='Documents'
                ))
BEGIN
update M_SubPageDetails set [IsDynamicFieldSupported]=0,SequenceNo=11 where Name='Documents'
END
GO



IF OBJECT_ID('[dbo].[ProcDeleteCloFootnoteTable]', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE [dbo].[ProcDeleteCloFootnoteTable]
END
GO
CREATE PROCEDURE [dbo].[ProcDeleteCloFootnoteTable]
 (
 @FootnoteIdentifier varchar(100),
 @FootnoteMapping nvarchar(500)=null
 )
AS
BEGIN
IF EXISTS (SELECT COUNT(1) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'CLO_TableFootnotes')
BEGIN

IF @FootnoteMapping is null
BEGIN
DELETE FROM CLO_TableFootnotes WHERE FootnoteIdentifier = @FootnoteIdentifier
END
ELSE
BEGIN
DELETE FROM CLO_TableFootnotes WHERE FootnoteIdentifier = @FootnoteIdentifier AND FootnoteMapping=@FootnoteMapping
END
END
END
GO

-------------------menu changes(rearrangement for all the parent menus)-----------------


DECLARE @InvestmentFeatureID INT = (select top 1 FeatureID from M_Features where Feature='Investment Company');
DECLARE @CLOFeatureID INT = (select top 1 FeatureID from M_Features where Feature='CLO Page');
DECLARE @ReportFeatureID INT =(select top 1 FeatureID from M_Features where Feature='Report');

DECLARE @DashboardFeatureID INT = (select top 1 FeatureID from M_Features where Feature='Dashboard');
DECLARE @PortfolioCompanyFeatureID INT = (select top 1 FeatureID from M_Features where Feature='Portfolio Company');
DECLARE @FundFeatureID INT =(select top 1 FeatureID from M_Features where Feature='Fund');

DECLARE @DealFeatureID INT = (select top 1 FeatureID from M_Features where Feature='Deal');
DECLARE @InvestorsFeatureID INT = (select top 1 FeatureID from M_Features where Feature='Investors');
DECLARE @FirmFeatureID INT =(select top 1 FeatureID from M_Features where Feature='Firm');

DECLARE @RepositoryFeatureID INT = (select top 1 FeatureID from M_Features where Feature='Repository');
DECLARE @ValuationModelFeatureID INT = (select top 1 FeatureID from M_Features where Feature='ValuationModel');
DECLARE @ESGFeatureID INT =(select top 1 FeatureID from M_Features where Feature='ESG');

DECLARE @DataIngestionFeatureID INT = (select top 1 FeatureID from M_Features where Feature='DataIngestion');
DECLARE @AdminFeatureID INT = (select top 1 FeatureID from M_Features where Feature='Admin');
DECLARE @CashflowFeatureID INT = (select top 1 FeatureID from M_Features where Feature='Cashflow');
DECLARE @FxRatesFeatureID INT =(select top 1 FeatureID from M_Features where Feature='FxRates');

DECLARE @PipelineFeatureID INT = (select top 1 FeatureID from M_Features where Feature='Pipeline');

DECLARE @DataCollectionFeatureID INT =(select top 1 FeatureID from M_Features where Feature='DataCollection');

IF (EXISTS (select top 1 FeatureID from M_Features
				 where FeatureID=@DashboardFeatureID
                )) AND (select top 1 SequenceNo from M_Features where Feature='Dashboard')!=1
BEGIN
update M_Features set SequenceNo=1 where FeatureID=@DashboardFeatureID
END
IF (EXISTS (select top 1 FeatureID from M_Features
				 where FeatureID=@PortfolioCompanyFeatureID
                )) AND (select top 1 SequenceNo from M_Features where Feature='Portfolio Company')!=2
BEGIN
update M_Features set SequenceNo=2 where FeatureID=@PortfolioCompanyFeatureID
END
IF (EXISTS (select top 1 FeatureID from M_Features
				 where FeatureID=@FundFeatureID
                )) AND (select top 1 SequenceNo from M_Features where Feature='Fund')!=3
BEGIN
update M_Features set SequenceNo=3 where FeatureID=@FundFeatureID
END
IF (EXISTS (select top 1 FeatureID from M_Features
				 where FeatureID=@DealFeatureID
                )) AND (select top 1 SequenceNo from M_Features where Feature='Deal')!=4
BEGIN
update M_Features set SequenceNo=4 where FeatureID=@DealFeatureID
END
IF (EXISTS (select top 1 FeatureID from M_Features
				 where FeatureID=@InvestorsFeatureID
                )) AND (select top 1 SequenceNo from M_Features where Feature='Investors')!=5
BEGIN
update M_Features set SequenceNo=5 where FeatureID=@InvestorsFeatureID
END
IF (EXISTS (select top 1 FeatureID from M_Features
				 where FeatureID=@FirmFeatureID
                )) AND (select top 1 SequenceNo from M_Features where Feature='Firm')!=6
BEGIN
update M_Features set SequenceNo=6 where FeatureID=@FirmFeatureID
END
IF (EXISTS (select top 1 FeatureID from M_Features
				 where FeatureID=@InvestmentFeatureID
                )) AND (select top 1 SequenceNo from M_Features where Feature='Investment Company')!=7
BEGIN
update M_Features set SequenceNo=7 where FeatureID=@InvestmentFeatureID
END
IF (EXISTS (select top 1 FeatureID from M_Features
				 where FeatureID=@CLOFeatureID
                )) AND (select top 1 SequenceNo from M_Features where Feature='CLO Page')!=8
BEGIN
update M_Features set SequenceNo=8 where FeatureID=@CLOFeatureID
END
IF (EXISTS (select top 1 FeatureID from M_Features
				 where FeatureID=@ReportFeatureID
                )) AND (select top 1 SequenceNo from M_Features where Feature='Report')!=9
BEGIN
update M_Features set SequenceNo=9 where FeatureID=@ReportFeatureID
END
IF (EXISTS (select top 1 FeatureID from M_Features
				 where FeatureID=@RepositoryFeatureID
                )) AND (select top 1 SequenceNo from M_Features where Feature='Repository')!=10
BEGIN
update M_Features set SequenceNo=10 where FeatureID=@RepositoryFeatureID
END
IF (EXISTS (select top 1 FeatureID from M_Features
				 where FeatureID=@ValuationModelFeatureID
                )) AND (select top 1 SequenceNo from M_Features where Feature='ValuationModel')!=11
BEGIN
update M_Features set SequenceNo=11 where FeatureID=@ValuationModelFeatureID
END
IF (EXISTS (select top 1 FeatureID from M_Features
				 where FeatureID=@ESGFeatureID
                )) AND (select top 1 SequenceNo from M_Features where Feature='ESG')!=12
BEGIN
update M_Features set SequenceNo=12 where FeatureID=@ESGFeatureID
END
IF (EXISTS (select top 1 FeatureID from M_Features
				 where FeatureID=@DataIngestionFeatureID
                )) AND (select top 1 SequenceNo from M_Features where Feature='DataIngestion')!=13
BEGIN
update M_Features set SequenceNo=13 where FeatureID=@DataIngestionFeatureID
END
IF (EXISTS (select top 1 FeatureID from M_Features
				 where FeatureID=@AdminFeatureID
                )) AND (select top 1 SequenceNo from M_Features where Feature='Admin')!=14
BEGIN
update M_Features set SequenceNo=14 where FeatureID=@AdminFeatureID
END
IF (EXISTS (select top 1 FeatureID from M_Features
				 where FeatureID=@CashflowFeatureID
                )) AND (select top 1 SequenceNo from M_Features where Feature='Cashflow')!=15
BEGIN
update M_Features set SequenceNo=15 where FeatureID=@CashflowFeatureID
END
IF (EXISTS (select top 1 FeatureID from M_Features
				 where FeatureID=@FxRatesFeatureID
                )) AND (select top 1 SequenceNo from M_Features where Feature='FxRates')!=16
BEGIN
update M_Features set SequenceNo=16 where FeatureID=@FxRatesFeatureID
END
IF (EXISTS (select top 1 FeatureID from M_Features
				 where FeatureID=@PipelineFeatureID
                )) AND (select top 1 SequenceNo from M_Features where Feature='Pipeline')!=17
BEGIN
update M_Features set SequenceNo=17 where FeatureID=@PipelineFeatureID
END
IF (EXISTS (select top 1 FeatureID from M_Features
				 where FeatureID=@DataCollectionFeatureID
                )) AND (select top 1 SequenceNo from M_Features where Feature='DataCollection')!=18
BEGIN
update M_Features set SequenceNo=18 where FeatureID=@DataCollectionFeatureID
END
GO
-------------------menu changes-----------------
-----------------------BFB-10153----------
IF NOT EXISTS (
    SELECT *
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'CLO_CloDetails'
    AND COLUMN_NAME = 'LastRefiResetArranger'
) and EXISTS (SELECT * FROM sysobjects WHERE name='CLO_CloDetails'  AND xtype='U')
BEGIN
    ALTER TABLE CLO_CloDetails
	ADD LastRefiResetArranger nvarchar(100) NULL
END
GO
-----------------------BFB-10153----------

IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'M_UserCategory')
BEGIN
    CREATE TABLE [dbo].[M_UserCategory] (
         CategoryID INTEGER IDENTITY(1,1) PRIMARY KEY
        ,Category NVARCHAR(500)
        ,CreatedOn DATETIME
        ,CreatedBy INTEGER
        ,ModifiedOn DATETIME
        ,ModifiedBy INTEGER
        ,IsDeleted BIT
        ,IsActive BIT
    );
END
GO

IF NOT EXISTS (SELECT * FROM [dbo].[M_UserCategory] WHERE [Category] = 'Acuity Analyst')
BEGIN
    INSERT INTO [dbo].[M_UserCategory] (
        Category, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted, IsActive
    ) VALUES (
        'Acuity Analyst', GETDATE(), 1, NULL, NULL, 0, 1
    );
END
GO

IF NOT EXISTS (SELECT * FROM [dbo].[M_UserCategory] WHERE [Category] = 'Deal Team Member')
BEGIN
    INSERT INTO [dbo].[M_UserCategory] (
        Category, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted, IsActive
    ) VALUES (
        'Deal Team Member', GETDATE(), 1, NULL, NULL, 0, 1
    );
END
GO


IF NOT EXISTS (SELECT * FROM [dbo].[M_UserCategory] WHERE [Category] = 'Portfolio Company Member')
BEGIN
    INSERT INTO [dbo].[M_UserCategory] (
        Category, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted, IsActive
    ) VALUES (
        'Portfolio Company Member', GETDATE(), 1, NULL, NULL, 0, 1
    );
END
GO

IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'UserInformation')
BEGIN
    CREATE TABLE [dbo].[UserInformation] (
        UserInformationID INT IDENTITY(1,1) PRIMARY KEY,
        Name VARCHAR(100),
        Email VARCHAR(100),
        CategoryID INT NOT NULL,
        FeatureId INT,
        EntityID INT,
        CreatedOn DATETIME,
        CreatedBy INT,
        ModifiedOn DATETIME,
        ModifiedBy INT,
        IsDeleted BIT,
        IsActive BIT,
        EncryptedUserInfoID NVARCHAR(200),
        CONSTRAINT FK_UserInformation_CategoryID FOREIGN KEY (CategoryID) REFERENCES M_UserCategory(CategoryID)
    );
END
GO

IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'User_Documents')
BEGIN
    CREATE TABLE [dbo].[User_Documents] (
        UserDocumentID INT IDENTITY(1,1) PRIMARY KEY,
        UserInformationID INT NOT NULL,
        DocumentTypeID INT NOT NULL,
        CreatedOn DATETIME,
        CreatedBy INT,
        ModifiedOn DATETIME,
        ModifiedBy INT,
        CONSTRAINT FK_UserDocuments_UserInformationID FOREIGN KEY (UserInformationID) REFERENCES UserInformation(UserInformationID),
        CONSTRAINT FK_UserDocuments_DocumentTypeID FOREIGN KEY (DocumentTypeID) REFERENCES DataExtractionTypes(ID)
    );
END
------------------action changes for data ingestion and investment company--
-- Declare Feature IDs
DECLARE @DataIngestion INT = (SELECT FeatureID FROM M_Features WHERE Feature = 'DataIngestion');
DECLARE @Investment INT = (SELECT FeatureID FROM M_Features WHERE Feature = 'Investment Company');

-- Insert actions for DataIngestion
DECLARE @ActionID INT = 1;
WHILE @ActionID <= 5
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM Mapping_FeatureAction
        WHERE FeatureID = @DataIngestion AND ActionID = @ActionID
    )
    BEGIN
        INSERT INTO Mapping_FeatureAction
        (FeatureID, ActionID, IsEnabled, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, EncryptedFeatureActionMappingID)
        VALUES (@DataIngestion, @ActionID, 1, GETUTCDATE(), 1, NULL, NULL, NULL);
    END
    SET @ActionID += 1;
END

-- Insert actions for Investment Company
SET @ActionID = 1;
WHILE @ActionID <= 3
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM Mapping_FeatureAction
        WHERE FeatureID = @Investment AND ActionID = @ActionID
    )
    BEGIN
        INSERT INTO Mapping_FeatureAction
        (FeatureID, ActionID, IsEnabled, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, EncryptedFeatureActionMappingID)
        VALUES (@Investment, @ActionID, 1, GETUTCDATE(), 1, NULL, NULL, NULL);
    END
    SET @ActionID += 1;
END

-- Delete actions 4 and 5 for Investment Company if they exist
IF EXISTS (
    SELECT 1 FROM Mapping_FeatureAction
    WHERE FeatureID = @Investment AND ActionID IN (4, 5)
)
BEGIN
    DELETE FROM Mapping_FeatureAction
    WHERE FeatureID = @Investment AND ActionID IN (4, 5);
END
GO
------------------action changes for data ingestion and investment company--

-----------------------BFB-10035 ---------------
IF NOT EXISTS (
    SELECT *
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'UserInformation'
    AND COLUMN_NAME = 'Recipient'
) and EXISTS (SELECT * FROM sysobjects WHERE name='UserInformation')
BEGIN
    ALTER TABLE [dbo].[UserInformation]
	ADD Recipient VARCHAR(10) NULL;
END
GO

IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'EmailReminder')
BEGIN
    CREATE TABLE [dbo].[EmailReminder] (
        [ReminderId] UNIQUEIDENTIFIER PRIMARY KEY NOT NULL,
        [FeatureID] [int] NOT NULL,
        [EntityIDs] [nvarchar](MAX) NOT NULL, -- Stores multiple company IDs
        [DocumentTypeIds] [nvarchar](MAX) NOT NULL, -- Stores multiple document type IDs
        [Subject] [nvarchar](500) NULL,
        [EmailBody] [nvarchar](MAX) NULL,
        [IsActive] [bit] NOT NULL DEFAULT 1,
        [CreatedOn] [datetime] NOT NULL DEFAULT GETUTCDATE(),
        [CreatedBy] [int] NOT NULL,
        [ModifiedOn] [datetime] NULL,
        [ModifiedBy] [int] NULL,
        [IsDeleted] [bit] NOT NULL DEFAULT 0
    );
END
GO
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'EmailReminderRecipients')
BEGIN
    CREATE TABLE [dbo].[EmailReminderRecipients] (
        [Id] [int] IDENTITY(1,1) PRIMARY KEY,
        [ReminderId] UNIQUEIDENTIFIER NOT NULL, -- FOREIGN KEY REFERENCES EmailReminder(ReminderId),
        [RecipientType] [int] NOT NULL, -- 1 for To, 2 for CC
        [RecipientId] [int] NULL, -- User ID if internal user
        [EmailAddress] [nvarchar](255) NULL, -- Email if external user
        [IsGroupMember] [bit] NOT NULL DEFAULT 0,
        [GroupID] [int] NULL, -- Group ID if group member, else null. Maps to EmailNotificationGroups table.
        [CreatedOn] [datetime] NOT NULL DEFAULT GETUTCDATE(),
        [CreatedBy] [int] NOT NULL,
        [ModifiedOn] [datetime] NULL,
        [ModifiedBy] [int] NULL,
        [IsDeleted] [bit] NOT NULL DEFAULT 0
    );
END
GO
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'EmailReminderConfig')
BEGIN
    CREATE TABLE [dbo].[EmailReminderConfig] (
        [Id] [int] IDENTITY(1,1) PRIMARY KEY,
        [ReminderId] UNIQUEIDENTIFIER NOT NULL, --FOREIGN KEY REFERENCES EmailReminder(ReminderId),
        [FrequencyType] [int] NOT NULL, -- 1=Daily, 2=Weekly, 3=Monthly, etc.
        [TotalRemindersPerCycle] [int] NOT NULL,
        [Remainder1Date] [datetime] NOT NULL,
        [Remainder2]  [nvarchar](255) NULL,
        [Remainder3]  [nvarchar](255) NULL,
        [Remainder4]  [nvarchar](255) NULL,
        [Remainder5]  [nvarchar](255) NULL,
        [CreatedOn] [datetime] NOT NULL DEFAULT GETUTCDATE(),
        [CreatedBy] [int] NOT NULL,
        [ModifiedOn] [datetime] NULL,
        [ModifiedBy] [int] NULL,
        [IsDeleted] [bit] NOT NULL DEFAULT 0
    );
END
GO

IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'EmailReminderSchedule')
BEGIN
    CREATE TABLE [dbo].[EmailReminderSchedule] (
        [Id] [int] IDENTITY(1,1) PRIMARY KEY,
        [ReminderConfigId] [int] NOT NULL, --FOREIGN KEY REFERENCES EmailReminderConfig(Id),
        [ReminderDate] [datetime] NOT NULL,
        [ScheduleOccurrence] [int] NOT NULL, -- Occurrence number in the cycle
        [Status] [int] NOT NULL DEFAULT 0, -- 0=Pending, 1=Sent, 2=Failed
        [SentDate] [datetime] NULL,
        [Error] [nvarchar](MAX) NULL,
        [IsActive] [bit] NOT NULL DEFAULT 1,
        [CreatedOn] [datetime] NOT NULL DEFAULT GETUTCDATE(),
        [CreatedBy] [int] NOT NULL,
        [ModifiedOn] [datetime] NULL,
        [ModifiedBy] [int] NULL,
        [IsDeleted] [bit] NOT NULL DEFAULT 0
    );
END
GO  