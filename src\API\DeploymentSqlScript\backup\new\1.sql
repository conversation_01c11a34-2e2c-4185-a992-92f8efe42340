GO
IF NOT EXISTS ( SELECT 1 FROM M_ValueTypes WHERE HeaderValue = 'IC2')
BEGIN
    INSERT INTO M_ValueTypes ( <PERSON>er<PERSON>al<PERSON>, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted, EncryptedValueTypeID)
    VALUES ('IC2', GETDATE(), 3, NULL, NULL, 0, NULL);
END
GO
IF NOT EXISTS (SELECT 1 FROM M_ValueTypes WHERE HeaderValue = 'IC3')
BEGIN
    INSERT INTO M_ValueTypes ( HeaderValue, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted, EncryptedValueTypeID)
    VALUES ('IC3', GETDATE(), 3, NULL, NULL, 0, NULL);
END
GO 
IF NOT EXISTS (SELECT 1 FROM M_ValueTypes WHERE HeaderValue = 'IC4')
BEGIN
    INSERT INTO M_ValueTypes ( <PERSON><PERSON><PERSON>al<PERSON>, <PERSON>On, <PERSON>By, ModifiedOn, ModifiedBy, IsD<PERSON><PERSON>, EncryptedValueTypeID)
    VALUES ('IC4', GETDATE(), 3, NULL, NULL, 0, NULL);
END
GO
IF NOT EXISTS (SELECT 1 FROM M_ValueTypes WHERE HeaderValue = 'IC5')
BEGIN
    INSERT INTO M_ValueTypes ( HeaderValue, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted, EncryptedValueTypeID)
    VALUES ('IC5', GETDATE(), 3, NULL, NULL, 0, NULL);
END
GO
IF (NOT EXISTS (SELECT * 
                FROM MSubSectionFields
                WHERE Name  IN('IC2','IC3','IC4','IC5') AND FieldID=(SELECT FieldID FROM M_SubPageFields WHERE SubPageID=2 AND Name='CreditKPI')
               ))
BEGIN
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=2 AND Name='CreditKPI'), N'IC2', N'IC2', 2, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=2 AND Name='CreditKPI'), N'IC3', N'IC3', 2, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=2 AND Name='CreditKPI'), N'IC4', N'IC4', 2, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=2 AND Name='CreditKPI'), N'IC5', N'IC5', 2, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
END
GO
IF (NOT EXISTS (SELECT * 
                FROM MSubSectionFields
                WHERE Name  IN('IC2','IC3','IC4','IC5')  AND FieldID=(SELECT FieldID FROM M_SubPageFields WHERE SubPageID=2 AND Name='TradingRecords')
               ))
BEGIN
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=2 AND Name='TradingRecords'), N'IC2', N'IC2', 2, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=2 AND Name='TradingRecords'), N'IC3', N'IC3', 2, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=2 AND Name='TradingRecords'), N'IC4', N'IC4', 2, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=2 AND Name='TradingRecords'), N'IC5', N'IC5', 2, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
END
GO
ALTER PROCEDURE [dbo].[SpGetCompanyWisePluginKPIValues](
    @ModuleId INT = 0,
    @CompanyId varchar(max)='')
AS
BEGIN
    -- Reduce network traffic by not returning affected row counts
    SET NOCOUNT ON;

    -- Create temp table instead of table variable for better statistics and indexing
    CREATE TABLE #CompanyDetails (PortfolioCopanyId INT)
    
    -- Add index to improve join performance
    CREATE CLUSTERED INDEX IX_CompanyDetails ON #CompanyDetails(PortfolioCopanyId)
    
    INSERT INTO #CompanyDetails(PortfolioCopanyId)
    SELECT Item FROM dbo.SplitString(@CompanyId,',')

    DECLARE @InvestmentKPI INT = 4,
            @MasterKPI INT = 1,
            @CreditKPI INT = 2,
            @ImpactKPI INT = 6,
            @OperationalKPI INT = 3,
            @CompanyKPI INT = 5,
            @ProfitAndLoss INT = 7,
            @BalanceSheet INT = 8,
            @CashFlow INT = 9;
IF(@ModuleId=@InvestmentKPI)
BEGIN

SELECT
	V.PortfolioCompanyID PortfolioCompanyId,
	MV.HeaderValue,CASE
		WHEN (
			V.Month IS NULL
			OR V.Month = 0
		)
		AND V.Quarter IS NULL THEN '(' + MV.HeaderValue + ') ' + Cast(V.Year AS VARCHAR)
		WHEN V.Month IS NULL
		OR V.Month = 0 THEN '(' + MV.HeaderValue + ') ' + V.Quarter + ' ' + Cast(V.Year AS VARCHAR)
		WHEN V.Quarter IS NULL THEN '(' + MV.HeaderValue + ') ' + [dbo].[GetMonthName](V.Month) + ' ' + Cast(V.Year AS VARCHAR)
	END PeriodType,
	Mst.KpiInfo,
	A.KpiID KpiId,
	CASE
        WHEN Mst.IsHeader = 1 THEN NULL
        ELSE ISNULL(V.KPIActualValue, 'NA')
    END AS KPIValue,
	V.Year,
	V.Quarter,
	V.Month,
	Mst.KPI,
	A.DisplayOrder,
	Mst.IsHeader,
	Mst.IsBoldKPI IsBoldKpi,
	A.ParentKPIID ParentId,
	ParentKpi.KPI ParentKpi
FROM
	Mapping_PortfolioInvestmentKPI A WITH (NOLOCK)
	INNER JOIN PCInvestmentKPIQuarterlyValue V WITH (NOLOCK) ON A.KpiID = V.InvestmentKPIID
	AND A.PortfolioCompanyID = V.PortfolioCompanyID
	INNER JOIN M_InvestmentKPI Mst WITH (NOLOCK) ON Mst.InvestmentKPIId = A.KpiID
	INNER JOIN M_ValueTypes MV WITH (NOLOCK) ON MV.ValueTypeID = V.ValueTypeID
	INNER JOIN #CompanyDetails CD ON v.PortfolioCompanyID=CD.PortfolioCopanyId
	LEFT JOIN M_InvestmentKPI ParentKpi WITH (NOLOCK) ON ParentKpi.InvestmentKPIID = A.ParentKPIID 
        AND ParentKpi.IsDeleted = 0
WHERE
	A.IsDeleted = 0
	AND V.IsDeleted = 0
	AND MV.IsDeleted = 0
	AND Mst.IsDeleted = 0
ORDER BY
	A.DisplayOrder
OPTION (OPTIMIZE FOR UNKNOWN)

END
IF(@ModuleId IN (1,2,17,18,19,20,21,22,23,24,25,26,27,28,29,30))
BEGIN

Select
	MV.HeaderValue,CASE
		WHEN (
			V.Month IS NULL
			OR V.Month = 0
		)
		AND V.Quarter IS NULL THEN '(' + MV.HeaderValue + ') ' + Cast(V.Year as varchar)
		WHEN V.Month IS NULL
		OR V.Month = 0 THEN '(' + MV.HeaderValue + ') ' + V.Quarter + ' ' + Cast(V.Year as varchar)
		WHEN V.Quarter IS NULL THEN '(' + MV.HeaderValue + ') ' + [dbo].[GetMonthName](V.Month) + ' ' + Cast(V.Year as varchar)
	END PeriodType,
	Mst.ModuleID ModuleId,
	Mst.KPI KPI,
	Mst.KpiInfo,
	A.KpiID KpiId,
	CASE
        WHEN Mst.IsHeader = 1 THEN NULL
        ELSE ISNULL(V.KPIValue, 'NA')
    END AS KPIValue,
	V.Year,
	V.Quarter,
	V.Month,
	A.PortfolioCompanyID PortfolioCompanyId,
	Mst.IsHeader,
	A.DisplayOrder,
	Mst.IsBoldKPI IsBoldKpi,
	A.ParentKPIID ParentId,
	ParentKpi.KPI ParentKpi
FROM
	Mapping_Kpis A WITH (NOLOCK)
	INNER JOIN PCMasterKpiValues V WITH (NOLOCK) On A.Mapping_KpisID = V.MappingKpisID
	INNER JOIN M_MasterKpis Mst WITH (NOLOCK) on Mst.MasterKpiID = A.KpiID
	INNER JOIN M_ValueTypes MV WITH (NOLOCK) on MV.ValueTypeID = V.ValueTypeID
	INNER JOIN #CompanyDetails CD On A.PortfolioCompanyID=CD.PortfolioCopanyId
	LEFT JOIN M_MasterKpis ParentKpi WITH (NOLOCK) ON ParentKpi.MasterKpiID = A.ParentKPIID 
	    AND ParentKpi.IsDeleted = 0 
	    AND ParentKpi.ModuleID = @ModuleId
WHERE
	A.IsDeleted = 0
	AND V.IsDeleted = 0
	AND Mst.IsDeleted = 0
	AND Mv.IsDeleted = 0
	AND Mst.ModuleID=@ModuleId
	AND A.ModuleID=@ModuleId
ORDER BY A.DisplayOrder
OPTION (OPTIMIZE FOR UNKNOWN)

END
IF(@ModuleId=@ImpactKPI)
BEGIN

SELECT
	MV.HeaderValue,CASE
		WHEN (
			V.Month IS NULL
			OR V.Month = 0
		)
		AND V.Quarter IS NULL THEN '(' + MV.HeaderValue + ') ' + Cast(V.Year as varchar)
		WHEN V.Month IS NULL
		OR V.Month = 0 THEN '(' + MV.HeaderValue + ') ' + V.Quarter + ' ' + Cast(V.Year as varchar)
		WHEN V.Quarter IS NULL THEN '(' + MV.HeaderValue + ') ' + [dbo].[GetMonthName](V.Month) + ' ' + Cast(V.Year as varchar)
	END PeriodType,
	Mst.KpiInfo,
	CASE
        WHEN Mst.IsHeader = 1 THEN NULL
        ELSE ISNULL(V.KPIActualValue, 'NA')
    END AS KPIValue,
	V.PortfolioCompanyID as PortfolioCompanyId,
	V.Year,
	V.Quarter,
	V.Month,
	V.ImpactKPIID KpiId,
	Mst.KPI KPI,
	Map.KPIOrder DisplayOrder,
	Mst.IsHeader,
	Mst.IsBoldKPI IsBoldKpi,
	Map.ParentKPIID ParentId,
	ParentKpi.KPI ParentKpi
FROM
	PCImpactKPIQuarterlyValue V WITH (NOLOCK)
	Inner Join Mapping_ImpactKPI_Order Map WITH (NOLOCK) on V.PortfolioCompanyID = Map.PortfolioCompanyID
	and V.ImpactKPIID = Map.ImpactKPIID
	Inner Join M_ImpactKPI Mst WITH (NOLOCK) on Mst.ImpactKPIID = Map.ImpactKPIID
	INNER JOIN M_ValueTypes MV WITH (NOLOCK) on MV.ValueTypeID = V.ValueTypeID
	INNER JOIN #CompanyDetails CD On Map.PortfolioCompanyID=CD.PortfolioCopanyId
	LEFT JOIN M_ImpactKPI ParentKpi WITH (NOLOCK) ON ParentKpi.ImpactKPIID = Map.ParentKPIID
	    AND ParentKpi.IsDeleted = 0
WHERE
	V.IsDeleted = 0
	and Map.IsDeleted = 0
	and Mst.IsDeleted = 0
	AND MV.IsDeleted = 0
Order By
	Map.KPIOrder
OPTION (OPTIMIZE FOR UNKNOWN)

END
IF(@ModuleId=@OperationalKPI)
BEGIN

SELECT
	MV.HeaderValue,CASE
		WHEN (
			Q.Month IS NULL
			OR Q.Month = 0
		)
		AND Q.Quarter IS NULL THEN '(' + MV.HeaderValue + ') ' + Cast(Q.Year as varchar)
		WHEN Q.Month IS NULL
		OR Q.Month = 0 THEN '(' + MV.HeaderValue + ') ' + Q.Quarter + ' ' + Cast(Q.Year as varchar)
		WHEN Q.Quarter IS NULL THEN '(' + MV.HeaderValue + ') ' + [dbo].[GetMonthName](Q.Month) + ' ' + Cast(Q.Year as varchar)
	END PeriodType,
	MST.KPIInfo KpiInfo,
	MST.SectorwiseOperationalKPIID KpiId,
	CASE
        WHEN Mst.IsHeader = 1 THEN NULL
        ELSE ISNULL(V.KPIValue, 'NA')
    END AS KPIValue,
	MAP.PortfolioCompanyID PortfolioCompanyId,
	Q.Quarter,
	Q.Year,
	Q.Month,
	MST.KPI,
	Map.DisplayOrder,
	MST.IsHeader,
	MST.IsBoldKPI IsBoldKpi,
	MAP.ParentKPIID ParentId,
	ParentKpi.KPI ParentKpi
FROM
	Mapping_PortfolioOperationalKPI MAP WITH (NOLOCK)
	INNER JOIN M_SectorwiseOperationalKPI MST WITH (NOLOCK) ON MST.SectorwiseOperationalKPIID = MAP.KpiID
	INNER JOIN PortfolioCompanyOperationalKPIQuarters Q WITH (NOLOCK) ON MAP.PortfolioCompanyID = Q.PortfolioCompanyID
	INNER JOIN PortfolioCompanyOperationalKPIValues V WITH (NOLOCK) on V.PortfolioCompanyOperationalKPIQuarterID = Q.PortfolioCompanyOperationalKPIQuarterID
	AND V.SectorwiseOperationalKPIID = MST.SectorwiseOperationalKPIID
	INNER JOIN M_ValueTypes MV WITH (NOLOCK) on MV.ValueTypeID = Q.ValueTypeID
	INNER JOIN #CompanyDetails CD On Map.PortfolioCompanyID=CD.PortfolioCopanyId
	LEFT JOIN M_SectorwiseOperationalKPI ParentKpi WITH (NOLOCK) ON ParentKpi.SectorwiseOperationalKPIID = MAP.ParentKPIID
	    AND ParentKpi.IsDeleted = 0
WHERE
	MAP.IsDeleted = 0
	and V.IsDeleted = 0
	AND Q.IsDeleted = 0
	AND MV.IsDeleted = 0
ORDER BY
	MAP.DisplayOrder
OPTION (OPTIMIZE FOR UNKNOWN)

END
IF(@ModuleId=@CompanyKPI)
BEGIN
	
Select
	A.CompanyKPIID KpiId,
	CASE
        WHEN Mst.IsHeader = 1 THEN NULL
        ELSE ISNULL(A.KPIActualValue, 'NA')
    END AS KPIValue,
	A.Year,
	A.Quarter,
	A.Month,
	CASE
		WHEN Month IS NULL
		and Quarter IS NULL THEN '(Actual) ' + Cast(A.Year as varchar)
		WHEN Month IS NULL THEN '(Actual) ' + A.Quarter + ' ' + Cast(A.Year as varchar)
		WHEN Quarter IS NULL THEN '(Actual) ' + [dbo].[GetMonthName](A.Month) + ' ' + Cast(A.Year as varchar)
	END PeriodType,
	A.PortfolioCompanyID PortfolioCompanyId,
	mst.KPI,
	Map.DisplayOrder,
	mst.KpiInfo KPIInfo,
	Map.ParentKPIID ParentId,mst.IsHeader,mst.IsBoldKPI IsBoldKpi,
	ParentKpi.KPI ParentKpi
FROM
	PCCompanyKPIMonthlyValue A WITH (NOLOCK)
	INNER JOIN Mapping_PortfolioCompanyKPI Map WITH (NOLOCK) on Map.PortfolioCompanyID = A.PortfolioCompanyID
	AND Map.KpiID = A.CompanyKPIID
	INNER JOIN M_CompanyKPI mst WITH (NOLOCK) on mst.CompanyKPIID = Map.KpiID
	INNER JOIN #CompanyDetails CD On Map.PortfolioCompanyID=CD.PortfolioCopanyId
	LEFT JOIN M_CompanyKPI ParentKpi WITH (NOLOCK) ON ParentKpi.CompanyKPIID = Map.ParentKPIID
	    AND ParentKpi.IsDeleted = 0
WHERE
	A.IsDeleted = 0
	and Map.IsDeleted = 0
	and mst.IsDeleted = 0
UNION ALL
Select
	A.CompanyKPIID KpiId,
	CASE
        WHEN Mst.IsHeader = 1 THEN NULL
        ELSE ISNULL(A.KPIBudgetValue, 'NA')
    END AS KPIValue,
	A.Year,
	A.Quarter,
	A.Month,
	CASE
		WHEN Month IS NULL
		and Quarter IS NULL THEN '(Budget) ' + Cast(A.Year as varchar)
		WHEN Month IS NULL THEN '(Budget) ' + A.Quarter + ' ' + Cast(A.Year as varchar)
		WHEN Quarter IS NULL THEN '(Budget) ' + [dbo].[GetMonthName](A.Month) + ' ' + Cast(A.Year as varchar)
	END PeriodType,
	A.PortfolioCompanyID PortfolioCompanyId,
	mst.KPI,
	Map.DisplayOrder,
	mst.KpiInfo KPIInfo,
	Map.ParentKPIID ParentId,mst.IsHeader,mst.IsBoldKPI IsBoldKpi,
	ParentKpi.KPI ParentKpi
FROM
	PCCompanyKPIMonthlyValue A WITH (NOLOCK)
	INNER JOIN Mapping_PortfolioCompanyKPI Map WITH (NOLOCK) on Map.PortfolioCompanyID = A.PortfolioCompanyID
	AND Map.KpiID = A.CompanyKPIID
	INNER JOIN M_CompanyKPI mst WITH (NOLOCK) on mst.CompanyKPIID = Map.KpiID
	INNER JOIN #CompanyDetails CD On Map.PortfolioCompanyID=CD.PortfolioCopanyId
	LEFT JOIN M_CompanyKPI ParentKpi WITH (NOLOCK) ON ParentKpi.CompanyKPIID = Map.ParentKPIID
	    AND ParentKpi.IsDeleted = 0
WHERE
	A.IsDeleted = 0
	and Map.IsDeleted = 0
	and mst.IsDeleted = 0
UNION ALL
Select
	A.CompanyKPIID KpiId,
	CASE
        WHEN Mst.IsHeader = 1 THEN NULL
        ELSE ISNULL(A.KPIValue, 'NA')
    END AS KPIValue,
	A.Year,
	A.Quarter,
	A.Month,
	CASE
		WHEN Month IS NULL
		and Quarter IS NULL THEN '(Forecast) ' + Cast(A.Year as varchar)
		WHEN Month IS NULL THEN '(Forecast) ' + A.Quarter + ' ' + Cast(A.Year as varchar)
		WHEN Quarter IS NULL THEN '(Forecast) ' + [dbo].[GetMonthName](A.Month) + ' ' + Cast(A.Year as varchar)
	END PeriodType,
	A.PortfolioCompanyID PortfolioCompanyId,
	mst.KPI,
	Map.DisplayOrder,
	mst.KpiInfo KPIInfo,
	Map.ParentKPIID ParentId,mst.IsHeader,mst.IsBoldKPI IsBoldKpi,
	ParentKpi.KPI ParentKpi
FROM
	CompanyKPIForecastValues A WITH (NOLOCK)
	INNER JOIN Mapping_PortfolioCompanyKPI Map WITH (NOLOCK) on Map.PortfolioCompanyID = A.PortfolioCompanyID
	AND Map.KpiID = A.CompanyKPIID
	INNER JOIN M_CompanyKPI mst WITH (NOLOCK) on mst.CompanyKPIID = Map.KpiID
	INNER JOIN #CompanyDetails CD On Map.PortfolioCompanyID=CD.PortfolioCopanyId
	LEFT JOIN M_CompanyKPI ParentKpi WITH (NOLOCK) ON ParentKpi.CompanyKPIID = Map.ParentKPIID
	    AND ParentKpi.IsDeleted = 0
WHERE
	A.IsDeleted = 0
	and Map.IsDeleted = 0
	and mst.IsDeleted = 0
ORDER BY
	DisplayOrder
OPTION (RECOMPILE)

END
IF(@ModuleId=@BalanceSheet)
BEGIN

-- Add query hint to optimize the UNION operations
;WITH BalanceSheetData AS (
Select
	mst.BalanceSheetLineItemID KpiId,
	CASE
        WHEN Mst.IsHeader = 1 THEN NULL
		WHEN IsNumeric(A.ActualValue) = 1  AND mst.KPIInfo='$' THEN CONVERT(VARCHAR(MAX),CONVERT(Decimal(38,8),A.ActualValue)*1000)
        ELSE ISNULL(A.ActualValue, 'NA')
    END AS KPIValue,
	A.Year,
	A.Quarter,
	A.Month,
	CASE
		WHEN Month IS NULL
		and Quarter IS NULL THEN '(Actual) ' + Cast(A.Year as varchar)
		WHEN Month IS NULL THEN '(Actual) ' + A.Quarter + ' ' + Cast(A.Year as varchar)
		WHEN Quarter IS NULL THEN '(Actual) ' + [dbo].[GetMonthName](A.Month) + ' ' + Cast(A.Year as varchar)
	END PeriodType,
	Map.PortfolioCompanyID PortfolioCompanyId,
	Map.DisplayOrder,
	mst.IsHeader,
	mst.IsBoldKPI,
	map.ParentLineItemID 'ParentId',
	mst.BalanceSheetLineItem 'KPI',
	mst.KpiInfo KPIInfo,
	mst.MethodologyID MethodologyID,
	ParentItem.BalanceSheetLineItem ParentKpi
FROM
	BalanceSheetValues A WITH (NOLOCK)
	INNER JOIN Mapping_CompanyBalanceSheetLineItems Map WITH (NOLOCK) on Map.CompanyBalanceSheetLineItemMappingID = A.CompanyBalanceSheetLineItemMappingID
	AND Map.IsDeleted = 0
	and A.IsDeleted = 0
	INNER JOIN M_BalanceSheet_LineItems mst WITH (NOLOCK) on mst.BalanceSheetLineItemID = Map.BalanceSheetLineItemID
	and mst.IsDeleted = 0
	INNER JOIN #CompanyDetails CD On Map.PortfolioCompanyID=CD.PortfolioCopanyId
	LEFT JOIN M_BalanceSheet_LineItems ParentItem WITH (NOLOCK) ON ParentItem.BalanceSheetLineItemID = Map.ParentLineItemID
		AND ParentItem.IsDeleted = 0
WHERE
	A.IsDeleted = 0
	and Map.IsDeleted = 0
	and mst.IsDeleted = 0
	AND A.ActualValue IS NOT NULL

UNION ALL

Select
	mst.BalanceSheetLineItemID KpiId,
	CASE
        WHEN Mst.IsHeader = 1 THEN NULL
		WHEN IsNumeric(A.BudgetValue) = 1  AND mst.KPIInfo='$' THEN CONVERT(VARCHAR(MAX),CONVERT(Decimal(38,8),A.BudgetValue)*1000)
        ELSE ISNULL(A.BudgetValue, 'NA')
    END AS KPIValue,
	A.Year,
	A.Quarter,
	A.Month,
	CASE
		WHEN Month IS NULL
		and Quarter IS NULL THEN '(Budget) ' + Cast(A.Year as varchar)
		WHEN Month IS NULL THEN '(Budget) ' + A.Quarter + ' ' + Cast(A.Year as varchar)
		WHEN Quarter IS NULL THEN '(Budget) ' + [dbo].[GetMonthName](A.Month) + ' ' + Cast(A.Year as varchar)
	END PeriodType,
	Map.PortfolioCompanyID PortfolioCompanyId,
	Map.DisplayOrder,
	mst.IsHeader,
	mst.IsBoldKPI,
	map.ParentLineItemID 'ParentId',
	mst.BalanceSheetLineItem 'KPI',
	mst.KpiInfo KPIInfo,
	mst.MethodologyID MethodologyID,
	ParentItem.BalanceSheetLineItem ParentKpi
FROM
	BalanceSheetValues A WITH (NOLOCK)
	INNER JOIN Mapping_CompanyBalanceSheetLineItems Map WITH (NOLOCK) on Map.CompanyBalanceSheetLineItemMappingID = A.CompanyBalanceSheetLineItemMappingID
	AND Map.IsDeleted = 0
	and A.IsDeleted = 0
	INNER JOIN M_BalanceSheet_LineItems mst WITH (NOLOCK) on mst.BalanceSheetLineItemID = Map.BalanceSheetLineItemID
	and mst.IsDeleted = 0
	INNER JOIN #CompanyDetails CD On Map.PortfolioCompanyID=CD.PortfolioCopanyId
	LEFT JOIN M_BalanceSheet_LineItems ParentItem WITH (NOLOCK) ON ParentItem.BalanceSheetLineItemID = Map.ParentLineItemID
		AND ParentItem.IsDeleted = 0
WHERE
	A.IsDeleted = 0
	and Map.IsDeleted = 0
	and mst.IsDeleted = 0
	AND A.BudgetValue IS NOT NULL

UNION ALL

Select
	mst.BalanceSheetLineItemID KpiId,
	CASE
        WHEN Mst.IsHeader = 1 THEN NULL
		WHEN IsNumeric(A.ActualValue) = 1  AND mst.KPIInfo='$' THEN CONVERT(VARCHAR(MAX),CONVERT(Decimal(38,8),A.ActualValue)*1000)
        ELSE ISNULL(A.ActualValue, 'NA')
    END AS KPIValue,
	A.Year,
	A.Quarter,
	A.Month,
	CASE
		WHEN Month IS NULL
		and Quarter IS NULL THEN '(Forecast) ' + Cast(A.Year as varchar)
		WHEN Month IS NULL THEN '(Forecast) ' + A.Quarter + ' ' + Cast(A.Year as varchar)
		WHEN Quarter IS NULL THEN '(Forecast) ' + [dbo].[GetMonthName](A.Month) + ' ' + Cast(A.Year as varchar)
	END PeriodType,
	Map.PortfolioCompanyID PortfolioCompanyId,
	Map.DisplayOrder,
	mst.IsHeader,
	mst.IsBoldKPI,
	map.ParentLineItemID 'ParentId',
	mst.BalanceSheetLineItem 'KPI',
	mst.KpiInfo KPIInfo,
	mst.MethodologyID MethodologyID,
	ParentItem.BalanceSheetLineItem ParentKpi
FROM
	BalanceSheet_ForecastData A WITH (NOLOCK)
	INNER JOIN Mapping_CompanyBalanceSheetLineItems Map WITH (NOLOCK) on Map.CompanyBalanceSheetLineItemMappingID = A.CompanyBalanceSheetLineItemMappingID
	AND Map.IsDeleted = 0
	and A.IsDeleted = 0
	INNER JOIN M_BalanceSheet_LineItems mst WITH (NOLOCK) on mst.BalanceSheetLineItemID = Map.BalanceSheetLineItemID
	and mst.IsDeleted = 0
	INNER JOIN #CompanyDetails CD On Map.PortfolioCompanyID=CD.PortfolioCopanyId
	LEFT JOIN M_BalanceSheet_LineItems ParentItem WITH (NOLOCK) ON ParentItem.BalanceSheetLineItemID = Map.ParentLineItemID
		AND ParentItem.IsDeleted = 0
WHERE
	A.IsDeleted = 0
	and Map.IsDeleted = 0
	and mst.IsDeleted = 0
	AND A.ActualValue IS NOT NULL

UNION ALL

Select
	mst.BalanceSheetLineItemID KpiId,
	CASE
        WHEN Mst.IsHeader = 1 THEN NULL
		WHEN IsNumeric(A.BaseValue) = 1  AND mst.KPIInfo='$' THEN CONVERT(VARCHAR(MAX),CONVERT(Decimal(38,8),A.BaseValue)*1000)
        ELSE ISNULL(A.BaseValue , 'NA')
    END AS KPIValue,
	A.Year,
	'' Quarter,
	0 Month,
	CASE
		WHEN Year IS NOT NULL THEN '(Ic) ' + Cast(A.Year as varchar)
	END PeriodType,
	Map.PortfolioCompanyID PortfolioCompanyId,
	Map.DisplayOrder,
	mst.IsHeader,
	mst.IsBoldKPI,
	map.ParentLineItemID 'ParentId',
	mst.BalanceSheetLineItem 'KPI',
	mst.KpiInfo KPIInfo,
	mst.MethodologyID MethodologyID,
	ParentItem.BalanceSheetLineItem ParentKpi
FROM
	BalanceSheetICCaseValues A WITH (NOLOCK)
	INNER JOIN Mapping_CompanyBalanceSheetLineItems Map WITH (NOLOCK) on Map.CompanyBalanceSheetLineItemMappingID = A.CompanyBalanceSheetLineItemMappingID
	AND Map.IsDeleted = 0
	and A.IsDeleted = 0
	INNER JOIN M_BalanceSheet_LineItems mst WITH (NOLOCK) on mst.BalanceSheetLineItemID = Map.BalanceSheetLineItemID
	and mst.IsDeleted = 0
	INNER JOIN #CompanyDetails CD On Map.PortfolioCompanyID=CD.PortfolioCopanyId
	LEFT JOIN M_BalanceSheet_LineItems ParentItem WITH (NOLOCK) ON ParentItem.BalanceSheetLineItemID = Map.ParentLineItemID
		AND ParentItem.IsDeleted = 0
WHERE
	A.IsDeleted = 0
	and Map.IsDeleted = 0
	and mst.IsDeleted = 0
	AND A.BaseValue IS NOT NULL

UNION ALL

Select
	A.BalanceSheetLineItemID KpiId,
	CASE
        WHEN Mst.IsHeader = 1 THEN NULL
		WHEN IsNumeric(V.KPIValue) = 1  AND mst.KPIInfo='$' THEN CONVERT(VARCHAR(MAX),CONVERT(Decimal(38,8),V.KPIValue)*1000)
        ELSE ISNULL(V.KPIValue, 'NA')
    END AS KPIValue,
	V.Year,
	V.Quarter,
	V.Month,
	CASE
		WHEN (
			V.Month IS NULL
			OR V.Month = 0
		)
		AND V.Quarter IS NULL THEN '(' + MV.HeaderValue + ') ' + Cast(V.Year as varchar)
		WHEN V.Month IS NULL
		OR V.Month = 0 THEN '(' + MV.HeaderValue + ') ' + V.Quarter + ' ' + Cast(V.Year as varchar)
		WHEN V.Quarter IS NULL THEN '(' + MV.HeaderValue + ') ' + [dbo].[GetMonthName](V.Month) + ' ' + Cast(V.Year as varchar)
	END PeriodType,
	A.PortfolioCompanyID PortfolioCompanyId,
	A.DisplayOrder,	
	Mst.IsHeader,
	Mst.IsBoldKPI,
	A.ParentLineItemID ParentId,
	Mst.BalanceSheetLineItem KPI,
	Mst.KpiInfo KPIInfo,
	Mst.MethodologyID MethodologyID,
	ParentItem.BalanceSheetLineItem ParentKpi
FROM
	Mapping_CompanyBalanceSheetLineItems A WITH (NOLOCK)
	INNER JOIN PCFinancialsValues V WITH (NOLOCK) On A.CompanyBalanceSheetLineItemMappingID = V.MappingId
	INNER JOIN M_BalanceSheet_LineItems Mst WITH (NOLOCK) on Mst.BalanceSheetLineItemID = A.BalanceSheetLineItemID
	INNER JOIN M_ValueTypes MV WITH (NOLOCK) on MV.ValueTypeID = V.ValueTypeID
	INNER JOIN #CompanyDetails CD On A.PortfolioCompanyID=CD.PortfolioCopanyId
	LEFT JOIN M_BalanceSheet_LineItems ParentItem WITH (NOLOCK) ON ParentItem.BalanceSheetLineItemID = A.ParentLineItemID
		AND ParentItem.IsDeleted = 0
WHERE
	A.IsDeleted = 0
	AND V.IsDeleted = 0
	AND Mst.IsDeleted = 0
	AND V.ModuleId=@ModuleId
	AND V.KPIValue IS NOT NULL
)
SELECT * FROM BalanceSheetData
ORDER BY DisplayOrder
OPTION (RECOMPILE)

END
IF(@ModuleId=@ProfitAndLoss)
BEGIN
	
-- Add query hint to optimize the UNION operations
;WITH ProfitAndLossData AS (
Select
	mst.ProfitAndLossLineItemID KpiId,
	CASE
        WHEN Mst.IsHeader = 1 THEN NULL
		WHEN IsNumeric(A.ActualValue) = 1  AND mst.KPIInfo='$' THEN CONVERT(VARCHAR(MAX),CONVERT(Decimal(38,8),A.ActualValue)*1000)
        ELSE ISNULL(A.ActualValue, 'NA')
    END AS KPIValue,
	A.Year,
	A.Quarter,
	A.Month,
	CASE
		WHEN Month IS NULL
		and Quarter IS NULL THEN '(Actual) ' + Cast(A.Year as varchar)
		WHEN Month IS NULL THEN '(Actual) ' + A.Quarter + ' ' + Cast(A.Year as varchar)
		WHEN Quarter IS NULL THEN '(Actual) ' + [dbo].[GetMonthName](A.Month) + ' ' + Cast(A.Year as varchar)
	END PeriodType,
	Map.PortfolioCompanyID PortfolioCompanyId,
	Map.DisplayOrder,
	mst.IsHeader,
	mst.IsBoldKPI,
	map.ParentLineItemID 'ParentId',
	mst.ProfitAndLossLineItem 'KPI',
	mst.KpiInfo KPIInfo,
	mst.MethodologyID MethodologyID,
	ParentItem.ProfitAndLossLineItem ParentKpi
FROM
	ProfitAndLossValues A WITH (NOLOCK)
	INNER JOIN Mapping_CompanyProfitAndLossLineItems Map WITH (NOLOCK) on Map.CompanyProfitAndLossLineItemMappingID = A.CompanyProfitAndLossLineItemMappingID
	AND Map.IsDeleted = 0
	and A.IsDeleted = 0
	INNER JOIN M_ProfitAndLoss_LineItems mst WITH (NOLOCK) on mst.ProfitAndLossLineItemID = Map.ProfitAndLossLineItemID
	and mst.IsDeleted = 0
	INNER JOIN #CompanyDetails CD On Map.PortfolioCompanyID=CD.PortfolioCopanyId
	LEFT JOIN M_ProfitAndLoss_LineItems ParentItem WITH (NOLOCK) ON ParentItem.ProfitAndLossLineItemID = Map.ParentLineItemID
		AND ParentItem.IsDeleted = 0
WHERE
	A.IsDeleted = 0
	and Map.IsDeleted = 0
	and mst.IsDeleted = 0
	AND A.ActualValue IS NOT NULL

UNION ALL

Select
	mst.ProfitAndLossLineItemID KpiId,
	CASE
        WHEN Mst.IsHeader = 1 THEN NULL
		WHEN IsNumeric(A.BudgetValue) = 1  AND mst.KPIInfo='$' THEN CONVERT(VARCHAR(MAX),CONVERT(Decimal(38,8),A.BudgetValue)*1000)
        ELSE ISNULL(A.BudgetValue, 'NA')
    END AS KPIValue,
	A.Year,
	A.Quarter,
	A.Month,
	CASE
		WHEN Month IS NULL
		and Quarter IS NULL THEN '(Budget) ' + Cast(A.Year as varchar)
		WHEN Month IS NULL THEN '(Budget) ' + A.Quarter + ' ' + Cast(A.Year as varchar)
		WHEN Quarter IS NULL THEN '(Budget) ' + [dbo].[GetMonthName](A.Month) + ' ' + Cast(A.Year as varchar)
	END PeriodType,
	Map.PortfolioCompanyID PortfolioCompanyId,
	Map.DisplayOrder,
	mst.IsHeader,
	mst.IsBoldKPI,
	map.ParentLineItemID 'ParentId',
	mst.ProfitAndLossLineItem 'KPI',
	mst.KpiInfo KPIInfo,
	mst.MethodologyID MethodologyID,
	ParentItem.ProfitAndLossLineItem ParentKpi
FROM
	ProfitAndLossValues A WITH (NOLOCK)
	INNER JOIN Mapping_CompanyProfitAndLossLineItems Map WITH (NOLOCK) on Map.CompanyProfitAndLossLineItemMappingID = A.CompanyProfitAndLossLineItemMappingID
	AND Map.IsDeleted = 0
	and A.IsDeleted = 0
	INNER JOIN M_ProfitAndLoss_LineItems mst WITH (NOLOCK) on mst.ProfitAndLossLineItemID = Map.ProfitAndLossLineItemID
	and mst.IsDeleted = 0
	INNER JOIN #CompanyDetails CD On Map.PortfolioCompanyID=CD.PortfolioCopanyId
	LEFT JOIN M_ProfitAndLoss_LineItems ParentItem WITH (NOLOCK) ON ParentItem.ProfitAndLossLineItemID = Map.ParentLineItemID
		AND ParentItem.IsDeleted = 0
WHERE
	A.IsDeleted = 0
	and Map.IsDeleted = 0
	and mst.IsDeleted = 0
	AND A.BudgetValue IS NOT NULL

UNION ALL

Select
	mst.ProfitAndLossLineItemID KpiId,
	CASE
        WHEN Mst.IsHeader = 1 THEN NULL
		WHEN IsNumeric(A.ActualValue) = 1  AND mst.KPIInfo='$' THEN CONVERT(VARCHAR(MAX),CONVERT(Decimal(38,8),A.ActualValue)*1000)
        ELSE ISNULL(A.ActualValue, 'NA')
    END AS KPIValue,
	A.Year,
	A.Quarter,
	A.Month,
	CASE
		WHEN Month IS NULL
		and Quarter IS NULL THEN '(Forecast) ' + Cast(A.Year as varchar)
		WHEN Month IS NULL THEN '(Forecast) ' + A.Quarter + ' ' + Cast(A.Year as varchar)
		WHEN Quarter IS NULL THEN '(Forecast) ' + [dbo].[GetMonthName](A.Month) + ' ' + Cast(A.Year as varchar)
	END PeriodType,
	Map.PortfolioCompanyID PortfolioCompanyId,
	Map.DisplayOrder,
	mst.IsHeader,
	mst.IsBoldKPI,
	map.ParentLineItemID 'ParentId',
	mst.ProfitAndLossLineItem 'KPI',
	mst.KpiInfo KPIInfo,
	mst.MethodologyID MethodologyID,
	ParentItem.ProfitAndLossLineItem ParentKpi
FROM
	ProfitAndLoss_ForecastData A WITH (NOLOCK)
	INNER JOIN Mapping_CompanyProfitAndLossLineItems Map WITH (NOLOCK) on Map.CompanyProfitAndLossLineItemMappingID = A.CompanyProfitAndLossLineItemMappingID
	AND Map.IsDeleted = 0
	and A.IsDeleted = 0
	INNER JOIN M_ProfitAndLoss_LineItems mst WITH (NOLOCK) on mst.ProfitAndLossLineItemID = Map.ProfitAndLossLineItemID
	and mst.IsDeleted = 0
	INNER JOIN #CompanyDetails CD On Map.PortfolioCompanyID=CD.PortfolioCopanyId
	LEFT JOIN M_ProfitAndLoss_LineItems ParentItem WITH (NOLOCK) ON ParentItem.ProfitAndLossLineItemID = Map.ParentLineItemID
		AND ParentItem.IsDeleted = 0
WHERE
	A.IsDeleted = 0
	and Map.IsDeleted = 0
	and mst.IsDeleted = 0
	AND A.ActualValue IS NOT NULL

UNION ALL

Select
	mst.ProfitAndLossLineItemID KpiId,
	CASE
        WHEN Mst.IsHeader = 1 THEN NULL
		WHEN IsNumeric(A.BaseValue) = 1  AND mst.KPIInfo='$' THEN CONVERT(VARCHAR(MAX),CONVERT(Decimal(38,8),A.BaseValue)*1000)
        ELSE ISNULL(A.BaseValue , 'NA')
    END AS KPIValue,
	A.Year,
	'' Quarter,
	0 Month,
	CASE
		WHEN Year IS NOT NULL THEN '(Ic) ' + Cast(A.Year as varchar)
	END PeriodType,
	Map.PortfolioCompanyID PortfolioCompanyId,
	Map.DisplayOrder,
	mst.IsHeader,
	mst.IsBoldKPI,
	map.ParentLineItemID 'ParentId',
	mst.ProfitAndLossLineItem 'KPI',
	mst.KpiInfo KPIInfo,
	mst.MethodologyID MethodologyID,
	ParentItem.ProfitAndLossLineItem ParentKpi
FROM
	ProfitAndLossICCaseValues A WITH (NOLOCK)
	INNER JOIN Mapping_CompanyProfitAndLossLineItems Map WITH (NOLOCK) on Map.CompanyProfitAndLossLineItemMappingID = A.CompanyProfitAndLossLineItemMappingID
	AND Map.IsDeleted = 0
	and A.IsDeleted = 0
	INNER JOIN M_ProfitAndLoss_LineItems mst WITH (NOLOCK) on mst.ProfitAndLossLineItemID = Map.ProfitAndLossLineItemID
	and mst.IsDeleted = 0
	INNER JOIN #CompanyDetails CD On Map.PortfolioCompanyID=CD.PortfolioCopanyId
	LEFT JOIN M_ProfitAndLoss_LineItems ParentItem WITH (NOLOCK) ON ParentItem.ProfitAndLossLineItemID = Map.ParentLineItemID
		AND ParentItem.IsDeleted = 0
WHERE
	A.IsDeleted = 0
	and Map.IsDeleted = 0
	and mst.IsDeleted = 0
	AND A.BaseValue IS NOT NULL

UNION ALL

Select
	A.ProfitAndLossLineItemID KpiId,
	CASE
        WHEN Mst.IsHeader = 1 THEN NULL
		WHEN IsNumeric(V.KPIValue) = 1  AND mst.KPIInfo='$' THEN CONVERT(VARCHAR(MAX),CONVERT(Decimal(38,8),V.KPIValue)*1000)
        ELSE ISNULL(V.KPIValue, 'NA')
    END AS KPIValue,
	V.Year,
	V.Quarter,
	V.Month,
	CASE
		WHEN (
			V.Month IS NULL
			OR V.Month = 0
		)
		AND V.Quarter IS NULL THEN '(' + MV.HeaderValue + ') ' + Cast(V.Year as varchar)
		WHEN V.Month IS NULL
		OR V.Month = 0 THEN '(' + MV.HeaderValue + ') ' + V.Quarter + ' ' + Cast(V.Year as varchar)
		WHEN V.Quarter IS NULL THEN '(' + MV.HeaderValue + ') ' + [dbo].[GetMonthName](V.Month) + ' ' + Cast(V.Year as varchar)
	END PeriodType,
	A.PortfolioCompanyID PortfolioCompanyId,
	A.DisplayOrder,	
	Mst.IsHeader,
	Mst.IsBoldKPI,
	A.ParentLineItemID ParentId,
	Mst.ProfitAndLossLineItem KPI,
	Mst.KpiInfo KPIInfo,
	Mst.MethodologyID MethodologyID,
	ParentItem.ProfitAndLossLineItem ParentKpi
FROM
	Mapping_CompanyProfitAndLossLineItems A WITH (NOLOCK)
	INNER JOIN PCFinancialsValues V WITH (NOLOCK) On A.CompanyProfitAndLossLineItemMappingID = V.MappingId
	INNER JOIN M_ProfitAndLoss_LineItems Mst WITH (NOLOCK) on Mst.ProfitAndLossLineItemID = A.ProfitAndLossLineItemID
	INNER JOIN M_ValueTypes MV WITH (NOLOCK) on MV.ValueTypeID = V.ValueTypeID
	INNER JOIN #CompanyDetails CD On A.PortfolioCompanyID=CD.PortfolioCopanyId
	LEFT JOIN M_ProfitAndLoss_LineItems ParentItem WITH (NOLOCK) ON ParentItem.ProfitAndLossLineItemID = A.ParentLineItemID
		AND ParentItem.IsDeleted = 0
WHERE
	A.IsDeleted = 0
	AND V.IsDeleted = 0
	AND Mst.IsDeleted = 0
	AND V.ModuleId=@ModuleId
	AND V.KPIValue IS NOT NULL
)
SELECT * FROM ProfitAndLossData
ORDER BY DisplayOrder
OPTION (RECOMPILE)

END
IF(@ModuleId=@CashFlow)
BEGIN

-- Add query hint to optimize the UNION operations
;WITH CashFlowData AS (
Select
	mst.CashFlowLineItemID KpiId,
	CASE
        WHEN Mst.IsHeader = 1 THEN NULL
		WHEN IsNumeric(A.ActualValue) = 1  AND mst.KPIInfo='$' THEN CONVERT(VARCHAR(MAX),CONVERT(Decimal(38,8),A.ActualValue)*1000)
        ELSE ISNULL(A.ActualValue, 'NA')
    END AS KPIValue,
	A.Year,
	A.Quarter,
	A.Month,
	CASE
		WHEN Month IS NULL
		and Quarter IS NULL THEN '(Actual) ' + Cast(A.Year as varchar)
		WHEN Month IS NULL THEN '(Actual) ' + A.Quarter + ' ' + Cast(A.Year as varchar)
		WHEN Quarter IS NULL THEN '(Actual) ' + [dbo].[GetMonthName](A.Month) + ' ' + Cast(A.Year as varchar)
	END PeriodType,
	Map.PortfolioCompanyID PortfolioCompanyId,
	Map.DisplayOrder,
	mst.IsHeader,
	mst.IsBoldKPI,
	map.ParentLineItemID 'ParentId',
	mst.CashFlowLineItem 'KPI',
	mst.KpiInfo KPIInfo,
	mst.MethodologyID MethodologyID,
	ParentItem.CashFlowLineItem ParentKpi
FROM
	CashFlowValues A WITH (NOLOCK)
	INNER JOIN Mapping_CompanyCashFlowLineItems Map WITH (NOLOCK) on Map.CompanyCashFlowLineItemMappingID = A.CompanyCashFlowLineItemMappingID
	AND Map.IsDeleted = 0
	and A.IsDeleted = 0
	INNER JOIN M_CashFlow_LineItems mst WITH (NOLOCK) on mst.CashFlowLineItemID = Map.CashFlowLineItemID
	and mst.IsDeleted = 0
	INNER JOIN #CompanyDetails CD On Map.PortfolioCompanyID=CD.PortfolioCopanyId
	LEFT JOIN M_CashFlow_LineItems ParentItem WITH (NOLOCK) ON ParentItem.CashFlowLineItemID = Map.ParentLineItemID
		AND ParentItem.IsDeleted = 0
WHERE
	A.IsDeleted = 0
	and Map.IsDeleted = 0
	and mst.IsDeleted = 0
	AND A.ActualValue IS NOT NULL

UNION ALL

Select
	mst.CashFlowLineItemID KpiId,
	CASE
        WHEN Mst.IsHeader = 1 THEN NULL
		WHEN IsNumeric(A.BudgetValue) = 1  AND mst.KPIInfo='$' THEN CONVERT(VARCHAR(MAX),CONVERT(Decimal(38,8),A.BudgetValue)*1000)
        ELSE ISNULL(A.BudgetValue, 'NA')
    END AS KPIValue,
	A.Year,
	A.Quarter,
	A.Month,
	CASE
		WHEN Month IS NULL
		and Quarter IS NULL THEN '(Budget) ' + Cast(A.Year as varchar)
		WHEN Month IS NULL THEN '(Budget) ' + A.Quarter + ' ' + Cast(A.Year as varchar)
		WHEN Quarter IS NULL THEN '(Budget) ' + [dbo].[GetMonthName](A.Month) + ' ' + Cast(A.Year as varchar)
	END PeriodType,
	Map.PortfolioCompanyID PortfolioCompanyId,
	Map.DisplayOrder,
	mst.IsHeader,
	mst.IsBoldKPI,
	map.ParentLineItemID 'ParentId',
	mst.CashFlowLineItem 'KPI',
	mst.KpiInfo KPIInfo,
	mst.MethodologyID MethodologyID,
	ParentItem.CashFlowLineItem ParentKpi
FROM
	CashFlowValues A WITH (NOLOCK)
	INNER JOIN Mapping_CompanyCashFlowLineItems Map WITH (NOLOCK) on Map.CompanyCashFlowLineItemMappingID = A.CompanyCashFlowLineItemMappingID
	AND Map.IsDeleted = 0
	and A.IsDeleted = 0
	INNER JOIN M_CashFlow_LineItems mst WITH (NOLOCK) on mst.CashFlowLineItemID = Map.CashFlowLineItemID
	and mst.IsDeleted = 0
	INNER JOIN #CompanyDetails CD On Map.PortfolioCompanyID=CD.PortfolioCopanyId
	LEFT JOIN M_CashFlow_LineItems ParentItem WITH (NOLOCK) ON ParentItem.CashFlowLineItemID = Map.ParentLineItemID
		AND ParentItem.IsDeleted = 0
WHERE
	A.IsDeleted = 0
	and Map.IsDeleted = 0
	and mst.IsDeleted = 0
	AND A.BudgetValue IS NOT NULL
	
UNION ALL

Select
	mst.CashFlowLineItemID KpiId,
	CASE
        WHEN Mst.IsHeader = 1 THEN NULL
		WHEN IsNumeric(A.ActualValue) = 1  AND mst.KPIInfo='$' THEN CONVERT(VARCHAR(MAX),CONVERT(Decimal(38,8),A.ActualValue)*1000)
        ELSE ISNULL(A.ActualValue, 'NA')
    END AS KPIValue,
	A.Year,
	A.Quarter,
	A.Month,
	CASE
		WHEN Month IS NULL
		and Quarter IS NULL THEN '(Forecast) ' + Cast(A.Year as varchar)
		WHEN Month IS NULL THEN '(Forecast) ' + A.Quarter + ' ' + Cast(A.Year as varchar)
		WHEN Quarter IS NULL THEN '(Forecast) ' + [dbo].[GetMonthName](A.Month) + ' ' + Cast(A.Year as varchar)
	END PeriodType,
	Map.PortfolioCompanyID PortfolioCompanyId,
	Map.DisplayOrder,
	mst.IsHeader,
	mst.IsBoldKPI,
	map.ParentLineItemID 'ParentId',
	mst.CashFlowLineItem 'KPI',
	mst.KpiInfo KPIInfo,
	mst.MethodologyID MethodologyID,
	ParentItem.CashFlowLineItem ParentKpi
FROM
	CashFlow_ForecastData A WITH (NOLOCK)
	INNER JOIN Mapping_CompanyCashFlowLineItems Map WITH (NOLOCK) on Map.CompanyCashFlowLineItemMappingID = A.CompanyCashFlowLineItemMappingID
	AND Map.IsDeleted = 0
	and A.IsDeleted = 0
	INNER JOIN M_CashFlow_LineItems mst WITH (NOLOCK) on mst.CashFlowLineItemID = Map.CashFlowLineItemID
	and mst.IsDeleted = 0
	INNER JOIN #CompanyDetails CD On Map.PortfolioCompanyID=CD.PortfolioCopanyId
	LEFT JOIN M_CashFlow_LineItems ParentItem WITH (NOLOCK) ON ParentItem.CashFlowLineItemID = Map.ParentLineItemID
		AND ParentItem.IsDeleted = 0
WHERE
	A.IsDeleted = 0
	and Map.IsDeleted = 0
	and mst.IsDeleted = 0
	AND A.ActualValue IS NOT NULL

UNION ALL

Select
	mst.CashFlowLineItemID KpiId,
	CASE
        WHEN Mst.IsHeader = 1 THEN NULL
		WHEN IsNumeric(A.BaseValue) = 1  AND mst.KPIInfo='$' THEN CONVERT(VARCHAR(MAX),CONVERT(Decimal(38,8),A.BaseValue)*1000)
        ELSE ISNULL(A.BaseValue , 'NA')
    END AS KPIValue,
	A.Year,
	'' Quarter,
	0 Month,
	CASE
		WHEN Year IS NOT NULL THEN '(Ic) ' + Cast(A.Year as varchar)
	END PeriodType,
	Map.PortfolioCompanyID PortfolioCompanyId,
	Map.DisplayOrder,
	mst.IsHeader,
	mst.IsBoldKPI,
	map.ParentLineItemID 'ParentId',
	mst.CashFlowLineItem 'KPI',
	mst.KpiInfo KPIInfo,
	mst.MethodologyID MethodologyID,
	ParentItem.CashFlowLineItem ParentKpi
FROM
	CashFlowICCaseValues A WITH (NOLOCK)
	INNER JOIN Mapping_CompanyCashFlowLineItems Map WITH (NOLOCK) on Map.CompanyCashFlowLineItemMappingID = A.CompanyCashFlowLineItemMappingID
	AND Map.IsDeleted = 0
	and A.IsDeleted = 0
	INNER JOIN M_CashFlow_LineItems mst WITH (NOLOCK) on mst.CashFlowLineItemID = Map.CashFlowLineItemID
	and mst.IsDeleted = 0
	INNER JOIN #CompanyDetails CD On Map.PortfolioCompanyID=CD.PortfolioCopanyId
	LEFT JOIN M_CashFlow_LineItems ParentItem WITH (NOLOCK) ON ParentItem.CashFlowLineItemID = Map.ParentLineItemID
		AND ParentItem.IsDeleted = 0
WHERE
	A.IsDeleted = 0
	and Map.IsDeleted = 0
	and mst.IsDeleted = 0
	AND A.BaseValue IS NOT NULL

UNION ALL

Select
	A.CashFlowLineItemID KpiId,
	CASE
        WHEN Mst.IsHeader = 1 THEN NULL
		WHEN IsNumeric(V.KPIValue) = 1  AND mst.KPIInfo='$' THEN CONVERT(VARCHAR(MAX),CONVERT(Decimal(38,8),V.KPIValue)*1000)
        ELSE ISNULL(V.KPIValue, 'NA')
    END AS KPIValue,
	V.Year,
	V.Quarter,
	V.Month,
	CASE
		WHEN (
			V.Month IS NULL
			OR V.Month = 0
		)
		AND V.Quarter IS NULL THEN '(' + MV.HeaderValue + ') ' + Cast(V.Year as varchar)
		WHEN V.Month IS NULL
		OR V.Month = 0 THEN '(' + MV.HeaderValue + ') ' + V.Quarter + ' ' + Cast(V.Year as varchar)
		WHEN V.Quarter IS NULL THEN '(' + MV.HeaderValue + ') ' + [dbo].[GetMonthName](V.Month) + ' ' + Cast(V.Year as varchar)
	END PeriodType,
	A.PortfolioCompanyID PortfolioCompanyId,
	A.DisplayOrder,	
	Mst.IsHeader,
	Mst.IsBoldKPI,
	A.ParentLineItemID ParentId,
	Mst.CashFlowLineItem KPI,
	Mst.KpiInfo KPIInfo,
	Mst.MethodologyID MethodologyID,
	ParentItem.CashFlowLineItem ParentKpi
FROM
	Mapping_CompanyCashFlowLineItems A WITH (NOLOCK)
	INNER JOIN PCFinancialsValues V WITH (NOLOCK) On A.CompanyCashFlowLineItemMappingID = V.MappingId
	INNER JOIN M_CashFlow_LineItems Mst WITH (NOLOCK) on Mst.CashFlowLineItemID = A.CashFlowLineItemID
	INNER JOIN M_ValueTypes MV WITH (NOLOCK) on MV.ValueTypeID = V.ValueTypeID
	INNER JOIN #CompanyDetails CD On A.PortfolioCompanyID=CD.PortfolioCopanyId
	LEFT JOIN M_CashFlow_LineItems ParentItem WITH (NOLOCK) ON ParentItem.CashFlowLineItemID = A.ParentLineItemID
		AND ParentItem.IsDeleted = 0
WHERE
	A.IsDeleted = 0
	AND V.IsDeleted = 0
	AND Mst.IsDeleted = 0
	AND V.ModuleId=@ModuleId
	AND V.KPIValue IS NOT NULL
)
SELECT * FROM CashFlowData
ORDER BY DisplayOrder
OPTION (RECOMPILE)

END

-- Clean up the temp table at the end of the procedure
IF OBJECT_ID('tempdb..#CompanyDetails') IS NOT NULL
    DROP TABLE #CompanyDetails

-- Set the session back to its original state
SET NOCOUNT OFF
END
GO
MERGE INTO FinancialValueTypes AS target
USING (
    VALUES 
        ('IC2', GETDATE(), 3, NULL, NULL, 0, NULL),
        ('IC3', GETDATE(), 3, NULL, NULL, 0, NULL),
        ('IC4', GETDATE(), 3, NULL, NULL, 0, NULL),
        ('IC5', GETDATE(), 3, NULL, NULL, 0, NULL)
) AS source ( Name, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted, Alias) ON target.Name = source.Name
WHEN NOT MATCHED THEN INSERT (Name, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted, Alias) VALUES (source.Name, source.CreatedOn, source.CreatedBy, source.ModifiedOn, source.ModifiedBy, source.IsDeleted, source.Alias);
GO
ALTER VIEW [dbo].[ViewMasterKpiChartValues]
AS
SELECT DISTINCT val.PortfolioCompanyID PortfolioCompanyId
	,mst.MasterKpiID KPIId
	,val.Month
	,val.Year
	,val.Quarter
	,val.ValueTypeID ValueTypeId
	,vt.HeaderValue ValueType
	,mst.KPI
	,mst.KpiInfo
	,val.KPIValue
	,CASE 
		WHEN vt.HeaderValue IN ('Actual', 'Actual YTD', 'Actual LTM')
			THEN val.KPIValue
		ELSE NULL
		END ActualValue
	,CASE 
		WHEN vt.HeaderValue IN ('Budget', 'Budget YTD', 'Budget LTM')
			THEN val.KPIValue
		ELSE NULL
		END BudgetValue
	,CASE 
		WHEN vt.HeaderValue IN ('Forecast', 'Forecast YTD', 'Forecast LTM')
			THEN val.KPIValue
		ELSE NULL
		END ForecastValue
	,CASE 
		WHEN vt.HeaderValue = 'IC'
			THEN val.KPIValue
		ELSE NULL
		END ICValue
	,CASE 
		WHEN vt.HeaderValue ='IC2'
			THEN val.KPIValue
		ELSE NULL
		END IC2Value
	,CASE 
		WHEN vt.HeaderValue ='IC3'
			THEN val.KPIValue
		ELSE NULL
		END IC3Value
	,CASE 
		WHEN vt.HeaderValue ='IC4'
			THEN val.KPIValue
		ELSE NULL
		END IC4Value
	,CASE 
		WHEN vt.HeaderValue ='IC5'
			THEN val.KPIValue
		ELSE NULL
		END IC5Value,
		mst.ModuleID ModuleId
FROM Mapping_Kpis map
INNER JOIN PcMasterKpiValues val ON map.Mapping_KpisID = val.MappingKpisID
	AND map.PortfolioCompanyID = val.PortfolioCompanyID
INNER JOIN M_MasterKpis mst ON mst.MasterKpiID = map.KpiID
INNER JOIN M_ValueTypes vt ON vt.ValueTypeID = val.ValueTypeID
WHERE 
	val.IsDeleted = 0
	AND mst.IsDeleted = 0
	AND vt.IsDeleted = 0
	AND map.IsDeleted = 0
GO
ALTER VIEW [dbo].[view_Analytics_MasterKpiValues]
As
SELECT        Map.KpiID, A.KPIValue, A.Year, A.Quarter, A.Month, CASE WHEN (Month IS NULL OR
                         Month = 0) AND Quarter IS NULL THEN CAST(A.Year AS varchar) WHEN (Month IS NULL OR
                         Month = 0) THEN A.Quarter + ' ' + CAST(A.Year AS varchar) WHEN Quarter IS NULL THEN [dbo].[GetMonthName](A.Month) + ' ' + CAST(A.Year AS varchar) END AS Period, CASE WHEN (Month IS NULL OR
                         Month = 0) AND Quarter IS NULL THEN CAST(CAST(A.Year AS varchar) + '-12-31' AS VARCHAR) WHEN (Month IS NULL OR
                         Month = 0) THEN CAST(dbo.GetLastDateOfQuarter(SUBSTRING(A.Quarter, 2, 1), A.Year) AS VARCHAR) WHEN Quarter IS NULL THEN CAST(dbo.GetLastDateOfMonth(A.Month, A.Year) AS VARCHAR) END AS PeriodDate, 
                         (CASE WHEN A.ValueTypeId IN (11, 13, 14) THEN (CASE WHEN Quarter IS NOT NULL THEN 'Quarterly YTD' WHEN Month > 0 THEN 'Monthly YTD' ELSE 'Not Applicable' END) WHEN A.ValueTypeId IN (16, 17, 18) 
                         THEN (CASE WHEN Quarter IS NOT NULL THEN 'Quarterly LTM' WHEN Month > 0 THEN 'Monthly LTM' ELSE 'Not Applicable' END) ELSE (CASE WHEN (Month IS NULL OR
                         Month = 0) AND Quarter IS NULL THEN 'Yearly' WHEN (Month IS NULL OR
                         Month = 0) THEN 'Quarterly' WHEN Quarter IS NULL THEN 'Monthly' END) END) AS PeriodType, CASE WHEN
                             (SELECT        TOP 1 KpiID
                               FROM            Mapping_Kpis
                               WHERE        EXISTS
                                                             (SELECT DISTINCT *
                                                               FROM            M_MasterKpis b INNER JOIN
                                                                                         Mapping_Kpis m ON m.KpiID = b.MasterKpiID
                                                               WHERE        b.KPI = mst.KPI) AND PortfolioCompanyID = Map.PortfolioCompanyID) IS NOT NULL AND Map.ParentKPIID > 0 THEN
                             (SELECT        KPI
                               FROM            M_MasterKpis
                               WHERE        MasterKpiID = Map.ParentKPIID) + '-' + mst.KPI ELSE mst.KPI END AS Kpi, 
							( SELECT        TOP 1 AliasName
                               FROM            M_SubPageFields
                               WHERE        name = (SELECT TOP 1 Name from M_KpiModules WHERE ModuleID = A.ModuleID)) AS KpiType, A.PortfolioCompanyID, Map.DisplayOrder, Map.ParentKPIID AS ParentId, mst.KpiInfo, mst.MethodologyID, (CASE WHEN V.ValueTypeId IN (14, 16, 4) 
                         THEN 'Actual' WHEN V.ValueTypeId IN (11, 17, 5) THEN 'Budget' WHEN V.ValueTypeId = 3 THEN 'Ic' WHEN V.ValueTypeId = 22 THEN 'IC2' WHEN V.ValueTypeId = 23 THEN 'IC3' WHEN V.ValueTypeId = 24 THEN 'IC4' WHEN V.ValueTypeId = 25 THEN 'IC5' ELSE 'Forecast' END) AS ValueType, A.ModuleID
FROM            dbo.Mapping_Kpis AS Map INNER JOIN
                         dbo.PCMasterKpiValues AS A ON Map.PortfolioCompanyID = A.PortfolioCompanyID AND Map.Mapping_KpisID = A.MappingKpisID AND Map.ModuleID = A.ModuleID INNER JOIN
                         dbo.M_MasterKpis AS mst ON mst.MasterKpiID = Map.KpiID AND mst.ModuleID = Map.ModuleID INNER JOIN
                         dbo.M_ValueTypes AS V ON A.ValueTypeID = V.ValueTypeID
WHERE        (A.IsDeleted = 0) AND (Map.IsDeleted = 0) AND (mst.IsDeleted = 0)

GO
ALTER PROCEDURE  [dbo].[GetPortfolioCompanyKpiChildData]
(
		@ParentKpiID INT = NULL,
		@CompIds nvarchar(MAX), 
        @FYear nvarchar(200),
		@TYear nvarchar(200),
		@ModuleId nvarchar(200)
)
AS
BEGIN
    DECLARE @ParentKPI NVARCHAR(MAX);
    DECLARE @SQL NVARCHAR(MAX);

    -- Set the value of the variable
	IF(@ModuleId = 3)
	BEGIN 
		SET @ParentKPI = (SELECT TOP 1 KPI FROM M_SectorwiseOperationalKPI WHERE SectorwiseOperationalKPIID = @ParentKpiID);
		-- Construct the dynamic SQL query
	SET @SQL = ' SELECT Mst.MethodologyId AS MethodologyId, Mst.KpiInfo, A.KpiID AS KpiId, V.KPIValue AS KpiValue, A.PortfolioCompanyID AS PortfolioCompanyId, CASE WHEN Month IS NULL AND Quarter IS NULL THEN CAST(Q.Year AS VARCHAR) 
	WHEN Month IS NULL THEN Q.Quarter + '' '' + CAST(Q.Year AS VARCHAR) WHEN Quarter IS NULL THEN [dbo].[GetMonthName](Q.Month) + '' '' + CAST(Q.Year AS VARCHAR) END AS Period, ''Actual'' AS ValueType, CASE WHEN Month IS NULL 
	AND Quarter IS NULL THEN ''Yearly'' WHEN Month IS NULL THEN ''Quarterly'' WHEN Quarter IS NULL THEN ''Monthly'' END AS PeriodType, CASE WHEN Month IS NULL AND Quarter IS NULL THEN CAST(CAST(Q.Year AS VARCHAR) + ''-12-31'' AS VARCHAR) 
	WHEN Month IS NULL THEN CAST(dbo.GetLastDateOfQuarter(SUBSTRING(Q.Quarter, 2, 1), Q.Year) AS VARCHAR) WHEN Quarter IS NULL THEN CAST(dbo.GetLastDateOfMonth(Q.Month, Q.Year) AS VARCHAR) END AS PeriodDate, Q.Year, Quarter
	, CASE WHEN (SELECT TOP 1 KpiID FROM Mapping_PortfolioOperationalKPI WHERE EXISTS (SELECT DISTINCT * FROM M_SectorwiseOperationalKPI b INNER JOIN Mapping_PortfolioOperationalKPI m ON m.KpiID = b.SectorwiseOperationalKPIID WHERE b.KPI = mst.KPI 
	AND m.KpiID != mst.SectorwiseOperationalKPIID) AND PortfolioCompanyID = A.PortfolioCompanyID) IS NOT NULL AND A.ParentKPIID > 0 THEN (SELECT KPI FROM M_SectorwiseOperationalKPI WHERE SectorwiseOperationalKPIID = A.ParentKPIID) + ''-'' + mst.KPI ELSE mst.KPI END AS ' + QUOTENAME(@ParentKPI) + ' 
	FROM PortfolioCompanyOperationalKPIQuarters Q INNER JOIN Mapping_PortfolioOperationalKPI A ON Q.PortfolioCompanyID = A.PortfolioCompanyID INNER JOIN PortfolioCompanyOperationalKPIValues V ON A.KpiID = V.SectorwiseOperationalKPIID AND 
	V.PortfolioCompanyOperationalKPIQuarterID = Q.PortfolioCompanyOperationalKPIQuarterID INNER JOIN M_SectorwiseOperationalKPI Mst ON Mst.SectorwiseOperationalKPIID = A.KpiID WHERE A.IsDeleted = 0 AND V.IsDeleted = 0 AND Q.IsDeleted = 0 
	AND ValueTypeID = 4 AND A.ParentKPIID = @ParentKpiID AND A.PortfolioCompanyID IN (' + @CompIds + ') AND Q.Year BETWEEN @FYear AND @TYear UNION ALL 
	SELECT Mst.MethodologyId AS MethodologyId, Mst.KpiInfo, A.KpiID AS KpiId, V.KPIValue AS KpiValue, A.PortfolioCompanyID AS PortfolioCompanyId, CASE WHEN Month IS NULL AND Quarter IS NULL THEN CAST(Q.Year AS VARCHAR) 
	WHEN Month IS NULL THEN Q.Quarter + '' '' + CAST(Q.Year AS VARCHAR) WHEN Quarter IS NULL THEN [dbo].[GetMonthName](Q.Month) + '' '' + CAST(Q.Year AS VARCHAR) END AS Period, ''Budget'' AS ValueType, CASE WHEN Month IS NULL 
	AND Quarter IS NULL THEN ''Yearly'' WHEN Month IS NULL THEN ''Quarterly'' WHEN Quarter IS NULL THEN ''Monthly'' END AS PeriodType, CASE WHEN Month IS NULL AND Quarter IS NULL THEN CAST(CAST(Q.Year AS VARCHAR) + ''-12-31'' AS VARCHAR) 
	WHEN Month IS NULL THEN CAST(dbo.GetLastDateOfQuarter(SUBSTRING(Q.Quarter, 2, 1), Q.Year) AS VARCHAR) WHEN Quarter IS NULL THEN CAST(dbo.GetLastDateOfMonth(Q.Month, Q.Year) AS VARCHAR) END AS PeriodDate, Q.Year, Quarter, 
	 CASE WHEN (SELECT TOP 1 KpiID FROM Mapping_PortfolioOperationalKPI WHERE EXISTS (SELECT DISTINCT * FROM M_SectorwiseOperationalKPI b INNER JOIN Mapping_PortfolioOperationalKPI m ON m.KpiID = b.SectorwiseOperationalKPIID 
	 WHERE b.KPI = mst.KPI AND m.KpiID != mst.SectorwiseOperationalKPIID) AND PortfolioCompanyID = A.PortfolioCompanyID) IS NOT NULL AND A.ParentKPIID > 0 THEN (SELECT KPI FROM M_SectorwiseOperationalKPI WHERE 
	 SectorwiseOperationalKPIID = A.ParentKPIID) + ''-'' + mst.KPI ELSE mst.KPI END AS ' + QUOTENAME(@ParentKPI) + ' FROM PortfolioCompanyOperationalKPIQuarters Q INNER JOIN Mapping_PortfolioOperationalKPI A ON Q.PortfolioCompanyID = A.PortfolioCompanyID 
	 INNER JOIN PortfolioCompanyOperationalKPIValues V ON A.KpiID = V.SectorwiseOperationalKPIID AND V.PortfolioCompanyOperationalKPIQuarterID = Q.PortfolioCompanyOperationalKPIQuarterID INNER JOIN M_SectorwiseOperationalKPI Mst ON
	 Mst.SectorwiseOperationalKPIID = A.KpiID WHERE A.IsDeleted = 0 AND V.IsDeleted = 0 AND Q.IsDeleted = 0 AND ValueTypeID = 5 AND A.ParentKPIID = @ParentKpiID AND A.PortfolioCompanyID IN (' + @CompIds + ') AND Q.Year BETWEEN
	 @FYear AND @TYear UNION ALL SELECT Mst.MethodologyId AS MethodologyId, Mst.KpiInfo, A.KpiID AS KpiId, V.KPIValue AS KpiValue, A.PortfolioCompanyID AS PortfolioCompanyId, CASE WHEN Month IS NULL AND Quarter IS NULL 
	 THEN CAST(Q.Year AS VARCHAR) WHEN Month IS NULL THEN Q.Quarter + '' '' + CAST(Q.Year AS VARCHAR) WHEN Quarter IS NULL THEN [dbo].[GetMonthName](Q.Month) + '' '' + CAST(Q.Year AS VARCHAR) END AS Period, ''Forecast'' AS ValueType,
	 CASE WHEN Month IS NULL AND Quarter IS NULL THEN ''Yearly'' WHEN Month IS NULL THEN ''Quarterly'' WHEN Quarter IS NULL THEN ''Monthly'' END AS PeriodType, CASE WHEN Month IS NULL AND Quarter IS NULL THEN CAST(CAST(Q.Year AS VARCHAR) + ''-12-31'' AS VARCHAR) 
	 WHEN Month IS NULL THEN CAST(dbo.GetLastDateOfQuarter(SUBSTRING(Q.Quarter, 2, 1), Q.Year) AS VARCHAR) WHEN Quarter IS NULL THEN CAST(dbo.GetLastDateOfMonth(Q.Month, Q.Year) AS VARCHAR) END AS PeriodDate, Q.Year, Quarter,  
	 CASE WHEN (SELECT TOP 1 KpiID FROM Mapping_PortfolioOperationalKPI WHERE EXISTS (SELECT DISTINCT * FROM M_SectorwiseOperationalKPI b INNER JOIN Mapping_PortfolioOperationalKPI m ON m.KpiID = b.SectorwiseOperationalKPIID WHERE
	 b.KPI = mst.KPI AND m.KpiID != mst.SectorwiseOperationalKPIID) AND PortfolioCompanyID = A.PortfolioCompanyID) IS NOT NULL AND A.ParentKPIID > 0 THEN (SELECT KPI FROM M_SectorwiseOperationalKPI WHERE SectorwiseOperationalKPIID = A.ParentKPIID) + ''-'' + mst.KPI ELSE mst.KPI END AS ' + QUOTENAME(@ParentKPI) + ' 
	 FROM PortfolioCompanyOperationalKPIQuarters Q INNER JOIN Mapping_PortfolioOperationalKPI A ON Q.PortfolioCompanyID = A.PortfolioCompanyID INNER JOIN PortfolioCompanyOperationalKPIValues V ON A.KpiID = V.SectorwiseOperationalKPIID AND 
	 V.PortfolioCompanyOperationalKPIQuarterID = Q.PortfolioCompanyOperationalKPIQuarterID INNER JOIN M_SectorwiseOperationalKPI Mst ON Mst.SectorwiseOperationalKPIID = A.KpiID WHERE A.IsDeleted = 0 AND V.IsDeleted = 0
	 AND Q.IsDeleted = 0 AND ValueTypeID = 6 AND A.ParentKPIID = @ParentKpiID AND A.PortfolioCompanyID IN (' + @CompIds + ') AND Q.Year BETWEEN @FYear AND @TYear ' 
	END
	ELSE IF(@ModuleId IN (1,2,17,18,19,20,21,22,23,24,25,26,27,28,29,30))
	BEGIN
		SET @ParentKPI = (SELECT TOP 1 KPI FROM M_MasterKpis WHERE MasterKpiID = @ParentKpiID AND ModuleID = @ModuleId);
		SET @SQL = 'SELECT Map.KpiID AS KpiId, A.KPIValue AS KpiValue, A.Year, A.Quarter, A.Month, CASE WHEN (A.Month IS NULL OR A.Month = 0) AND A.Quarter IS NULL THEN CAST(A.Year AS VARCHAR) WHEN (A.Month IS NULL OR A.Month = 0) 
		THEN A.Quarter + '' '' + CAST(A.Year AS VARCHAR) WHEN A.Quarter IS NULL THEN [dbo].[GetMonthName](A.Month) + '' '' + CAST(A.Year AS VARCHAR) END AS Period, CASE WHEN (A.Month IS NULL OR A.Month = 0) AND A.Quarter IS NULL 
		THEN CAST(CAST(A.Year AS VARCHAR) + ''-12-31'' AS VARCHAR) WHEN (A.Month IS NULL OR A.Month = 0) THEN CAST(dbo.GetLastDateOfQuarter(SUBSTRING(A.Quarter, 2, 1), A.Year) AS VARCHAR) WHEN A.Quarter IS NULL 
		THEN CAST(dbo.GetLastDateOfMonth(A.Month, A.Year) AS VARCHAR) END AS PeriodDate, CASE WHEN A.ValueTypeId IN (11, 13, 14) THEN CASE WHEN A.Quarter IS NOT NULL THEN ''Quarterly YTD'' WHEN A.Month > 0 THEN ''Monthly YTD'' 
		ELSE ''Not Applicable'' END WHEN A.ValueTypeId IN (16, 17, 18) THEN CASE WHEN A.Quarter IS NOT NULL THEN ''Quarterly LTM'' WHEN A.Month > 0 THEN ''Monthly LTM'' ELSE ''Not Applicable'' END ELSE CASE WHEN (A.Month IS NULL 
		OR A.Month = 0) AND A.Quarter IS NULL THEN ''Yearly'' WHEN (A.Month IS NULL OR A.Month = 0) THEN ''Quarterly'' WHEN A.Quarter IS NULL THEN ''Monthly'' END END AS PeriodType, CASE WHEN (SELECT TOP 1 KpiID FROM Mapping_Kpis 
		WHERE EXISTS (SELECT DISTINCT * FROM M_MasterKpis b INNER JOIN Mapping_Kpis m ON m.KpiID = b.MasterKpiID WHERE b.KPI = mst.KPI AND m.KpiID != mst.MasterKpiID) AND PortfolioCompanyID = Map.PortfolioCompanyID) IS NOT NULL AND 
		Map.ParentKPIID > 0 THEN (SELECT KPI FROM M_MasterKpis WHERE MasterKpiID = Map.ParentKPIID) + ''-'' + mst.KPI ELSE mst.KPI END AS ' + QUOTENAME(@ParentKPI) + ', A.PortfolioCompanyID AS PortfolioCompanyId,
		mst.KpiInfo, mst.MethodologyID AS MethodologyId, CASE WHEN V.ValueTypeId IN (14, 16, 4) THEN ''Actual'' WHEN V.ValueTypeId IN (11, 17, 5) THEN ''Budget'' WHEN V.ValueTypeId = 3 THEN ''Ic''  
		WHEN V.ValueTypeId = 22 THEN ''IC2''  WHEN V.ValueTypeId = 23 THEN ''IC3''  WHEN V.ValueTypeId = 24 THEN ''IC4''  WHEN V.ValueTypeId = 25 THEN ''IC5'' 
		ELSE ''Forecast'' END AS ValueType FROM Mapping_Kpis Map INNER JOIN PCMasterKpiValues A ON Map.PortfolioCompanyID = A.PortfolioCompanyID AND Map.Mapping_KpisID = A.MappingKpisID AND 
		Map.ModuleID = A.ModuleID INNER JOIN M_MasterKpis mst ON mst.MasterKpiID = Map.KpiID AND mst.ModuleID = Map.ModuleID INNER JOIN M_ValueTypes V ON A.ValueTypeID = V.ValueTypeID WHERE A.IsDeleted = 0 AND Map.IsDeleted = 0
		AND mst.IsDeleted = 0 AND Map.ParentKPIID = @ParentKpiID AND Map.PortfolioCompanyID IN (' + @CompIds + ') AND A.Year BETWEEN @FYear AND @TYear AND A.ModuleID = ' +@ModuleId;
	END
	ELSE IF(@ModuleId = 4)
	BEGIN
		SET @ParentKPI = (SELECT TOP 1 KPI FROM M_InvestmentKPI WHERE InvestmentKPIId = @ParentKpiID);
		SET @SQL = ' SELECT Map.KpiID AS KpiId, A.KPIActualValue AS KpiValue, A.Year, CASE WHEN (A.Month IS NULL OR A.Month = 0) AND A.Quarter IS NULL THEN CAST(A.Year AS VARCHAR) WHEN (A.Month IS NULL OR A.Month = 0) THEN A.Quarter + 
		'' '' + CAST(A.Year AS VARCHAR) WHEN A.Quarter IS NULL THEN [dbo].[GetMonthName](A.Month) + '' '' + CAST(A.Year AS VARCHAR) END AS Period, CASE WHEN (A.Month IS NULL OR A.Month = 0) AND A.Quarter IS NULL THEN CAST(CAST(A.Year AS
		VARCHAR) + ''-12-31'' AS VARCHAR) WHEN (A.Month IS NULL OR A.Month = 0) THEN CAST(dbo.GetLastDateOfQuarter(SUBSTRING(A.Quarter, 2, 1), A.Year) AS VARCHAR) WHEN A.Quarter IS NULL THEN CAST(dbo.GetLastDateOfMonth(A.Month, A.Year) AS 
		VARCHAR) END AS PeriodDate, CASE WHEN (A.Month IS NULL OR A.Month = 0) AND A.Quarter IS NULL THEN ''Yearly'' WHEN (A.Month IS NULL OR A.Month = 0) THEN ''Quarterly'' WHEN A.Quarter IS NULL THEN ''Monthly'' END AS PeriodType, CASE WHEN 
		(SELECT TOP 1 KpiID FROM Mapping_PortfolioInvestmentKPI WHERE EXISTS (SELECT DISTINCT * FROM M_InvestmentKPI b INNER JOIN Mapping_PortfolioInvestmentKPI m ON m.KpiID = b.InvestmentKPIId WHERE b.KPI = mst.KPI AND m.KpiID != mst.InvestmentKPIId) 
		AND PortfolioCompanyID = Map.PortfolioCompanyID) IS NOT NULL AND Map.ParentKPIID > 0 THEN (SELECT KPI FROM M_InvestmentKPI WHERE InvestmentKPIId = Map.ParentKPIID) + ''-'' + mst.KPI ELSE mst.KPI END AS ' + QUOTENAME(@ParentKPI) + ', A.PortfolioCompanyID AS PortfolioCompanyId, A.Month, A.Quarter, mst.KpiInfo, v.HeaderValue AS ValueType, mst.MethodologyID FROM Mapping_PortfolioInvestmentKPI Map 
		INNER JOIN PCInvestmentKPIQuarterlyValue A ON Map.PortfolioCompanyID = A.PortfolioCompanyID AND Map.KpiID = A.InvestmentKPIID INNER JOIN M_InvestmentKPI mst ON mst.InvestmentKPIId = Map.KpiID INNER JOIN M_ValueTypes V ON A.ValueTypeID = V.ValueTypeID 
		WHERE A.IsDeleted = 0 AND Map.IsDeleted = 0 AND mst.IsDeleted = 0 AND Map.ParentKPIID = @ParentKpiID AND Map.PortfolioCompanyID IN ('+@CompIds+') AND A.Year BETWEEN @FYear AND @TYear;';
	END
	ELSE IF(@ModuleId = 5)
	BEGIN
		SET @ParentKPI = (SELECT TOP 1 KPI FROM M_CompanyKPI WHERE CompanyKPIID = @ParentKpiID);
		SET @SQL = ' SELECT A.CompanyKPIID AS KpiId, A.KPIActualValue AS KpiValue, A.Year, A.Quarter, A.Month, CASE WHEN A.Month IS NULL AND A.Quarter IS NULL THEN CAST(A.Year AS VARCHAR) WHEN A.Month IS NULL THEN A.Quarter + '' '' + 
		CAST(A.Year AS VARCHAR) WHEN A.Quarter IS NULL THEN [dbo].[GetMonthName](A.Month) + '' '' + CAST(A.Year AS VARCHAR) END AS Period, CASE WHEN A.Month IS NULL AND A.Quarter IS NULL THEN CAST(CAST(A.Year AS VARCHAR) + ''-12-31'' AS VARCHAR)
		WHEN A.Month IS NULL THEN CAST(dbo.GetLastDateOfQuarter(SUBSTRING(A.Quarter, 2, 1), A.Year) AS VARCHAR) WHEN A.Quarter IS NULL THEN CAST(dbo.GetLastDateOfMonth(A.Month, A.Year) AS VARCHAR) END AS PeriodDate, CASE WHEN A.Month IS NULL 
		AND A.Quarter IS NULL THEN ''Yearly'' WHEN A.Month IS NULL THEN ''Quarterly'' WHEN A.Quarter IS NULL THEN ''Monthly'' END AS PeriodType, A.PortfolioCompanyID AS PortfolioCompanyId, mst.KPI AS ' + QUOTENAME(@ParentKPI) + ', mst.KpiInfo AS KpiInfo, ''Actual'' 
		AS ValueType FROM PCCompanyKPIMonthlyValue A INNER JOIN Mapping_PortfolioCompanyKPI Map ON Map.PortfolioCompanyID = A.PortfolioCompanyID AND Map.KpiID = A.CompanyKPIID INNER JOIN M_CompanyKPI mst ON mst.CompanyKPIID = Map.KpiID WHERE 
		A.IsDeleted = 0 AND Map.IsDeleted = 0 AND mst.IsDeleted = 0 AND Map.ParentKPIID = @ParentKpiID AND Map.PortfolioCompanyID IN (' + @CompIds + ') AND A.Year BETWEEN @FYear AND @TYear UNION ALL SELECT A.CompanyKPIID AS KpiId, A.KPIBudgetValue AS KpiValue, A.Year, A.Quarter, A.Month, CASE WHEN A.Month IS NULL AND A.Quarter IS NULL THEN CAST(A.Year AS VARCHAR)
		WHEN A.Month IS NULL THEN A.Quarter + '' '' + CAST(A.Year AS VARCHAR) WHEN A.Quarter IS NULL THEN [dbo].[GetMonthName](A.Month) + '' '' + CAST(A.Year AS VARCHAR) END AS Period, CASE WHEN A.Month IS NULL AND A.Quarter IS NULL THEN 
		CAST(CAST(A.Year AS VARCHAR) + ''-12-31'' AS VARCHAR) WHEN A.Month IS NULL THEN CAST(dbo.GetLastDateOfQuarter(SUBSTRING(A.Quarter, 2, 1), A.Year) AS VARCHAR) WHEN A.Quarter IS NULL THEN CAST(dbo.GetLastDateOfMonth(A.Month, A.Year) AS 
		VARCHAR) END AS PeriodDate, CASE WHEN A.Month IS NULL AND A.Quarter IS NULL THEN ''Yearly'' WHEN A.Month IS NULL THEN ''Quarterly'' WHEN A.Quarter IS NULL THEN ''Monthly'' END AS PeriodType, A.PortfolioCompanyID AS PortfolioCompanyId,
		mst.KPI AS ' + QUOTENAME(@ParentKPI) + ', mst.KpiInfo AS KpiInfo, ''Budget'' AS ValueType FROM PCCompanyKPIMonthlyValue A INNER JOIN Mapping_PortfolioCompanyKPI Map ON Map.PortfolioCompanyID = A.PortfolioCompanyID AND Map.KpiID = A.CompanyKPIID INNER JOIN M_CompanyKPI
		mst ON mst.CompanyKPIID = Map.KpiID WHERE A.IsDeleted = 0 AND Map.IsDeleted = 0 AND mst.IsDeleted = 0 AND Map.ParentKPIID = @ParentKpiID AND Map.PortfolioCompanyID IN (' + @CompIds + ') AND A.Year BETWEEN @FYear AND @TYear UNION ALL SELECT A.CompanyKPIID AS KpiId, A.KPIValue AS KpiValue, A.Year, A.Quarter, A.Month, CASE WHEN A.Month IS NULL AND A.Quarter
		IS NULL THEN CAST(A.Year AS VARCHAR) WHEN A.Month IS NULL THEN A.Quarter + '' '' + CAST(A.Year AS VARCHAR) WHEN A.Quarter IS NULL THEN [dbo].[GetMonthName](A.Month) + '' '' + CAST(A.Year AS VARCHAR) END AS Period, CASE WHEN A.Month 
		IS NULL AND A.Quarter IS NULL THEN CAST(CAST(A.Year AS VARCHAR) + ''-12-31'' AS VARCHAR) WHEN A.Month IS NULL THEN CAST(dbo.GetLastDateOfQuarter(SUBSTRING(A.Quarter, 2, 1), A.Year) AS VARCHAR) WHEN A.Quarter IS NULL THEN CAST(dbo.GetLastDateOfMonth(A.Month, A.Year) 
		AS VARCHAR) END AS PeriodDate, CASE WHEN A.Month IS NULL AND A.Quarter IS NULL THEN ''Yearly'' WHEN A.Month IS NULL THEN ''Quarterly'' WHEN A.Quarter IS NULL THEN ''Monthly'' END AS PeriodType, A.PortfolioCompanyID AS PortfolioCompanyId,
		mst.KPI AS ' + QUOTENAME(@ParentKPI) + ', mst.KpiInfo AS KpiInfo, ''Forecast'' AS ValueType FROM CompanyKPIForecastValues A INNER JOIN Mapping_PortfolioCompanyKPI Map ON Map.PortfolioCompanyID = A.PortfolioCompanyID AND Map.KpiID = A.CompanyKPIID 
		INNER JOIN M_CompanyKPI mst ON mst.CompanyKPIID = Map.KpiID WHERE A.IsDeleted = 0 AND Map.IsDeleted = 0 AND mst.IsDeleted = 0 AND Map.ParentKPIID = @ParentKpiID AND Map.PortfolioCompanyID IN (' + @CompIds + ') AND A.Year BETWEEN @FYear AND @TYear; ';
	END
	ELSE IF(@ModuleId = 6)
	BEGIN
		SET @ParentKPI = (SELECT TOP 1 KPI FROM M_ImpactKPI WHERE ImpactKPIID = @ParentKpiID)
		SET @SQL = ' SELECT Map.ImpactKPIID AS KpiId, A.KPIActualValue AS KpiValue, A.Year, A.Quarter, A.Month, CASE WHEN (A.Month IS NULL OR A.Month = 0) AND A.Quarter IS NULL THEN CAST(A.Year AS VARCHAR) WHEN (A.Month IS NULL OR 
		A.Month = 0) THEN A.Quarter + '' '' + CAST(A.Year AS VARCHAR) WHEN A.Quarter IS NULL THEN [dbo].[GetMonthName](A.Month) + '' '' + CAST(A.Year AS VARCHAR) END AS Period, CASE WHEN (A.Month IS NULL OR A.Month = 0) AND A.Quarter 
		IS NULL THEN CAST(CAST(A.Year AS VARCHAR) + ''-12-31'' AS VARCHAR) WHEN (A.Month IS NULL OR A.Month = 0) THEN CAST(dbo.GetLastDateOfQuarter(SUBSTRING(A.Quarter, 2, 1), A.Year) AS VARCHAR) WHEN A.Quarter IS NULL THEN 
		CAST(dbo.GetLastDateOfMonth(A.Month, A.Year) AS VARCHAR) END AS PeriodDate, CASE WHEN A.ValueTypeId IN (11, 13, 14) THEN CASE WHEN A.Quarter IS NOT NULL THEN ''Quarterly YTD'' WHEN A.Month > 0 THEN ''Monthly YTD'' ELSE ''Not
		Applicable'' END WHEN A.ValueTypeId IN (16, 17, 18) THEN CASE WHEN A.Quarter IS NOT NULL THEN ''Quarterly LTM'' WHEN A.Month > 0 THEN ''Monthly LTM'' ELSE ''Not Applicable'' END ELSE CASE WHEN (A.Month IS NULL OR A.Month = 0) 
		AND A.Quarter IS NULL THEN ''Yearly'' WHEN (A.Month IS NULL OR A.Month = 0) THEN ''Quarterly'' WHEN A.Quarter IS NULL THEN ''Monthly'' END END AS PeriodType, CASE WHEN (SELECT TOP 1 ImpactKPIID FROM Mapping_ImpactKPI_Order 
		WHERE EXISTS (SELECT DISTINCT * FROM M_ImpactKPI b INNER JOIN Mapping_ImpactKPI_Order m ON m.ImpactKPIID = b.ImpactKPIID WHERE b.KPI = mst.KPI AND m.ImpactKPIID != mst.ImpactKPIID) AND PortfolioCompanyID = Map.PortfolioCompanyID) 
		IS NOT NULL AND Map.ParentKPIID > 0 THEN (SELECT KPI FROM M_ImpactKPI WHERE ImpactKPIID = Map.ParentKPIID) + ''-'' + mst.KPI ELSE mst.KPI END AS ' + QUOTENAME(@ParentKPI) + ', A.PortfolioCompanyID AS PortfolioCompanyId, mst.KpiInfo AS KpiInfo, mst.MethodologyID AS MethodologyId, CASE WHEN 
		V.ValueTypeId IN (14, 16, 4) THEN ''Actual'' WHEN V.ValueTypeId IN (11, 17, 5) THEN ''Budget'' WHEN V.ValueTypeId = 3 THEN ''Ic'' ELSE ''Forecast'' END AS ValueType FROM Mapping_ImpactKPI_Order Map INNER JOIN 
		PCImpactKPIQuarterlyValue A ON Map.PortfolioCompanyID = A.PortfolioCompanyID AND Map.ImpactKPIID = A.ImpactKPIID INNER JOIN M_ImpactKPI mst ON mst.ImpactKPIID = Map.ImpactKPIID INNER JOIN M_ValueTypes V ON A.ValueTypeID = 
		V.ValueTypeID WHERE A.IsDeleted = 0 AND Map.IsDeleted = 0 AND mst.IsDeleted = 0 AND Map.ParentKPIID = @ParentKpiID AND Map.PortfolioCompanyID IN (' + @CompIds + ') AND A.Year BETWEEN @FYear AND @TYear; ';
	END
	ELSE IF(@ModuleId = 7)
	BEGIN
		SET @ParentKPI = (SELECT TOP 1 ProfitAndLossLineItem FROM M_ProfitAndLoss_LineItems WHERE ProfitAndLossLineItemID = @ParentKpiID)
		SET @SQL = 'SELECT mst.ProfitAndLossLineItemID AS KpiId, CASE WHEN IsNumeric(ActualValue) = 1 AND mst.KPIInfo = ''$'' THEN CONVERT(VARCHAR(MAX), CONVERT(Decimal(38, 8), ActualValue) * 1000) ELSE ActualValue END AS KpiValue, A.Year, A.Quarter, A.Month, CASE WHEN (A.Month IS NULL OR A.Month = 0) AND A.Quarter IS NULL THEN CAST(A.Year AS VARCHAR) WHEN (A.Month IS NULL OR A.Month = 0) THEN A.Quarter + '' '' + CAST(A.Year AS VARCHAR) WHEN A.Quarter IS NULL THEN [dbo].[GetMonthName](A.Month) + '' '' + CAST(A.Year AS VARCHAR) END AS Period, CASE WHEN (A.Month IS NULL OR A.Month = 0) AND A.Quarter IS NULL THEN CAST(CAST(A.Year AS VARCHAR) + ''-12-31'' AS VARCHAR) WHEN (A.Month IS NULL OR A.Month = 0) THEN CAST(dbo.GetLastDateOfQuarter(SUBSTRING(A.Quarter, 2, 1), A.Year) AS VARCHAR) WHEN A.Quarter IS NULL THEN CAST(dbo.GetLastDateOfMonth(A.Month, A.Year) AS VARCHAR) END AS PeriodDate, CASE WHEN (A.Month IS NULL OR A.Month = 0) AND A.Quarter IS NULL THEN ''Yearly'' WHEN (A.Month IS NULL OR A.Month = 0) THEN ''Quarterly'' WHEN A.Quarter IS NULL THEN ''Monthly'' END AS PeriodType, CASE WHEN EXISTS ( SELECT 1 FROM Mapping_CompanyProfitAndLossLineItems m INNER JOIN M_ProfitAndLoss_LineItems b ON m.ProfitAndLossLineItemID = b.ProfitAndLossLineItemID WHERE b.ProfitAndLossLineItem = mst.ProfitAndLossLineItem AND m.PortfolioCompanyID = Map.PortfolioCompanyID ) AND Map.ParentLineItemID > 0 THEN ( SELECT ProfitAndLossLineItem FROM M_ProfitAndLoss_LineItems WHERE ProfitAndLossLineItemID = Map.ParentLineItemID ) + ''-'' + mst.ProfitAndLossLineItem ELSE mst.ProfitAndLossLineItem END AS ' + QUOTENAME(@ParentKPI) + ', Map.PortfolioCompanyID AS PortfolioCompanyId, mst.KpiInfo, mst.MethodologyID AS MethodologyId, ''Actual'' AS ValueType FROM ProfitAndLossValues A INNER JOIN Mapping_CompanyProfitAndLossLineItems Map ON Map.CompanyProfitAndLossLineItemMappingID = A.CompanyProfitAndLossLineItemMappingID AND Map.IsDeleted = 0 AND A.IsDeleted = 0 INNER JOIN M_ProfitAndLoss_LineItems mst ON mst.ProfitAndLossLineItemID = Map.ProfitAndLossLineItemID AND mst.IsDeleted = 0 WHERE A.IsDeleted = 0 AND Map.IsDeleted = 0 AND mst.IsDeleted = 0 AND A.ActualValue IS NOT NULL AND Map.ParentLineItemID = @ParentKpiID AND Map.PortfolioCompanyID IN ('+@CompIds+') AND A.Year BETWEEN @FYear AND @TYear UNION ALL SELECT mst.ProfitAndLossLineItemID AS KpiId, CASE WHEN IsNumeric(BudgetValue) = 1 AND mst.KPIInfo = ''$'' THEN CONVERT(VARCHAR, CONVERT(Decimal(38, 8), BudgetValue) * 1000) ELSE BudgetValue END AS KpiValue, A.Year, A.Quarter, A.Month, CASE WHEN (A.Month IS NULL OR A.Month = 0) AND A.Quarter IS NULL THEN CAST(A.Year AS VARCHAR) WHEN (A.Month IS NULL OR A.Month = 0) THEN A.Quarter + '' '' + CAST(A.Year AS VARCHAR) WHEN A.Quarter IS NULL THEN [dbo].[GetMonthName](A.Month) + '' '' + CAST(A.Year AS VARCHAR) END AS Period, CASE WHEN (A.Month IS NULL OR A.Month = 0) AND A.Quarter IS NULL THEN CAST(CAST(A.Year AS VARCHAR) + ''-12-31'' AS VARCHAR) WHEN (A.Month IS NULL OR A.Month = 0) THEN CAST(dbo.GetLastDateOfQuarter(SUBSTRING(A.Quarter, 2, 1), A.Year) AS VARCHAR) WHEN A.Quarter IS NULL THEN CAST(dbo.GetLastDateOfMonth(A.Month, A.Year) AS VARCHAR) END AS PeriodDate, CASE WHEN (A.Month IS NULL OR A.Month = 0) AND A.Quarter IS NULL THEN ''Yearly'' WHEN (A.Month IS NULL OR A.Month = 0) THEN ''Quarterly'' WHEN A.Quarter IS NULL THEN ''Monthly'' END AS PeriodType, CASE WHEN EXISTS ( SELECT 1 FROM Mapping_CompanyProfitAndLossLineItems m INNER JOIN M_ProfitAndLoss_LineItems b ON m.ProfitAndLossLineItemID = b.ProfitAndLossLineItemID WHERE b.ProfitAndLossLineItem = mst.ProfitAndLossLineItem AND m.PortfolioCompanyID = Map.PortfolioCompanyID ) AND Map.ParentLineItemID > 0 THEN ( SELECT ProfitAndLossLineItem FROM M_ProfitAndLoss_LineItems WHERE ProfitAndLossLineItemID = Map.ParentLineItemID ) + ''-'' + mst.ProfitAndLossLineItem ELSE mst.ProfitAndLossLineItem END AS ' + QUOTENAME(@ParentKPI) + ', Map.PortfolioCompanyID AS PortfolioCompanyId, mst.KpiInfo, mst.MethodologyID AS MethodologyId, ''Budget'' AS ValueType FROM ProfitAndLossValues A INNER JOIN Mapping_CompanyProfitAndLossLineItems Map ON Map.CompanyProfitAndLossLineItemMappingID = A.CompanyProfitAndLossLineItemMappingID AND Map.IsDeleted = 0 AND A.IsDeleted = 0 INNER JOIN M_ProfitAndLoss_LineItems mst ON mst.ProfitAndLossLineItemID = Map.ProfitAndLossLineItemID AND mst.IsDeleted = 0 WHERE A.IsDeleted = 0 AND Map.IsDeleted = 0 AND mst.IsDeleted = 0 AND A.BudgetValue IS NOT NULL AND Map.ParentLineItemID = @ParentKpiID AND Map.PortfolioCompanyID IN ('+@CompIds+') AND A.Year BETWEEN @FYear AND @TYear UNION ALL SELECT mst.ProfitAndLossLineItemID AS KpiId, CASE WHEN IsNumeric(ActualValue) = 1 AND mst.KPIInfo = ''$'' THEN CONVERT(VARCHAR, CONVERT(Decimal(38, 8), ActualValue) * 1000) ELSE ActualValue END AS KpiValue, A.Year, A.Quarter, A.Month, CASE WHEN (A.Month IS NULL OR A.Month = 0) AND A.Quarter IS NULL THEN CAST(A.Year AS VARCHAR) WHEN (A.Month IS NULL OR A.Month = 0) THEN A.Quarter + '' '' + CAST(A.Year AS VARCHAR) WHEN A.Quarter IS NULL THEN [dbo].[GetMonthName](A.Month) + '' '' + CAST(A.Year AS VARCHAR) END AS Period, CASE WHEN (A.Month IS NULL OR A.Month = 0) AND A.Quarter IS NULL THEN CAST(CAST(A.Year AS VARCHAR) + ''-12-31'' AS VARCHAR) WHEN (A.Month IS NULL OR A.Month = 0) THEN CAST(dbo.GetLastDateOfQuarter(SUBSTRING(A.Quarter, 2, 1), A.Year) AS VARCHAR) WHEN A.Quarter IS NULL THEN CAST(dbo.GetLastDateOfMonth(A.Month, A.Year) AS VARCHAR) END AS PeriodDate, CASE WHEN (A.Month IS NULL OR A.Month = 0) AND A.Quarter IS NULL THEN ''Yearly'' WHEN (A.Month IS NULL OR A.Month = 0) THEN ''Quarterly'' WHEN A.Quarter IS NULL THEN ''Monthly'' END AS PeriodType, CASE WHEN EXISTS ( SELECT 1 FROM Mapping_CompanyProfitAndLossLineItems m INNER JOIN M_ProfitAndLoss_LineItems b ON m.ProfitAndLossLineItemID = b.ProfitAndLossLineItemID WHERE b.ProfitAndLossLineItem = mst.ProfitAndLossLineItem AND m.PortfolioCompanyID = Map.PortfolioCompanyID ) AND Map.ParentLineItemID > 0 THEN ( SELECT ProfitAndLossLineItem FROM M_ProfitAndLoss_LineItems WHERE ProfitAndLossLineItemID = Map.ParentLineItemID ) + ''-'' + mst.ProfitAndLossLineItem ELSE mst.ProfitAndLossLineItem END AS ' + QUOTENAME(@ParentKPI) + ', Map.PortfolioCompanyID AS PortfolioCompanyId, mst.KpiInfo, mst.MethodologyID AS MethodologyId, ''Forecast'' AS ValueType FROM ProfitAndLoss_ForecastData A INNER JOIN Mapping_CompanyProfitAndLossLineItems Map ON Map.CompanyProfitAndLossLineItemMappingID = A.CompanyProfitAndLossLineItemMappingID AND Map.IsDeleted = 0 AND A.IsDeleted = 0 INNER JOIN M_ProfitAndLoss_LineItems mst ON mst.ProfitAndLossLineItemID = Map.ProfitAndLossLineItemID AND mst.IsDeleted = 0 WHERE A.IsDeleted = 0 AND Map.IsDeleted = 0 AND mst.IsDeleted = 0 AND A.ActualValue IS NOT NULL AND Map.ParentLineItemID = @ParentKpiID AND Map.PortfolioCompanyID IN ('+@CompIds+') AND A.Year BETWEEN @FYear AND @TYear UNION ALL SELECT mst.ProfitAndLossLineItemID AS KpiId, CASE WHEN IsNumeric(CAST(A.BaseValue AS VARCHAR)) = 1 AND mst.KPIInfo = ''$'' THEN CONVERT(VARCHAR, CONVERT(Decimal(38, 8), CAST(A.BaseValue AS VARCHAR)) * 1000) ELSE CAST(A.BaseValue AS VARCHAR) END AS KpiValue, A.Year, NULL AS Quarter, NULL AS Month, CASE WHEN A.Year IS NOT NULL THEN CAST(A.Year AS VARCHAR) END AS Period, CASE WHEN A.Year IS NOT NULL THEN CAST(CAST(A.Year AS VARCHAR) + ''-12-31'' AS VARCHAR) END AS PeriodDate, ''Yearly'' AS PeriodType, CASE WHEN EXISTS ( SELECT 1 FROM Mapping_CompanyProfitAndLossLineItems m INNER JOIN M_ProfitAndLoss_LineItems b ON m.ProfitAndLossLineItemID = b.ProfitAndLossLineItemID WHERE b.ProfitAndLossLineItem = mst.ProfitAndLossLineItem AND m.PortfolioCompanyID = Map.PortfolioCompanyID ) AND Map.ParentLineItemID > 0 THEN ( SELECT ProfitAndLossLineItem FROM M_ProfitAndLoss_LineItems WHERE ProfitAndLossLineItemID = Map.ParentLineItemID ) + ''-'' + mst.ProfitAndLossLineItem ELSE mst.ProfitAndLossLineItem END AS ' + QUOTENAME(@ParentKPI) + ', Map.PortfolioCompanyID AS PortfolioCompanyId, mst.KpiInfo, mst.MethodologyID AS MethodologyId, ''Ic'' AS ValueType FROM ProfitAndLossICCaseValues A INNER JOIN Mapping_CompanyProfitAndLossLineItems Map ON Map.CompanyProfitAndLossLineItemMappingID = A.CompanyProfitAndLossLineItemMappingID AND Map.IsDeleted = 0 AND A.IsDeleted = 0 INNER JOIN M_ProfitAndLoss_LineItems mst ON mst.ProfitAndLossLineItemID = Map.ProfitAndLossLineItemID AND mst.IsDeleted = 0 WHERE A.IsDeleted = 0 AND Map.IsDeleted = 0 AND mst.IsDeleted = 0 AND A.BaseValue IS NOT NULL AND Map.ParentLineItemID = @ParentKpiID AND Map.PortfolioCompanyID IN ('+@CompIds+') AND A.Year BETWEEN @FYear AND @TYear UNION ALL SELECT mst.ProfitAndLossLineItemID AS KpiId, CASE WHEN IsNumeric(KPIValue) = 1 AND mst.KPIInfo = ''$'' THEN CONVERT(VARCHAR, CONVERT(Decimal(38, 8), KPIValue) * 1000) ELSE KPIValue END AS KpiValue, A.Year, A.Quarter, A.Month, CASE WHEN (A.Month IS NULL OR A.Month = 0) AND A.Quarter IS NULL THEN CAST(A.Year AS VARCHAR) WHEN (A.Month IS NULL OR A.Month = 0) THEN A.Quarter + '' '' + CAST(A.Year AS VARCHAR) WHEN A.Quarter IS NULL THEN [dbo].[GetMonthName](A.Month) + '' '' + CAST(A.Year AS VARCHAR) END AS Period, CASE WHEN (A.Month IS NULL OR A.Month = 0) AND A.Quarter IS NULL THEN CAST(CAST(A.Year AS VARCHAR) + ''-12-31'' AS VARCHAR) WHEN (A.Month IS NULL OR A.Month = 0) THEN CAST(dbo.GetLastDateOfQuarter(SUBSTRING(A.Quarter, 2, 1), A.Year) AS VARCHAR) WHEN A.Quarter IS NULL THEN CAST(dbo.GetLastDateOfMonth(A.Month, A.Year) AS VARCHAR) END AS PeriodDate, CASE WHEN A.ValueTypeId IN (11, 13, 14) THEN CASE WHEN A.Quarter IS NOT NULL THEN ''Quarterly YTD'' WHEN A.Month > 0 THEN ''Monthly YTD'' ELSE ''Not Applicable'' END WHEN A.ValueTypeId IN (16, 17, 18) THEN CASE WHEN A.Quarter IS NOT NULL THEN ''Quarterly LTM'' WHEN A.Month > 0 THEN ''Monthly LTM'' ELSE ''Not Applicable'' END ELSE ''NA'' END AS PeriodType, CASE WHEN EXISTS ( SELECT 1 FROM Mapping_CompanyProfitAndLossLineItems m INNER JOIN M_ProfitAndLoss_LineItems b ON m.ProfitAndLossLineItemID = b.ProfitAndLossLineItemID WHERE b.ProfitAndLossLineItem = mst.ProfitAndLossLineItem AND m.PortfolioCompanyID = Map.PortfolioCompanyID ) AND Map.ParentLineItemID > 0 THEN ( SELECT ProfitAndLossLineItem FROM M_ProfitAndLoss_LineItems WHERE ProfitAndLossLineItemID = Map.ParentLineItemID ) + ''-'' + mst.ProfitAndLossLineItem ELSE mst.ProfitAndLossLineItem END AS ' + QUOTENAME(@ParentKPI) + ', Map.PortfolioCompanyID AS PortfolioCompanyId, mst.KpiInfo, mst.MethodologyID AS MethodologyId, CASE WHEN A.ValueTypeId IN (14, 16) THEN ''Actual'' WHEN A.ValueTypeId IN (11, 17) THEN ''Budget'' ELSE ''Forecast'' END AS ValueType FROM PCFinancialsValues A INNER JOIN Mapping_CompanyProfitAndLossLineItems Map ON Map.CompanyProfitAndLossLineItemMappingID = A.MappingId AND Map.IsDeleted = 0 AND A.IsDeleted = 0 INNER JOIN M_ProfitAndLoss_LineItems mst ON mst.ProfitAndLossLineItemID = Map.ProfitAndLossLineItemID AND mst.IsDeleted = 0 WHERE A.IsDeleted = 0 AND Map.IsDeleted = 0 AND mst.IsDeleted = 0 AND A.KPIValue IS NOT NULL AND A.ModuleId = 7 AND Map.ParentLineItemID = @ParentKpiID AND Map.PortfolioCompanyID IN ('+@CompIds+') AND A.Year BETWEEN @FYear AND @TYear';
	END
	ELSE IF(@ModuleId = 8)
	BEGIN
		SET @ParentKPI = (SELECT TOP 1 BalanceSheetLineItem FROM M_BalanceSheet_LineItems WHERE BalanceSheetLineItemID = @ParentKpiID)
		SET @SQL = ' SELECT mst.BalanceSheetLineItemID AS KpiId, CASE WHEN IsNumeric(ActualValue) = 1 AND mst.KPIInfo = ''$'' THEN CONVERT(VARCHAR(MAX), CONVERT(Decimal(38, 8), ActualValue) * 1000) ELSE ActualValue END AS KpiValue, A.Year, A.Quarter, A.Month, CASE WHEN (A.Month IS NULL OR A.Month = 0) AND A.Quarter IS NULL THEN CAST(A.Year AS VARCHAR) WHEN (A.Month IS NULL OR A.Month = 0) THEN A.Quarter + '' '' + CAST(A.Year AS VARCHAR) WHEN A.Quarter IS NULL THEN [dbo].[GetMonthName](A.Month) + '' '' + CAST(A.Year AS VARCHAR) END AS Period, CASE WHEN (A.Month IS NULL OR A.Month = 0) AND A.Quarter IS NULL THEN CAST(CAST(A.Year AS VARCHAR) + ''-12-31'' AS VARCHAR) WHEN (A.Month IS NULL OR A.Month = 0) THEN CAST(dbo.GetLastDateOfQuarter(SUBSTRING(A.Quarter, 2, 1), A.Year) AS VARCHAR) WHEN A.Quarter IS NULL THEN CAST(dbo.GetLastDateOfMonth(A.Month, A.Year) AS VARCHAR) END AS PeriodDate, CASE WHEN (A.Month IS NULL OR A.Month = 0) AND A.Quarter IS NULL THEN ''Yearly'' WHEN (A.Month IS NULL OR A.Month = 0) THEN ''Quarterly'' WHEN A.Quarter IS NULL THEN ''Monthly'' END AS PeriodType, CASE WHEN EXISTS ( SELECT 1 FROM Mapping_CompanyBalanceSheetLineItems m INNER JOIN M_BalanceSheet_LineItems b ON m.BalanceSheetLineItemID = b.BalanceSheetLineItemID WHERE b.BalanceSheetLineItem = mst.BalanceSheetLineItem AND m.PortfolioCompanyID = Map.PortfolioCompanyID ) AND Map.ParentLineItemID > 0 THEN ( SELECT BalanceSheetLineItem FROM M_BalanceSheet_LineItems WHERE BalanceSheetLineItemID = Map.ParentLineItemID ) + ''-'' + mst.BalanceSheetLineItem ELSE mst.BalanceSheetLineItem END AS ' + QUOTENAME(@ParentKPI) + ', Map.PortfolioCompanyID AS PortfolioCompanyId, mst.KpiInfo, mst.MethodologyID AS MethodologyId, ''Actual'' AS ValueType FROM BalanceSheetValues A INNER JOIN Mapping_CompanyBalanceSheetLineItems Map ON Map.CompanyBalanceSheetLineItemMappingID = A.CompanyBalanceSheetLineItemMappingID AND Map.IsDeleted = 0 AND A.IsDeleted = 0 INNER JOIN M_BalanceSheet_LineItems mst ON mst.BalanceSheetLineItemID = Map.BalanceSheetLineItemID AND mst.IsDeleted = 0 WHERE A.IsDeleted = 0 AND Map.IsDeleted = 0 AND mst.IsDeleted = 0 AND A.ActualValue IS NOT NULL AND Map.ParentLineItemID = @ParentKpiID AND Map.PortfolioCompanyID IN (' + @CompIds + ') AND A.Year BETWEEN @FYear AND @TYear UNION ALL SELECT mst.BalanceSheetLineItemID AS KpiId, CASE WHEN IsNumeric(BudgetValue) = 1 AND mst.KPIInfo = ''$'' THEN CONVERT(VARCHAR, CONVERT(Decimal(38, 8), BudgetValue) * 1000) ELSE BudgetValue END AS KpiValue, A.Year, A.Quarter, A.Month, CASE WHEN (A.Month IS NULL OR A.Month = 0) AND A.Quarter IS NULL THEN CAST(A.Year AS VARCHAR) WHEN (A.Month IS NULL OR A.Month = 0) THEN A.Quarter + '' '' + CAST(A.Year AS VARCHAR) WHEN A.Quarter IS NULL THEN [dbo].[GetMonthName](A.Month) + '' '' + CAST(A.Year AS VARCHAR) END AS Period, CASE WHEN (A.Month IS NULL OR A.Month = 0) AND A.Quarter IS NULL THEN CAST(CAST(A.Year AS VARCHAR) + ''-12-31'' AS VARCHAR) WHEN (A.Month IS NULL OR A.Month = 0) THEN CAST(dbo.GetLastDateOfQuarter(SUBSTRING(A.Quarter, 2, 1), A.Year) AS VARCHAR) WHEN A.Quarter IS NULL THEN CAST(dbo.GetLastDateOfMonth(A.Month, A.Year) AS VARCHAR) END AS PeriodDate, CASE WHEN (A.Month IS NULL OR A.Month = 0) AND A.Quarter IS NULL THEN ''Yearly'' WHEN (A.Month IS NULL OR A.Month = 0) THEN ''Quarterly'' WHEN A.Quarter IS NULL THEN ''Monthly'' END AS PeriodType, CASE WHEN EXISTS ( SELECT 1 FROM Mapping_CompanyBalanceSheetLineItems m INNER JOIN M_BalanceSheet_LineItems b ON m.BalanceSheetLineItemID = b.BalanceSheetLineItemID WHERE b.BalanceSheetLineItem = mst.BalanceSheetLineItem AND m.PortfolioCompanyID = Map.PortfolioCompanyID ) AND Map.ParentLineItemID > 0 THEN ( SELECT BalanceSheetLineItem FROM M_BalanceSheet_LineItems WHERE BalanceSheetLineItemID = Map.ParentLineItemID ) + ''-'' + mst.BalanceSheetLineItem ELSE mst.BalanceSheetLineItem END AS ' + QUOTENAME(@ParentKPI) + ', Map.PortfolioCompanyID AS PortfolioCompanyId, mst.KpiInfo, mst.MethodologyID AS MethodologyId, ''Budget'' AS ValueType FROM BalanceSheetValues A INNER JOIN Mapping_CompanyBalanceSheetLineItems Map ON Map.CompanyBalanceSheetLineItemMappingID = A.CompanyBalanceSheetLineItemMappingID AND Map.IsDeleted = 0 AND A.IsDeleted = 0 INNER JOIN M_BalanceSheet_LineItems mst ON mst.BalanceSheetLineItemID = Map.BalanceSheetLineItemID AND mst.IsDeleted = 0 WHERE A.IsDeleted = 0 AND Map.IsDeleted = 0 AND mst.IsDeleted = 0 AND A.BudgetValue IS NOT NULL AND Map.ParentLineItemID = @ParentKpiID AND Map.PortfolioCompanyID IN (' + @CompIds + ') AND A.Year BETWEEN @FYear AND @TYear UNION ALL SELECT mst.BalanceSheetLineItemID AS KpiId, CASE WHEN IsNumeric(ActualValue) = 1 AND mst.KPIInfo = ''$'' THEN CONVERT(VARCHAR, CONVERT(Decimal(38, 8), ActualValue) * 1000) ELSE ActualValue END AS KpiValue, A.Year, A.Quarter, A.Month, CASE WHEN (A.Month IS NULL OR A.Month = 0) AND A.Quarter IS NULL THEN CAST(A.Year AS VARCHAR) WHEN (A.Month IS NULL OR A.Month = 0) THEN A.Quarter + '' '' + CAST(A.Year AS VARCHAR) WHEN A.Quarter IS NULL THEN [dbo].[GetMonthName](A.Month) + '' '' + CAST(A.Year AS VARCHAR) END AS Period, CASE WHEN (A.Month IS NULL OR A.Month = 0) AND A.Quarter IS NULL THEN CAST(CAST(A.Year AS VARCHAR) + ''-12-31'' AS VARCHAR) WHEN (A.Month IS NULL OR A.Month = 0) THEN CAST(dbo.GetLastDateOfQuarter(SUBSTRING(A.Quarter, 2, 1), A.Year) AS VARCHAR) WHEN A.Quarter IS NULL THEN CAST(dbo.GetLastDateOfMonth(A.Month, A.Year) AS VARCHAR) END AS PeriodDate, CASE WHEN (A.Month IS NULL OR A.Month = 0) AND A.Quarter IS NULL THEN ''Yearly'' WHEN (A.Month IS NULL OR A.Month = 0) THEN ''Quarterly'' WHEN A.Quarter IS NULL THEN ''Monthly'' END AS PeriodType, CASE WHEN EXISTS ( SELECT 1 FROM Mapping_CompanyBalanceSheetLineItems m INNER JOIN M_BalanceSheet_LineItems b ON m.BalanceSheetLineItemID = b.BalanceSheetLineItemID WHERE b.BalanceSheetLineItem = mst.BalanceSheetLineItem AND m.PortfolioCompanyID = Map.PortfolioCompanyID ) AND Map.ParentLineItemID > 0 THEN ( SELECT BalanceSheetLineItem FROM M_BalanceSheet_LineItems WHERE BalanceSheetLineItemID = Map.ParentLineItemID ) + ''-'' + mst.BalanceSheetLineItem ELSE mst.BalanceSheetLineItem END AS ' + QUOTENAME(@ParentKPI) + ', Map.PortfolioCompanyID AS PortfolioCompanyId, mst.KpiInfo, mst.MethodologyID AS MethodologyId, ''Forecast'' AS ValueType FROM BalanceSheet_ForecastData A INNER JOIN Mapping_CompanyBalanceSheetLineItems Map ON Map.CompanyBalanceSheetLineItemMappingID = A.CompanyBalanceSheetLineItemMappingID AND Map.IsDeleted = 0 AND A.IsDeleted = 0 INNER JOIN M_BalanceSheet_LineItems mst ON mst.BalanceSheetLineItemID = Map.BalanceSheetLineItemID AND mst.IsDeleted = 0 WHERE A.IsDeleted = 0 AND Map.IsDeleted = 0 AND mst.IsDeleted = 0 AND A.ActualValue IS NOT NULL AND Map.ParentLineItemID = @ParentKpiID AND Map.PortfolioCompanyID IN (' + @CompIds + ') AND A.Year BETWEEN @FYear AND @TYear UNION ALL SELECT mst.BalanceSheetLineItemID AS KpiId, CASE WHEN IsNumeric(CAST(A.BaseValue AS VARCHAR)) = 1 AND mst.KPIInfo = ''$'' THEN CONVERT(VARCHAR, CONVERT(Decimal(38, 8), CAST(A.BaseValue AS VARCHAR)) * 1000) ELSE CAST(A.BaseValue AS VARCHAR) END AS KpiValue, A.Year, NULL AS Quarter, NULL AS Month, CASE WHEN A.Year IS NOT NULL THEN CAST(A.Year AS VARCHAR) END AS Period, CASE WHEN A.Year IS NOT NULL THEN CAST(CAST(A.Year AS VARCHAR) + ''-12-31'' AS VARCHAR) END AS PeriodDate, ''Yearly'' AS PeriodType, CASE WHEN EXISTS ( SELECT 1 FROM Mapping_CompanyBalanceSheetLineItems m INNER JOIN M_BalanceSheet_LineItems b ON m.BalanceSheetLineItemID = b.BalanceSheetLineItemID WHERE b.BalanceSheetLineItem = mst.BalanceSheetLineItem AND m.PortfolioCompanyID = Map.PortfolioCompanyID ) AND Map.ParentLineItemID > 0 THEN ( SELECT BalanceSheetLineItem FROM M_BalanceSheet_LineItems WHERE BalanceSheetLineItemID = Map.ParentLineItemID ) + ''-'' + mst.BalanceSheetLineItem ELSE mst.BalanceSheetLineItem END AS ' + QUOTENAME(@ParentKPI) + ', Map.PortfolioCompanyID AS PortfolioCompanyId, mst.KpiInfo, mst.MethodologyID AS MethodologyId, ''Ic'' AS ValueType FROM BalanceSheetICCaseValues A INNER JOIN Mapping_CompanyBalanceSheetLineItems Map ON Map.CompanyBalanceSheetLineItemMappingID = A.CompanyBalanceSheetLineItemMappingID AND Map.IsDeleted = 0 AND A.IsDeleted = 0 INNER JOIN M_BalanceSheet_LineItems mst ON mst.BalanceSheetLineItemID = Map.BalanceSheetLineItemID AND mst.IsDeleted = 0 WHERE A.IsDeleted = 0 AND Map.IsDeleted = 0 AND mst.IsDeleted = 0 AND A.BaseValue IS NOT NULL AND Map.ParentLineItemID = @ParentKpiID AND Map.PortfolioCompanyID IN (' + @CompIds + ') AND A.Year BETWEEN @FYear AND @TYear UNION ALL SELECT mst.BalanceSheetLineItemID AS KpiId, CASE WHEN IsNumeric(KPIValue) = 1 AND mst.KPIInfo = ''$'' THEN CONVERT(VARCHAR, CONVERT(Decimal(38, 8), KPIValue) * 1000) ELSE KPIValue END AS KpiValue, A.Year, A.Quarter, A.Month, CASE WHEN (A.Month IS NULL OR A.Month = 0) AND A.Quarter IS NULL THEN CAST(A.Year AS VARCHAR) WHEN (A.Month IS NULL OR A.Month = 0) THEN A.Quarter + '' '' + CAST(A.Year AS VARCHAR) WHEN A.Quarter IS NULL THEN [dbo].[GetMonthName](A.Month) + '' '' + CAST(A.Year AS VARCHAR) END AS Period, CASE WHEN (A.Month IS NULL OR A.Month = 0) AND A.Quarter IS NULL THEN CAST(CAST(A.Year AS VARCHAR) + ''-12-31'' AS VARCHAR) WHEN (A.Month IS NULL OR A.Month = 0) THEN CAST(dbo.GetLastDateOfQuarter(SUBSTRING(A.Quarter, 2, 1), A.Year) AS VARCHAR) WHEN A.Quarter IS NULL THEN CAST(dbo.GetLastDateOfMonth(A.Month, A.Year) AS VARCHAR) END AS PeriodDate, CASE WHEN A.ValueTypeId IN (11, 13, 14) THEN CASE WHEN A.Quarter IS NOT NULL THEN ''Quarterly YTD'' WHEN A.Month > 0 THEN ''Monthly YTD'' ELSE ''Not Applicable'' END WHEN A.ValueTypeId IN (16, 17, 18) THEN CASE WHEN A.Quarter IS NOT NULL THEN ''Quarterly LTM'' WHEN A.Month > 0 THEN ''Monthly LTM'' ELSE ''Not Applicable'' END ELSE ''NA'' END AS PeriodType, CASE WHEN EXISTS ( SELECT 1 FROM Mapping_CompanyBalanceSheetLineItems m INNER JOIN M_BalanceSheet_LineItems b ON m.BalanceSheetLineItemID = b.BalanceSheetLineItemID WHERE b.BalanceSheetLineItem = mst.BalanceSheetLineItem AND m.PortfolioCompanyID = Map.PortfolioCompanyID ) AND Map.ParentLineItemID > 0 THEN ( SELECT BalanceSheetLineItem FROM M_BalanceSheet_LineItems WHERE BalanceSheetLineItemID = Map.ParentLineItemID ) + ''-'' + mst.BalanceSheetLineItem ELSE mst.BalanceSheetLineItem END AS ' + QUOTENAME(@ParentKPI) + ', Map.PortfolioCompanyID AS PortfolioCompanyId, mst.KpiInfo, mst.MethodologyID AS MethodologyId, CASE WHEN A.ValueTypeId IN (14, 16) THEN ''Actual'' WHEN A.ValueTypeId IN (11, 17) THEN ''Budget'' ELSE ''Forecast'' END AS ValueType FROM PCFinancialsValues A INNER JOIN Mapping_CompanyBalanceSheetLineItems Map ON Map.CompanyBalanceSheetLineItemMappingID = A.MappingId AND Map.IsDeleted = 0 AND A.IsDeleted = 0 INNER JOIN M_BalanceSheet_LineItems mst ON mst.BalanceSheetLineItemID = Map.BalanceSheetLineItemID AND mst.IsDeleted = 0 WHERE A.IsDeleted = 0 AND Map.IsDeleted = 0 AND mst.IsDeleted = 0 AND A.KPIValue IS NOT NULL AND A.ModuleId = 8 AND Map.ParentLineItemID = @ParentKpiID AND Map.PortfolioCompanyID IN (' + @CompIds + ') AND A.Year BETWEEN @FYear AND @TYear';
	END
	ELSE IF(@ModuleId = 9)
	BEGIN
		SET @ParentKPI = (SELECT TOP 1 CashFlowLineItem FROM M_CashFlow_LineItems WHERE CashFlowLineItemID = @ParentKpiID)
		SET @SQL = ' SELECT mst.CashFlowLineItemID AS KpiId, CASE WHEN IsNumeric(ActualValue) = 1 AND mst.KPIInfo = ''$'' THEN CONVERT(VARCHAR(MAX), CONVERT(DECIMAL(38, 8), ActualValue) * 1000) ELSE ActualValue END AS KpiValue, A.Year, A.Quarter, A.Month, CASE WHEN (A.Month IS NULL OR A.Month = 0) AND A.Quarter IS NULL THEN CAST(A.Year AS VARCHAR) WHEN (A.Month IS NULL OR A.Month = 0) THEN A.Quarter + '' '' + CAST(A.Year AS VARCHAR) WHEN A.Quarter IS NULL THEN [dbo].[GetMonthName](A.Month) + '' '' + CAST(A.Year AS VARCHAR) END AS Period, CASE WHEN (A.Month IS NULL OR A.Month = 0) AND A.Quarter IS NULL THEN CAST(CAST(A.Year AS VARCHAR) + ''-12-31'' AS VARCHAR) WHEN (A.Month IS NULL OR A.Month = 0) THEN CAST(dbo.GetLastDateOfQuarter(SUBSTRING(A.Quarter, 2, 1), A.Year) AS VARCHAR) WHEN A.Quarter IS NULL THEN CAST(dbo.GetLastDateOfMonth(A.Month, A.Year) AS VARCHAR) END AS PeriodDate, CASE WHEN (A.Month IS NULL OR A.Month = 0) AND A.Quarter IS NULL THEN ''Yearly'' WHEN (A.Month IS NULL OR A.Month = 0) THEN ''Quarterly'' WHEN A.Quarter IS NULL THEN ''Monthly'' END AS PeriodType, CASE WHEN EXISTS ( SELECT 1 FROM Mapping_CompanyCashFlowLineItems m INNER JOIN M_CashFlow_LineItems b ON m.CashFlowLineItemID = b.CashFlowLineItemID WHERE b.CashFlowLineItem = mst.CashFlowLineItem AND m.PortfolioCompanyID = Map.PortfolioCompanyID ) AND Map.ParentLineItemID > 0 THEN ( SELECT CashFlowLineItem FROM M_CashFlow_LineItems WHERE CashFlowLineItemID = Map.ParentLineItemID ) + ''-'' + mst.CashFlowLineItem ELSE mst.CashFlowLineItem END AS ' + QUOTENAME(@ParentKPI) + ', Map.PortfolioCompanyID AS PortfolioCompanyId, mst.KpiInfo, mst.MethodologyID AS MethodologyId, ''Actual'' AS ValueType FROM CashFlowValues A INNER JOIN Mapping_CompanyCashFlowLineItems Map ON Map.CompanyCashFlowLineItemMappingID = A.CompanyCashFlowLineItemMappingID AND Map.IsDeleted = 0 AND A.IsDeleted = 0 INNER JOIN M_CashFlow_LineItems mst ON mst.CashFlowLineItemID = Map.CashFlowLineItemID AND mst.IsDeleted = 0 WHERE A.IsDeleted = 0 AND Map.IsDeleted = 0 AND mst.IsDeleted = 0 AND A.ActualValue IS NOT NULL AND Map.ParentLineItemID = @ParentKpiID AND Map.PortfolioCompanyID IN (' + @CompIds + ') AND A.Year BETWEEN @FYear AND @TYear UNION ALL SELECT mst.CashFlowLineItemID AS KpiId, CASE WHEN IsNumeric(BudgetValue) = 1 AND mst.KPIInfo = ''$'' THEN CONVERT(VARCHAR, CONVERT(DECIMAL(38, 8), BudgetValue) * 1000) ELSE BudgetValue END AS KpiValue, A.Year, A.Quarter, A.Month, CASE WHEN (A.Month IS NULL OR A.Month = 0) AND A.Quarter IS NULL THEN CAST(A.Year AS VARCHAR) WHEN (A.Month IS NULL OR A.Month = 0) THEN A.Quarter + '' '' + CAST(A.Year AS VARCHAR) WHEN A.Quarter IS NULL THEN [dbo].[GetMonthName](A.Month) + '' '' + CAST(A.Year AS VARCHAR) END AS Period, CASE WHEN (A.Month IS NULL OR A.Month = 0) AND A.Quarter IS NULL THEN CAST(CAST(A.Year AS VARCHAR) + ''-12-31'' AS VARCHAR) WHEN (A.Month IS NULL OR A.Month = 0) THEN CAST(dbo.GetLastDateOfQuarter(SUBSTRING(A.Quarter, 2, 1), A.Year) AS VARCHAR) WHEN A.Quarter IS NULL THEN CAST(dbo.GetLastDateOfMonth(A.Month, A.Year) AS VARCHAR) END AS PeriodDate, CASE WHEN (A.Month IS NULL OR A.Month = 0) AND A.Quarter IS NULL THEN ''Yearly'' WHEN (A.Month IS NULL OR A.Month = 0) THEN ''Quarterly'' WHEN A.Quarter IS NULL THEN ''Monthly'' END AS PeriodType, CASE WHEN EXISTS ( SELECT 1 FROM Mapping_CompanyCashFlowLineItems m INNER JOIN M_CashFlow_LineItems b ON m.CashFlowLineItemID = b.CashFlowLineItemID WHERE b.CashFlowLineItem = mst.CashFlowLineItem AND m.PortfolioCompanyID = Map.PortfolioCompanyID ) AND Map.ParentLineItemID > 0 THEN ( SELECT CashFlowLineItem FROM M_CashFlow_LineItems WHERE CashFlowLineItemID = Map.ParentLineItemID ) + ''-'' + mst.CashFlowLineItem ELSE mst.CashFlowLineItem END AS ' + QUOTENAME(@ParentKPI) + ', Map.PortfolioCompanyID AS PortfolioCompanyId, mst.KpiInfo, mst.MethodologyID AS MethodologyId, ''Budget'' AS ValueType FROM CashFlowValues A INNER JOIN Mapping_CompanyCashFlowLineItems Map ON Map.CompanyCashFlowLineItemMappingID = A.CompanyCashFlowLineItemMappingID AND Map.IsDeleted = 0 AND A.IsDeleted = 0 INNER JOIN M_CashFlow_LineItems mst ON mst.CashFlowLineItemID = Map.CashFlowLineItemID AND mst.IsDeleted = 0 WHERE A.IsDeleted = 0 AND Map.IsDeleted = 0 AND mst.IsDeleted = 0 AND A.BudgetValue IS NOT NULL AND Map.ParentLineItemID = @ParentKpiID AND Map.PortfolioCompanyID IN (' + @CompIds + ') AND A.Year BETWEEN @FYear AND @TYear UNION ALL SELECT mst.CashFlowLineItemID AS KpiId, CASE WHEN IsNumeric(ActualValue) = 1 AND mst.KPIInfo = ''$'' THEN CONVERT(VARCHAR, CONVERT(DECIMAL(38, 8), ActualValue) * 1000) ELSE ActualValue END AS KpiValue, A.Year, A.Quarter, A.Month, CASE WHEN (A.Month IS NULL OR A.Month = 0) AND A.Quarter IS NULL THEN CAST(A.Year AS VARCHAR) WHEN (A.Month IS NULL OR A.Month = 0) THEN A.Quarter + '' '' + CAST(A.Year AS VARCHAR) WHEN A.Quarter IS NULL THEN [dbo].[GetMonthName](A.Month) + '' '' + CAST(A.Year AS VARCHAR) END AS Period, CASE WHEN (A.Month IS NULL OR A.Month = 0) AND A.Quarter IS NULL THEN CAST(CAST(A.Year AS VARCHAR) + ''-12-31'' AS VARCHAR) WHEN (A.Month IS NULL OR A.Month = 0) THEN CAST(dbo.GetLastDateOfQuarter(SUBSTRING(A.Quarter, 2, 1), A.Year) AS VARCHAR) WHEN A.Quarter IS NULL THEN CAST(dbo.GetLastDateOfMonth(A.Month, A.Year) AS VARCHAR) END AS PeriodDate, CASE WHEN (A.Month IS NULL OR A.Month = 0) AND A.Quarter IS NULL THEN ''Yearly'' WHEN (A.Month IS NULL OR A.Month = 0) THEN ''Quarterly'' WHEN A.Quarter IS NULL THEN ''Monthly'' END AS PeriodType, CASE WHEN EXISTS ( SELECT 1 FROM Mapping_CompanyCashFlowLineItems m INNER JOIN M_CashFlow_LineItems b ON m.CashFlowLineItemID = b.CashFlowLineItemID WHERE b.CashFlowLineItem = mst.CashFlowLineItem AND m.PortfolioCompanyID = Map.PortfolioCompanyID ) AND Map.ParentLineItemID > 0 THEN ( SELECT CashFlowLineItem FROM M_CashFlow_LineItems WHERE CashFlowLineItemID = Map.ParentLineItemID ) + ''-'' + mst.CashFlowLineItem ELSE mst.CashFlowLineItem END AS ' + QUOTENAME(@ParentKPI) + ', Map.PortfolioCompanyID AS PortfolioCompanyId, mst.KpiInfo, mst.MethodologyID AS MethodologyId, ''Forecast'' AS ValueType FROM CashFlow_ForecastData A INNER JOIN Mapping_CompanyCashFlowLineItems Map ON Map.CompanyCashFlowLineItemMappingID = A.CompanyCashFlowLineItemMappingID AND Map.IsDeleted = 0 AND A.IsDeleted = 0 INNER JOIN M_CashFlow_LineItems mst ON mst.CashFlowLineItemID = Map.CashFlowLineItemID AND mst.IsDeleted = 0 WHERE A.IsDeleted = 0 AND Map.IsDeleted = 0 AND mst.IsDeleted = 0 AND A.ActualValue IS NOT NULL AND Map.ParentLineItemID = @ParentKpiID AND Map.PortfolioCompanyID IN (' + @CompIds + ') AND A.Year BETWEEN @FYear AND @TYear UNION ALL SELECT mst.CashFlowLineItemID AS KpiId, CASE WHEN IsNumeric(CAST(A.BaseValue AS VARCHAR)) = 1 AND mst.KPIInfo = ''$'' THEN CONVERT(VARCHAR, CONVERT(DECIMAL(38, 8), CAST(A.BaseValue AS VARCHAR)) * 1000) ELSE CAST(A.BaseValue AS VARCHAR) END AS KpiValue, A.Year, NULL AS Quarter, NULL AS Month, CASE WHEN A.Year IS NOT NULL THEN CAST(A.Year AS VARCHAR) END AS Period, CASE WHEN A.Year IS NOT NULL THEN CAST(CAST(A.Year AS VARCHAR) + ''-12-31'' AS VARCHAR) END AS PeriodDate, ''Yearly'' AS PeriodType, CASE WHEN EXISTS ( SELECT 1 FROM Mapping_CompanyCashFlowLineItems m INNER JOIN M_CashFlow_LineItems b ON m.CashFlowLineItemID = b.CashFlowLineItemID WHERE b.CashFlowLineItem = mst.CashFlowLineItem AND m.PortfolioCompanyID = Map.PortfolioCompanyID ) AND Map.ParentLineItemID > 0 THEN ( SELECT CashFlowLineItem FROM M_CashFlow_LineItems WHERE CashFlowLineItemID = Map.ParentLineItemID ) + ''-'' + mst.CashFlowLineItem ELSE mst.CashFlowLineItem END AS ' + QUOTENAME(@ParentKPI) + ', Map.PortfolioCompanyID AS PortfolioCompanyId, mst.KpiInfo, mst.MethodologyID AS MethodologyId, ''Ic'' AS ValueType FROM CashFlowICCaseValues A INNER JOIN Mapping_CompanyCashFlowLineItems Map ON Map.CompanyCashFlowLineItemMappingID = A.CompanyCashFlowLineItemMappingID AND Map.IsDeleted = 0 AND A.IsDeleted = 0 INNER JOIN M_CashFlow_LineItems mst ON mst.CashFlowLineItemID = Map.CashFlowLineItemID AND mst.IsDeleted = 0 WHERE A.IsDeleted = 0 AND Map.IsDeleted = 0 AND mst.IsDeleted = 0 AND A.BaseValue IS NOT NULL AND Map.ParentLineItemID = @ParentKpiID AND Map.PortfolioCompanyID IN (' + @CompIds + ') AND A.Year BETWEEN @FYear AND @TYear UNION ALL SELECT mst.CashFlowLineItemID AS KpiId, CASE WHEN IsNumeric(KPIValue) = 1 AND mst.KPIInfo = ''$'' THEN CONVERT(VARCHAR, CONVERT(Decimal(38, 8), KPIValue) * 1000) ELSE KPIValue END AS KpiValue, A.Year, A.Quarter, A.Month, CASE WHEN (A.Month IS NULL OR A.Month = 0) AND A.Quarter IS NULL THEN CAST(A.Year AS VARCHAR) WHEN (A.Month IS NULL OR A.Month = 0) THEN A.Quarter + '' '' + CAST(A.Year AS VARCHAR) WHEN A.Quarter IS NULL THEN [dbo].[GetMonthName](A.Month) + '' '' + CAST(A.Year AS VARCHAR) END AS Period, CASE WHEN (A.Month IS NULL OR A.Month = 0) AND A.Quarter IS NULL THEN CAST(CAST(A.Year AS VARCHAR) + ''-12-31'' AS VARCHAR) WHEN (A.Month IS NULL OR A.Month = 0) THEN CAST(dbo.GetLastDateOfQuarter(SUBSTRING(A.Quarter, 2, 1), A.Year) AS VARCHAR) WHEN A.Quarter IS NULL THEN CAST(dbo.GetLastDateOfMonth(A.Month, A.Year) AS VARCHAR) END AS PeriodDate, CASE WHEN A.ValueTypeId IN (11, 13, 14) THEN CASE WHEN A.Quarter IS NOT NULL THEN ''Quarterly YTD'' WHEN A.Month > 0 THEN ''Monthly YTD'' ELSE ''Not Applicable'' END WHEN A.ValueTypeId IN (16, 17, 18) THEN CASE WHEN A.Quarter IS NOT NULL THEN ''Quarterly LTM'' WHEN A.Month > 0 THEN ''Monthly LTM'' ELSE ''Not Applicable'' END ELSE ''NA'' END AS PeriodType, CASE WHEN EXISTS ( SELECT 1 FROM Mapping_CompanyCashFlowLineItems m INNER JOIN M_CashFlow_LineItems b ON m.CashFlowLineItemID = b.CashFlowLineItemID WHERE b.CashFlowLineItem = mst.CashFlowLineItem AND m.PortfolioCompanyID = Map.PortfolioCompanyID ) AND Map.ParentLineItemID > 0 THEN ( SELECT CashFlowLineItem FROM M_CashFlow_LineItems WHERE CashFlowLineItemID = Map.ParentLineItemID ) + ''-'' + mst.CashFlowLineItem ELSE mst.CashFlowLineItem END AS ' + QUOTENAME(@ParentKPI) + ', Map.PortfolioCompanyID AS PortfolioCompanyId, mst.KpiInfo, mst.MethodologyID AS MethodologyId, CASE WHEN A.ValueTypeId IN (14, 16) THEN ''Actual'' WHEN A.ValueTypeId IN (11, 17) THEN ''Budget'' ELSE ''Forecast'' END AS ValueType FROM PCFinancialsValues A INNER JOIN Mapping_CompanyCashFlowLineItems Map ON Map.CompanyCashFlowLineItemMappingID = A.MappingId AND Map.IsDeleted = 0 AND A.IsDeleted = 0 INNER JOIN M_CashFlow_LineItems mst ON mst.CashFlowLineItemID = Map.CashFlowLineItemID AND mst.IsDeleted = 0 WHERE A.IsDeleted = 0 AND Map.IsDeleted = 0 AND mst.IsDeleted = 0 AND A.KPIValue IS NOT NULL AND A.ModuleId = 9 AND Map.ParentLineItemID = @ParentKpiID AND Map.PortfolioCompanyID IN (' + @CompIds + ') AND A.Year BETWEEN @FYear AND @TYear ';
	END
    -- Execute the dynamic SQL query
   EXEC sp_executesql @SQL, N'@ParentKpiID INT, @FYear NVARCHAR(200), @TYear NVARCHAR(200)', @ParentKpiID, @FYear, @TYear;
END;
GO
ALTER PROCEDURE  [dbo].[sp_AnalyticsMasterKpiValues]
(
@CurrencyRateType VARCHAR(50)=NULL,
@ToCurrencyId INT=0,
@Fx BIT = 0,
@FromYear INT=0,
@ToYear INT=0,
@CompanyId Varchar(Max)=NULL,
@KpiId Varchar(Max)=NULL,
@ModuleId INT
)
As
BEGIN
DECLARE @CompanyDetails TABLE (PortfolioCompanyId INT)
INSERT INTO @CompanyDetails(PortfolioCompanyId)
SELECT Item FROM dbo.SplitString(@CompanyId,',')

DECLARE @KpiIds TABLE (KpiId INT)
INSERT INTO @KpiIds(KpiId)
SELECT Item FROM dbo.SplitString(@KpiId,',')

Select CASE
	WHEN (SELECT TOP 1 KpiID FROM Mapping_Kpis WHERE EXISTS (SELECT DISTINCT * FROM M_MasterKpis b inner join Mapping_Kpis m ON m.KpiID = b.MasterKpiID WHERE b.KPI = mst.KPI and m.KpiID!=mst.MasterKpiID) and PortfolioCompanyID=Map.PortfolioCompanyID) is not null and Map.ParentKPIID > 0 THEN (SELECT KPI FROM M_MasterKpis WHERE MasterKpiID = Map.ParentKPIID) + '-' + mst.KPI
	ELSE mst.KPI
END
Kpi,
Map.KpiID KpiId,mst.KpiInfo KpiInfo, CASE 
        WHEN A.ModuleID = 1 THEN (SELECT Top 1 AliasName FROM M_SubPageFields where name = 'TradingRecords')
        WHEN A.ModuleID = 2 THEN (SELECT Top 1 AliasName FROM M_SubPageFields where name = 'CreditKPI')
    END 'KpiType'
,(CASE WHEN @Fx=1 AND mst.KpiInfo='$'AND IsNumeric(KpiValue) = 1 AND @ToCurrencyId > 0 AND @ToCurrencyId <> P.FromCurrencyId  THEN CAST((SELECT dbo.CalculateFxConversion(P.FinancialYearEnd,A.Year,A.Month,A.Quarter,@CurrencyRateType,mst.KpiInfo,mst.MethodologyID, (CASE 
        WHEN A.ValueTypeId IN (11, 13, 14) THEN  
            (CASE 
                WHEN Quarter IS NOT NULL THEN 'Quarterly YTD'
                WHEN Month > 0 THEN 'Monthly YTD'
				ELSE 'Not Applicable'
            END)
        WHEN A.ValueTypeId IN (16, 17, 18) THEN 
            (CASE 
                WHEN Quarter IS NOT NULL THEN 'Quarterly LTM'
                WHEN Month > 0 THEN 'Monthly LTM'
				ELSE 'Not Applicable'
            END)
		ELSE
		(CASE 
	WHEN (Month IS NULL or Month = 0) and Quarter IS NULL THEN  'Yearly'
	WHEN (Month IS NULL or Month = 0) THEN 'Quarterly'
	WHEN Quarter IS NULL THEN  'Monthly'
	END)
    END),@ToCurrencyId,P.FromCurrencyId,A.KpiValue)) as nvarchar(Max))
ELSE KpiValue END) KpiValue,
CASE 
	WHEN (Month IS NULL or Month = 0) and Quarter IS NULL THEN  Cast(A.Year as varchar)
	WHEN (Month IS NULL or Month = 0) THEN A.Quarter+' '+Cast(A.Year as varchar)
	WHEN Quarter IS NULL THEN  [dbo].[GetMonthName](A.Month)+' '+Cast(A.Year as varchar)
	END
	Period,
CASE 
	WHEN (Month IS NULL or Month = 0) and Quarter IS NULL THEN  CAST(Cast(A.Year as varchar) +'-12-31' AS VARCHAR)
	WHEN (Month IS NULL or Month = 0) THEN  CAST(dbo.GetLastDateOfQuarter(SUBSTRING(A.Quarter,2,1),A.Year)AS VARCHAR)
	WHEN Quarter IS NULL THEN  CAST(dbo.GetLastDateOfMonth(A.Month,A.Year)AS VARCHAR)
	END
	PeriodDate,
 (CASE 
        WHEN A.ValueTypeId IN (11, 13, 14) THEN  
            (CASE 
                WHEN Quarter IS NOT NULL THEN 'Quarterly YTD'
                WHEN Month > 0 THEN 'Monthly YTD'
				ELSE 'Not Applicable'
            END)
        WHEN A.ValueTypeId IN (16, 17, 18) THEN 
            (CASE 
                WHEN Quarter IS NOT NULL THEN 'Quarterly LTM'
                WHEN Month > 0 THEN 'Monthly LTM'
				ELSE 'Not Applicable'
            END)
		ELSE
		(CASE 
	WHEN (Month IS NULL or Month = 0) and Quarter IS NULL THEN  'Yearly'
	WHEN (Month IS NULL or Month = 0) THEN 'Quarterly'
	WHEN Quarter IS NULL THEN  'Monthly'
	END)
    END) AS PeriodType,Quarter,Month,A.Year,Map.ParentKPIID 'ParentId',
A.PortfolioCompanyID PortfolioCompanyId,Map.DisplayOrder,(CASE 
        WHEN V.ValueTypeId IN (14, 16,4) THEN 'Actual'
        WHEN V.ValueTypeId IN (11, 17,5) THEN 'Budget'
		WHEN V.ValueTypeId = 3 THEN 'Ic'
		WHEN V.ValueTypeId = 22 THEN 'IC2'
		WHEN V.ValueTypeId = 23 THEN 'IC3'
		WHEN V.ValueTypeId = 24 THEN 'IC4'
		WHEN V.ValueTypeId = 25 THEN 'IC5'
        ELSE 'Forecast' 
    END) AS ValueType,mst.MethodologyID'MethodologyId',
p.FinancialYearEnd,
P.FromCurrencyId,
V.HeaderValue
FROM Mapping_Kpis Map 
INNER JOIN PCMasterKpiValues A on
Map.PortfolioCompanyID=A.PortfolioCompanyID AND Map.Mapping_KpisID=A.MappingKpisID AND MAP.ModuleID = A.ModuleID AND MAP.PortfolioCompanyID IN (SELECT PortfolioCompanyId FROM @CompanyDetails) AND A.PortfolioCompanyID IN (SELECT PortfolioCompanyId FROM @CompanyDetails)
INNER JOIN M_MasterKpis mst on mst.MasterKpiID=Map.KpiID AND MST.ModuleID = MAP.ModuleID AND KpiID IN (SELECT KpiId FROM @KpiIds)
INNER JOIN M_ValueTypes V on a.ValueTypeID = v.ValueTypeID
LEFT JOIN  (SELECT PortfolioCompanyID,ReportingCurrencyID FromCurrencyId,dbo.GetFinancialYearEnd(FinancialYearEnd) AS FinancialYearEnd FROM PortfolioCompanyDetails Where IsDeleted=0 AND PortfolioCompanyID IN (SELECT PortfolioCompanyId from @CompanyDetails)) P ON A.PortfolioCompanyID = P.PortfolioCompanyID
WHERE A.IsDeleted=0 and Map.IsDeleted=0 and mst.IsDeleted=0 and a.PortfolioCompanyID IN   ( SELECT PortfolioCompanyId from @CompanyDetails) AND MAP.ModuleID = @ModuleId AND A.ModuleID = @ModuleId AND MAP.KpiID IN (SELECT KpiId FROM @KpiIds) AND YEAR BETWEEN @FromYear AND @ToYear
END
GO
ALTER PROCEDURE  [dbo].[spGetOperationalKpiValues](
	@CompanyId int=0,
	@dataType varchar(10) = 'M',
	@ValueTypeId int=0,
	@FromYear int=0,
	@toYear int=0,
	@isLastYear bit = 0
	)
	AS
	BEGIN
	DECLARE @ValueType varchar(10) = 'Actual'
	SET @ValueType = (SELECT HeaderValue FROM M_ValueTypes WHERE ValueTypeID =@ValueTypeId)

IF @dataType = 'M' 
BEGIN
IF @FromYear = 0 or @toYear = 0
BEGIN
	IF @isLastYear = 1
	BEGIN
		SET @FromYear = (SELECT TOP 1 year FROM PortfolioCompanyOperatiONalKPIValues pcokv
					INNER JOIN dbo.PortfolioCompanyOperatiONalKPIQuarters pcokq
					ON pcokv.PortfolioCompanyOperatiONalKPIQuarterID = pcokq.PortfolioCompanyOperatiONalKPIQuarterID
					WHERE pcokq.PortfolioCompanyID=@CompanyId and pcokq.ValueTypeID = (SELECT ValueTypeID FROM M_ValueTypes WHERE HeaderValue='Actual') and Month is not null ORDER BY year DESC
					)
		SET @toYear = (SELECT TOP 1 year FROM PortfolioCompanyOperatiONalKPIValues pcokv
					INNER JOIN dbo.PortfolioCompanyOperatiONalKPIQuarters pcokq
					ON pcokv.PortfolioCompanyOperatiONalKPIQuarterID = pcokq.PortfolioCompanyOperatiONalKPIQuarterID
					WHERE pcokq.PortfolioCompanyID=@CompanyId and pcokq.ValueTypeID = (SELECT ValueTypeID FROM M_ValueTypes WHERE HeaderValue='Actual') and Month is not null ORDER BY year DESC
					)
	END
	ELSE
	BEGIN
		SET @FromYear = (SELECT TOP 1 year FROM PortfolioCompanyOperatiONalKPIValues pcokv
					INNER JOIN dbo.PortfolioCompanyOperatiONalKPIQuarters pcokq
					ON pcokv.PortfolioCompanyOperatiONalKPIQuarterID = pcokq.PortfolioCompanyOperatiONalKPIQuarterID
					WHERE pcokq.PortfolioCompanyID=@CompanyId and pcokq.ValueTypeID = @ValueTypeId and Month is not null ORDER BY year DESC
					)
		SET @toYear = (SELECT TOP 1 year FROM PortfolioCompanyOperatiONalKPIValues pcokv
					INNER JOIN dbo.PortfolioCompanyOperatiONalKPIQuarters pcokq
					ON pcokv.PortfolioCompanyOperatiONalKPIQuarterID = pcokq.PortfolioCompanyOperatiONalKPIQuarterID
					WHERE pcokq.PortfolioCompanyID=@CompanyId and pcokq.ValueTypeID = @ValueTypeId and Month is not null ORDER BY year DESC
					)
	END
END
set @FromYear = @FromYear - 1;
SELECT
  DISTINCT 
  mo.SectorwiseOperatiONalKPIID AS KpiId,
  mpo.ParentKPIID AS ParentId,
  mpo.DisplayOrder,
  pcokq.PortfolioCompanyID,
  mo.KPI,
  mo.KpiInfo,
  mo.IsBoldKPI,
  mo.IsHeader,
  pcokv.KPIValue,
  pcokq.Quarter,
  pcokq.Year,
  pcokq.Month,
  pcokv.PortfolioCompanyOperatiONalKPIValueID AS ValuesKpiId,
  mo.Formula AS MasterFormula,
  mpo.Formula,
  mo.MethodologyId,
  pcokv.CreatedBy,
  pcokv.CreatedOn,
  dbo.AuditLogCompanyKPIFunctiON(pcokq.PortfolioCompanyID,'Operational KPIs',(SELECT [dbo].[GetMonthName](Month))+' '+CAST(ISNULL(Year,0) AS VARCHAR),KPI,PortfolioCompanyOperatiONalKPIValueID,@ValueType) ActualAuditLog
FROM
  dbo.PortfolioCompanyOperatiONalKPIValues pcokv
INNER JOIN
  dbo.PortfolioCompanyOperatiONalKPIQuarters pcokq
ON pcokv.PortfolioCompanyOperatiONalKPIQuarterID = pcokq.PortfolioCompanyOperatiONalKPIQuarterID
INNER JOIN
  dbo.Mapping_PortfolioOperatiONalKPI mpo
ON pcokv.SectorwiseOperatiONalKPIID = mpo.KpiID
INNER JOIN
  dbo.M_SectorwiseOperatiONalKPI mo
ON pcokv.SectorwiseOperatiONalKPIID = mo.SectorwiseOperatiONalKPIID

WHERE pcokq.PortfolioCompanyID = @CompanyId and mpo.PortfolioCompanyID = @CompanyId and pcokq.ValueTypeID = @ValueTypeId 
and mo.IsDeleted = 0 and mpo.IsDeleted = 0 and pcokq.IsDeleted = 0 and pcokv.IsDeleted = 0 and Month is not null and Year >= @FromYear and Year <= @toYear
END
ELSE IF @dataType = 'Q'
BEGIN
IF @FromYear = 0 or @toYear = 0
BEGIN
	IF @isLastYear = 1
	BEGIN
		SET @FromYear = (SELECT TOP 1 year FROM PortfolioCompanyOperatiONalKPIValues pcokv
					INNER JOIN dbo.PortfolioCompanyOperatiONalKPIQuarters pcokq
					ON pcokv.PortfolioCompanyOperatiONalKPIQuarterID = pcokq.PortfolioCompanyOperatiONalKPIQuarterID
					WHERE pcokq.PortfolioCompanyID=@CompanyId and pcokq.ValueTypeID = (SELECT ValueTypeID FROM M_ValueTypes WHERE HeaderValue='Actual') and Quarter is not null ORDER BY year DESC
					)
		SET @toYear = (SELECT TOP 1 year FROM PortfolioCompanyOperatiONalKPIValues pcokv
					INNER JOIN dbo.PortfolioCompanyOperatiONalKPIQuarters pcokq
					ON pcokv.PortfolioCompanyOperatiONalKPIQuarterID = pcokq.PortfolioCompanyOperatiONalKPIQuarterID
					WHERE pcokq.PortfolioCompanyID=@CompanyId and pcokq.ValueTypeID = (SELECT ValueTypeID FROM M_ValueTypes WHERE HeaderValue='Actual') and Quarter is not null ORDER BY year DESC
					)
	END
	ELSE
	BEGIN
		SET @FromYear = (SELECT TOP 1 year FROM PortfolioCompanyOperatiONalKPIValues pcokv
					INNER JOIN dbo.PortfolioCompanyOperatiONalKPIQuarters pcokq
					ON pcokv.PortfolioCompanyOperatiONalKPIQuarterID = pcokq.PortfolioCompanyOperatiONalKPIQuarterID
					WHERE pcokq.PortfolioCompanyID=@CompanyId and pcokq.ValueTypeID = @ValueTypeId and Quarter is not null ORDER BY year DESC
					)
		SET @toYear = (SELECT TOP 1 year FROM PortfolioCompanyOperatiONalKPIValues pcokv
					INNER JOIN dbo.PortfolioCompanyOperatiONalKPIQuarters pcokq
					ON pcokv.PortfolioCompanyOperatiONalKPIQuarterID = pcokq.PortfolioCompanyOperatiONalKPIQuarterID
					WHERE pcokq.PortfolioCompanyID=@CompanyId and pcokq.ValueTypeID = @ValueTypeId and Quarter is not null ORDER BY year DESC
					)
	END
END
set @FromYear = @FromYear - 1;
SELECT
  DISTINCT 
  mo.SectorwiseOperatiONalKPIID AS KpiId,
  mpo.ParentKPIID AS ParentId,
  mpo.DisplayOrder,
  pcokq.PortfolioCompanyID,
  mo.KPI,
  mo.KpiInfo,
  mo.IsBoldKPI,
  mo.IsHeader,
  pcokv.KPIValue,
  pcokq.Quarter,
  pcokq.Year,
  pcokq.Month,
  pcokv.PortfolioCompanyOperatiONalKPIValueID AS ValuesKpiId,
  mo.Formula AS MasterFormula,
  mpo.Formula,
  mo.MethodologyId,
  pcokv.CreatedBy,
  pcokv.CreatedOn,
  dbo.AuditLogCompanyKPIFunctiON(pcokq.PortfolioCompanyID,'Operational KPIs',Quarter+' '+CAST(ISNULL(Year,0) AS VARCHAR),KPI,PortfolioCompanyOperatiONalKPIValueID,@ValueType) ActualAuditLog
FROM
  dbo.PortfolioCompanyOperatiONalKPIValues pcokv
INNER JOIN
  dbo.PortfolioCompanyOperatiONalKPIQuarters pcokq
ON pcokv.PortfolioCompanyOperatiONalKPIQuarterID = pcokq.PortfolioCompanyOperatiONalKPIQuarterID
INNER JOIN
  dbo.Mapping_PortfolioOperatiONalKPI mpo
ON pcokv.SectorwiseOperatiONalKPIID = mpo.KpiID
INNER JOIN
  dbo.M_SectorwiseOperatiONalKPI mo
ON pcokv.SectorwiseOperatiONalKPIID = mo.SectorwiseOperatiONalKPIID

WHERE pcokq.PortfolioCompanyID = @CompanyId and mpo.PortfolioCompanyID = @CompanyId and pcokq.ValueTypeID = @ValueTypeId 
and mo.IsDeleted = 0 and mpo.IsDeleted = 0 and pcokq.IsDeleted = 0 and pcokv.IsDeleted = 0 and Quarter is not null and Year >= @FromYear and Year <= @toYear
END
ELSE IF @dataType = 'A'
BEGIN
IF @FromYear = 0 or @toYear = 0
BEGIN
	IF @isLastYear = 1
	BEGIN
		SET @FromYear = (SELECT TOP 1 year FROM PortfolioCompanyOperatiONalKPIValues pcokv
					INNER JOIN dbo.PortfolioCompanyOperatiONalKPIQuarters pcokq
					ON pcokv.PortfolioCompanyOperatiONalKPIQuarterID = pcokq.PortfolioCompanyOperatiONalKPIQuarterID
					WHERE pcokq.PortfolioCompanyID=@CompanyId and pcokq.ValueTypeID = (SELECT ValueTypeID FROM M_ValueTypes WHERE HeaderValue='Actual') and Month is null and Quarter is null ORDER BY year DESC
					)
		SET @toYear = (SELECT TOP 1 year FROM PortfolioCompanyOperatiONalKPIValues pcokv
					INNER JOIN dbo.PortfolioCompanyOperatiONalKPIQuarters pcokq
					ON pcokv.PortfolioCompanyOperatiONalKPIQuarterID = pcokq.PortfolioCompanyOperatiONalKPIQuarterID
					WHERE pcokq.PortfolioCompanyID=@CompanyId and pcokq.ValueTypeID = (SELECT ValueTypeID FROM M_ValueTypes WHERE HeaderValue='Actual') and Month is null and Quarter is null ORDER BY year DESC
					)
	END
	ELSE
	BEGIN
		SET @FromYear = (SELECT TOP 1 year FROM PortfolioCompanyOperatiONalKPIValues pcokv
					INNER JOIN dbo.PortfolioCompanyOperatiONalKPIQuarters pcokq
					ON pcokv.PortfolioCompanyOperatiONalKPIQuarterID = pcokq.PortfolioCompanyOperatiONalKPIQuarterID
					WHERE pcokq.PortfolioCompanyID=@CompanyId and pcokq.ValueTypeID = @ValueTypeId and Month is null and Quarter is null ORDER BY year DESC
					)
		SET @toYear = (SELECT TOP 1 year FROM PortfolioCompanyOperatiONalKPIValues pcokv
					INNER JOIN dbo.PortfolioCompanyOperatiONalKPIQuarters pcokq
					ON pcokv.PortfolioCompanyOperatiONalKPIQuarterID = pcokq.PortfolioCompanyOperatiONalKPIQuarterID
					WHERE pcokq.PortfolioCompanyID=@CompanyId and pcokq.ValueTypeID = @ValueTypeId and Month is null and Quarter is null ORDER BY year DESC
					)
	END
END
set @FromYear = @FromYear - 1;
SELECT
  DISTINCT 
  mo.SectorwiseOperatiONalKPIID AS KpiId,
  mpo.ParentKPIID AS ParentId,
  mpo.DisplayOrder,
  pcokq.PortfolioCompanyID,
  mo.KPI,
  mo.KpiInfo,
  mo.IsBoldKPI,
  mo.IsHeader,
  pcokv.KPIValue,
  pcokq.Quarter,
  pcokq.Year,
  pcokq.Month,
  pcokv.PortfolioCompanyOperatiONalKPIValueID AS ValuesKpiId,
  mo.Formula AS MasterFormula,
  mpo.Formula,
  mo.MethodologyId,
  pcokv.CreatedBy,
  pcokv.CreatedOn,
  dbo.AuditLogCompanyKPIFunctiON(pcokq.PortfolioCompanyID,'Operational KPIs',CAST(ISNULL(Year,0) AS VARCHAR),KPI,PortfolioCompanyOperatiONalKPIValueID,@ValueType) ActualAuditLog
FROM
  dbo.PortfolioCompanyOperatiONalKPIValues pcokv
INNER JOIN
  dbo.PortfolioCompanyOperatiONalKPIQuarters pcokq
ON pcokv.PortfolioCompanyOperatiONalKPIQuarterID = pcokq.PortfolioCompanyOperatiONalKPIQuarterID
INNER JOIN
  dbo.Mapping_PortfolioOperatiONalKPI mpo
ON pcokv.SectorwiseOperatiONalKPIID = mpo.KpiID
INNER JOIN
  dbo.M_SectorwiseOperatiONalKPI mo
ON pcokv.SectorwiseOperatiONalKPIID = mo.SectorwiseOperatiONalKPIID

WHERE pcokq.PortfolioCompanyID = @CompanyId and mpo.PortfolioCompanyID = @CompanyId and pcokq.ValueTypeID = @ValueTypeId 
and mo.IsDeleted = 0 and mpo.IsDeleted = 0 and pcokq.IsDeleted = 0 and pcokv.IsDeleted = 0 and Month is null and Quarter is null and Year >= @FromYear and Year <= @toYear
END
ELSE
BEGIN

SELECT
  DISTINCT 
  mo.SectorwiseOperatiONalKPIID AS KpiId,
  mpo.ParentKPIID AS ParentId,
  mpo.DisplayOrder,
  pcokq.PortfolioCompanyID,
  mo.KPI,
  mo.KpiInfo,
  mo.IsBoldKPI,
  mo.IsHeader,
  pcokv.KPIValue,
  pcokq.Quarter,
  pcokq.Year,
  pcokq.Month,
  pcokv.PortfolioCompanyOperatiONalKPIValueID AS ValuesKpiId,
  mo.Formula AS MasterFormula,
  mpo.Formula,
  mo.MethodologyId,
  pcokv.CreatedBy,
  pcokv.CreatedOn,
  dbo.AuditLogCompanyKPIFunctiON(pcokq.PortfolioCompanyID,'Operational KPIs',CAST(ISNULL(Year,0) AS VARCHAR),KPI,PortfolioCompanyOperatiONalKPIValueID,@ValueType) ActualAuditLog
FROM
  dbo.PortfolioCompanyOperatiONalKPIValues pcokv
INNER JOIN
  dbo.PortfolioCompanyOperatiONalKPIQuarters pcokq
ON pcokv.PortfolioCompanyOperatiONalKPIQuarterID = pcokq.PortfolioCompanyOperatiONalKPIQuarterID
INNER JOIN
  dbo.Mapping_PortfolioOperatiONalKPI mpo
ON pcokv.SectorwiseOperatiONalKPIID = mpo.KpiID
INNER JOIN
  dbo.M_SectorwiseOperatiONalKPI mo
ON pcokv.SectorwiseOperatiONalKPIID = mo.SectorwiseOperatiONalKPIID

WHERE pcokq.PortfolioCompanyID = @CompanyId and mpo.PortfolioCompanyID = @CompanyId and pcokq.ValueTypeID = @ValueTypeId 
and mo.IsDeleted = 0 and mpo.IsDeleted = 0 and pcokq.IsDeleted = 0 and pcokv.IsDeleted = 0 ;
END
END
GO
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[spBulkUploadPcFinancials]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[spBulkUploadPcFinancials]
GO
CREATE PROCEDURE [dbo].[spBulkUploadPcFinancials]
	@TableName VARCHAR(100),
	@UserId INT,
	@DocumentId INT = NULL,
    @SupportingDocumentsId NVARCHAR(MAX) = NULL,
    @CommentId INT = NULL,
	@CompanyId INT,
	@ModuleId INT = NULL,
	@DataIngestion BIT=0
AS
BEGIN
	SET NOCOUNT ON;
	DECLARE @SQLString NVARCHAR(max);
	DECLARE @RowCount INT;

	IF @ModuleId = 7
	BEGIN
		SET @SQLString = 'SELECT KpiId,ValueTypeId,KpiValue,Month,Year,Quarter FROM ' + @TableName + ' WHERE ValueTypeId = 4 OR ValueTypeId = 5';
		EXEC spBulkUploadProfitAndLossActual @SQLString, @UserId, @CompanyId, @DocumentId, @SupportingDocumentsId, @CommentId, @DataIngestion

		SET @SQLString = 'SELECT KpiId,KpiValue,Year,Quarter,Month FROM ' + @TableName + ' WHERE ValueTypeId = 6';
		EXEC spBulkUploadProfitAndLossForecast @SQLString, @UserId, @CompanyId, @DocumentId, @SupportingDocumentsId, @CommentId, @DataIngestion

		SET @SQLString = 'SELECT KpiId,KpiValue,Year FROM ' + @TableName + ' WHERE ValueTypeId = 3';
		EXEC spBulkUploadProfitAndLossICCase @SQLString, @UserId, @CompanyId, @DocumentId, @SupportingDocumentsId, @CommentId, @DataIngestion

		SET @SQLString = 'SELECT KpiId,ValueTypeId,KpiValue,Month,Year,Quarter FROM ' + @TableName + ' WHERE ValueTypeId > 6 order by ValueTypeId';
		EXEC spBulkUploadPcFinancialsValues  @UserId, @CompanyId, @DocumentId, @SupportingDocumentsId, @CommentId, @SQLString, @ModuleId, @DataIngestion

	END
	ELSE IF @ModuleId = 8
	BEGIN
		SET @SQLString = 'SELECT KpiId,ValueTypeId,KpiValue,Month,Year,Quarter FROM ' + @TableName + ' WHERE ValueTypeId = 4 OR ValueTypeId = 5';
		EXEC spBulkUploadBalanceSheetActual @SQLString, @UserId, @CompanyId, @DocumentId, @SupportingDocumentsId, @CommentId, @DataIngestion

		SET @SQLString = 'SELECT KpiId,KpiValue,Year,Quarter,Month FROM ' + @TableName + ' WHERE ValueTypeId = 6';
		EXEC spBulkUploadBalanceSheetForecast @SQLString, @UserId, @CompanyId, @DocumentId, @SupportingDocumentsId, @CommentId, @DataIngestion

		SET @SQLString = 'SELECT KpiId,KpiValue,Year FROM ' + @TableName + ' WHERE ValueTypeId = 3';
		EXEC spBulkUploadBalanceSheetICCase @SQLString, @UserId, @CompanyId, @DocumentId, @SupportingDocumentsId, @CommentId, @DataIngestion

		SET @SQLString = 'SELECT KpiId,ValueTypeId,KpiValue,Month,Year,Quarter FROM ' + @TableName + ' WHERE ValueTypeId > 6 order by ValueTypeId';
		EXEC spBulkUploadPcFinancialsValues  @UserId, @CompanyId, @DocumentId, @SupportingDocumentsId, @CommentId, @SQLString, @ModuleId, @DataIngestion

	END
	ELSE IF @ModuleId = 9
	BEGIN
	print 'here'
		SET @SQLString = 'SELECT KpiId,ValueTypeId,KpiValue,Month,Year,Quarter FROM ' + @TableName + ' WHERE ValueTypeId = 4 OR ValueTypeId = 5';
		EXEC spBulkUploadCashFlowActual @SQLString, @UserId, @CompanyId, @DocumentId, @SupportingDocumentsId, @CommentId, @DataIngestion

		SET @SQLString = 'SELECT KpiId,KpiValue,Year,Quarter,Month FROM ' + @TableName + ' WHERE ValueTypeId = 6';
		EXEC spBulkUploadCashFlowForecast @SQLString, @UserId, @CompanyId, @DocumentId, @SupportingDocumentsId, @CommentId, @DataIngestion

		SET @SQLString = 'SELECT KpiId,KpiValue,Year FROM ' + @TableName + ' WHERE ValueTypeId = 3';
		EXEC spBulkUploadCashFlowICCase @SQLString, @UserId, @CompanyId, @DocumentId, @SupportingDocumentsId, @CommentId, @DataIngestion

		SET @SQLString = 'SELECT KpiId,ValueTypeId,KpiValue,Month,Year,Quarter FROM ' + @TableName + ' WHERE ValueTypeId > 6 order by ValueTypeId';
		EXEC spBulkUploadPcFinancialsValues  @UserId, @CompanyId, @DocumentId, @SupportingDocumentsId, @CommentId, @SQLString, @ModuleId, @DataIngestion

	END
END
GO
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[spBulkUploadPcFinancialsValues]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[spBulkUploadPcFinancialsValues]
GO
CREATE PROCEDURE [dbo].[spBulkUploadPcFinancialsValues]
	@UserId INT,
	@CompanyId INT,
	@DocumentId INT = NULL,
    @SupportingDocumentsId NVARCHAR(MAX) = NULL,
    @CommentId INT = NULL,
	@SQLString NVARCHAR(max),
	@ModuleId INT = NULL,
	@DataIngestion BIT=0
AS
BEGIN
	SET NOCOUNT ON;
	DECLARE @financials TABLE (
	[ID] [int],
	[ValueTypeId] INT NULL,
	[KpiValue] NVARCHAR(MAX) NULL,
	[Month] [int] NULL,
	[Year] [int] NOT NULL,
	[Quarter] [nvarchar](10) NULL
	)
	--INSERT INTO @financials EXEC sys.Sp_executesql @SQLString;
	DECLARE @ID INT,@KpiValue NVARCHAR(max), @Year INT,@Quarter VARCHAR(10), @Month INT = NULL, @ValueTypeId INT = NULL, @valueType  NVARCHAR(30) = NULL, @inserr INT, @upderr INT, @maxerr INT, @CurrencyCode NVARCHAR(50) = NULL;
	INSERT INTO @financials EXEC sys.sp_executesql @SQLString;	

	DECLARE cursor_Financials CURSOR
	FOR SELECT 
	[ID],
	[ValueTypeId],
	[KpiValue],
	[Month],
	[Year],
	[Quarter] 
	FROM @financials;
 
	OPEN cursor_Financials;
 
	FETCH NEXT FROM cursor_Financials INTO @ID,@ValueTypeId,@KpiValue,@Month,@Year,@Quarter;
	WHILE @@FETCH_STATUS = 0
		BEGIN
			DECLARE @ValueID INT = 0, @MappingID INT = 0, @LineItemID INT = 0, @KPIInfo VARCHAR(20) = NULL;
			SET @valueType = (SELECT TOP 1 HeaderValue FROM M_ValueTypes WHERE ValueTypeID = @ValueTypeId);
			IF @ModuleId = 7 
			BEGIN
				IF(@Quarter IS NULL AND (@Month IS NULL or @Month = 0))
				BEGIN
					SELECT @ValueID = [PLV].[PCFinancialsValuesId]
					FROM [dbo].[M_ProfitAndLoss_LineItems] PLL 
					INNER JOIN [dbo].[Mapping_CompanyProfitAndLossLineItems] MCPL ON [MCPL].[ProfitAndLossLineItemID] = [PLL].[ProfitAndLossLineItemID] AND [MCPL].[PortfolioCompanyID] = @CompanyId
					INNER JOIN [dbo].[PCFinancialsValues] PLV ON [PLV].[MappingId] = [MCPL].[CompanyProfitAndLossLineItemMappingID] AND [PLV].[Year] = @Year AND [PLV].[IsDeleted]=0 And ([PLV].[Month] IS NULL OR [PLV].[Month] = 0) AND [PLV].[Quarter] IS NULL
					WHERE [MCPL].[PortfolioCompanyID] = @CompanyId AND [PLL].[ProfitAndLossLineItemID] = @ID AND [MCPL].[IsDeleted] = 0 AND [PLL].[IsDeleted] = 0 AND [PLV].[IsDeleted] = 0 AND [PLV].ValueTypeID = @ValueTypeId AND [PLV].ModuleId = @ModuleId;
				END
			IF(@Quarter IS NOT NULL AND @Year IS NOT NULL)
				BEGIN
				SELECT @ValueID = [PLV].[PCFinancialsValuesId]
					FROM [dbo].[M_ProfitAndLoss_LineItems] PLL 
					INNER JOIN [dbo].[Mapping_CompanyProfitAndLossLineItems] MCPL ON [MCPL].[ProfitAndLossLineItemID] = [PLL].[ProfitAndLossLineItemID] AND [MCPL].[PortfolioCompanyID] = @CompanyId
					INNER JOIN [dbo].[PCFinancialsValues] PLV ON [PLV].[MappingId] = [MCPL].[CompanyProfitAndLossLineItemMappingID] AND [PLV].[Year] = @Year AND [PLV].[Quarter] =@Quarter 
					WHERE [MCPL].[PortfolioCompanyID] = @CompanyId AND [PLL].[ProfitAndLossLineItemID] = @ID AND [MCPL].[IsDeleted] = 0 AND [PLL].[IsDeleted] = 0 AND [PLV].[IsDeleted] = 0 AND [PLV].ValueTypeID = @ValueTypeId AND [PLV].ModuleId = @ModuleId;
				END
			IF(@Month IS NOT NULL AND @Month > 0 AND @Year IS NOT NULL)
				BEGIN
				SELECT @ValueID = [PLV].[PCFinancialsValuesId]
					FROM [dbo].[M_ProfitAndLoss_LineItems] PLL 
					INNER JOIN [dbo].[Mapping_CompanyProfitAndLossLineItems] MCPL ON [MCPL].[ProfitAndLossLineItemID] = [PLL].[ProfitAndLossLineItemID] AND [MCPL].[PortfolioCompanyID] = @CompanyId
					INNER JOIN [dbo].[PCFinancialsValues] PLV ON [PLV].[MappingId] = [MCPL].[CompanyProfitAndLossLineItemMappingID] AND [PLV].[Year] = @Year AND [PLV].[Month] =@Month
					WHERE [MCPL].[PortfolioCompanyID] = @CompanyId AND [PLL].[ProfitAndLossLineItemID] = @ID AND [MCPL].[IsDeleted] = 0 AND [PLL].[IsDeleted] = 0 AND [PLV].[IsDeleted] = 0 AND [PLV].ValueTypeID = @ValueTypeId AND [PLV].ModuleId = @ModuleId;
				END
			SELECT @MappingID = [MCPL].[CompanyProfitAndLossLineItemMappingID]
			FROM [dbo].[M_ProfitAndLoss_LineItems] PLL 
			INNER JOIN [dbo].[Mapping_CompanyProfitAndLossLineItems] MCPL ON [MCPL].[ProfitAndLossLineItemID] = [PLL].[ProfitAndLossLineItemID] AND [MCPL].[PortfolioCompanyID] = @CompanyId		
			WHERE [PLL].[ProfitAndLossLineItemID] = @ID AND [MCPL].[IsDeleted] = 0 AND [PLL].[IsDeleted] = 0;
			END
			IF @ModuleId = 8
			BEGIN
				IF(@Quarter IS NULL AND (@Month IS NULL or @Month = 0))
				BEGIN
					SELECT @ValueID = [PLV].[PCFinancialsValuesId]
					FROM [dbo].[M_BalanceSheet_LineItems] PLL 
					INNER JOIN [dbo].[Mapping_CompanyBalanceSheetLineItems] MCPL ON [MCPL].[BalanceSheetLineItemID] = [PLL].[BalanceSheetLineItemID] AND [MCPL].[PortfolioCompanyID] = @CompanyId
					INNER JOIN [dbo].[PCFinancialsValues] PLV ON [PLV].[MappingId] = [MCPL].[CompanyBalanceSheetLineItemMappingID] AND [PLV].[Year] = @Year AND [PLV].[IsDeleted]=0 And ([PLV].[Month] IS NULL OR [PLV].[Month] = 0) AND [PLV].[Quarter] IS NULL
					WHERE [MCPL].[PortfolioCompanyID] = @CompanyId AND [PLL].[BalanceSheetLineItemID] = @ID AND [MCPL].[IsDeleted] = 0 AND [PLL].[IsDeleted] = 0 AND [PLV].[IsDeleted] = 0 AND [PLV].ValueTypeID = @ValueTypeId AND [PLV].ModuleId = @ModuleId;
				END
			IF(@Quarter IS NOT NULL AND @Year IS NOT NULL)
				BEGIN
				SELECT @ValueID = [PLV].[PCFinancialsValuesId]
					FROM [dbo].[M_BalanceSheet_LineItems] PLL 
					INNER JOIN [dbo].[Mapping_CompanyBalanceSheetLineItems] MCPL ON [MCPL].[BalanceSheetLineItemID] = [PLL].[BalanceSheetLineItemID] AND [MCPL].[PortfolioCompanyID] = @CompanyId
					INNER JOIN [dbo].[PCFinancialsValues] PLV ON [PLV].[MappingId] = [MCPL].[CompanyBalanceSheetLineItemMappingID] AND [PLV].[Year] = @Year AND [PLV].[Quarter] =@Quarter 
					WHERE [MCPL].[PortfolioCompanyID] = @CompanyId AND [PLL].[BalanceSheetLineItemID] = @ID AND [MCPL].[IsDeleted] = 0 AND [PLL].[IsDeleted] = 0 AND [PLV].[IsDeleted] = 0 AND [PLV].ValueTypeID = @ValueTypeId AND [PLV].ModuleId = @ModuleId;
				END
			IF(@Month IS NOT NULL AND @Month > 0 AND @Year IS NOT NULL)
				BEGIN
				SELECT @ValueID = [PLV].[PCFinancialsValuesId]
					FROM [dbo].[M_BalanceSheet_LineItems] PLL 
					INNER JOIN [dbo].[Mapping_CompanyBalanceSheetLineItems] MCPL ON [MCPL].[BalanceSheetLineItemID] = [PLL].[BalanceSheetLineItemID] AND [MCPL].[PortfolioCompanyID] = @CompanyId
					INNER JOIN [dbo].[PCFinancialsValues] PLV ON [PLV].[MappingId] = [MCPL].[CompanyBalanceSheetLineItemMappingID] AND [PLV].[Year] = @Year AND [PLV].[Month] =@Month
					WHERE [MCPL].[PortfolioCompanyID] = @CompanyId AND [PLL].[BalanceSheetLineItemID] = @ID AND [MCPL].[IsDeleted] = 0 AND [PLL].[IsDeleted] = 0 AND [PLV].[IsDeleted] = 0 AND [PLV].ValueTypeID = @ValueTypeId AND [PLV].ModuleId = @ModuleId;
				END
			SELECT @MappingID = [MCPL].[CompanyBalanceSheetLineItemMappingID]
			FROM [dbo].[M_BalanceSheet_LineItems] PLL 
			INNER JOIN [dbo].[Mapping_CompanyBalanceSheetLineItems] MCPL ON [MCPL].[BalanceSheetLineItemID] = [PLL].[BalanceSheetLineItemID] AND [MCPL].[PortfolioCompanyID] = @CompanyId		
			WHERE [PLL].[BalanceSheetLineItemID] = @ID AND [MCPL].[IsDeleted] = 0 AND [PLL].[IsDeleted] = 0;
			END
			IF @ModuleId = 9 
			BEGIN
				IF(@Quarter IS NULL AND (@Month IS NULL or @Month = 0))
				BEGIN
					SELECT @ValueID = [PLV].[PCFinancialsValuesId]
					FROM [dbo].[M_CashFlow_LineItems] PLL 
					INNER JOIN [dbo].[Mapping_CompanyCashFlowLineItems] MCPL ON [MCPL].[CashFlowLineItemID] = [PLL].CashFlowLineItemID AND [MCPL].[PortfolioCompanyID] = @CompanyId
					INNER JOIN [dbo].[PCFinancialsValues] PLV ON [PLV].[MappingId] = [MCPL].[CompanyCashFlowLineItemMappingID] AND [PLV].[Year] = @Year AND [PLV].[IsDeleted]=0 And ([PLV].[Month] IS NULL OR [PLV].[Month] = 0) AND [PLV].[Quarter] IS NULL
					WHERE [MCPL].[PortfolioCompanyID] = @CompanyId AND [PLL].[CashFlowLineItemID] = @ID AND [MCPL].[IsDeleted] = 0 AND [PLL].[IsDeleted] = 0 AND [PLV].[IsDeleted] = 0 AND [PLV].ValueTypeID = @ValueTypeId AND [PLV].ModuleId = @ModuleId;
				END
			IF(@Quarter IS NOT NULL AND @Year IS NOT NULL)
				BEGIN
				SELECT @ValueID = [PLV].[PCFinancialsValuesId]
					FROM [dbo].[M_CashFlow_LineItems] PLL 
					INNER JOIN [dbo].[Mapping_CompanyCashFlowLineItems] MCPL ON [MCPL].[CashFlowLineItemID] = [PLL].[CashFlowLineItemID] AND [MCPL].[PortfolioCompanyID] = @CompanyId
					INNER JOIN [dbo].[PCFinancialsValues] PLV ON [PLV].[MappingId] = [MCPL].[CompanyCashFlowLineItemMappingID] AND [PLV].[Year] = @Year AND [PLV].[Quarter] =@Quarter 
					WHERE [MCPL].[PortfolioCompanyID] = @CompanyId AND [PLL].[CashFlowLineItemID] = @ID AND [MCPL].[IsDeleted] = 0 AND [PLL].[IsDeleted] = 0 AND [PLV].[IsDeleted] = 0 AND [PLV].ValueTypeID = @ValueTypeId AND [PLV].ModuleId = @ModuleId;
				END
			IF(@Month IS NOT NULL AND @Month > 0 AND @Year IS NOT NULL)
				BEGIN
				SELECT @ValueID = [PLV].[PCFinancialsValuesId]
					FROM [dbo].[M_CashFlow_LineItems] PLL 
					INNER JOIN [dbo].[Mapping_CompanyCashFlowLineItems] MCPL ON [MCPL].[CashFlowLineItemID] = [PLL].[CashFlowLineItemID] AND [MCPL].[PortfolioCompanyID] = @CompanyId
					INNER JOIN [dbo].[PCFinancialsValues] PLV ON [PLV].[MappingId] = [MCPL].[CompanyCashFlowLineItemMappingID] AND [PLV].[Year] = @Year AND [PLV].[Month] =@Month
					WHERE [MCPL].[PortfolioCompanyID] = @CompanyId AND [PLL].[CashFlowLineItemID] = @ID AND [MCPL].[IsDeleted] = 0 AND [PLL].[IsDeleted] = 0 AND [PLV].[IsDeleted] = 0 AND [PLV].ValueTypeID = @ValueTypeId AND [PLV].ModuleId = @ModuleId;
				END
			SELECT @MappingID = [MCPL].[CompanyCashFlowLineItemMappingID]
			FROM [dbo].[M_CashFlow_LineItems] PLL 
			INNER JOIN [dbo].[Mapping_CompanyCashFlowLineItems] MCPL ON [MCPL].[CashFlowLineItemID] = [PLL].[CashFlowLineItemID] AND [MCPL].[PortfolioCompanyID] = @CompanyId		
			WHERE [PLL].[CashFlowLineItemID] = @ID AND [MCPL].[IsDeleted] = 0 AND [PLL].[IsDeleted] = 0;
			END
			
			---Replace -1 with Null - Included NULL value as -1 in unpivot 
			IF (LTRIM(RTRIM(@KpiValue)) = '0.00000000')
			BEGIN
				SET @KpiValue = NULL;
			END		
			
			IF (@KpiValue IS NOT NULL)
			BEGIN
				IF (LOWER(@KpiValue) = 'n.a.' OR LOWER(@KpiValue) = 'na.' OR LOWER(@KpiValue) = 'na' OR LOWER(@KpiValue) = 'n.a')
				BEGIN
					SET @KpiValue = NULL;
				END
			END		
			Select @KPIInfo = dbo.GetKPIInfo(@ID,'PL');
			IF(LTRIM(RTRIM(@KPIInfo)) = '$')
			BEGIN
				SELECT @CurrencyCode = dbo.GetCurrencyCode(@CompanyId);		
			END
			ELSE
			BEGIN
				SET @CurrencyCode = @KPIInfo;
			END	
			BEGIN TRANSACTION [Tran_Forecast]
			
			IF(@ValueID = 0)
			BEGIN
			DECLARE @CopySupportingDocumentsId Varchar(MAX)=NULL, @CopyCommentId Varchar(MAX)=NULL, @CopyDocumentId Varchar(MAX)=NULL;
					SET @CopySupportingDocumentsId = 
					    (CASE 
					        WHEN @KpiValue IS NULL THEN NULL
					        ELSE @SupportingDocumentsId
					     END);
					
					SET @CopyCommentId = 
					    (CASE 
					        WHEN @KpiValue IS NULL THEN NULL
					        ELSE @CommentId
					     END);
					
					SET @CopyDocumentId = 
					    (CASE 
					        WHEN @KpiValue IS NULL THEN NULL
					        ELSE @DocumentId
					     END);
					IF(@MappingID = 0)
					BEGIN					
						SELECT @LineItemID = [ProfitAndLossLineItemID] FROM [dbo].[M_ProfitAndLoss_LineItems] WHERE [ProfitAndLossLineItemID] = @ID;
						SELECT @MappingID = CompanyProfitAndLossLineItemMappingID FROM [dbo].[Mapping_CompanyProfitAndLossLineItems] WHERE [PortfolioCompanyID] = @CompanyId AND [ProfitAndLossLineItemID] = @LineItemID;						

						INSERT INTO [dbo].[PCFinancialsValues] ([MappingId],[KPIValue],[ValueInfo],[Year],[CreatedOn],[CreatedBy],[IsDeleted],[IsActive],[Quarter],[Month],DocumentId,SupportingDocumentsId,CommentId,ModuleId,ValueTypeId)  
						VALUES(@MappingID, @KpiValue, @CurrencyCode, @Year, GETDATE(), @UserID,0,1,@Quarter,@Month,@CopyDocumentId,@CopySupportingDocumentsId,@CopyCommentId,@ModuleId,@ValueTypeId);
						-- Save error number returned from Insert statement  
						---First entry for audit log
						SELECT @ValueID = @@IDENTITY;
						IF @KpiValue IS NOT NULL
							exec AddFinancialAuditLogDetails @ModuleId,@ValueID,null,@KpiValue,@valueType,@UserID,@CompanyId,null,@CurrencyCode,@CopyDocumentId,@CopySupportingDocumentsId,@CopyCommentId,@DataIngestion;
						SET @inserr = @@error  
						IF @inserr > @maxerr
						SET @maxerr = @inserr 
					END
					ELSE IF(@MappingID > 0)
					BEGIN							
						INSERT INTO [dbo].[PCFinancialsValues] ([MappingId],[KPIValue],[ValueInfo],[Year],[CreatedOn],[CreatedBy],[IsDeleted],[IsActive],[Quarter],[Month],DocumentId,SupportingDocumentsId,CommentId,ModuleId,ValueTypeId)  
						VALUES(@MappingID, @KpiValue, @CurrencyCode, @Year, GETDATE(), @UserID,0,1,@Quarter,@Month,@CopyDocumentId,@CopySupportingDocumentsId,@CopyCommentId,@ModuleId,@ValueTypeId);
						-- Save error number returned from Insert statement  
						---First entry for audit log
						SELECT @ValueID = @@IDENTITY;
						IF @KpiValue IS NOT NULL
							exec AddFinancialAuditLogDetails @ModuleId,@ValueID,null,@KpiValue,@valueType,@UserID,@CompanyId,null,@CurrencyCode,@CopyDocumentId,@CopySupportingDocumentsId,@CopyCommentId,@DataIngestion;
						SET @inserr = @@error  
						IF @inserr > @maxerr  
						SET @maxerr = @inserr   
					END			
			END
			ELSE IF(@ValueID > 0 AND @KpiValue IS NOT NULL)
			BEGIN		
			DECLARE @oldKPIValue NVARCHAR(MAX),@oldCurrencyCode NVARCHAR(50);
					SELECT @oldKPIValue = KPIValue, @oldCurrencyCode = ValueInfo
						FROM PCFinancialsValues
						WHERE PCFinancialsValuesId = @ValueID;
						
						exec AddFinancialAuditLogDetails @ModuleId,@ValueID,@oldKPIValue,@KpiValue,@valueType,@UserID,@CompanyId,@oldCurrencyCode,@CurrencyCode,@DocumentId,@SupportingDocumentsId,@CommentId,@DataIngestion;						
						UPDATE [dbo].[PCFinancialsValues] SET [KPIValue] = CASE WHEN @KpiValue IS NOT NULL THEN @KpiValue ELSE [KPIValue] END, [ModifiedOn] = GETDATE(), [ModifiedBy]= @UserID ,[DocumentId] = @DocumentId,[SupportingDocumentsId] = @SupportingDocumentsId,[CommentId] =@CommentId 
						WHERE [PCFinancialsValuesId] = @ValueID; 
				
				-- Save error number returned from Update statement  
				SET @upderr = @@error  
				IF @upderr > @maxerr  
				SET @maxerr = @upderr
			END

			-- If an error occurred, roll back  
			IF @maxerr <> 0  
			BEGIN  
				ROLLBACK TRANSACTION [Tran_Forecast];
			END  
			ELSE  
			BEGIN  
				COMMIT TRANSACTION [Tran_Forecast];							 
			END  

			FETCH NEXT FROM cursor_Financials INTO @ID,@ValueTypeId,@KpiValue,@Month,@Year,@Quarter;
				   
		END;
 
		CLOSE cursor_Financials; 
		DEALLOCATE cursor_Financials;
		SET NOCOUNT OFF;
		SET ANSI_WARNINGS ON;
END
GO
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[addFinancialAuditLogDetails]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[addFinancialAuditLogDetails]
GO
CREATE PROCEDURE [dbo].[addFinancialAuditLogDetails]
 (@ModuleId INT,
 @AttributeID int,
 @OldValue varchar(100),
 @NewValue varchar(100),
 @ValueType nvarchar(500),
 @CreatedBy int,
 @PortfolioCompanyId int,
 @OldCurrencyType varchar(10),
 @NewCurrencyType varchar(10),
 @DocumentId BIGINT = NULL,
 @SupportingDocumentsId NVARCHAR(MAX) = NULL,
 @CommentId BIGINT = NULL,
 @DataIngestion BIT=0
 )
AS
BEGIN
	SET NOCOUNT ON;
INSERT INTO [dbo].[FinancialAuditLog]
           ([ModuleId]
           ,[AttributeID]
           ,[OldValue]
           ,[NewValue]
           ,[ValueType]
           ,[CreatedBy]
           ,[PortfolioCompanyId]
		   ,[OldCurrencyType]
		   ,[NewCurrencyType]
		   ,[DocumentId]
		   ,[SupportingDocumentsId]
		   ,[CommentId]
		   ,[AuditType]
		   )
     VALUES
           (@ModuleId,
           @AttributeID,
           @OldValue, 
           @NewValue,
           @ValueType, 
           @CreatedBy, 
           @PortfolioCompanyId,
		   @OldCurrencyType,
		   @NewCurrencyType,
		   @DocumentId,
		   @SupportingDocumentsId,
		   @CommentId,
		   CASE WHEN @DataIngestion = 0 THEN 'File Upload' ELSE 'Data Ingestion' END
		   )
END
GO
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[spBulkUploadCashFlowICCase]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[spBulkUploadCashFlowICCase]
GO
CREATE PROCEDURE [dbo].[spBulkUploadCashFlowICCase]
           @SQLString NVARCHAR(MAX)
		  ,@UserID INT
		  ,@PortfolioCompanyID INT		 
		  ,@DocumentId BIGINT = NULL
		  ,@SupportingDocumentsId NVARCHAR(MAX) = NULL
		  ,@CommentId BIGINT = NULL
		  ,@DataIngestion BIT=0
AS
BEGIN
	SET NOCOUNT ON;
	SET ANSI_WARNINGS OFF;
	DECLARE @ICCase TABLE([ID] INT,[ActualValue] NVARCHAR(MAX),[Year] INT);
	DECLARE @CashFlowLineItem VARCHAR(100),@ID INT,@ActualValue NVARCHAR(MAX),@Year INT;	
	DECLARE @inserr INT;  
	DECLARE @upderr INT;  
	DECLARE @maxerr INT;
	DECLARE @CurrencyCode NVARCHAR(50) = NULL;
	  
	INSERT INTO @ICCase
	EXEC sys.sp_executesql @SQLString;
	---Get Currency Code based on PortfolioCompanyID
	SELECT @CurrencyCode = dbo.GetCurrencyCode(@PortfolioCompanyID);

	DECLARE cursor_iccase CURSOR
	FOR SELECT 
			[ID],
			[ActualValue],			
			[Year]
	FROM @ICCase;
 
	OPEN cursor_iccase;
 
	FETCH NEXT FROM cursor_iccase INTO 
	   @ID,
	   @ActualValue,
	   @Year
	WHILE @@FETCH_STATUS = 0
		BEGIN
			DECLARE @CashFlowICCaseValueID INT = 0;
			DECLARE @CompanyCashFlowLineItemMappingID INT = 0;
			DECLARE @CashFlowLineItemID INT = 0;
		DECLARE @KPIInfo VARCHAR(20) = NULL;

			SELECT @CashFlowICCaseValueID = [BLV].[CashFlowICCaseValueID]
			FROM [dbo].[M_CashFlow_LineItems] BLL 
			INNER JOIN [dbo].[Mapping_CompanyCashFlowLineItems] MCPL ON [MCPL].[CashFlowLineItemID] = [BLL].[CashFlowLineItemID] AND [MCPL].[PortfolioCompanyID] = @PortfolioCompanyID
			INNER JOIN [dbo].[CashFlowICCaseValues] BLV ON [BLV].[CompanyCashFlowLineItemMappingID] = [MCPL].[CompanyCashFlowLineItemMappingID] AND [BLV].[Year] = @Year
			WHERE [BLL].[CashFlowLineItemID] = @ID AND [MCPL].[IsDeleted] = 0 AND [BLL].[IsDeleted] = 0 AND [BLV].[IsDeleted] = 0;

			SELECT @CompanyCashFlowLineItemMappingID = [MCBL].[CompanyCashFlowLineItemMappingID]
			FROM [dbo].[M_CashFlow_LineItems] BLL 
			INNER JOIN [dbo].[Mapping_CompanyCashFlowLineItems] MCBL ON [MCBL].[CashFlowLineItemID] = [BLL].[CashFlowLineItemID] AND [MCBL].[PortfolioCompanyID] = @PortfolioCompanyID		
			WHERE [BLL].[CashFlowLineItemID] = @ID AND [MCBL].[IsDeleted] = 0 AND [BLL].[IsDeleted] = 0;
			
			IF (LTRIM(RTRIM(@ActualValue)) = '0.00000000')
			BEGIN
				SET @ActualValue = NULL;
			END		
			
			IF (@ActualValue IS NOT NULL)
			BEGIN
				IF (LOWER(@ActualValue) = 'n.a.' OR LOWER(@ActualValue) = 'na.' OR LOWER(@ActualValue) = 'na' OR LOWER(@ActualValue) = 'n.a')
				BEGIN
					SET @ActualValue = NULL;
				END
			END	
	
			Select @KPIInfo = dbo.GetKPIInfo(@ID,'BS');
			IF(LTRIM(RTRIM(@KPIInfo)) = '$')
			BEGIN
				SELECT @CurrencyCode = dbo.GetCurrencyCode(@PortfolioCompanyID);		
			END
			ELSE
			BEGIN
				SET @CurrencyCode = @KPIInfo;
			END	
			BEGIN TRANSACTION [Tran_ICCase]			
				IF(@CashFlowICCaseValueID = 0)
				BEGIN	

					DECLARE @CopySupportingDocumentsId Varchar(MAX)=NULL, @CopyCommentId Varchar(MAX)=NULL, @CopyDocumentId Varchar(MAX)=NULL;
					SET @CopySupportingDocumentsId = 
					  (CASE 
						WHEN @ActualValue IS NULL THEN NULL
						ELSE @SupportingDocumentsId
					   END);
					
					SET @CopyCommentId = 
					    (CASE 
					        WHEN @ActualValue IS NULL THEN NULL
					        ELSE @CommentId
					     END);
					
					SET @CopyDocumentId = 
					    (CASE 
					        WHEN @ActualValue IS NULL THEN NULL
					        ELSE @DocumentId
					     END);

				IF(@CompanyCashFlowLineItemMappingID = 0)
					BEGIN	
						SELECT @CashFlowLineItemID = [CashFlowLineItemID] FROM [dbo].[M_CashFlow_LineItems] WHERE [CashFlowLineItemID] = @ID;
						SELECT @CompanyCashFlowLineItemMappingID = [CompanyCashFlowLineItemMappingID] FROM [dbo].[Mapping_CompanyCashFlowLineItems] WHERE [PortfolioCompanyID] = @PortfolioCompanyID AND [CashFlowLineItemID] = @CashFlowLineItemID;						
						INSERT INTO [dbo].[CashFlowICCaseValues] ([CompanyCashFlowLineItemMappingID],[BaseValue],[UpsideValue],[DownValue],[ValueInfo],[Year],[CreatedOn],[CreatedBy],[IsDeleted],[IsActive],DocumentId,SupportingDocumentsId,CommentId)  
						VALUES(@CompanyCashFlowLineItemMappingID, @ActualValue, NULL, NULL, @CurrencyCode, @Year, GETDATE(), @UserID,0,1,@CopyDocumentId,@CopySupportingDocumentsId,@CopyCommentId);
						-- Save error number returned from Insert statement  
						SELECT @CashFlowICCaseValueID = @@IDENTITY;
						---First entry for audit log
						exec AddFinancialAuditLogDetails 9,@CashFlowICCaseValueID,null,@ActualValue,'ic',@UserID,@PortfolioCompanyID,null,@CurrencyCode,@CopyDocumentId,@CopySupportingDocumentsId,@CopyCommentId,@DataIngestion;
						SET @inserr = @@error  
						IF @inserr > @maxerr
						SET @maxerr = @inserr 
					END
					ELSE IF(@CompanyCashFlowLineItemMappingID > 0)
					BEGIN	
						INSERT INTO [dbo].[CashFlowICCaseValues] ([CompanyCashFlowLineItemMappingID],[BaseValue],[UpsideValue],[DownValue],[ValueInfo],[Year],[CreatedOn],[CreatedBy],[IsDeleted],[IsActive],DocumentId,SupportingDocumentsId,CommentId)  
						VALUES(@CompanyCashFlowLineItemMappingID, @ActualValue, NULL, NULL, @CurrencyCode, @Year, GETDATE(), @UserID,0,1,@CopyDocumentId,@CopySupportingDocumentsId,@CopyCommentId);
						-- Save error number returned from Insert statement  
						SELECT @CashFlowICCaseValueID = @@IDENTITY;
						---First entry for audit log
						exec AddFinancialAuditLogDetails 9,@CashFlowICCaseValueID,null,@ActualValue,'ic',@UserID,@PortfolioCompanyID,null,@CurrencyCode,@CopyDocumentId,@CopySupportingDocumentsId,@CopyCommentId,@DataIngestion;
						SET @inserr = @@error  
						IF @inserr > @maxerr  
						SET @maxerr = @inserr 
					END					
				END
				ELSE IF(@CashFlowICCaseValueID > 0 AND @ActualValue IS NOT NULL)
				BEGIN		
						DECLARE @oldKpiValue NVARCHAR(MAX),@oldCurrencyCode NVARCHAR(50);
						SELECT @oldKpiValue = BaseValue,@oldCurrencyCode = ValueInfo
						FROM CashFlowICCaseValues
						WHERE CashFlowICCaseValueID = @CashFlowICCaseValueID;

							exec AddFinancialAuditLogDetails 9,@CashFlowICCaseValueID,@oldKpiValue,@ActualValue,'ic',@UserID,@PortfolioCompanyID,@oldCurrencyCode,@CurrencyCode,@DocumentId,@SupportingDocumentsId,@CommentId,@DataIngestion;
							UPDATE [dbo].[CashFlowICCaseValues] SET [BaseValue] = CASE WHEN @ActualValue IS NOT NULL THEN @ActualValue ELSE [BaseValue] END, [UpsideValue]  = NULL,[DownValue] =NULL, [ModifiedOn] = GETDATE(), [ModifiedBy]= @UserID ,[DocumentId] = @DocumentId,[SupportingDocumentsId] = @SupportingDocumentsId,[CommentId] =@CommentId 
								WHERE [CashFlowICCaseValueID] = @CashFlowICCaseValueID; 
					-- Save error number returned from Update statement  
					SET @upderr = @@error  
					IF @upderr > @maxerr  
					SET @maxerr = @upderr
				END			
			-- If an error occurred, roll back  
			IF @maxerr <> 0  
			BEGIN  
				ROLLBACK TRANSACTION [Tran_ICCase];
			END  
			ELSE  
			BEGIN  
				COMMIT TRANSACTION [Tran_ICCase];							 
			END  

			FETCH NEXT FROM cursor_iccase INTO  @ID,@ActualValue,@Year;
						   
		END;
 
		CLOSE cursor_iccase; 
		DEALLOCATE cursor_iccase;
		SET NOCOUNT OFF;
END
GO
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[spBulkUploadCashFlowForecast]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[spBulkUploadCashFlowForecast]
GO
CREATE PROCEDURE [dbo].[spBulkUploadCashFlowForecast]
          @SQLString NVARCHAR(MAX)
		  ,@UserID INT
		  ,@PortfolioCompanyID INT		 
		  ,@DocumentId BIGINT = NULL
		  ,@SupportingDocumentsId NVARCHAR(MAX) = NULL
		  ,@CommentId BIGINT = NULL
		  ,@DataIngestion BIT=0
AS
BEGIN
	SET NOCOUNT ON;
	DECLARE @ProfitLossForecast TABLE([ID] INT,[ActualValue] NVARCHAR(max),[Year] VARCHAR(100),[Quarter] VARCHAR(10),[Month] INT);
	DECLARE @ID INT,@ActualValue NVARCHAR(max),@Year INT,@Quarter VARCHAR(10),@Month INT = NULL;
	DECLARE @inserr INT;  
	DECLARE @upderr INT;  
	DECLARE @maxerr INT;  
	DECLARE @CurrencyCode NVARCHAR(50) = NULL;

	--PRINT @SQLString;
	INSERT INTO @ProfitLossForecast
	EXEC sys.sp_executesql @SQLString;

	---Get Currency Code based on PortfolioCompanyID
	SELECT @CurrencyCode = dbo.GetCurrencyCode(@PortfolioCompanyID);		

	DECLARE cursor_Forecast CURSOR
	FOR SELECT 
			[ID],
			[ActualValue],			
			[Year],
			[Quarter],
			[Month]
	FROM @ProfitLossForecast;
 
	OPEN cursor_Forecast;
 
	FETCH NEXT FROM cursor_Forecast INTO @ID,@ActualValue,@Year,@Quarter,@Month;
 
	WHILE @@FETCH_STATUS = 0
		BEGIN
			DECLARE @CashFlowForecastValueID INT = 0;
			DECLARE @CompanyCashFlowLineItemMappingID INT = 0;
			DECLARE @CashFlowLineItemID INT = 0;	
			IF(@Quarter IS NULL AND (@Month IS NULL or @Month = 0))
				BEGIN
					SELECT @CashFlowForecastValueID = [PLV].[CashFlowForecastValueID]
					FROM [dbo].[M_CashFlow_LineItems] PLL 
					INNER JOIN [dbo].[Mapping_CompanyCashFlowLineItems] MCPL ON [MCPL].[CashFlowLineItemID] = [PLL].[CashFlowLineItemID] AND [MCPL].[PortfolioCompanyID] = @PortfolioCompanyID
					INNER JOIN [dbo].[CashFlow_ForecastData] PLV ON [PLV].[CompanyCashFlowLineItemMappingID] = [MCPL].[CompanyCashFlowLineItemMappingID] AND [PLV].[Year] = @Year AND ([PLV].[Month] IS NULL OR [PLV].[Month] = 0) AND [PLV].[Quarter] IS NULL
					WHERE [PLL].[CashFlowLineItemID] = @ID AND [PLL].[IsDeleted] = 0 AND [MCPL].[IsDeleted] = 0 AND [PLV].[IsDeleted] = 0;
				END
			IF(@Quarter IS NOT NULL AND @Year IS NOT NULL)
				BEGIN
					SELECT @CashFlowForecastValueID = [PLV].[CashFlowForecastValueID]
					FROM [dbo].[M_CashFlow_LineItems] PLL 
					INNER JOIN [dbo].[Mapping_CompanyCashFlowLineItems] MCPL ON [MCPL].[CashFlowLineItemID] = [PLL].[CashFlowLineItemID] AND [MCPL].[PortfolioCompanyID] = @PortfolioCompanyID
					INNER JOIN [dbo].[CashFlow_ForecastData] PLV ON [PLV].[CompanyCashFlowLineItemMappingID] = [MCPL].[CompanyCashFlowLineItemMappingID] AND [PLV].[Year] = @Year AND [PLV].[Quarter] =@Quarter
					WHERE [PLL].[CashFlowLineItemID] = @ID AND [PLL].[IsDeleted] = 0 AND [MCPL].[IsDeleted] = 0 AND [PLV].[IsDeleted] = 0;
				END
			IF(@Month IS NOT NULL AND @Month > 0 AND @Year IS NOT NULL)
				BEGIN
					SELECT @CashFlowForecastValueID = [PLV].[CashFlowForecastValueID]
					FROM [dbo].[M_CashFlow_LineItems] PLL 
					INNER JOIN [dbo].[Mapping_CompanyCashFlowLineItems] MCPL ON [MCPL].[CashFlowLineItemID] = [PLL].[CashFlowLineItemID] AND [MCPL].[PortfolioCompanyID] = @PortfolioCompanyID
					INNER JOIN [dbo].[CashFlow_ForecastData] PLV ON [PLV].[CompanyCashFlowLineItemMappingID] = [MCPL].[CompanyCashFlowLineItemMappingID] AND [PLV].[Year] = @Year AND [PLV].[Month] =@Month
					WHERE [PLL].[CashFlowLineItemID] = @ID AND [PLL].[IsDeleted] = 0 AND [MCPL].[IsDeleted] = 0 AND [PLV].[IsDeleted] = 0;
				END

			SELECT @CompanyCashFlowLineItemMappingID = [MCPL].[CompanyCashFlowLineItemMappingID],@CurrencyCode = KPIInfo
			FROM [dbo].[M_CashFlow_LineItems] PLL 
			INNER JOIN [dbo].[Mapping_CompanyCashFlowLineItems] MCPL ON [MCPL].[CashFlowLineItemID] = [PLL].[CashFlowLineItemID] AND [MCPL].[PortfolioCompanyID] = @PortfolioCompanyID		
			WHERE [PLL].[CashFlowLineItemID] = @ID AND [PLL].[IsDeleted] = 0 AND [MCPL].[IsDeleted] = 0;
			
			---Replace -1 with Null - Included NULL value as -1 in unpivot 
			IF (LTRIM(RTRIM(@ActualValue)) ='0.00000000')
			BEGIN
				SET @ActualValue = NULL;
			END	
			
			IF (@ActualValue IS NOT NULL)
			BEGIN
				IF (LOWER(@ActualValue) = 'n.a.' OR LOWER(@ActualValue) = 'na.' OR LOWER(@ActualValue) = 'na' OR LOWER(@ActualValue) = 'n.a')
				BEGIN
					SET @ActualValue = NULL;
				END
			END	

			BEGIN TRANSACTION [Tran_Forecast]
			
			IF(@CashFlowForecastValueID = 0)
			BEGIN
					DECLARE @CopySupportingDocumentsId Varchar(MAX)=NULL, @CopyCommentId Varchar(MAX)=NULL, @CopyDocumentId Varchar(MAX)=NULL;
					SET @CopySupportingDocumentsId = 
					    (CASE 
					        WHEN @ActualValue IS NULL THEN NULL
					        ELSE @SupportingDocumentsId
					     END);
					
					SET @CopyCommentId = 
					    (CASE 
					        WHEN @ActualValue IS NULL THEN NULL
					        ELSE @CommentId
					     END);
					
					SET @CopyDocumentId = 
					    (CASE 
					        WHEN @ActualValue IS NULL THEN NULL
					        ELSE @DocumentId
					     END);
					IF(@CompanyCashFlowLineItemMappingID = 0)
					BEGIN					
						SELECT @CashFlowLineItemID = [CashFlowLineItemID] FROM [dbo].[M_CashFlow_LineItems] WHERE [CashFlowLineItemID] = @ID;
						SELECT @CompanyCashFlowLineItemMappingID = CompanyCashFlowLineItemMappingID FROM [dbo].[Mapping_CompanyCashFlowLineItems] WHERE [PortfolioCompanyID] = @PortfolioCompanyID AND [CashFlowLineItemID] = @CashFlowLineItemID;						
						INSERT INTO [dbo].[CashFlow_ForecastData] ([CompanyCashFlowLineItemMappingID],[ActualValue],[ActualValueInfo],[Year],[CreatedOn],[CreatedBy],[IsDeleted],[IsActive],[Quarter],[Month],DocumentId,SupportingDocumentsId,CommentId)  
						VALUES(@CompanyCashFlowLineItemMappingID, @ActualValue, @CurrencyCode, @Year, GETDATE(), @UserID,0,1,@Quarter,@Month,@CopyDocumentId,@CopySupportingDocumentsId,@CopyCommentId);
						-- Save error number returned from Insert statement 
						SELECT @CashFlowForecastValueID = @@IDENTITY;
						---First entry for audit log
						IF @ActualValue IS NOT NULL
						exec AddFinancialAuditLogDetails 9,@CashFlowForecastValueID,null,@ActualValue,'forecast',@UserID,@PortfolioCompanyID,null,@CurrencyCode,@CopyDocumentId,@CopySupportingDocumentsId,@CopyCommentId,@DataIngestion;
						SET @inserr = @@error  
						IF @inserr > @maxerr
						SET @maxerr = @inserr 
					END
					ELSE IF(@CompanyCashFlowLineItemMappingID > 0)
					BEGIN							
						INSERT INTO [dbo].[CashFlow_ForecastData] ([CompanyCashFlowLineItemMappingID],[ActualValue],[ActualValueInfo],[Year],[CreatedOn],[CreatedBy],[IsDeleted],[IsActive],[Quarter],[Month],DocumentId,SupportingDocumentsId,CommentId)  
						VALUES(@CompanyCashFlowLineItemMappingID, @ActualValue, @CurrencyCode, @Year, GETDATE(), @UserID,0,1,@Quarter,@Month,@CopyDocumentId,@CopySupportingDocumentsId,@CopyCommentId);
						SELECT @CashFlowForecastValueID = @@IDENTITY;
						---First entry for audit log
						IF @ActualValue IS NOT NULL
						exec AddFinancialAuditLogDetails 9,@CashFlowForecastValueID,null,@ActualValue,'forecast',@UserID,@PortfolioCompanyID,null,@CurrencyCode,@CopyDocumentId,@CopySupportingDocumentsId,@CopyCommentId,@DataIngestion;
						-- Save error number returned from Insert statement  
						SET @inserr = @@error  
						IF @inserr > @maxerr  
						SET @maxerr = @inserr   
					END
			END
			ELSE IF(@CashFlowForecastValueID > 0 AND  @ActualValue IS NOT NULL)
			BEGIN	
				DECLARE @oldKpiValue NVARCHAR(MAX),@oldCurrencyCode NVARCHAR(50);
						SELECT @oldKpiValue = ActualValue, @oldCurrencyCode = ActualValueInfo
						FROM CashFlow_ForecastData
						WHERE CashFlowForecastValueID = @CashFlowForecastValueID;
						exec AddFinancialAuditLogDetails 9,@CashFlowForecastValueID,@oldKpiValue,@ActualValue,'forecast',@UserID,@PortfolioCompanyID,@oldCurrencyCode,@CurrencyCode,@DocumentId,@SupportingDocumentsId,@CommentId,@DataIngestion;
						UPDATE [dbo].[CashFlow_ForecastData] SET [ActualValue] = CASE WHEN @ActualValue IS NOT NULL THEN @ActualValue ELSE [ActualValue] END, [ModifiedOn] = GETDATE(), [ModifiedBy]= @UserID ,[DocumentId] = @DocumentId,[SupportingDocumentsId] = @SupportingDocumentsId,[CommentId] =@CommentId 
						WHERE [CashFlowForecastValueID] = @CashFlowForecastValueID; 
				-- Save error number returned from Update statement  
				SET @upderr = @@error  
				IF @upderr > @maxerr  
				SET @maxerr = @upderr
			END

			-- If an error occurred, roll back  
			IF @maxerr <> 0  
			BEGIN  
				ROLLBACK TRANSACTION [Tran_Forecast];
			END  
			ELSE  
			BEGIN  
				COMMIT TRANSACTION [Tran_Forecast];							 
			END  

			FETCH NEXT FROM cursor_Forecast INTO @ID,@ActualValue,@Year,@Quarter,@Month;
				   
		END;
 
		CLOSE cursor_Forecast; 
		DEALLOCATE cursor_Forecast;
		SET NOCOUNT OFF;
		SET ANSI_WARNINGS ON;
END
GO
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[spBulkUploadCashFlowActual]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[spBulkUploadCashFlowActual]
GO
CREATE PROCEDURE [dbo].[spBulkUploadCashFlowActual]
          @SQLString NVARCHAR(MAX)
		  ,@UserID INT
		  ,@PortfolioCompanyID INT	
		  ,@DocumentId BIGINT = NULL
		  ,@SupportingDocumentsId NVARCHAR(MAX) = NULL
		  ,@CommentId BIGINT = NULL
		  ,@DataIngestion BIT=0
AS
BEGIN
	SET NOCOUNT ON;
	DECLARE @ProfitLossActual TABLE([ID] INT,[Type] INT,[KpiValue] NVARCHAR(max),[Month] INT,[Year] VARCHAR(100),[Quarter] VARCHAR(10));
	DECLARE @ID INT, @KpiValue NVARCHAR(max),@Month INT,@Year INT,@Type INT,@Quarter VARCHAR(10);	
	DECLARE @inserr INT;  
	DECLARE @upderr INT;  
	DECLARE @maxerr INT;  
	DECLARE @CurrencyCode NVARCHAR(50) = NULL;

	PRINT @SQLString;
	INSERT INTO @ProfitLossActual
	EXEC sys.sp_executesql @SQLString;
	
	DECLARE cursor_actual CURSOR
	FOR SELECT 
			[ID],
			[Type],
			[KpiValue],
			[Month],
			[Year],
			[Quarter]
	FROM @ProfitLossActual;
 
	OPEN cursor_actual;
 
	FETCH NEXT FROM cursor_actual INTO 
	   @ID,	   
	   @Type,
	   @KpiValue,
	   @Month,
	   @Year,
	   @Quarter
 
	WHILE @@FETCH_STATUS = 0
		BEGIN
			DECLARE @CashFlowValueID INT = 0;			
			DECLARE @CompanyCashFlowLineItemMappingID INT = 0;	
			DECLARE @CashFlowLineItemID INT = 0;					
			IF(@Month IS NOT NULL  AND @Month > 0)
				BEGIN
					SELECT @CashFlowValueID = [PLV].[CashFlowValueID]
					FROM [dbo].[M_CashFlow_LineItems] PLL 
					INNER JOIN [dbo].[Mapping_CompanyCashFlowLineItems] MCPL ON [MCPL].[CashFlowLineItemID] = [PLL].[CashFlowLineItemID] AND [MCPL].[PortfolioCompanyID] = @PortfolioCompanyID
					INNER JOIN [dbo].[CashFlowValues] PLV ON [PLV].[CompanyCashFlowLineItemMappingID] = [MCPL].[CompanyCashFlowLineItemMappingID] AND [PLV].[Month] = @Month AND [PLV].[Year] = @Year
					WHERE [PLL].CashFlowLineItemID = @ID AND [PLL].[IsDeleted] = 0 AND [MCPL].[IsDeleted] = 0 AND [PLV].[IsDeleted] = 0;
				END
			ELSE IF(@Quarter IS NOT NULL)
				BEGIN
					SELECT @CashFlowValueID = [PLV].[CashFlowValueID]
					FROM [dbo].[M_CashFlow_LineItems] PLL 
					INNER JOIN [dbo].[Mapping_CompanyCashFlowLineItems] MCPL ON [MCPL].[CashFlowLineItemID] = [PLL].[CashFlowLineItemID] AND [MCPL].[PortfolioCompanyID] = @PortfolioCompanyID
					INNER JOIN [dbo].[CashFlowValues] PLV ON [PLV].[CompanyCashFlowLineItemMappingID] = [MCPL].[CompanyCashFlowLineItemMappingID] AND [PLV].[Quarter] = @Quarter AND [PLV].[Year] = @Year
					WHERE [PLL].CashFlowLineItemID = @ID AND [PLL].[IsDeleted] = 0 AND [MCPL].[IsDeleted] = 0 AND [PLV].[IsDeleted] = 0;
				END
			ELSE IF(@Quarter IS NULL and (@Month IS NULL or @Month = 0))
				BEGIN
					SELECT @CashFlowValueID = [PLV].[CashFlowValueID]
					FROM [dbo].[M_CashFlow_LineItems] PLL 
					INNER JOIN [dbo].[Mapping_CompanyCashFlowLineItems] MCPL ON [MCPL].[CashFlowLineItemID] = [PLL].[CashFlowLineItemID] AND [MCPL].[PortfolioCompanyID] = @PortfolioCompanyID
					INNER JOIN [dbo].[CashFlowValues] PLV ON [PLV].[CompanyCashFlowLineItemMappingID] = [MCPL].[CompanyCashFlowLineItemMappingID] AND ([PLV].[Month] IS NULL OR [PLV].[Month] = 0)  AND [PLV].[Quarter] is null AND [PLV].[Year] = @Year
					WHERE [PLL].CashFlowLineItemID = @ID AND [PLL].[IsDeleted] = 0 AND [MCPL].[IsDeleted] = 0 AND [PLV].[IsDeleted] = 0;
				END
			SELECT @CompanyCashFlowLineItemMappingID = [MCPL].[CompanyCashFlowLineItemMappingID],@CurrencyCode = KPIInfo
			FROM [dbo].[M_CashFlow_LineItems] PLL 
			INNER JOIN [dbo].[Mapping_CompanyCashFlowLineItems] MCPL ON [MCPL].[CashFlowLineItemID] = [PLL].[CashFlowLineItemID] AND [MCPL].[PortfolioCompanyID] = @PortfolioCompanyID		
			WHERE [PLL].CashFlowLineItemID = @ID AND [PLL].[IsDeleted] = 0 AND [MCPL].[IsDeleted] = 0;

			---Replace -1 with Null - Included NULL value as -1 in unpivot
			IF (LTRIM(RTRIM(@KpiValue)) ='0.00000000')
			BEGIN
				SET @KpiValue = NULL;
			END			
			
			BEGIN TRANSACTION [Tran_Actual]
			--IF(@KpiValue IS NOT NULL)
			--BEGIN
				IF(@CashFlowValueID = 0)
				BEGIN				
					IF(@CompanyCashFlowLineItemMappingID = 0)
					BEGIN				
						-- Save error number returned from Insert statement  
						SET @inserr = @@error  
						IF @inserr > @maxerr
						SET @maxerr = @inserr 
					END
					ELSE IF(@CompanyCashFlowLineItemMappingID > 0)
					BEGIN	
					DECLARE @CopySupportingDocumentsId Varchar(MAX)=NULL, @CopyCommentId Varchar(MAX)=NULL, @CopyDocumentId Varchar(MAX)=NULL;
					SET @CopySupportingDocumentsId = 
					    (CASE 
					        WHEN @KpiValue IS NULL THEN NULL
					        ELSE @SupportingDocumentsId
					     END);

					SET @CopyCommentId = 
					    (CASE 
					        WHEN @KpiValue IS NULL THEN NULL
					        ELSE @CommentId
					     END);

					SET @CopyDocumentId = 
					    (CASE 
					        WHEN @KpiValue IS NULL THEN NULL
					        ELSE @DocumentId
					     END);
						If (@Type =4)
						BEGIN
						INSERT INTO [dbo].[CashFlowValues] ([CompanyCashFlowLineItemMappingID],[ActualValue],[ValueInfo],[Month],[Year],[CreatedOn],[CreatedBy],IsDeleted,IsActive,[Quarter],DocumentId,SupportingDocumentsId,CommentId)  
						VALUES(@CompanyCashFlowLineItemMappingID, @KpiValue, @CurrencyCode, @Month, @Year, GETDATE(), @UserID,0,1,@Quarter,@CopyDocumentId,@CopySupportingDocumentsId,@CopyCommentId);
						SELECT @CashFlowValueID = @@IDENTITY;
						---First entry for audit log
						IF @KPIValue IS NOT NULL
						exec AddFinancialAuditLogDetails 9,@CashFlowValueID,null,@KpiValue,'actual',@UserID,@PortfolioCompanyID,null,@CurrencyCode,@CopyDocumentId,@CopySupportingDocumentsId,@CopyCommentId,@DataIngestion;
						END
						If (@Type =5)
						BEGIN
						INSERT INTO [dbo].[CashFlowValues] ([CompanyCashFlowLineItemMappingID],[BudgetValue],[ValueInfo],[Month],[Year],[CreatedOn],[CreatedBy],IsDeleted,IsActive,[Quarter],BudgetDocumentId,BudgetSupportingDocumentsId,BudgetCommentId)  
						VALUES(@CompanyCashFlowLineItemMappingID, @KpiValue, @CurrencyCode, @Month, @Year, GETDATE(), @UserID,0,1,@Quarter,@CopyDocumentId,@CopySupportingDocumentsId,@CopyCommentId);
						SELECT @CashFlowValueID = @@IDENTITY;
						---First entry for audit log
						IF @KPIValue IS NOT NULL
						exec AddFinancialAuditLogDetails 9,@CashFlowValueID,null,@KpiValue,'budget',@UserID,@PortfolioCompanyID,null,@CurrencyCode,@CopyDocumentId,@CopySupportingDocumentsId,@CopyCommentId,@DataIngestion;
						END
						-- Save error number returned from Insert statement  
						SET @inserr = @@error  
						IF @inserr > @maxerr  
						SET @maxerr = @inserr 
					END
				END
				ELSE IF(@CashFlowValueID > 0 AND @KpiValue IS NOT NULL)
				BEGIN		
					If (@Type =4)
					BEGIN
						DECLARE @oldKpiValue NVARCHAR(MAX),@oldCurrencyCode NVARCHAR(50);
						SELECT @oldKpiValue = ActualValue, @oldCurrencyCode = ValueInfo
						FROM CashFlowValues
						WHERE CashFlowValueID = @CashFlowValueID;						
						exec AddFinancialAuditLogDetails 9,@CashFlowValueID,@oldKpiValue,@KpiValue,'actual',@UserID,@PortfolioCompanyID,@oldCurrencyCode,@CurrencyCode,@DocumentId,@SupportingDocumentsId,@CommentId,@DataIngestion;						
						UPDATE CashFlowValues SET [ActualValue] = CASE WHEN @KpiValue Is NULL THEN [ActualValue] ELSE @KpiValue END, [ModifiedOn] = GETDATE(), [ModifiedBy]= @UserID,[DocumentId] = @DocumentId,[SupportingDocumentsId] = @SupportingDocumentsId,[CommentId] =@CommentId  WHERE CashFlowValueID = @CashFlowValueID;	 
					END
					If (@Type =5)
					BEGIN
						DECLARE @oldKpiBudgetValue NVARCHAR(MAX),@oldCurrency NVARCHAR(50);
						SELECT @oldKpiBudgetValue = BudgetValue, @oldCurrency = ValueInfo
						FROM CashFlowValues
						WHERE CashFlowValueID = @CashFlowValueID;
						exec AddFinancialAuditLogDetails 9,@CashFlowValueID,@oldKpiBudgetValue,@KpiValue,'budget',@UserID,@PortfolioCompanyID,@oldCurrency,@CurrencyCode,@DocumentId,@SupportingDocumentsId,@CommentId,@DataIngestion;							
						UPDATE CashFlowValues SET [BudgetValue] = CASE WHEN @KpiValue Is NULL THEN [BudgetValue] ELSE @KpiValue END, [ModifiedOn] = GETDATE(), [ModifiedBy]= @UserID,[BudgetDocumentId] = @DocumentId,[BudgetSupportingDocumentsId] = @SupportingDocumentsId,[BudgetCommentId] =@CommentId  WHERE CashFlowValueID = @CashFlowValueID; 			
					  END
					-- Save error number returned from Update statement  
					SET @upderr = @@error  
					IF @upderr > @maxerr  
					SET @maxerr = @upderr
				END
			--END
			-- If an error occurred, roll back  
			IF @maxerr <> 0  
			BEGIN  
				ROLLBACK TRANSACTION [Tran_Actual];				 
			END  
			ELSE  
			BEGIN  
				COMMIT TRANSACTION [Tran_Actual];							  
			END  

			FETCH NEXT FROM cursor_actual INTO 
				@ID,
				@Type,
				@KpiValue,
				@Month,
				@Year,
				@Quarter
		END;
 
		CLOSE cursor_actual; 
		DEALLOCATE cursor_actual;
		SET NOCOUNT OFF;
END
GO
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[spBulkUploadBalanceSheetICCase]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[spBulkUploadBalanceSheetICCase]
GO
CREATE PROCEDURE [dbo].[spBulkUploadBalanceSheetICCase]
           @SQLString NVARCHAR(MAX)
		  ,@UserID INT
		  ,@PortfolioCompanyID INT		 
		  ,@DocumentId BIGINT = NULL
		  ,@SupportingDocumentsId NVARCHAR(MAX) = NULL
		  ,@CommentId BIGINT = NULL
		  ,@DataIngestion BIT=0
AS
BEGIN
	SET NOCOUNT ON;
	SET ANSI_WARNINGS OFF;
	
	DECLARE @ICCase TABLE([ID] INT,[ActualValue] NVARCHAR(MAX),[Year] INT);
	DECLARE @ID INT,@ActualValue NVARCHAR(MAX),@Year INT;		
	DECLARE @inserr INT;  
	DECLARE @upderr INT;  
	DECLARE @maxerr INT;
	DECLARE @CurrencyCode NVARCHAR(50) = NULL;

	--PRINT @SQLString;
	INSERT INTO @ICCase
	EXEC sys.sp_executesql @SQLString;
	---Get Currency Code based on PortfolioCompanyID
	--SELECT @CurrencyCode = dbo.GetCurrencyCode(@PortfolioCompanyID);	

	DECLARE cursor_iccase CURSOR
	FOR SELECT 
			[ID],
			[ActualValue],			
			[Year]
	FROM @ICCase;
 
	OPEN cursor_iccase;
 
	FETCH NEXT FROM cursor_iccase INTO 
	   @ID,
	   @ActualValue,
	   @Year
	WHILE @@FETCH_STATUS = 0
		BEGIN
			DECLARE @BalanceSheetICCaseValueID INT = 0;
			DECLARE @CompanyBalanceSheetLineItemMappingID INT = 0;
			DECLARE @BalanceSheetLineItemID INT = 0;
		DECLARE @KPIInfo VARCHAR(20) = NULL;

			SELECT @BalanceSheetICCaseValueID = [BLV].[BalanceSheetICCaseValueID]
			FROM [dbo].[M_BalanceSheet_LineItems] BLL 
			INNER JOIN [dbo].[Mapping_CompanyBalanceSheetLineItems] MCPL ON [MCPL].[BalanceSheetLineItemID] = [BLL].[BalanceSheetLineItemID] AND [MCPL].[PortfolioCompanyID] = @PortfolioCompanyID
			INNER JOIN [dbo].[BalanceSheetICCaseValues] BLV ON [BLV].[CompanyBalanceSheetLineItemMappingID] = [MCPL].[CompanyBalanceSheetLineItemMappingID] AND [BLV].[Year] = @Year
			WHERE [BLL].[BalanceSheetLineItemID] = @ID AND [MCPL].[IsDeleted] = 0 AND [BLL].[IsDeleted] = 0 AND [BLV].[IsDeleted] = 0;

			SELECT @CompanyBalanceSheetLineItemMappingID = [MCBL].[CompanyBalanceSheetLineItemMappingID]
			FROM [dbo].[M_BalanceSheet_LineItems] BLL 
			INNER JOIN [dbo].[Mapping_CompanyBalanceSheetLineItems] MCBL ON [MCBL].[BalanceSheetLineItemID] = [BLL].[BalanceSheetLineItemID] AND [MCBL].[PortfolioCompanyID] = @PortfolioCompanyID		
			WHERE [BLL].[BalanceSheetLineItemID] = @ID AND [MCBL].[IsDeleted] = 0 AND [BLL].[IsDeleted] = 0;
			
			IF (LTRIM(RTRIM(@ActualValue)) = '0.00000000')
			BEGIN
				SET @ActualValue = NULL;
			END		
			
			IF (@ActualValue IS NOT NULL)
			BEGIN
				IF (LOWER(@ActualValue) = 'n.a.' OR LOWER(@ActualValue) = 'na.' OR LOWER(@ActualValue) = 'na' OR LOWER(@ActualValue) = 'n.a')
				BEGIN
					SET @ActualValue = NULL;
				END
			END	
	
			Select @KPIInfo = dbo.GetKPIInfo(@ID,'BS');
			IF(LTRIM(RTRIM(@KPIInfo)) = '$')
			BEGIN
				SELECT @CurrencyCode = dbo.GetCurrencyCode(@PortfolioCompanyID);		
			END
			ELSE
			BEGIN
				SET @CurrencyCode = @KPIInfo;
			END	
			BEGIN TRANSACTION [Tran_ICCase]		
				IF(@BalanceSheetICCaseValueID = 0)
				BEGIN	
				DECLARE @CopySupportingDocumentsId Varchar(MAX)=NULL, @CopyCommentId Varchar(MAX)=NULL, @CopyDocumentId Varchar(MAX)=NULL;
						SET @CopySupportingDocumentsId = 
					    (CASE 
					        WHEN @ActualValue IS NULL THEN NULL
					        ELSE @SupportingDocumentsId
					     END);
					
					SET @CopyCommentId = 
					    (CASE 
					        WHEN @ActualValue IS NULL THEN NULL
					        ELSE @CommentId
					     END);
					
					SET @CopyDocumentId = 
					    (CASE 
					        WHEN @ActualValue IS NULL THEN NULL
					        ELSE @DocumentId
					     END);
				IF(@CompanyBalanceSheetLineItemMappingID = 0)
					BEGIN	
						SELECT @BalanceSheetLineItemID = [BalanceSheetLineItemID] FROM [dbo].[M_BalanceSheet_LineItems] WHERE [BalanceSheetLineItemID] = @ID;
						SELECT @CompanyBalanceSheetLineItemMappingID = [CompanyBalanceSheetLineItemMappingID] FROM [dbo].[Mapping_CompanyBalanceSheetLineItems] WHERE [PortfolioCompanyID] = @PortfolioCompanyID AND [BalanceSheetLineItemID] = @BalanceSheetLineItemID;						
						INSERT INTO [dbo].[BalanceSheetICCaseValues] ([CompanyBalanceSheetLineItemMappingID],[BaseValue],[UpsideValue],[DownValue],[ValueInfo],[Year],[CreatedOn],[CreatedBy],[IsDeleted],[IsActive],DocumentId,SupportingDocumentsId,CommentId)  
						VALUES(@CompanyBalanceSheetLineItemMappingID, @ActualValue, NULL, NULL, @CurrencyCode, @Year, GETDATE(), @UserID,0,1,@CopyDocumentId,@CopySupportingDocumentsId,@CopyCommentId);
						-- Save error number returned from Insert statement  
						SELECT @BalanceSheetICCaseValueID = @@IDENTITY;
						---First entry for audit log
						exec AddFinancialAuditLogDetails 8,@BalanceSheetICCaseValueID,null,@ActualValue,'ic',@UserID,@PortfolioCompanyID,null,@CurrencyCode,@CopyDocumentId,@CopySupportingDocumentsId,@CopyCommentId,@DataIngestion;
						SET @inserr = @@error  
						IF @inserr > @maxerr
						SET @maxerr = @inserr 
					END
					ELSE IF(@CompanyBalanceSheetLineItemMappingID > 0)
					BEGIN	
						INSERT INTO [dbo].[BalanceSheetICCaseValues] ([CompanyBalanceSheetLineItemMappingID],[BaseValue],[UpsideValue],[DownValue],[ValueInfo],[Year],[CreatedOn],[CreatedBy],[IsDeleted],[IsActive],DocumentId,SupportingDocumentsId,CommentId)  
						VALUES(@CompanyBalanceSheetLineItemMappingID, @ActualValue, NULL, NULL, @CurrencyCode, @Year, GETDATE(), @UserID,0,1,@CopyDocumentId,@CopySupportingDocumentsId,@CopyCommentId);
						SELECT @BalanceSheetICCaseValueID = @@IDENTITY;
						---First entry for audit log
						exec AddFinancialAuditLogDetails 8,@BalanceSheetICCaseValueID,null,@ActualValue,'ic',@UserID,@PortfolioCompanyID,null,@CurrencyCode,@CopyDocumentId,@CopySupportingDocumentsId,@CopyCommentId,@DataIngestion;
						-- Save error number returned from Insert statement  
						SET @inserr = @@error  
						IF @inserr > @maxerr  
						SET @maxerr = @inserr 
					END					
				END
				ELSE IF(@BalanceSheetICCaseValueID > 0 AND @ActualValue IS NOT NULL)
				BEGIN	
						DECLARE @oldKpiValue NVARCHAR(MAX),@oldCurrencyCode NVARCHAR(50);
						SELECT @oldKpiValue = BaseValue, @oldCurrencyCode = ValueInfo
						FROM BalanceSheetICCaseValues
						WHERE BalanceSheetICCaseValueID = @BalanceSheetICCaseValueID;
						
								exec AddFinancialAuditLogDetails 8,@BalanceSheetICCaseValueID,@oldKpiValue,@ActualValue,'ic',@UserID,@PortfolioCompanyID,@oldCurrencyCode,@CurrencyCode,@DocumentId,@SupportingDocumentsId,@CommentId,@DataIngestion;
								
								UPDATE [dbo].[BalanceSheetICCaseValues] SET [BaseValue] = CASE WHEN @ActualValue IS NOT NULL THEN @ActualValue ELSE [BaseValue] END, [UpsideValue]  = NULL,[DownValue] =NULL, [ModifiedOn] = GETDATE(), [ModifiedBy]= @UserID ,[DocumentId] = @DocumentId,[SupportingDocumentsId] = @SupportingDocumentsId,[CommentId] =@CommentId 
								WHERE [BalanceSheetICCaseValueID] = @BalanceSheetICCaseValueID; 
							
					-- Save error number returned from Update statement  
					SET @upderr = @@error  
					IF @upderr > @maxerr  
					SET @maxerr = @upderr
				END			
			-- If an error occurred, roll back  
			IF @maxerr <> 0  
			BEGIN  
				ROLLBACK TRANSACTION [Tran_ICCase];
			END  
			ELSE  
			BEGIN  
				COMMIT TRANSACTION [Tran_ICCase];							 
			END  

			FETCH NEXT FROM cursor_iccase INTO @ID,@ActualValue,@Year;
						   
		END;
 
		CLOSE cursor_iccase; 
		DEALLOCATE cursor_iccase;
		SET NOCOUNT OFF;
END
GO
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[spBulkUploadBalanceSheetForecast]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[spBulkUploadBalanceSheetForecast]
GO
CREATE PROCEDURE [dbo].[spBulkUploadBalanceSheetForecast]
          @SQLString NVARCHAR(MAX)
		  ,@UserID INT
		  ,@PortfolioCompanyID INT		 
		  ,@DocumentId BIGINT = NULL
		  ,@SupportingDocumentsId NVARCHAR(MAX) = NULL
		  ,@CommentId BIGINT = NULL
		  ,@DataIngestion BIT=0
AS
BEGIN
	SET NOCOUNT ON;
	DECLARE @BalanceSheetForecast TABLE([ID] INT,[ActualValue] NVARCHAR(max),[Year] VARCHAR(100),[Quarter] VARCHAR(10),[Month] INT);
	DECLARE @ID INT,@ActualValue NVARCHAR(max),@Year INT,@Quarter VARCHAR(10),@Month INT = NULL;
	DECLARE @inserr INT;  
	DECLARE @upderr INT;  
	DECLARE @maxerr INT;  
	DECLARE @CurrencyCode NVARCHAR(50) = NULL;

	--PRINT @SQLString;
	INSERT INTO @BalanceSheetForecast
	EXEC sys.sp_executesql @SQLString;

	---Get Currency Code based on PortfolioCompanyID
	--SELECT @CurrencyCode = dbo.GetCurrencyCode(@PortfolioCompanyID);		

	DECLARE cursor_Forecast CURSOR
	FOR SELECT 
			[ID],
			[ActualValue],			
			[Year],
			[Quarter],
			[Month]
	FROM @BalanceSheetForecast;
 
	OPEN cursor_Forecast;
 
	FETCH NEXT FROM cursor_Forecast INTO @ID,@ActualValue,@Year,@Quarter,@Month;
 
	WHILE @@FETCH_STATUS = 0
		BEGIN
			DECLARE @BalanceSheetForecastValueID INT = 0;
			DECLARE @CompanyBalanceSheetLineItemMappingID INT = 0;
			DECLARE @BalanceSheetLineItemID INT = 0;
		DECLARE @KPIInfo VARCHAR(20) = NULL;
			IF(@Quarter IS NULL AND (@Month IS NULL or @Month = 0))
				BEGIN
					SELECT @BalanceSheetForecastValueID = [BLV].[BalanceSheetForecastValueID]
					FROM [dbo].[M_BalanceSheet_LineItems] BLL 
					INNER JOIN [dbo].[Mapping_CompanyBalanceSheetLineItems] MCPL ON [MCPL].[BalanceSheetLineItemID] = [BLL].[BalanceSheetLineItemID] AND [MCPL].[PortfolioCompanyID] = @PortfolioCompanyID
					INNER JOIN [dbo].[BalanceSheet_ForecastData] BLV ON [BLV].[CompanyBalanceSheetLineItemMappingID] = [MCPL].[CompanyBalanceSheetLineItemMappingID] AND [BLV].[Year] = @Year AND ([BLV].[Month] IS NULL OR [BLV].[Month] = 0)  AND [BLV].[Quarter] IS NULL
					WHERE [BLL].[BalanceSheetLineItemID] = @ID AND [MCPL].[IsDeleted] = 0 AND [BLL].[IsDeleted] = 0 AND [BLV].[IsDeleted] = 0;
				END
			IF(@Quarter IS NOT NULL AND @Year IS NOT NULL)
				BEGIN
					SELECT @BalanceSheetForecastValueID = [BLV].[BalanceSheetForecastValueID]
					FROM [dbo].[M_BalanceSheet_LineItems] BLL 
					INNER JOIN [dbo].[Mapping_CompanyBalanceSheetLineItems] MCPL ON [MCPL].[BalanceSheetLineItemID] = [BLL].[BalanceSheetLineItemID] AND [MCPL].[PortfolioCompanyID] = @PortfolioCompanyID
					INNER JOIN [dbo].[BalanceSheet_ForecastData] BLV ON [BLV].[CompanyBalanceSheetLineItemMappingID] = [MCPL].[CompanyBalanceSheetLineItemMappingID] AND [BLV].[Year] = @Year AND [BLV].[Quarter] =@Quarter
					WHERE [BLL].[BalanceSheetLineItemID] = @ID AND [MCPL].[IsDeleted] = 0 AND [BLL].[IsDeleted] = 0 AND [BLV].[IsDeleted] = 0;
				END
			IF(@Month IS NOT NULL AND @Month > 0 AND @Year IS NOT NULL)
				BEGIN
					SELECT @BalanceSheetForecastValueID = [BLV].[BalanceSheetForecastValueID]
					FROM [dbo].[M_BalanceSheet_LineItems] BLL 
					INNER JOIN [dbo].[Mapping_CompanyBalanceSheetLineItems] MCPL ON [MCPL].[BalanceSheetLineItemID] = [BLL].[BalanceSheetLineItemID] AND [MCPL].[PortfolioCompanyID] = @PortfolioCompanyID
					INNER JOIN [dbo].[BalanceSheet_ForecastData] BLV ON [BLV].[CompanyBalanceSheetLineItemMappingID] = [MCPL].[CompanyBalanceSheetLineItemMappingID] AND [BLV].[Year] = @Year AND [BLV].[Month] =@Month
					WHERE [BLL].[BalanceSheetLineItemID] = @ID AND [MCPL].[IsDeleted] = 0 AND [BLL].[IsDeleted] = 0 AND [BLV].[IsDeleted] = 0;
				END

			SELECT @CompanyBalanceSheetLineItemMappingID = [MCBL].[CompanyBalanceSheetLineItemMappingID]
			FROM [dbo].[M_BalanceSheet_LineItems] BLL 
			INNER JOIN [dbo].[Mapping_CompanyBalanceSheetLineItems] MCBL ON [MCBL].[BalanceSheetLineItemID] = [BLL].[BalanceSheetLineItemID] AND [MCBL].[PortfolioCompanyID] = @PortfolioCompanyID		
			WHERE [BLL].[BalanceSheetLineItemID] = @ID AND [MCBL].[IsDeleted] = 0 AND [BLL].[IsDeleted] = 0;
			
			---Replace -1 with Null - Included NULL value as -1 in unpivot
			IF (LTRIM(RTRIM(@ActualValue))='0.00000000')
			BEGIN
				SET @ActualValue = NULL;
			END	

			Select @KPIInfo = dbo.GetKPIInfo(@ID,'BS');
			IF(LTRIM(RTRIM(@KPIInfo)) = '$')
			BEGIN
				SELECT @CurrencyCode = dbo.GetCurrencyCode(@PortfolioCompanyID);		
			END
			ELSE
			BEGIN
				SET @CurrencyCode = @KPIInfo;
			END	

			BEGIN TRANSACTION [Tran_Forecast]
			IF(@BalanceSheetForecastValueID = 0)
			BEGIN	
			DECLARE @CopySupportingDocumentsId Varchar(MAX)=NULL, @CopyCommentId Varchar(MAX)=NULL, @CopyDocumentId Varchar(MAX)=NULL;
					SET @CopySupportingDocumentsId = 
					    (CASE 
					        WHEN @ActualValue IS NULL THEN NULL
					        ELSE @SupportingDocumentsId
					     END);
					
					SET @CopyCommentId = 
					    (CASE 
					        WHEN @ActualValue IS NULL THEN NULL
					        ELSE @CommentId
					     END);
					
					SET @CopyDocumentId = 
					    (CASE 
					        WHEN @ActualValue IS NULL THEN NULL
					        ELSE @DocumentId
					     END);
					IF(@CompanyBalanceSheetLineItemMappingID = 0)
					BEGIN					
						SELECT @BalanceSheetLineItemID = [BalanceSheetLineItemID] FROM [dbo].[M_BalanceSheet_LineItems] WHERE [BalanceSheetLineItemID] = @ID;

						SELECT @CompanyBalanceSheetLineItemMappingID = [CompanyBalanceSheetLineItemMappingID] FROM [dbo].[Mapping_CompanyBalanceSheetLineItems] WHERE [PortfolioCompanyID] = @PortfolioCompanyID AND [BalanceSheetLineItemID] = @BalanceSheetLineItemID;						
						INSERT INTO [dbo].[BalanceSheet_ForecastData] ([CompanyBalanceSheetLineItemMappingID],[ActualValue],[ActualValueInfo],[Year],[CreatedOn],[CreatedBy],[IsDeleted],[IsActive],[Quarter],[Month],DocumentId,SupportingDocumentsId,CommentId)  
						VALUES(@CompanyBalanceSheetLineItemMappingID, @ActualValue, @CurrencyCode, @Year, GETDATE(), @UserID,0,1,@Quarter,@Month,@CopyDocumentId,@CopySupportingDocumentsId,@CopyCommentId);
						-- Save error number returned from Insert statement 
						SELECT @BalanceSheetForecastValueID = @@IDENTITY;
						---First entry for audit log
						exec AddFinancialAuditLogDetails 8,@BalanceSheetForecastValueID,null,@ActualValue,'forecast',@UserID,@PortfolioCompanyID,null,@CurrencyCode,@CopyDocumentId,@CopySupportingDocumentsId,@CopyCommentId,@DataIngestion;
						SET @inserr = @@error  
						IF @inserr > @maxerr
						SET @maxerr = @inserr 
					END
					ELSE IF(@CompanyBalanceSheetLineItemMappingID > 0)
					BEGIN	
						INSERT INTO [dbo].[BalanceSheet_ForecastData] ([CompanyBalanceSheetLineItemMappingID],[ActualValue],[ActualValueInfo],[Year],[CreatedOn],[CreatedBy],[IsDeleted],[IsActive],[Quarter],[Month],DocumentId,SupportingDocumentsId,CommentId)  
						VALUES(@CompanyBalanceSheetLineItemMappingID, @ActualValue, @CurrencyCode, @Year, GETDATE(), @UserID,0,1,@Quarter,@Month,@CopyDocumentId,@CopySupportingDocumentsId,@CopyCommentId);
						-- Save error number returned from Insert statement  
						SELECT @BalanceSheetForecastValueID = @@IDENTITY;
						---First entry for audit log
						exec AddFinancialAuditLogDetails 8,@BalanceSheetForecastValueID,null,@ActualValue,'forecast',@UserID,@PortfolioCompanyID,null,@CurrencyCode,@CopyDocumentId,@CopySupportingDocumentsId,@CopyCommentId,@DataIngestion;
						SET @inserr = @@error  
						IF @inserr > @maxerr  
						SET @maxerr = @inserr 
					END									
			END
			ELSE IF(@BalanceSheetForecastValueID > 0 AND @ActualValue IS NOT NULL)
			BEGIN	
			DECLARE @oldKpiValue NVARCHAR(MAX),@oldCurrencyCode NVARCHAR(50);
						SELECT @oldKpiValue = ActualValue, @oldCurrencyCode = ActualValueInfo
						FROM BalanceSheet_ForecastData
						WHERE BalanceSheetForecastValueID = @BalanceSheetForecastValueID;
						
						exec AddFinancialAuditLogDetails 8,@BalanceSheetForecastValueID,@oldKpiValue,@ActualValue,'forecast',@UserID,@PortfolioCompanyID,@oldCurrencyCode,@CurrencyCode,@DocumentId,@SupportingDocumentsId,@CommentId,@DataIngestion;
												
						UPDATE [dbo].[BalanceSheet_ForecastData] SET [ActualValue] = CASE WHEN @ActualValue IS NOT NULL THEN @ActualValue ELSE [ActualValue] END, [ModifiedOn] = GETDATE(), [ModifiedBy]= @UserID,[DocumentId] = @DocumentId,[SupportingDocumentsId] = @SupportingDocumentsId,[CommentId] =@CommentId  
						WHERE [BalanceSheetForecastValueID] = @BalanceSheetForecastValueID; 
										
				-- Save error number returned from Update statement  
				SET @upderr = @@error  
				IF @upderr > @maxerr  
				SET @maxerr = @upderr
			END

			-- If an error occurred, roll back  
			IF @maxerr <> 0  
			BEGIN  
				ROLLBACK TRANSACTION [Tran_Forecast];
			END  
			ELSE  
			BEGIN  
				COMMIT TRANSACTION [Tran_Forecast];							 
			END  

			FETCH NEXT FROM cursor_Forecast INTO @ID,@ActualValue,@Year,@Quarter,@Month;
						   
		END;
 
		CLOSE cursor_Forecast; 
		DEALLOCATE cursor_Forecast;
		SET NOCOUNT OFF;
		SET ANSI_WARNINGS ON;
END
GO
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[spBulkUploadBalanceSheetActual]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[spBulkUploadBalanceSheetActual]
GO
CREATE PROCEDURE [dbo].[spBulkUploadBalanceSheetActual]
          @SQLString NVARCHAR(MAX)
		  ,@UserID INT
		  ,@PortfolioCompanyID INT	
		  ,@DocumentId BIGINT = NULL
		  ,@SupportingDocumentsId NVARCHAR(MAX) = NULL
		  ,@CommentId BIGINT = NULL
		  ,@DataIngestion BIT=0
AS
BEGIN
	SET NOCOUNT ON;
	DECLARE @BalanceSheetActual TABLE([ID] INT,[Type] INT,[KpiValue] NVARCHAR(max),[Month] INT,[Year] VARCHAR(100),[Quarter] VARCHAR(10));
	DECLARE @ID INT, @KpiValue NVARCHAR(max),@Month INT,@Year INT,@Type INT,@Quarter VARCHAR(10);	
	DECLARE @inserr INT;  
	DECLARE @upderr INT;  
	DECLARE @maxerr INT; 
	DECLARE @CurrencyCode NVARCHAR(50) = NULL;

	--PRINT @SQLString;
	INSERT INTO @BalanceSheetActual
	EXEC sys.sp_executesql @SQLString;	

	DECLARE cursor_actual CURSOR
	FOR SELECT 
			[ID],
			[Type],
			[KpiValue],
			[Month],
			[Year],
			[Quarter]
		FROM @BalanceSheetActual;
 
	OPEN cursor_actual;
 
	FETCH NEXT FROM cursor_actual INTO 
	   @ID,	   
	   @Type,
	   @KpiValue,
	   @Month,
	   @Year,
	   @Quarter
	WHILE @@FETCH_STATUS = 0
		BEGIN
			DECLARE @BalanceSheetValueID INT = 0;
			DECLARE @CompanyBalanceSheetLineItemMappingID INT = 0;
			DECLARE @BalanceSheetLineItemID INT = 0;
			DECLARE @KPIInfo VARCHAR(20) = NULL;
			IF(@Month IS NOT NULL AND @Month > 0)
				BEGIN
					SELECT @BalanceSheetValueID = BLV.[BalanceSheetValueID]
					FROM [dbo].[M_BalanceSheet_LineItems] BLL 
					INNER JOIN [dbo].[Mapping_CompanyBalanceSheetLineItems] MCBL ON [MCBL].[BalanceSheetLineItemID] = [BLL].[BalanceSheetLineItemID] AND MCBL.[PortfolioCompanyID] = @PortfolioCompanyID
					INNER JOIN [dbo].[BalanceSheetValues] BLV ON BLV.[CompanyBalanceSheetLineItemMappingID] = [MCBL].[CompanyBalanceSheetLineItemMappingID] AND BLV.[Month] = @Month AND BLV.[Year] = @Year
					WHERE [BLL].[BalanceSheetLineItemID] = @ID AND [MCBL].[IsDeleted] = 0 AND [BLL].[IsDeleted] = 0 AND [BLV].[IsDeleted] = 0;
				END
			ELSE IF(@Quarter IS NOT NULL)
				BEGIN
					SELECT @BalanceSheetValueID = BLV.[BalanceSheetValueID]
					FROM [dbo].[M_BalanceSheet_LineItems] BLL 
					INNER JOIN [dbo].[Mapping_CompanyBalanceSheetLineItems] MCBL ON [MCBL].[BalanceSheetLineItemID] = [BLL].[BalanceSheetLineItemID] AND MCBL.[PortfolioCompanyID] = @PortfolioCompanyID
					INNER JOIN [dbo].[BalanceSheetValues] BLV ON BLV.[CompanyBalanceSheetLineItemMappingID] = [MCBL].[CompanyBalanceSheetLineItemMappingID] AND BLV.[Quarter] = @Quarter AND BLV.[Year] = @Year
					WHERE [BLL].[BalanceSheetLineItemID] = @ID AND [MCBL].[IsDeleted] = 0 AND [BLL].[IsDeleted] = 0 AND [BLV].[IsDeleted] = 0;
				END
			Else IF(@Quarter IS NULL and (@Month IS NULL or @Month = 0))
				BEGIN
					SELECT @BalanceSheetValueID = BLV.[BalanceSheetValueID]
					FROM [dbo].[M_BalanceSheet_LineItems] BLL 
					INNER JOIN [dbo].[Mapping_CompanyBalanceSheetLineItems] MCBL ON [MCBL].[BalanceSheetLineItemID] = [BLL].[BalanceSheetLineItemID] AND MCBL.[PortfolioCompanyID] = @PortfolioCompanyID
					INNER JOIN [dbo].[BalanceSheetValues] BLV ON BLV.[CompanyBalanceSheetLineItemMappingID] = [MCBL].[CompanyBalanceSheetLineItemMappingID] AND ([BLV].[Month] IS NULL OR [BLV].[Month] = 0) AND BLV.[Quarter] is null AND BLV.[Year] = @Year
					WHERE [BLL].[BalanceSheetLineItemID] = @ID AND [MCBL].[IsDeleted] = 0 AND [BLL].[IsDeleted] = 0 AND [BLV].[IsDeleted] = 0;
				END
			SELECT @CompanyBalanceSheetLineItemMappingID = [MCBL].[CompanyBalanceSheetLineItemMappingID]
			FROM [dbo].[M_BalanceSheet_LineItems] BLL 
			INNER JOIN [dbo].[Mapping_CompanyBalanceSheetLineItems] MCBL ON [MCBL].[BalanceSheetLineItemID] = [BLL].[BalanceSheetLineItemID] AND [MCBL].[PortfolioCompanyID] = @PortfolioCompanyID		
			WHERE [BLL].[BalanceSheetLineItemID] = @ID AND [MCBL].[IsDeleted] = 0 AND [BLL].[IsDeleted] = 0;

			---Replace -1 with Null - Included NULL value as -1 in unpivot
			IF (LTRIM(RTRIM(@KpiValue)) = '0.00000000')
			BEGIN
				SET @KpiValue = NULL;
			END			

			Select @KPIInfo = '$';--dbo.GetKPIInfo(@ID,'BS');
			IF(LTRIM(RTRIM(@KPIInfo)) = '$')
			BEGIN
				SET @CurrencyCode = @KPIInfo;	
			END
			ELSE
			BEGIN
				SET @CurrencyCode = @KPIInfo;
			END	

			BEGIN TRANSACTION [Tran_BalanceSheetActual]
				IF(@BalanceSheetValueID = 0)
				BEGIN
					IF(@CompanyBalanceSheetLineItemMappingID = 0)
					BEGIN		
						-- Save error number returned from Insert statement  
						SET @inserr = @@error  
						IF @inserr > @maxerr
						SET @maxerr = @inserr 
					END
					ELSE IF(@CompanyBalanceSheetLineItemMappingID > 0)
					BEGIN
					DECLARE @CopySupportingDocumentsId Varchar(MAX)=NULL, @CopyCommentId Varchar(MAX)=NULL, @CopyDocumentId Varchar(MAX)=NULL;
					SET @CopySupportingDocumentsId = 
					    (CASE 
					        WHEN @KpiValue IS NULL THEN NULL
					        ELSE @SupportingDocumentsId
					     END);
					
					SET @CopyCommentId = 
					    (CASE 
					        WHEN @KpiValue IS NULL THEN NULL
					        ELSE @CommentId
					     END);
					
					SET @CopyDocumentId = 
					    (CASE 
					        WHEN @KpiValue IS NULL THEN NULL
					        ELSE @DocumentId
					     END);
						If (@Type = 4)
						BEGIN
						INSERT INTO [dbo].[BalanceSheetValues] ([CompanyBalanceSheetLineItemMappingID],[ActualValue],[ValueInfo],[Month],[Year],[CreatedOn],[CreatedBy],IsDeleted,IsActive,Quarter,DocumentId,SupportingDocumentsId,CommentId)  
						VALUES(@CompanyBalanceSheetLineItemMappingID, @KpiValue, @CurrencyCode, @Month, @Year, GETDATE(), @UserID,0,1,@Quarter,@CopyDocumentId,@CopySupportingDocumentsId,@CopyCommentId);
						SELECT @BalanceSheetValueID = @@IDENTITY;
						---First entry for audit log
						exec AddFinancialAuditLogDetails 8,@BalanceSheetValueID,null,@KpiValue,'actual',@UserID,@PortfolioCompanyID,null,@CurrencyCode,@CopyDocumentId,@CopySupportingDocumentsId,@CopyCommentId,@DataIngestion;
						END
						If (@Type = 5)
						BEGIN
						INSERT INTO [dbo].[BalanceSheetValues] ([CompanyBalanceSheetLineItemMappingID],[BudgetValue],[ValueInfo],[Month],[Year],[CreatedOn],[CreatedBy],IsDeleted,IsActive,Quarter,BudgetDocumentId,BudgetSupportingDocumentsId,BudgetCommentId)  
						VALUES(@CompanyBalanceSheetLineItemMappingID, @KpiValue, @CurrencyCode, @Month, @Year, GETDATE(), @UserID,0,1,@Quarter,@CopyDocumentId,@CopySupportingDocumentsId,@CopyCommentId);
						SELECT @BalanceSheetValueID = @@IDENTITY;
						---First entry for audit log
						exec AddFinancialAuditLogDetails 8,@BalanceSheetValueID,null,@KpiValue,'budget',@UserID,@PortfolioCompanyID,null,@CurrencyCode,@CopyDocumentId,@CopySupportingDocumentsId,@CopyCommentId,@DataIngestion;
						END
						-- Save error number returned from Insert statement  
						SET @inserr = @@error  
						IF @inserr > @maxerr  
						SET @maxerr = @inserr 
					END				
				END
				ELSE IF(@BalanceSheetValueID > 0 AND @KpiValue IS NOT NULL)
				BEGIN			
					If (@Type = 4)
					BEGIN
						DECLARE @oldKPIValue NVARCHAR(MAX),@oldCurrencyCode NVARCHAR(50);
						SELECT @oldKPIValue = ActualValue, @oldCurrencyCode = ValueInfo
						FROM BalanceSheetValues
						WHERE BalanceSheetValueID = @BalanceSheetValueID;
						
							exec AddFinancialAuditLogDetails 8,@BalanceSheetValueID,@oldKPIValue,@KpiValue,'actual',@UserID,@PortfolioCompanyID,@oldCurrencyCode,@CurrencyCode,@DocumentId,@SupportingDocumentsId,@CommentId,@DataIngestion;
															
							UPDATE BalanceSheetValues SET [ActualValue] = CASE WHEN @KpiValue Is Null THEN [ActualValue] ELSE @KpiValue END, [ModifiedOn] = GETDATE(), [ModifiedBy]= @UserID,[DocumentId] = @DocumentId,[SupportingDocumentsId] = @SupportingDocumentsId,[CommentId] =@CommentId  WHERE BalanceSheetValueID = @BalanceSheetValueID;
						
					 
					END
					If (@Type = 5)
					BEGIN
					DECLARE @oldKpiBudgetValue NVARCHAR(MAX),@oldCurrency NVARCHAR(50);
						SELECT @oldKpiBudgetValue = BudgetValue,@oldCurrency = ValueInfo
						FROM BalanceSheetValues
						WHERE BalanceSheetValueID = @BalanceSheetValueID;
						
							exec AddFinancialAuditLogDetails 8,@BalanceSheetValueID,@oldKpiBudgetValue,@KpiValue,'budget',@UserID,@PortfolioCompanyID,@oldCurrency,@CurrencyCode,@DocumentId,@SupportingDocumentsId,@CommentId,@DataIngestion;
												
							UPDATE BalanceSheetValues SET [BudgetValue] = CASE WHEN @KpiValue Is Null THEN [BudgetValue] ELSE @KpiValue END, [ModifiedOn] = GETDATE(), [ModifiedBy]= @UserID,[BudgetDocumentId] = @DocumentId,[BudgetSupportingDocumentsId] = @SupportingDocumentsId,[BudgetCommentId] =@CommentId  WHERE BalanceSheetValueID = @BalanceSheetValueID; 
										
					END
					-- Save error number returned from Update statement  
					SET @upderr = @@error  
					IF @upderr > @maxerr  
					SET @maxerr = @upderr
				END
			-- If an error occurred, roll back  
			IF @maxerr <> 0  
			BEGIN  
				ROLLBACK TRANSACTION [Tran_BalanceSheetActual];				 
			END  
			ELSE  
			BEGIN  
				COMMIT TRANSACTION [Tran_BalanceSheetActual];							  
			END  

			FETCH NEXT FROM cursor_actual INTO				
				@ID,
				@Type,
				@KpiValue,
				@Month,
				@Year,
				@Quarter
		END;
 
		CLOSE cursor_actual; 
		DEALLOCATE cursor_actual;
		SET NOCOUNT OFF;
END
GO
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[spBulkUploadProfitAndLossICCase]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[spBulkUploadProfitAndLossICCase]
GO
CREATE PROCEDURE [dbo].[spBulkUploadProfitAndLossICCase]
           @SQLString NVARCHAR(MAX)
		  ,@UserID INT
		  ,@PortfolioCompanyID INT		 
		  ,@DocumentId BIGINT = NULL
		  ,@SupportingDocumentsId NVARCHAR(MAX) = NULL
		  ,@CommentId BIGINT = NULL,
		   @DataIngestion BIT=0
AS
BEGIN
	SET NOCOUNT ON;
	SET ANSI_WARNINGS OFF;

	DECLARE @ICCase TABLE([ID] INT,[ActualValue] NVARCHAR(MAX),[Year] INT);
	DECLARE @ID INT,@ActualValue NVARCHAR(MAX),@Year INT;	
	DECLARE @inserr INT;  
	DECLARE @upderr INT;  
	DECLARE @maxerr INT;
	DECLARE @CurrencyCode NVARCHAR(50) = NULL;

	--PRINT @SQLString;
	INSERT INTO @ICCase
	EXEC sys.sp_executesql @SQLString;
	DECLARE cursor_iccase CURSOR
	FOR SELECT 
			[ID],
			[ActualValue],			
			[Year]
	FROM @ICCase;
 
	OPEN cursor_iccase;
 
	FETCH NEXT FROM cursor_iccase INTO 
	   @ID,
	   @ActualValue,
	   @Year
	WHILE @@FETCH_STATUS = 0
		BEGIN
			DECLARE @ProfitAndLossICCaseValueID INT = 0;
			DECLARE @CompanyProfitAndLossLineItemMappingID INT = 0;
			DECLARE @ProfitAndLossLineItemID INT = 0;
		DECLARE @KPIInfo VARCHAR(20) = NULL;

			SELECT @ProfitAndLossICCaseValueID = [BLV].[ProfitAndLossICCaseValueID]
			FROM [dbo].[M_ProfitAndLoss_LineItems] BLL 
			INNER JOIN [dbo].[Mapping_CompanyProfitAndLossLineItems] MCPL ON [MCPL].[ProfitAndLossLineItemID] = [BLL].[ProfitAndLossLineItemID] AND [MCPL].[PortfolioCompanyID] = @PortfolioCompanyID
			INNER JOIN [dbo].[ProfitAndLossICCaseValues] BLV ON [BLV].[CompanyProfitAndLossLineItemMappingID] = [MCPL].[CompanyProfitAndLossLineItemMappingID] AND [BLV].[Year] = @Year
			WHERE [BLL].[ProfitAndLossLineItemID] = @ID AND [MCPL].[IsDeleted] = 0 AND [BLL].[IsDeleted] = 0 AND [BLV].[IsDeleted] = 0;

			SELECT @CompanyProfitAndLossLineItemMappingID = [MCBL].[CompanyProfitAndLossLineItemMappingID]
			FROM [dbo].[M_ProfitAndLoss_LineItems] BLL 
			INNER JOIN [dbo].[Mapping_CompanyProfitAndLossLineItems] MCBL ON [MCBL].[ProfitAndLossLineItemID] = [BLL].[ProfitAndLossLineItemID] AND [MCBL].[PortfolioCompanyID] = @PortfolioCompanyID		
			WHERE [BLL].[ProfitAndLossLineItemID] = @ID AND [MCBL].[IsDeleted] = 0 AND [BLL].[IsDeleted] = 0;
			

			IF (LTRIM(RTRIM(@ActualValue)) = '0.00000000')
			BEGIN
				SET @ActualValue = NULL;
			END		
			
			IF (@ActualValue IS NOT NULL)
			BEGIN
				IF (LOWER(@ActualValue) = 'n.a.' OR LOWER(@ActualValue) = 'na.' OR LOWER(@ActualValue) = 'na' OR LOWER(@ActualValue) = 'n.a')
				BEGIN
					SET @ActualValue = NULL;
				END
			END	
			Select @CurrencyCode = dbo.GetKPIInfo(@ID,'PL');
			IF(LTRIM(RTRIM(@KPIInfo)) = '$')
			BEGIN
				SELECT @CurrencyCode = dbo.GetCurrencyCode(@PortfolioCompanyID);		
			END
			ELSE
			BEGIN
				SET @CurrencyCode = @KPIInfo;
			END	
			BEGIN TRANSACTION [Tran_ICCase]
				IF(@ProfitAndLossICCaseValueID = 0)
				BEGIN	
				DECLARE @CopySupportingDocumentsId Varchar(MAX)=NULL, @CopyCommentId Varchar(MAX)=NULL, @CopyDocumentId Varchar(MAX)=NULL;
						SET @CopySupportingDocumentsId = 
					    (CASE 
					        WHEN @ActualValue IS NULL THEN NULL
					        ELSE @SupportingDocumentsId
					     END);
					
					SET @CopyCommentId = 
					    (CASE 
					        WHEN @ActualValue IS NULL THEN NULL
					        ELSE @CommentId
					     END);
					
					SET @CopyDocumentId = 
					    (CASE 
					        WHEN @ActualValue IS NULL THEN NULL
					        ELSE @DocumentId
					     END);
				IF(@CompanyProfitAndLossLineItemMappingID = 0)
					BEGIN					
						SELECT @ProfitAndLossLineItemID = [ProfitAndLossLineItemID] FROM [dbo].[M_ProfitAndLoss_LineItems] WHERE [ProfitAndLossLineItemID] = @ID;
						SELECT @CompanyProfitAndLossLineItemMappingID = [CompanyProfitAndLossLineItemMappingID] FROM [dbo].[Mapping_CompanyProfitAndLossLineItems] WHERE [PortfolioCompanyID] = @PortfolioCompanyID AND [ProfitAndLossLineItemID] = @ProfitAndLossLineItemID;						
						INSERT INTO [dbo].[ProfitAndLossICCaseValues] ([CompanyProfitAndLossLineItemMappingID],[BaseValue],[UpsideValue],[DownValue],[ValueInfo],[Year],[CreatedOn],[CreatedBy],[IsDeleted],[IsActive],DocumentId,SupportingDocumentsId,CommentId)  
						VALUES(@CompanyProfitAndLossLineItemMappingID, @ActualValue, NULL, NULL, @CurrencyCode, @Year, GETDATE(), @UserID,0,1,@CopyDocumentId,@CopySupportingDocumentsId,@CopyCommentId);
						-- Save error number returned from Insert statement  
						SET @inserr = @@error  
						SELECT @ProfitAndLossICCaseValueID = @@IDENTITY;
						IF @ActualValue IS NOT NULL
							exec AddFinancialAuditLogDetails 7,@ProfitAndLossICCaseValueID,null,@ActualValue,'ic',@UserID,@PortfolioCompanyID,null,@CurrencyCode,@CopyDocumentId,@CopySupportingDocumentsId,@CopyCommentId,@DataIngestion;
						IF @inserr > @maxerr
						SET @maxerr = @inserr 
					END
					ELSE IF(@CompanyProfitAndLossLineItemMappingID > 0)
					BEGIN	
						INSERT INTO [dbo].[ProfitAndLossICCaseValues] ([CompanyProfitAndLossLineItemMappingID],[BaseValue],[UpsideValue],[DownValue],[ValueInfo],[Year],[CreatedOn],[CreatedBy],[IsDeleted],[IsActive],DocumentId,SupportingDocumentsId,CommentId)  
						VALUES(@CompanyProfitAndLossLineItemMappingID, @ActualValue, NULL, NULL, @CurrencyCode, @Year, GETDATE(), @UserID,0,1,@CopyDocumentId,@CopySupportingDocumentsId,@CopyCommentId);
						-- Save error number returned from Insert statement  
						---First entry for audit log
						SELECT @ProfitAndLossICCaseValueID = @@IDENTITY;
						IF @ActualValue IS NOT NULL
							exec AddFinancialAuditLogDetails 7,@ProfitAndLossICCaseValueID,null,@ActualValue,'ic',@UserID,@PortfolioCompanyID,null,@CurrencyCode,@CopyDocumentId,@CopySupportingDocumentsId,@CopyCommentId,@DataIngestion;
						SET @inserr = @@error  
						IF @inserr > @maxerr  
						SET @maxerr = @inserr 
					END					
				END
				ELSE IF(@ProfitAndLossICCaseValueID > 0 AND @ActualValue IS NOT NULL)
				BEGIN	
				DECLARE @oldKPIValue NVARCHAR(MAX),@oldCurrencyCode NVARCHAR(50);
					SELECT @oldKPIValue = BaseValue, @oldCurrencyCode = ValueInfo
						FROM ProfitAndLossICCaseValues
						WHERE ProfitAndLossICCaseValueID = @ProfitAndLossICCaseValueID;

								EXEC AddFinancialAuditLogDetails 7,@ProfitAndLossICCaseValueID,@oldKPIValue,@ActualValue,'ic',@UserID,@PortfolioCompanyID,@oldCurrencyCode,@CurrencyCode,@DocumentId,@SupportingDocumentsId,@CommentId,@DataIngestion;								
								UPDATE [dbo].[ProfitAndLossICCaseValues] SET [BaseValue] = CASE WHEN @ActualValue IS NOT NULL THEN @ActualValue ELSE [BaseValue] END, [UpsideValue]  = NULL,[DownValue] =NULL, [ModifiedOn] = GETDATE(), [ModifiedBy]= @UserID,[DocumentId] = @DocumentId,[SupportingDocumentsId] = @SupportingDocumentsId,[CommentId] =@CommentId  
								WHERE [ProfitAndLossICCaseValueID] = @ProfitAndLossICCaseValueID; 
		
					-- Save error number returned from Update statement  
					SET @upderr = @@error  
					IF @upderr > @maxerr  
					SET @maxerr = @upderr
				END
			-- If an error occurred, roll back  
			IF @maxerr <> 0  
			BEGIN  
				ROLLBACK TRANSACTION [Tran_ICCase];
			END  
			ELSE  
			BEGIN  
				COMMIT TRANSACTION [Tran_ICCase];							 
			END  

			FETCH NEXT FROM cursor_iccase INTO @ID,@ActualValue,@Year;
						   
		END;
 
		CLOSE cursor_iccase; 
		DEALLOCATE cursor_iccase;
		SET NOCOUNT OFF;
END
GO
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[spBulkUploadProfitAndLossForecast]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[spBulkUploadProfitAndLossForecast]
GO
CREATE PROCEDURE [dbo].[spBulkUploadProfitAndLossForecast]
          @SQLString NVARCHAR(MAX)
		  ,@UserID INT
		  ,@PortfolioCompanyID INT		 
		  ,@DocumentId BIGINT = NULL
		  ,@SupportingDocumentsId NVARCHAR(MAX) = NULL
		  ,@CommentId BIGINT = NULL,
		  @DataIngestion BIT=0
AS
BEGIN
	SET NOCOUNT ON;
	DECLARE @ProfitLossForecast TABLE([ID] INT,[ActualValue] NVARCHAR(max),[Year] VARCHAR(100),[Quarter] VARCHAR(10),[Month] INT);
	DECLARE @ID INT,@ActualValue NVARCHAR(max),@Year INT,@Quarter VARCHAR(10),@Month INT = NULL;	
	DECLARE @inserr INT;  
	DECLARE @upderr INT;  
	DECLARE @maxerr INT;  
	DECLARE @CurrencyCode NVARCHAR(50) = NULL;

	--PRINT @SQLString;
	INSERT INTO @ProfitLossForecast
	EXEC sys.sp_executesql @SQLString;	

	DECLARE cursor_Forecast CURSOR
	FOR SELECT 
			[ID],
			[ActualValue],			
			[Year],
			[Quarter],
			[Month]
	FROM @ProfitLossForecast;
 
	OPEN cursor_Forecast;
 
	FETCH NEXT FROM cursor_Forecast INTO @ID,@ActualValue,@Year,@Quarter,@Month;
 
	WHILE @@FETCH_STATUS = 0
		BEGIN
			DECLARE @ProfitAndLossForecastValueID INT = 0;
			DECLARE @CompanyProfitAndLossLineItemMappingID INT = 0;
			DECLARE @ProfitAndLossLineItemID INT = 0;	
			DECLARE @KPIInfo VARCHAR(20) = NULL;

			IF(@Quarter IS NULL AND (@Month IS NULL or @Month = 0))
				BEGIN
					SELECT @ProfitAndLossForecastValueID = [PLV].[ProfitAndLossForecastValueID]
					FROM [dbo].[M_ProfitAndLoss_LineItems] PLL 
					INNER JOIN [dbo].[Mapping_CompanyProfitAndLossLineItems] MCPL ON [MCPL].[ProfitAndLossLineItemID] = [PLL].[ProfitAndLossLineItemID] AND [MCPL].[PortfolioCompanyID] = @PortfolioCompanyID
					INNER JOIN [dbo].[ProfitAndLoss_ForecastData] PLV ON [PLV].[CompanyProfitAndLossLineItemMappingID] = [MCPL].[CompanyProfitAndLossLineItemMappingID] AND [PLV].[Year] = @Year AND [PLV].[IsDeleted]=0 And ([PLV].[Month] IS NULL OR [PLV].[Month] = 0) AND [PLV].[Quarter] IS NULL
					WHERE [MCPL].[PortfolioCompanyID] = @PortfolioCompanyID AND [PLL].[ProfitAndLossLineItemID] = @ID AND [MCPL].[IsDeleted] = 0 AND [PLL].[IsDeleted] = 0 AND [PLV].[IsDeleted] = 0;
				END
			IF(@Quarter IS NOT NULL AND @Year IS NOT NULL)
				BEGIN
				SELECT @ProfitAndLossForecastValueID = [PLV].[ProfitAndLossForecastValueID]
					FROM [dbo].[M_ProfitAndLoss_LineItems] PLL 
					INNER JOIN [dbo].[Mapping_CompanyProfitAndLossLineItems] MCPL ON [MCPL].[ProfitAndLossLineItemID] = [PLL].[ProfitAndLossLineItemID] AND [MCPL].[PortfolioCompanyID] = @PortfolioCompanyID
					INNER JOIN [dbo].[ProfitAndLoss_ForecastData] PLV ON [PLV].[CompanyProfitAndLossLineItemMappingID] = [MCPL].[CompanyProfitAndLossLineItemMappingID] AND [PLV].[Year] = @Year AND [PLV].[Quarter] =@Quarter 
					WHERE [MCPL].[PortfolioCompanyID] = @PortfolioCompanyID AND [PLL].[ProfitAndLossLineItemID] = @ID AND [MCPL].[IsDeleted] = 0 AND [PLL].[IsDeleted] = 0 AND [PLV].[IsDeleted] = 0;
				END
			IF(@Month IS NOT NULL AND @Month > 0 AND @Year IS NOT NULL)
				BEGIN
				SELECT @ProfitAndLossForecastValueID = [PLV].[ProfitAndLossForecastValueID]
					FROM [dbo].[M_ProfitAndLoss_LineItems] PLL 
					INNER JOIN [dbo].[Mapping_CompanyProfitAndLossLineItems] MCPL ON [MCPL].[ProfitAndLossLineItemID] = [PLL].[ProfitAndLossLineItemID] AND [MCPL].[PortfolioCompanyID] = @PortfolioCompanyID
					INNER JOIN [dbo].[ProfitAndLoss_ForecastData] PLV ON [PLV].[CompanyProfitAndLossLineItemMappingID] = [MCPL].[CompanyProfitAndLossLineItemMappingID] AND [PLV].[Year] = @Year AND [PLV].[Month] =@Month
					WHERE [MCPL].[PortfolioCompanyID] = @PortfolioCompanyID AND [PLL].[ProfitAndLossLineItemID] = @ID AND [MCPL].[IsDeleted] = 0 AND [PLL].[IsDeleted] = 0 AND [PLV].[IsDeleted] = 0;
				END
			SELECT @CompanyProfitAndLossLineItemMappingID = [MCPL].[CompanyProfitAndLossLineItemMappingID]
			FROM [dbo].[M_ProfitAndLoss_LineItems] PLL 
			INNER JOIN [dbo].[Mapping_CompanyProfitAndLossLineItems] MCPL ON [MCPL].[ProfitAndLossLineItemID] = [PLL].[ProfitAndLossLineItemID] AND [MCPL].[PortfolioCompanyID] = @PortfolioCompanyID		
			WHERE [PLL].[ProfitAndLossLineItemID] = @ID AND [MCPL].[IsDeleted] = 0 AND [PLL].[IsDeleted] = 0;
			
			---Replace -1 with Null - Included NULL value as -1 in unpivot 
			IF (LTRIM(RTRIM(@ActualValue)) = '0.00000000')
			BEGIN
				SET @ActualValue = NULL;
			END		
			
			IF (@ActualValue IS NOT NULL)
			BEGIN
				IF (LOWER(@ActualValue) = 'n.a.' OR LOWER(@ActualValue) = 'na.' OR LOWER(@ActualValue) = 'na' OR LOWER(@ActualValue) = 'n.a')
				BEGIN
					SET @ActualValue = NULL;
				END
			END		
			Select @KPIInfo = dbo.GetKPIInfo(@ID,'PL');
			IF(LTRIM(RTRIM(@KPIInfo)) = '$')
			BEGIN
				SELECT @CurrencyCode = dbo.GetCurrencyCode(@PortfolioCompanyID);		
			END
			ELSE
			BEGIN
				SET @CurrencyCode = @KPIInfo;
			END	
			BEGIN TRANSACTION [Tran_Forecast]
			
			IF(@ProfitAndLossForecastValueID = 0)
			BEGIN
			DECLARE @CopySupportingDocumentsId Varchar(MAX)=NULL, @CopyCommentId Varchar(MAX)=NULL, @CopyDocumentId Varchar(MAX)=NULL;
					SET @CopySupportingDocumentsId = 
					    (CASE 
					        WHEN @ActualValue IS NULL THEN NULL
					        ELSE @SupportingDocumentsId
					     END);
					
					SET @CopyCommentId = 
					    (CASE 
					        WHEN @ActualValue IS NULL THEN NULL
					        ELSE @CommentId
					     END);
					
					SET @CopyDocumentId = 
					    (CASE 
					        WHEN @ActualValue IS NULL THEN NULL
					        ELSE @DocumentId
					     END);
					IF(@CompanyProfitAndLossLineItemMappingID = 0)
					BEGIN					
						SELECT @ProfitAndLossLineItemID = [ProfitAndLossLineItemID] FROM [dbo].[M_ProfitAndLoss_LineItems] WHERE [ProfitAndLossLineItemID] = @ID;
						SELECT @CompanyProfitAndLossLineItemMappingID = CompanyProfitAndLossLineItemMappingID FROM [dbo].[Mapping_CompanyProfitAndLossLineItems] WHERE [PortfolioCompanyID] = @PortfolioCompanyID AND [ProfitAndLossLineItemID] = @ProfitAndLossLineItemID;						

						INSERT INTO [dbo].[ProfitAndLoss_ForecastData] ([CompanyProfitAndLossLineItemMappingID],[ActualValue],[ActualValueInfo],[Year],[CreatedOn],[CreatedBy],[IsDeleted],[IsActive],[Quarter],[Month],DocumentId,SupportingDocumentsId,CommentId)  
						VALUES(@CompanyProfitAndLossLineItemMappingID, @ActualValue, @CurrencyCode, @Year, GETDATE(), @UserID,0,1,@Quarter,@Month,@CopyDocumentId,@CopySupportingDocumentsId,@CopyCommentId);
						-- Save error number returned from Insert statement  
						---First entry for audit log
						SELECT @ProfitAndLossForecastValueID = @@IDENTITY;
						IF @ActualValue IS NOT NULL
							exec AddFinancialAuditLogDetails 7,@ProfitAndLossForecastValueID,null,@ActualValue,'forecast',@UserID,@PortfolioCompanyID,null,@CurrencyCode,@CopyDocumentId,@CopySupportingDocumentsId,@CopyCommentId,@DataIngestion;
						SET @inserr = @@error  
						IF @inserr > @maxerr
						SET @maxerr = @inserr 
					END
					ELSE IF(@CompanyProfitAndLossLineItemMappingID > 0)
					BEGIN							
						INSERT INTO [dbo].[ProfitAndLoss_ForecastData] ([CompanyProfitAndLossLineItemMappingID],[ActualValue],[ActualValueInfo],[Year],[CreatedOn],[CreatedBy],[IsDeleted],[IsActive],[Quarter],[Month],DocumentId,SupportingDocumentsId,CommentId)  
						VALUES(@CompanyProfitAndLossLineItemMappingID, @ActualValue, @CurrencyCode, @Year, GETDATE(), @UserID,0,1,@Quarter,@Month,@CopyDocumentId,@CopySupportingDocumentsId,@CopyCommentId);
						-- Save error number returned from Insert statement  
						---First entry for audit log
						SELECT @ProfitAndLossForecastValueID = @@IDENTITY;
						IF @ActualValue IS NOT NULL
							exec AddFinancialAuditLogDetails 7,@ProfitAndLossForecastValueID,null,@ActualValue,'forecast',@UserID,@PortfolioCompanyID,null,@CurrencyCode,@CopyDocumentId,@CopySupportingDocumentsId,@CopyCommentId,@DataIngestion;
						SET @inserr = @@error  
						IF @inserr > @maxerr  
						SET @maxerr = @inserr   
					END			
			END
			ELSE IF(@ProfitAndLossForecastValueID > 0 AND @ActualValue IS NOT NULL)
			BEGIN		
			DECLARE @oldKPIValue NVARCHAR(MAX),@oldCurrencyCode NVARCHAR(50);
					SELECT @oldKPIValue = ActualValue, @oldCurrencyCode = ActualValueInfo
						FROM ProfitAndLoss_ForecastData
						WHERE ProfitAndLossForecastValueID = @ProfitAndLossForecastValueID;
						
					exec AddFinancialAuditLogDetails 7,@ProfitAndLossForecastValueID,@oldKPIValue,@ActualValue,'forecast',@UserID,@PortfolioCompanyID,@oldCurrencyCode,@CurrencyCode,@DocumentId,@SupportingDocumentsId,@CommentId,@DataIngestion;						
					UPDATE [dbo].[ProfitAndLoss_ForecastData] SET [ActualValue] = CASE WHEN @ActualValue IS NOT NULL THEN @ActualValue ELSE [ActualValue] END, [ModifiedOn] = GETDATE(), [ModifiedBy]= @UserID ,[DocumentId] = @DocumentId,[SupportingDocumentsId] = @SupportingDocumentsId,[CommentId] =@CommentId 
					WHERE [ProfitAndLossForecastValueID] = @ProfitAndLossForecastValueID; 

				-- Save error number returned from Update statement  
				SET @upderr = @@error  
				IF @upderr > @maxerr  
				SET @maxerr = @upderr
			END

			-- If an error occurred, roll back  
			IF @maxerr <> 0  
			BEGIN  
				ROLLBACK TRANSACTION [Tran_Forecast];
			END  
			ELSE  
			BEGIN  
				COMMIT TRANSACTION [Tran_Forecast];							 
			END  

			FETCH NEXT FROM cursor_Forecast INTO @ID,@ActualValue, @Year,@Quarter,@Month;
				   
		END;
 
		CLOSE cursor_Forecast; 
		DEALLOCATE cursor_Forecast;
		SET NOCOUNT OFF;
		SET ANSI_WARNINGS ON;
END
GO
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[spBulkUploadProfitAndLossActual]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[spBulkUploadProfitAndLossActual]
GO
CREATE PROCEDURE [dbo].[spBulkUploadProfitAndLossActual]
          @SQLString NVARCHAR(MAX)
		  ,@UserID INT
		  ,@PortfolioCompanyID INT	
		  ,@DocumentId BIGINT = NULL
		  ,@SupportingDocumentsId NVARCHAR(MAX) = NULL
		  ,@CommentId BIGINT = NULL,
		  @DataIngestion BIT=0
AS
BEGIN
	SET NOCOUNT ON;
	DECLARE @ProfitLossActual TABLE([ID] INT,[Type] INT,[KpiValue] NVARCHAR(max),[Month] INT,[Year] VARCHAR(100),[Quarter] VARCHAR(10));
	DECLARE @ID INT, @KpiValue NVARCHAR(max),@Month INT,@Year INT,@Type INT,@Quarter VARCHAR(10);	
	DECLARE @inserr INT;  
	DECLARE @upderr INT;  
	DECLARE @maxerr INT;  
	DECLARE @CurrencyCode NVARCHAR(50) = NULL;

	--PRINT @SQLString;
	INSERT INTO @ProfitLossActual EXEC sys.sp_executesql @SQLString;
	---Get Currency Code based on PortfolioCompanyID
	--SELECT @CurrencyCode = dbo.GetCurrencyCode(@PortfolioCompanyID);	
	DECLARE cursor_actual CURSOR

	FOR SELECT 
			[ID],
			[Type],
			[KpiValue],
			[Month],
			[Year],
			[Quarter]
	FROM @ProfitLossActual;
 
	OPEN cursor_actual;
	FETCH NEXT FROM cursor_actual INTO 
	   @ID,	   
	   @Type,
	   @KpiValue,
	   @Month,
	   @Year,
	   @Quarter
	WHILE @@FETCH_STATUS = 0
		BEGIN
			DECLARE @ProfitAndLossValueID INT = 0;			
			DECLARE @CompanyProfitAndLossLineItemMappingID INT = 0;	
			DECLARE @ProfitAndLossLineItemID INT = 0;					
			DECLARE @KPIInfo VARCHAR(20) = NULL;
			IF(@Month IS NOT NULL and @Month > 0)
			BEGIN
				SELECT @ProfitAndLossValueID = [PLV].[ProfitAndLossValueID]
				FROM [dbo].[M_ProfitAndLoss_LineItems] PLL 
				INNER JOIN [dbo].[Mapping_CompanyProfitAndLossLineItems] MCPL ON [MCPL].[ProfitAndLossLineItemID] = [PLL].[ProfitAndLossLineItemID] AND [MCPL].[PortfolioCompanyID] = @PortfolioCompanyID
				INNER JOIN [dbo].[ProfitAndLossValues] PLV ON [PLV].[CompanyProfitAndLossLineItemMappingID] = [MCPL].[CompanyProfitAndLossLineItemMappingID] AND [PLV].[Month] = @Month And [PLV].[Month] > 0 AND [PLV].[Year] = @Year
				WHERE [PLL].[ProfitAndLossLineItemID] = @ID AND [PLL].[IsDeleted] = 0 AND [MCPL].[IsDeleted] = 0  AND [PLV].[IsDeleted]=0;
			END
			IF(@Quarter IS NOT NULL)
			BEGIN
				SELECT @ProfitAndLossValueID = [PLV].[ProfitAndLossValueID]
				FROM [dbo].[M_ProfitAndLoss_LineItems] PLL 
				INNER JOIN [dbo].[Mapping_CompanyProfitAndLossLineItems] MCPL ON [MCPL].[ProfitAndLossLineItemID] = [PLL].[ProfitAndLossLineItemID] AND [MCPL].[PortfolioCompanyID] = @PortfolioCompanyID
				INNER JOIN [dbo].[ProfitAndLossValues] PLV ON [PLV].[CompanyProfitAndLossLineItemMappingID] = [MCPL].[CompanyProfitAndLossLineItemMappingID] AND [PLV].[Quarter] = @Quarter AND [PLV].[Year] = @Year
				WHERE [PLL].[ProfitAndLossLineItemID] = @ID AND [PLL].[IsDeleted] = 0 AND [MCPL].[IsDeleted] = 0  AND [PLV].[IsDeleted]=0;
			END

			IF(@Quarter IS NULL and (@Month IS NULL or @Month = 0))
			BEGIN
				SELECT @ProfitAndLossValueID = [PLV].[ProfitAndLossValueID]
				FROM [dbo].[M_ProfitAndLoss_LineItems] PLL 
				INNER JOIN [dbo].[Mapping_CompanyProfitAndLossLineItems] MCPL ON [MCPL].[ProfitAndLossLineItemID] = [PLL].[ProfitAndLossLineItemID] AND [MCPL].[PortfolioCompanyID] = @PortfolioCompanyID
				INNER JOIN [dbo].[ProfitAndLossValues] PLV ON [PLV].[CompanyProfitAndLossLineItemMappingID] = [MCPL].[CompanyProfitAndLossLineItemMappingID] AND [PLV].[Year] = @Year
				WHERE [PLL].[ProfitAndLossLineItemID] = @ID AND [PLL].[IsDeleted] = 0 AND [MCPL].[IsDeleted] = 0  AND [PLV].[IsDeleted]=0 And ([PLV].[Month] IS NULL OR [PLV].[Month] = 0) And [PLV].[Quarter] is null;
			END

			SELECT @CompanyProfitAndLossLineItemMappingID = [MCPL].[CompanyProfitAndLossLineItemMappingID],@KPIInfo =KPIInfo
			FROM [dbo].[M_ProfitAndLoss_LineItems] PLL 
			INNER JOIN [dbo].[Mapping_CompanyProfitAndLossLineItems] MCPL ON [MCPL].[ProfitAndLossLineItemID] = [PLL].[ProfitAndLossLineItemID] AND [MCPL].[PortfolioCompanyID] = @PortfolioCompanyID		
			WHERE [PLL].[ProfitAndLossLineItemID] = @ID AND [PLL].[IsDeleted] = 0 AND [MCPL].[IsDeleted] = 0;
					
			---Replace -1 with Null - Included NULL value as -1 in unpivot
			IF (LTRIM(RTRIM(@KpiValue)) = '0.00000000')
			BEGIN
				SET @KpiValue = NULL;
			END			
			IF(LTRIM(RTRIM(@KPIInfo)) = '$')
			BEGIN
				SET @CurrencyCode = @KPIInfo;
				--SELECT @CurrencyCode = dbo.GetCurrencyCode(@PortfolioCompanyID);		
			END
			ELSE
			BEGIN
				SET @CurrencyCode = @KPIInfo;
			END	
			BEGIN TRANSACTION [Tran_Actual]
				IF(@ProfitAndLossValueID = 0)
				BEGIN							
					--print 'insert'
					IF(@CompanyProfitAndLossLineItemMappingID = 0)
					BEGIN			
						SET @inserr = @@error  
						IF @inserr > @maxerr
						SET @maxerr = @inserr 
					END
					ELSE IF(@CompanyProfitAndLossLineItemMappingID > 0)
					BEGIN
					DECLARE @CopySupportingDocumentsId Varchar(MAX)=NULL, @CopyCommentId Varchar(MAX)=NULL, @CopyDocumentId Varchar(MAX)=NULL;
					SET @CopySupportingDocumentsId = 
					    (CASE 
					        WHEN @KpiValue IS NULL THEN NULL
					        ELSE @SupportingDocumentsId
					     END);
					
					SET @CopyCommentId = 
					    (CASE 
					        WHEN @KpiValue IS NULL THEN NULL
					        ELSE @CommentId
					     END);
					
					SET @CopyDocumentId = 
					    (CASE 
					        WHEN @KpiValue IS NULL THEN NULL
					        ELSE @DocumentId
					     END);
						 print @Type
						If (@Type = 4)
						BEGIN
						INSERT INTO [dbo].[ProfitAndLossValues] ([CompanyProfitAndLossLineItemMappingID],[ActualValue],[ValueInfo],[Month],[Year],[CreatedOn],[CreatedBy],IsDeleted,IsActive,Quarter,DocumentId,SupportingDocumentsId,CommentId)  
						VALUES(@CompanyProfitAndLossLineItemMappingID, @KpiValue, @CurrencyCode, @Month, @Year, GETDATE(), @UserID,0,1,@Quarter,@CopyDocumentId,@CopySupportingDocumentsId,@CopyCommentId);
						SELECT @ProfitAndLossValueID = @@IDENTITY;
						---First entry for audit log
						IF @KPIValue IS NOT NULL
							exec AddFinancialAuditLogDetails 7,@ProfitAndLossValueID,null,@KPIValue,'actual',@UserID,@PortfolioCompanyID,null,@CurrencyCode,@CopyDocumentId,@CopySupportingDocumentsId,@CopyCommentId,@DataIngestion;
						end
						If (@Type = 5)
						BEGIN
						INSERT INTO [dbo].[ProfitAndLossValues] ([CompanyProfitAndLossLineItemMappingID],[BudgetValue],[ValueInfo],[Month],[Year],[CreatedOn],[CreatedBy],IsDeleted,IsActive,Quarter,BudgetDocumentId,BudgetSupportingDocumentsId,BudgetCommentId)  
						VALUES(@CompanyProfitAndLossLineItemMappingID, @KpiValue, @CurrencyCode, @Month, @Year, GETDATE(), @UserID,0,1,@Quarter,@CopyDocumentId,@CopySupportingDocumentsId,@CopyCommentId);
						SELECT @ProfitAndLossValueID = @@IDENTITY;
						---First entry for audit log
						IF @KPIValue IS NOT NULL
							exec AddFinancialAuditLogDetails 7,@ProfitAndLossValueID,null,@KPIValue,'budget',@UserID,@PortfolioCompanyID,null,@CurrencyCode,@CopyDocumentId,@CopySupportingDocumentsId,@CopyCommentId,@DataIngestion;
						end
						-- Save error number returned from Insert statement  
						SET @inserr = @@error  
						IF @inserr > @maxerr  
						SET @maxerr = @inserr 
					END
				END
				ELSE IF(@ProfitAndLossValueID > 0 AND @KpiValue IS NOT NULL)
				BEGIN		
					--print 'update'
					If (@Type = 4)
					BEGIN
					DECLARE @oldKPIValue NVARCHAR(MAX),@oldCurrencyCode NVARCHAR(50);
					SELECT @oldKPIValue = ActualValue, @oldCurrencyCode = ValueInfo
						FROM ProfitAndLossValues
						WHERE ProfitAndLossValueID = @ProfitAndLossValueID;
						
						exec AddFinancialAuditLogDetails 7,@ProfitAndLossValueID,@oldKPIValue,@KPIValue,'actual',@UserID,@PortfolioCompanyID,@oldCurrencyCode,@CurrencyCode,@DocumentId,@SupportingDocumentsId,@CommentId,@DataIngestion;								
						UPDATE ProfitAndLossValues SET [ActualValue] = CASE WHEN @KpiValue Is NULL THEN [ActualValue] ELSE @KpiValue END, [ModifiedOn] = GETDATE(), [ModifiedBy]= @UserID,[DocumentId] = @DocumentId,[SupportingDocumentsId] = @SupportingDocumentsId,[CommentId] =@CommentId  WHERE ProfitAndLossValueID = @ProfitAndLossValueID; 	
			
					END
					If (@Type = 5)
					BEGIN
						DECLARE @oldKpiBudgetValue NVARCHAR(MAX),@oldCurrency NVARCHAR(50);
							SELECT @oldKpiBudgetValue = BudgetValue, @oldCurrency = ValueInfo
						FROM ProfitAndLossValues
						WHERE ProfitAndLossValueID = @ProfitAndLossValueID;

						exec AddFinancialAuditLogDetails 7,@ProfitAndLossValueID,@oldKpiBudgetValue,@KPIValue,'budget',@UserID,@PortfolioCompanyID,@oldCurrency,@CurrencyCode,@DocumentId,@SupportingDocumentsId,@CommentId,@DataIngestion;								
						UPDATE ProfitAndLossValues SET [BudgetValue] = CASE WHEN @KpiValue Is NULL THEN [BudgetValue] ELSE @KpiValue END, [ModifiedOn] = GETDATE(), [ModifiedBy]= @UserID,[BudgetDocumentId] = @DocumentId,[BudgetSupportingDocumentsId] = @SupportingDocumentsId,[BudgetCommentId] =@CommentId  WHERE ProfitAndLossValueID = @ProfitAndLossValueID; 
								
					END
					
					-- Save error number returned from Update statement  
					SET @upderr = @@error  
					IF @upderr > @maxerr  
					SET @maxerr = @upderr
				END
			-- If an error occurred, roll back  
			IF @maxerr <> 0  
			BEGIN  
				ROLLBACK TRANSACTION [Tran_Actual];				 
			END  
			ELSE  
			BEGIN  
				COMMIT TRANSACTION [Tran_Actual];							  
			END  

			FETCH NEXT FROM cursor_actual INTO 
				@ID,
				@Type,
				@KpiValue,
				@Month,
				@Year,
				@Quarter
		END;
		CLOSE cursor_actual; 
		DEALLOCATE cursor_actual;
		SET NOCOUNT OFF;
END
GO
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'MFundSectionKpi' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
    CREATE TABLE [dbo].[MFundSectionKpi](
        [FundSectionKpiId] [int] IDENTITY(1,1) PRIMARY KEY NOT NULL,
        [ModuleId] [int] NULL,
        [Kpi] [nvarchar](2000) NOT NULL,
        [KpiInfo] [varchar](20) NULL,
        [Description] [nvarchar](500) NULL,
        [CreatedOn] [datetime] default getutcdate() NOT NULL,
        [CreatedBy] [int] NOT NULL,
        [ModifiedOn] [datetime] NULL,
        [ModifiedBy] [int] NULL,
        [IsDeleted] [bit] default 0 NOT NULL,
        [MethodologyId] [int] DEFAULT NULL,
        [IsBoldKpi] [bit] DEFAULT 0 NULL,
        [IsHeader] [bit] DEFAULT 0 NULL,
        [Formula] [nvarchar](max) NULL,
        [FormulaKpiId] [nvarchar](max) NULL
    )
END
GO
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_MFundSectionKpi_ModuleId' AND object_id = OBJECT_ID('dbo.MFundSectionKpi'))
BEGIN
    CREATE NONCLUSTERED INDEX [IX_MFundSectionKpi_ModuleId] ON [dbo].[MFundSectionKpi]
    (
        [ModuleId] ASC,
	[IsDeleted]
    )
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
END
GO
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_MFundSectionKpi_Composite' AND object_id = OBJECT_ID('dbo.MFundSectionKpi'))
BEGIN
    CREATE NONCLUSTERED INDEX [IX_MFundSectionKpi_Composite] ON [dbo].[MFundSectionKpi]
    (
        [ModuleId] ASC,
        [MethodologyId] ASC,
	[IsDeleted]
    )
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
END
go
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'MappingFundSectionKpi' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
CREATE TABLE [dbo].[MappingFundSectionKpi](
	[MappingFundSectionKpiId] [int] IDENTITY(1,1) PRIMARY KEY NOT NULL,
	[FundId] [int] NOT NULL,
	[KpiId] [int] NOT NULL,
	[CreatedOn] [datetime] default getutcdate() NOT NULL,
	[CreatedBy] [int] NOT NULL,
	[ModifiedOn] [datetime] NULL,
	[ModifiedBy] [int] NULL,
	[IsDeleted] [bit] default 0 NOT NULL,
	[ParentKpiId] [int] NULL,
	[DisplayOrder] [int] NULL,
	[IsHeader] [bit] NULL,
	[ModuleId] [int] NULL,
	[Formula] [nvarchar](max) NULL,
	[FormulaKpiId] [nvarchar](max) NULL
)
END
GO
-- Foreign key index (for joins)
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_MappingFundSectionKpi_FundId' AND object_id = OBJECT_ID('dbo.MappingFundSectionKpi'))
BEGIN
    CREATE NONCLUSTERED INDEX [IX_MappingFundSectionKpi_FundId] ON [dbo].[MappingFundSectionKpi]([FundId])
END
GO
-- Foreign key index (for joins)
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_MappingFundSectionKpi_KpiId' AND object_id = OBJECT_ID('dbo.MappingFundSectionKpi'))
BEGIN
    CREATE NONCLUSTERED INDEX [IX_MappingFundSectionKpi_KpiId] ON [dbo].[MappingFundSectionKpi]([KpiId])
END
GO
-- Filtered index for non-deleted records (most queries likely filter on this)
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_MappingFundSectionKpi_IsDeleted' AND object_id = OBJECT_ID('dbo.MappingFundSectionKpi'))
BEGIN
    CREATE NONCLUSTERED INDEX [IX_MappingFundSectionKpi_IsDeleted] ON [dbo].[MappingFundSectionKpi]([IsDeleted])
    WHERE IsDeleted = 0
END
GO
-- Composite index for common query pattern
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_MappingFundSectionKpi_ModuleId_IsDeleted' AND object_id = OBJECT_ID('dbo.MappingFundSectionKpi'))
BEGIN
    CREATE NONCLUSTERED INDEX [IX_MappingFundSectionKpi_ModuleId_IsDeleted] ON [dbo].[MappingFundSectionKpi]([ModuleId], [IsDeleted])
END
GO
-- Hierarchical relationship
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_MappingFundSectionKpi_ParentKpiId' AND object_id = OBJECT_ID('dbo.MappingFundSectionKpi'))
BEGIN
    CREATE NONCLUSTERED INDEX [IX_MappingFundSectionKpi_ParentKpiId] ON [dbo].[MappingFundSectionKpi]([ParentKpiId])
    WHERE ParentKpiId IS NOT NULL
END
GO
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'FundMasterKpiValues' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
	CREATE TABLE [dbo].[FundMasterKpiValues](
	[FundMasterKpiValueId] [int] IDENTITY(1,1) PRIMARY KEY NOT NULL,
	[FundId] [int] NOT NULL,
	[MappingId] [int] NOT NULL,
	[KpiValue] [varchar](max) NULL,
	[KpiInfo] [nvarchar](100) NULL,
	[Month] [int] NULL,
	[Year] [int] NULL,
	[IsNumeric] [bit] NULL,
	[Quarter] [nvarchar](10) NULL,
	[CreatedOn] [datetime] DEFAULT GETUTCDATE() NOT NULL,
	[CreatedBy] [int] NOT NULL,
	[ModifiedOn] [datetime] NULL,
	[ModifiedBy] [int] NULL,
	[IsDeleted] [bit] DEFAULT 0 NOT NULL,
	[ValueTypeId] [int] NULL,
	[ModuleId] [int] NULL,
	[DocumentId] [bigint] NULL,
	[SupportingDocumentsId] [nvarchar](2000) NULL,
	[CommentId] [bigint] NULL,
	[ProcessId] [bigint] NULL
)
END
GO
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_FundMasterKpiValues_Module_Value_Date' AND object_id = OBJECT_ID('dbo.FundMasterKpiValues'))
BEGIN
    CREATE NONCLUSTERED INDEX IX_FundMasterKpiValues_Module_Value_Date
    ON [dbo].[FundMasterKpiValues] (FundId, MappingId,ModuleId, ValueTypeId, Month, Year, Quarter);
END
GO
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'FundKpiAuditLog' AND schema_id = SCHEMA_ID('dbo'))
BEGIN
	CREATE TABLE [dbo].[FundKpiAuditLog](
	[AuditId] [int] IDENTITY(1,1) PRIMARY KEY NOT NULL,
	[ModuleId] [int] NOT NULL,
	[FundId] [int] NOT NULL,
	[AttributeId] [int] NOT NULL,
	[OldValue] [nvarchar](max) NULL,
	[NewValue] [nvarchar](max) NULL,
	[OldCurrency] [varchar](10) NULL,
	[NewCurrency] [varchar](10) NULL,
	[AuditType] [nvarchar](100) NULL,
	[IsDeleted] [bit] DEFAULT 0 NOT NULL,
	[CreatedOn] [datetime] DEFAULT GETUTCDATE() NOT NULL,
	[CreatedBy] [int] NOT NULL,
	[DocumentId] [bigint] NULL,
	[SupportingDocumentsId] [nvarchar](2000) NULL,
	[CommentId] [bigint] NULL,
	[ModifiedOn] [datetime] NULL,
	[ModifiedBy] [int] NULL,
)
END
GO
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_FundKpiAuditLog_FundId_ModuleId' AND object_id = OBJECT_ID('dbo.FundKpiAuditLog'))
BEGIN
    CREATE NONCLUSTERED INDEX IX_FundKpiAuditLog_FundId_ModuleId ON dbo.FundKpiAuditLog (FundId, ModuleId)
    INCLUDE (CreatedOn, AttributeId)
END
go
IF (NOT EXISTS (SELECT *
                 FROM [dbo].[M_SubPageDetails] 
				 where Name='Fund Key Performance Indicator'    
                ))
BEGIN
SET IDENTITY_INSERT [dbo].[M_SubPageDetails] ON 
INSERT [dbo].[M_SubPageDetails] ([SubPageID], [Name], [AliasName], [PageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [IsDynamicFieldSupported],IsDragDrop,IsDataType) VALUES (46, N'Fund Key Performance Indicator', N'Fund Key Performance Indicator', 2, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 6, NULL, 0, 0,1,0)
SET IDENTITY_INSERT [dbo].[M_SubPageDetails] OFF 
End 
go
IF (NOT EXISTS (SELECT* 
                 FROM [dbo].[M_SubPageFields]  
				 where Name='FundFinancials'
                ))
BEGIN
INSERT [dbo].[M_SubPageFields] ([Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom]) VALUES (N'FundFinancials', N'FundFinancials', 46, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 1, NULL, 0)
END
GO

IF (NOT EXISTS (SELECT * 
                FROM MSubSectionFields
                WHERE  FieldID=(SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials')
               ))
BEGIN
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials'), N'Actual', N'Actual', 46, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials'), N'Budget', N'Budget', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials'), N'Forecast', N'Forecast', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials'), N'IC', N'IC', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials'), N'Actual LTM', N'Actual LTM', 46, 1, 0,Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials'), N'Budget LTM', N'Budget LTM', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials'), N'Forecast LTM', N'Forecast LTM', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials'), N'Actual YTD', N'Actual YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials'), N'Budget YTD', N'Budget YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials'), N'Forecast YTD', N'Forecast YTD', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
INSERT [dbo].[MSubSectionFields] ( [FieldID], [Name], [AliasName], [SubPageID], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [SequenceNo], [Options], [ChartValue]) VALUES ( (SELECT FieldID FROM M_SubPageFields WHERE SubPageID=46 AND Name='FundFinancials'), N'Since Inception', N'Since Inception', 46, 1, 0, Getdate(), 3, NULL, NULL, 1, N'Monthly,Quarterly,Annual', 'Monthly,Quarterly,Annual')
END
go
-- Check if table exists before creating it
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[MFundKpiModules]') AND type in (N'U'))
BEGIN
    SET ANSI_NULLS ON
    SET QUOTED_IDENTIFIER ON
    
    CREATE TABLE [dbo].[MFundKpiModules](
        [ModuleId] [int] IDENTITY(1000,1) PRIMARY KEY NOT NULL,
        [Name] [nvarchar](100) NOT NULL,
        [CreatedOn] [datetime] DEFAULT GETUTCDATE() NOT NULL,
        [CreatedBy] [int] NOT NULL,
        [ModifiedOn] [datetime] NULL,
        [ModifiedBy] [int] NULL,
        [IsDeleted] [bit] DEFAULT 0 NOT NULL,
        [AliasName] [varchar](500) NULL,
        [TabName] [varchar](50) NULL,
        [IsActive] [bit] DEFAULT 1 NULL,
        [OrderBy] [int] NULL,
        [IsBulkUpload] [bit] DEFAULT 0 NULL,
        [PageConfigFieldName] [varchar](500) NULL
    )
    
END
GO

-- Create non-clustered indexes with IF NOT EXISTS
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_MFundKpiModules_Name' AND object_id = OBJECT_ID('dbo.MFundKpiModules'))
BEGIN
    CREATE NONCLUSTERED INDEX IX_MFundKpiModules_Name
    ON [dbo].[MFundKpiModules] ([Name]);
END
GO
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_MFundKpiModules_IsActive' AND object_id = OBJECT_ID('dbo.MFundKpiModules'))
BEGIN
    CREATE NONCLUSTERED INDEX IX_MFundKpiModules_IsActive
    ON [dbo].[MFundKpiModules] ([IsActive]);
END
GO
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_MFundKpiModules_TabName' AND object_id = OBJECT_ID('dbo.MFundKpiModules'))
BEGIN
    CREATE NONCLUSTERED INDEX IX_MFundKpiModules_TabName
    ON [dbo].[MFundKpiModules] ([TabName]);
END
GO
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_MFundKpiModules_IsActive_TabName' AND object_id = OBJECT_ID('dbo.MFundKpiModules'))
BEGIN
    CREATE NONCLUSTERED INDEX IX_MFundKpiModules_IsActive_TabName
    ON [dbo].[MFundKpiModules] ([IsActive], [TabName]) INCLUDE ([Name], [OrderBy]);
END
GO
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[GetFundkpiNotMappedList]') AND type in (N'P'))
BEGIN
    DROP PROCEDURE [dbo].[GetFundkpiNotMappedList]
    PRINT 'Procedure [GetFundkpiNotMappedList] dropped successfully'
END
GO
CREATE PROC [dbo].[GetFundkpiNotMappedList](
@FundId INT,
@ModuleId INT
)
AS
BEGIN
    SELECT DISTINCT(K.FundSectionKpiId) 'Id',K.KPI 'Name',K.IsHeader 'IsHeader',K.IsBoldKPI 'IsBoldKPI',K.Formula 'Formula',
    K.FormulaKPIId 'FormulaKPIId' FROM MFundSectionKpi K 
                                where K.FundSectionKpiId not in  (SELECT KpiId FROM MappingFundSectionKpi 
                                where FundId =@FundId AND IsDeleted=0) 
                                and K.IsDeleted=0 and K.ModuleId=@ModuleId  order by K.KPI
    PRINT 'Procedure [GetFundkpiNotMappedList] created successfully'
END
GO
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[ProcCreateDuplicateFundKPI]') AND type in (N'P'))
BEGIN
    DROP PROCEDURE [dbo].[ProcCreateDuplicateFundKPI]
    PRINT 'Procedure [ProcCreateDuplicateFundKPI] dropped successfully'
END
GO

CREATE PROCEDURE [dbo].[ProcCreateDuplicateFundKPI]
(
    @KpiId INT,
    @UserId INT,
    @Id INT OUTPUT
)
AS
BEGIN
    SET NOCOUNT ON
    BEGIN TRY
        BEGIN
            INSERT INTO MFundSectionKpi(KPI,KpiInfo,CreatedBy,CreatedOn,IsDeleted,MethodologyID,IsHeader,IsBoldKPI,Description,
            Formula,FormulaKPIId,ModuleId) SELECT KPI,KpiInfo,@UserId,GETDATE(),0,MethodologyID,IsHeader,IsBoldKPI,
            Description,Formula,FormulaKPIId,ModuleId FROM MFundSectionKpi
            WHERE FundSectionKpiId = @KpiId
            SET @Id = SCOPE_IDENTITY()
        END
    END TRY
    BEGIN CATCH
        SET @Id = 0
    END CATCH
    
    PRINT 'Procedure [ProcCreateDuplicateFundKPI] created successfully'
END
GO
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[ProcFundKpiCopyKPIToFunds]') AND type in (N'P'))
BEGIN
    DROP PROCEDURE [dbo].[ProcFundKpiCopyKPIToFunds]
END
GO
CREATE PROCEDURE  [dbo].[ProcFundKpiCopyKPIToFunds]
(
@ModuleId INT,
@FundId INT,
@UserId INT,
@FundIds NVARCHAR(MAX)
)
AS
BEGIN
SET NOCOUNT ON

	 BEGIN TRY
		 DECLARE @RowCount INT,@Row INT = 1;
		 DROP TABLE IF EXISTS #tempFundKpiCopyToFunds
			CREATE TABLE #tempFundKpiCopyToFunds
			(
			 Id INT IDENTITY(1,1) PRIMARY KEY,
			 FundId INT,
			 ModuleId INT
			)
			INSERT INTO #tempFundKpiCopyToFunds(FundId)
			SELECT Item AS FundId FROM[dbo].[SplitString](@FundIds,',') WHERE Item!=@FundId
			SET @RowCount = (SELECT Count(FundId) FROM #tempFundKpiCopyToFunds)
			WHILE (@Row <= @RowCount)
			BEGIN
			DECLARE @PortfolioFundId INT
			SELECT @PortfolioFundId=FundId FROM #tempFundKpiCopyToFunds Where ID= @Row
		   DECLARE @MappingFundKpi table(ID INT IDENTITY(1,1), FundId int, KpiId int, CreatedBy int, CreatedOn Datetime,
		   IsDeleted bit, ParentId int, DisplayOrder int, Formula nvarchar(max), FormulaKPIId nvarchar(max), ModuleId int);
	       DELETE FROM @MappingFundKpi
			BEGIN
					INSERT INTO @MappingFundKpi
					SELECT FundId, KpiId, CreatedBy, GETDATE(), IsDeleted, ParentKpiId, DisplayOrder, Formula, FormulaKPIId, ModuleId From
					MappingFundSectionKpi where FundId = @FundId AND ModuleId=@ModuleId AND IsDeleted = 0
					
					IF EXISTS(select * from MappingFundSectionKpi where  IsDeleted =0 and FundId = @PortfolioFundId and
					ModuleId=@ModuleId and KpiId IN (SELECT KpiId FROM @MappingFundKpi))
					BEGIN
					    UPDATE MappingFundSectionKpi SET DisplayOrder=B.DisplayOrder,KpiId=B.KpiId,ParentKpiId=B.ParentId,Formula=B.Formula,
						FormulaKPIId=B.FormulaKPIId FROM MappingFundSectionKpi A
						Inner Join @MappingFundKpi B On A.KpiId=B.KpiId WHERE A.IsDeleted=0 and A.FundId= @PortfolioFundId and A.ModuleId=@ModuleId
					END
						INSERT INTO MappingFundSectionKpi(FundId,KpiId,CreatedBy,CreatedOn,IsDeleted,ParentKpiId,DisplayOrder,Formula,FormulaKPIId,ModuleId)
						SELECT @PortfolioFundId,KpiId,CreatedBy,GETDATE(),0,ParentId,DisplayOrder,Formula,FormulaKPIId,@ModuleId From @MappingFundKpi
						where KpiId not in  (SELECT KpiId FROM MappingFundSectionKpi
							where FundId =@PortfolioFundId AND IsDeleted=0)
							and IsDeleted=0 and ModuleId=@ModuleId

			END
		
			SET @Row = @Row + 1
			END
			DROP TABLE IF EXISTS #tempFundKpiCopyToFunds		
         END TRY
         BEGIN CATCH
             
         END CATCH
	
END
GO
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[spDeleteFundKpi]') AND type in (N'P'))
BEGIN
    DROP PROCEDURE [dbo].[spDeleteFundKpi]
END
GO
CREATE PROCEDURE   [dbo].[spDeleteFundKpi]
(
@FundKpiId INT,
@ModuleId INT
)
AS BEGIN
	SET NOCOUNT ON;
	BEGIN
		UPDATE MFundSectionKpi SET IsDeleted = 1 WHERE FundSectionKpiId = @FundKpiId AND ModuleId= @ModuleId
		UPDATE MappingFundSectionKpi SET ParentKpiId = NULL WHERE ParentKpiId = @FundKpiId AND ModuleId= @ModuleId
		UPDATE MappingFundSectionKpi SET IsDeleted = 1 WHERE KpiId = @FundKpiId AND ModuleId= @ModuleId
	END
END
GO
IF (NOT EXISTS (SELECT *
                 FROM [dbo].[MFundKpiModules] where Name='FundFinancials'
                ))
		BEGIN
		SET IDENTITY_INSERT [dbo].[MFundKpiModules] ON
		INSERT INTO MFundKpiModules(ModuleId,Name,CreatedBy,CreatedOn,IsDeleted,IsActive,IsBulkUpload,OrderBy,AliasName,TabName,PageConfigFieldName)VALUES(1001,'FundFinancials',3,GETDATE(),0,1,0,10,'Fund Financials','Fund Financials','FundFinancials')
		SET IDENTITY_INSERT [dbo].[MFundKpiModules] OFF
		END
GO
IF object_id('AuditLogFundKPIFunction', 'FN') IS NOT NULL
BEGIN
    DROP FUNCTION [dbo].AuditLogFundKPIFunction
END
GO
CREATE   FUNCTION [dbo].[AuditLogFundKPIFunction](
    @FundId INT=0,
	@AttributeID int=0
)
RETURNS varchar(max)
AS 
BEGIN
RETURN (Select CASE WHEN COUNT(AuditId) <>0 THEN 'true' ELSE 'false' END IsExits FROM FundKpiAuditLog WHERE FundId=@FundId AND AttributeID=@AttributeID)
END
GO
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[spFundKpiValues]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[spFundKpiValues]
GO
CREATE PROCEDURE [dbo].[spFundKpiValues](
	@FundId int=0,
	@dataType varchar(10) = 'M',
	@ValueTypeId int=0,
	@ModuleId int=0
	)
	AS
	BEGIN
	DECLARE @ValueType varchar(10) = 'Actual'
	SET @ValueType = (select HeaderValue from M_ValueTypes where ValueTypeID =@ValueTypeId)
	DECLARE @TableValues table(IdentityId int  NULL,ValuesKpiId int  NULL,  
							 KPIActualValue varchar(Max) NULL, Year int NULL, Quarter nvarchar(100) NULL, Month int  NULL, Data varchar(10),			
							 CompanyId int  NULL,MappingKpisID int NULL)						 
	DECLARE @TableMappings table(KpiID int  NULL,
							 ParentKPIID int,
							 DisplayOrder int null,
							 PortfolioCompanyID int null,
							 KPI Varchar(max) NULL,
							 KPIInfo nvarchar(100) NULL,
							 IsBoldKPI bit NULL,
							 IsHeader bit NULL,MappingFundSectionKpiID int NULL,
							 MethodologyId INT NULL)
	DECLARE @TempTable table(KpiId int  NULL,
							 ParentId int,
							 DisplayOrder int null,
							 PortfolioCompanyID int null,
							 KPI Varchar(max) NULL,
							 KPIInfo Varchar(max) NULL,
							 IsBoldKPI bit NULL,
							 IsHeader bit NULL,
							 KPIActualValue varchar(Max) NULL,
							 Quarter	nvarchar(100) NULL,
							 Year int NULL,
							 Month int  NULL,
							 CompanyId int  NULL,
							 ValuesKpiId int  NULL,
							 IdentityId int  NULL,
							 MethodologyId INT NULL)
						 
	DECLARE @TempTable_Match table(KpiId int  NULL,
							 ParentId int,
							 DisplayOrder int null,
							 PortfolioCompanyID int null,
							 KPI Varchar(max) NULL,
							 KPIInfo nvarchar(100) NULL,
							 IsBoldKPI bit NULL,
							 IsHeader bit NULL,
							 KPIActualValue varchar(Max) NULL,
							 Quarter nvarchar(100) NULL,
							 Year int NULL,
							 Month int  NULL,
							 CompanyId int  NULL,
							 ValuesKpiId int  NULL,
							 IdentityId int  NULL,
							 MethodologyId INT NULL)
	INSERT INTO @TableValues
	SELECT IdentityId,KpiId,KPIValue, Year, Quarter, Month, Data,CompanyId,MappingId  FROM (Select A.FundMasterKpiValueId IdentityId,Map.KpiID KpiId,A.KPIValue,A.Year,A.Quarter,A.Month,
	CASE 
	WHEN (Month IS NULL OR Month=0) and Quarter IS NULL THEN 'A'
	WHEN (Month IS NULL OR Month=0) THEN 'Q'
	WHEN Quarter IS NULL THEN 'M'
	END Data,A.FundId CompanyId,A.MappingId
	FROM FundMasterKpiValues A 
	INNER JOIN MappingFundSectionKpi Map on A.MappingId=Map.MappingFundSectionKpiId
	WHERE A.IsDeleted=0 and Map.IsDeleted=0 AND A.FundId=@FundId and A.ModuleID=@ModuleId and A.ValueTypeID=@ValueTypeId) C WHERE C.Data=@dataType

	INSERT INTO @TableMappings
	Select Map.KpiID,Map.ParentKPIID,Map.DisplayOrder,Map.FundId,Matr.KPI,Matr.KPIInfo,Matr.IsBoldKPI,Matr.IsHeader,Map.MappingFundSectionKpiID,Matr.MethodologyId
	FROM (SELECT DISTINCT OI.KpiID,OI.ParentKPIID,OI.DisplayOrder,OI.FundId,OI.MappingFundSectionKpiID
		FROM MappingFundSectionKpi OI
		  LEFT JOIN MappingFundSectionKpi OI2
			  ON OI2.ParentKPIID = OI.KpiID  WHERE OI.IsDeleted=0 and OI.ModuleID=@ModuleId and OI.FundId=@FundId  ) Map 
	INNER JOIN MFundSectionKpi Matr ON Map.KpiID=Matr.FundSectionKpiId  
	WHERE Matr.IsDeleted=0 

	INSERT INTO @TempTable
	SELECT MappingTable.KpiID KpiId,ISNULL(MappingTable.ParentKPIID,0) ParentId,MappingTable.DisplayOrder,MappingTable.PortfolioCompanyID,
		   MappingTable.KPI,MappingTable.KPIInfo,MappingTable.IsBoldKPI,MappingTable.IsHeader,
		   ValuesTable.KPIActualValue,ValuesTable.Quarter,ValuesTable.Year,ValuesTable.Month,ValuesTable.CompanyId,ValuesTable.ValuesKpiID ValuesKpiId,ValuesTable.IdentityId,MappingTable.MethodologyId FROM ( 
	Select Map.KpiID,Map.ParentKPIID,Map.DisplayOrder,Map.PortfolioCompanyID,Map.KPI,Map.KPIInfo,Map.IsBoldKPI,Map.IsHeader,MappingFundSectionKpiID,Map.MethodologyId FROM @TableMappings Map) MappingTable LEFT OUTER JOIN  
	(
	select IdentityId,ValuesKpiId,KPIActualValue,Quarter,Year,Month,CompanyId,MappingKpisID FROM @TableValues WHERE Data=@dataType
	) ValuesTable on MappingTable.MappingFundSectionKpiID=ValuesTable.MappingKpisID  ORDER BY DisplayOrder

	INSERT INTO @TempTable_Match
	Select DISTINCT C.KpiId,C.ParentId,C.DisplayOrder,C.PortfolioCompanyID,C.KPI,C.KPIInfo,C.IsBoldKPI,C.IsHeader,C.KPIActualValue,A.Quarter,A.Year,A.Month,C.PortfolioCompanyID CompanyId,C.ValuesKpiId,C.IdentityId,C.MethodologyId FROM @TempTable A inner join (Select * FROM @TempTable WHERE Year IS NULL AND Quarter IS NULL AND (Month IS NULL OR Month=0) AND CompanyId IS NULL) C On A.ParentId=C.KpiId 

	INSERT INTO @TempTable_Match
	SELECT MappingTable.KpiID KpiId,ISNULL(MappingTable.ParentKPIID,0) ParentId,MappingTable.DisplayOrder,MappingTable.PortfolioCompanyID,
		   MappingTable.KPI,MappingTable.KPIInfo,MappingTable.IsBoldKPI,MappingTable.IsHeader,
		   ValuesTable.KPIActualValue,ValuesTable.Quarter,ValuesTable.Year,ValuesTable.Month,ValuesTable.CompanyId,ValuesTable.ValuesKpiID ValuesKpiId,ValuesTable.IdentityId,MappingTable.MethodologyId FROM ( 
	Select Map.KpiID,Map.ParentKPIID,Map.DisplayOrder,Map.PortfolioCompanyID,Map.KPI,Map.KPIInfo,Map.IsBoldKPI,Map.IsHeader,Map.MappingFundSectionKpiID,Map.MethodologyId FROM @TableMappings Map) MappingTable INNER JOIN 
	(
	select IdentityId,ValuesKpiId,KPIActualValue,Month,Quarter,Year,CompanyId,MappingKpisID FROM @TableValues WHERE Data=@dataType
	) ValuesTable on MappingTable.MappingFundSectionKpiID=ValuesTable.MappingKpisID Order By ValuesKpiId ASC,Year asc,CONVERT(INT, Replace(Quarter,'Q', '')) asc , Month asc

	SELECT KpiId,ParentId,DisplayOrder,PortfolioCompanyID,KPI,KPIInfo,IsBoldKPI,IsHeader,KPIActualValue as KPIValue,Month,Quarter,Year,CompanyId,
	ValuesKpiId,IdentityId,MethodologyId,dbo.AuditLogFundKPIFunction(PortfolioCompanyID,IdentityId) ActualAuditLog
	FROM @TempTable_Match  
	WHERE Year IS NOT NULL  ORDER BY DisplayOrder,Year,CONVERT(INT, Replace(Quarter,'Q', '')),Month
END
GO
SET IDENTITY_INSERT [dbo].[M_SubPageDetails] ON 
BEGIN
IF NOT EXISTS (SELECT 1 FROM [dbo].[M_SubPageDetails] WHERE [SubPageID] = 45)
INSERT [dbo].[M_SubPageDetails] ([SubPageID], [Name], [AliasName], [PageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [IsDynamicFieldSupported], [IsDataType], [IsDragDrop], [IsFootNote]) VALUES (45, N'Fund Ingestion', N'Fund Ingestion', 2, NULL, 1, 0, CAST(N'2025-04-24T01:19:39.607' AS DateTime), 3, NULL, NULL, NULL, 5, NULL, 0, 1, 1, 1, 0)
END
SET IDENTITY_INSERT [dbo].[M_SubPageDetails] OFF
GO
SET IDENTITY_INSERT [dbo].[M_SubPageFields] ON 
IF NOT EXISTS (SELECT 1 FROM [dbo].[M_SubPageFields] WHERE [FieldID] = 20001)
BEGIN
INSERT [dbo].[M_SubPageFields] ([FieldID], [Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [isMandatory], [DataTypeId], [IsListData], [ShowOnList], [IsChart], [IsHighLight]) VALUES (20001, N'CapitalCalls', N'Capital Calls (from Commitment)', 45, NULL, 1, 0, CAST(N'2025-04-28T12:10:37.173' AS DateTime), 3, NULL, NULL, NULL, 1, NULL, 0, 0, 1, 0, 0, 0, 0)

INSERT [dbo].[M_SubPageFields] ([FieldID], [Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [isMandatory], [DataTypeId], [IsListData], [ShowOnList], [IsChart], [IsHighLight]) VALUES (20002, N'RecallableDistributions', N'Recallable Distributions', 45, NULL, 1, 0, CAST(N'2025-04-28T12:10:37.183' AS DateTime), 3, NULL, NULL, NULL, 2, NULL, 0, 0, 1, 0, 0, 0, 0)

INSERT [dbo].[M_SubPageFields] ([FieldID], [Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [isMandatory], [DataTypeId], [IsListData], [ShowOnList], [IsChart], [IsHighLight]) VALUES (20003, N'NonRecallableDistributions', N'Non-recallable Distributions', 45, NULL, 1, 0, CAST(N'2025-04-28T12:10:37.193' AS DateTime), 3, NULL, NULL, NULL, 3, NULL, 0, 0, 1, 0, 0, 0, 0)

INSERT [dbo].[M_SubPageFields] ([FieldID], [Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [isMandatory], [DataTypeId], [IsListData], [ShowOnList], [IsChart], [IsHighLight]) VALUES (20004, N'FundSize', N'Fund Size (since Inception)', 45, NULL, 1, 0, CAST(N'2025-04-28T12:10:37.203' AS DateTime), 3, NULL, NULL, NULL, 4, NULL, 0, 0, 1, 0, 0, 0, 0)

INSERT [dbo].[M_SubPageFields] ([FieldID], [Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [isMandatory], [DataTypeId], [IsListData], [ShowOnList], [IsChart], [IsHighLight]) VALUES (20005, N'InvestorCommitments', N'Investor Commitments (since Inception)', 45, NULL, 1, 0, CAST(N'2025-04-28T12:10:37.213' AS DateTime), 3, NULL, NULL, NULL, 5, NULL, 0, 0, 1, 0, 0, 0, 0)

INSERT [dbo].[M_SubPageFields] ([FieldID], [Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [isMandatory], [DataTypeId], [IsListData], [ShowOnList], [IsChart], [IsHighLight]) VALUES (20006, N'CashEquivalents', N'Cash / Cash Equivalents (since Inception)', 45, NULL, 1, 0, CAST(N'2025-04-28T12:10:37.227' AS DateTime), 3, NULL, NULL, NULL, 6, NULL, 0, 0, 1, 0, 0, 0, 0)

INSERT [dbo].[M_SubPageFields] ([FieldID], [Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [isMandatory], [DataTypeId], [IsListData], [ShowOnList], [IsChart], [IsHighLight]) VALUES (20007, N'NAV', N'NAV', 45, NULL, 1, 0, CAST(N'2025-04-28T12:10:37.237' AS DateTime), 3, NULL, NULL, NULL, 7, NULL, 0, 0, 1, 0, 0, 0, 0)

INSERT [dbo].[M_SubPageFields] ([FieldID], [Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [isMandatory], [DataTypeId], [IsListData], [ShowOnList], [IsChart], [IsHighLight]) VALUES (20008, N'ManagementFees', N'Management Fees', 45, NULL, 1, 0, CAST(N'2025-04-28T12:10:37.243' AS DateTime), 3, NULL, NULL, NULL, 8, NULL, 0, 0, 1, 0, 0, 0, 0)

INSERT [dbo].[M_SubPageFields] ([FieldID], [Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [isMandatory], [DataTypeId], [IsListData], [ShowOnList], [IsChart], [IsHighLight]) VALUES (20009, N'DividendIncome', N'Dividend Income', 45, NULL, 1, 0, CAST(N'2025-04-28T12:10:37.253' AS DateTime), 3, NULL, NULL, NULL, 9, NULL, 0, 0, 1, 0, 0, 0, 0)

INSERT [dbo].[M_SubPageFields] ([FieldID], [Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [isMandatory], [DataTypeId], [IsListData], [ShowOnList], [IsChart], [IsHighLight]) VALUES (20010, N'GrossIRR', N'gross IRR', 45, NULL, 1, 0, CAST(N'2025-04-28T12:10:37.267' AS DateTime), 3, NULL, NULL, NULL, 10, NULL, 0, 0, 1, 0, 0, 0, 0)

INSERT [dbo].[M_SubPageFields] ([FieldID], [Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [isMandatory], [DataTypeId], [IsListData], [ShowOnList], [IsChart], [IsHighLight]) VALUES (20011, N'RealizedGainsLosses', N'Realized Gains/Losses', 45, NULL, 1, 0, CAST(N'2025-04-28T12:10:37.273' AS DateTime), 3, NULL, NULL, NULL, 11, NULL, 0, 0, 1, 0, 0, 0, 0)

INSERT [dbo].[M_SubPageFields] ([FieldID], [Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [isMandatory], [DataTypeId], [IsListData], [ShowOnList], [IsChart], [IsHighLight]) VALUES (20012, N'UnrealizedGainsLosses', N'Unrealized Gains/Losses', 45, NULL, 1, 0, CAST(N'2025-04-28T12:10:37.283' AS DateTime), 3, NULL, NULL, NULL, 12, NULL, 0, 0, 1, 0, 0, 0, 0)

INSERT [dbo].[M_SubPageFields] ([FieldID], [Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [isMandatory], [DataTypeId], [IsListData], [ShowOnList], [IsChart], [IsHighLight]) VALUES (20013, N'InvestmentFundsFV', N'Investment (Fund)s FV', 45, NULL, 1, 0, CAST(N'2025-04-28T12:10:37.290' AS DateTime), 3, NULL, NULL, NULL, 13, NULL, 0, 0, 1, 0, 0, 0, 0)

INSERT [dbo].[M_SubPageFields] ([FieldID], [Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [isMandatory], [DataTypeId], [IsListData], [ShowOnList], [IsChart], [IsHighLight]) VALUES (20014, N'GrossTVPI', N'gross TVPI', 45, NULL, 1, 0, CAST(N'2025-04-28T12:10:37.303' AS DateTime), 3, NULL, NULL, NULL, 14, NULL, 0, 0, 1, 0, 0, 0, 0)

INSERT [dbo].[M_SubPageFields] ([FieldID], [Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [isMandatory], [DataTypeId], [IsListData], [ShowOnList], [IsChart], [IsHighLight]) VALUES (20015, N'InterestIncome', N'Interest Income', 45, NULL, 1, 0, CAST(N'2025-04-28T12:10:37.313' AS DateTime), 3, NULL, NULL, NULL, 15, NULL, 0, 0, 1, 0, 0, 0, 0)

INSERT [dbo].[M_SubPageFields] ([FieldID], [Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [isMandatory], [DataTypeId], [IsListData], [ShowOnList], [IsChart], [IsHighLight]) VALUES (20016, N'InvestmentFundCost', N'Investment (Fund) Cost', 45, NULL, 1, 0, CAST(N'2025-04-28T12:10:37.327' AS DateTime), 3, NULL, NULL, NULL, 16, NULL, 0, 0, 1, 0, 0, 0, 0)

INSERT [dbo].[M_SubPageFields] ([FieldID], [Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [isMandatory], [DataTypeId], [IsListData], [ShowOnList], [IsChart], [IsHighLight]) VALUES (20017, N'OtherIncome', N'Other Income (Upfront fees)', 45, NULL, 1, 0, CAST(N'2025-04-28T12:10:37.337' AS DateTime), 3, NULL, NULL, NULL, 17, NULL, 0, 0, 1, 0, 0, 0, 0)

INSERT [dbo].[M_SubPageFields] ([FieldID], [Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [isMandatory], [DataTypeId], [IsListData], [ShowOnList], [IsChart], [IsHighLight]) VALUES (20018, N'InterestExpense', N'Interest Expense', 45, NULL, 1, 0, CAST(N'2025-04-28T12:10:37.350' AS DateTime), 3, NULL, NULL, NULL, 18, NULL, 0, 0, 1, 0, 0, 0, 0)

INSERT [dbo].[M_SubPageFields] ([FieldID], [Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [isMandatory], [DataTypeId], [IsListData], [ShowOnList], [IsChart], [IsHighLight]) VALUES (20019, N'OrganizationCosts', N'Organization Costs', 45, NULL, 1, 0, CAST(N'2025-04-28T12:10:37.360' AS DateTime), 3, NULL, NULL, NULL, 19, NULL, 0, 0, 1, 0, 0, 0, 0)

INSERT [dbo].[M_SubPageFields] ([FieldID], [Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [isMandatory], [DataTypeId], [IsListData], [ShowOnList], [IsChart], [IsHighLight]) VALUES (20020, N'LiabilitiesLongTerm', N'Liabilities - Long Term/Leverage', 45, NULL, 1, 0, CAST(N'2025-04-28T12:10:37.373' AS DateTime), 3, NULL, NULL, NULL, 20, NULL, 0, 0, 1, 0, 0, 0, 0)

INSERT [dbo].[M_SubPageFields] ([FieldID], [Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy], [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [isMandatory], [DataTypeId], [IsListData], [ShowOnList], [IsChart], [IsHighLight]) VALUES (20021, N'LiabilitiesShortTerm', N'Liabilities - Short Term/Bridge', 45, NULL, 1, 0, CAST(N'2025-04-28T12:10:37.387' AS DateTime), 3, NULL, NULL, NULL, 21, NULL, 0, 0, 1, 0, 0, 0, 0)

END
SET IDENTITY_INSERT [dbo].[M_SubPageFields] OFF
GO
