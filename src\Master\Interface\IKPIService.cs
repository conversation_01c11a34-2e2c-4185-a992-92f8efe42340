﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Contract.PortfolioCompany;
using DataAccessLayer.DBModel;

namespace Contract.KPI
{
    public interface IKpiService
    {

        int AddOrUpdateKPI(KpiModel kpiModel, int userId);

        FinancialKpiListModel GetFinancialKPIList(FinancialKpiFilter filter);
        Dictionary<string, List<KpiTemplate>> GetFinancialKPIList(int portfolioCompanyId);
        Task<List<KpiMappingModel>> GetKPIMapping(int PortfolioCompanyId, string Type,int moduleID);
        Task<List<KpiMappingBasedOnModuleId>> GetKPIMapping(int PortfolioCompanyId);
        Task<List<KpiMappingModel>> GetUnMappedKpi(int portfolioCompanyId, string type,int moduleId);
        Task<bool> UpdateKPIMapping(int PortfolioCompanyId, string Type, List<KpiMappingModel> model, int userId,int moduleID);
        List<KpiTypeModel> GetKPITypes();
        ImpactKpiListModel GetImpactList();
        InvestmentKpiListModel GetInvestmentKPIDetails();
        List<KpiTemplate> GetInvestmentKPIList(int portfolioCompanyId);
        Task<List<KpiTemplate>> GetMasterKPIList(int portfolioCompanyId,string moduleName);
        List<KpiTemplate> GetImpactKPIList(int portfolioCompanyId);
        List<KpiTemplate> GetOperationalKPIList(int portfolioCompanyId);
        List<KpiTemplate> GetCompanyKPIList(int portfolioCompanyId);
        BalanceSheetKpiListModel GetBalancesheetKPIList();
        ProfitLossKpiListModel GetProfitLossKPIList();
        CashflowtKpiListModel GetCashflowKPIList();
        Task<KpiExcelDataModel> GetMappedTradingRecords(int fundId);
        Task<KpiExcelDataModel> GetMappedInvestmentKpis(int decryptedFundId, KpiExcelDataModel fundDataModels);
        Task<int> DeleteKPI(DeleteKpiModel kPIModel);
        Task<int> CreateKPI(DuplicateKpiModel duplicateKPI);
        Task<List<KpiFormulaModel>> GetKpisByType(string type, int CompanyId,int moduleId);
        Task<int> UpdateFormulaByKPIId(KpiFormula kpiFormula);
        Task<int> CopyKPIToCompanies(CopyToKpiQueryModel copyToKPIQueryModel);
        Task<KpiExcelDataModel> GetMappedOperationalKpis(int fundId, KpiExcelDataModel kpiExcelDataModel);
        List<CompanyKpiModel> GetCompanyKpi();
        List<SectorwiseKpiDetails> GetOperationalKpi();
        string? GetKpiFormula(KpiFormula formula);
        Task<int> UpdateCompLevelSynonyms(MappedKpisSynonymsModel mappedKpisSynonymData);
        Task<List<AllKpiMappingModel>> GetAllMappedKPIs(string companyIds);
    }
}