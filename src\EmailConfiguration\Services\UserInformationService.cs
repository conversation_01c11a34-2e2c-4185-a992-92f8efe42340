﻿using Contract.Account;
using DataAccessLayer.Models.DocumentCollection;
using DataAccessLayer.UnitOfWork;
using EmailConfiguration.DTOs;
using EmailConfiguration.Interfaces;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;


namespace EmailConfiguration.Services
{
    public class UserInformationService : IUserInformationService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<UserInformationService> _logger;        
        private readonly ICategoryService _categoryService;
        private const int PortFolioCompanyFeatureId = (int)Features.PortfolioCompany;

        /// <summary>
        /// Initializes a new instance of the <see cref="UserInformationService"/> class
        /// </summary>
        /// <param name="unitOfWork">Unit of work for database operations</param>
        /// <param name="logger">Logger for logging</param>
        public UserInformationService(IUnitOfWork unitOfWork, ILogger<UserInformationService> logger, ICategoryService categoryService)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));            
            _categoryService = categoryService;
        }        
        
        /// <summary>
        /// Creates a new user information record along with associated document types
        /// </summary>
        /// <param name="model">The data transfer object containing all user information details</param>
        /// <param name="userId">ID of the user creating this record</param>
        /// <returns>The ID of the newly created user information record</returns>
        public async Task<ResponseDto<int>> CreateUserInformationWithDocumentTypesAsync(CreateUserInformationDto model, int userId)
        {
            try
            {
                _logger.LogInformation($"Creating new user information record for email: {model.Email}");

                // Validate required parameters                
                if (string.IsNullOrWhiteSpace(model.Email))
                    return new ResponseDto<int> { IsSuccess = false, Message = "Email cannot be null or empty" };
                if (model.CategoryId <= 0)
                    return new ResponseDto<int> { IsSuccess = false, Message = "Category ID must be greater than zero" };
                if (model.DocumentTypeIds == null || !model.DocumentTypeIds.Any())
                    return new ResponseDto<int> { IsSuccess = false, Message = "At least one document type ID must be provided" };

                // Check for duplicate email and exact document type combination
                var existingUsers = await _unitOfWork.UserInfoRepository.GetManyAsync(ui =>
                    !ui.IsDeleted && ui.IsActive && ui.FeatureId == PortFolioCompanyFeatureId &&
                    ui.EntityID == model.EntityId && ui.Email == model.Email);

                if (existingUsers != null && existingUsers.Any())
                {
                    var existingUserIds = existingUsers.Select(u => u.UserInformationID).ToList();
                    var existingUserDocs = await _unitOfWork.UserDocumentsRepository.GetManyAsync(ud => existingUserIds.Contains(ud.UserInformationID));
                    // Group by UserInformationID to check for exact match
                    var docTypeIdSet = new HashSet<int>(model.DocumentTypeIds);
                    foreach (var userIdVal in existingUserIds)
                    {
                        var docsForUser = existingUserDocs.Where(ud => ud.UserInformationID == userIdVal).Select(ud => ud.DocumentTypeID).ToList();
                        if (docsForUser.Count == docTypeIdSet.Count && !docsForUser.Except(docTypeIdSet).Any() && !docTypeIdSet.Except(docsForUser).Any())
                        {
                            return new ResponseDto<int>
                            {
                                IsSuccess = false,
                                Message = "An identical email ID already exists with the same data document type."
                            };
                        }
                    }
                }

                // Create new UserInformation record
                var userInformation = new UserInformation
                {
                    Name = string.IsNullOrWhiteSpace(model.Name) ? "--" : model.Name,
                    Email = model.Email,
                    CategoryID = model.CategoryId,
                    FeatureId = PortFolioCompanyFeatureId,
                    Recipient = model.Recipient,
                    EntityID = model.EntityId,
                    IsActive = true,
                    CreatedBy = userId,
                    CreatedOn = DateTime.UtcNow,
                    IsDeleted = false
                };

                // Add the UserInformation record
                await _unitOfWork.UserInfoRepository.AddAsyn(userInformation);
                await _unitOfWork.SaveAsync();

                var userDocuments = model.DocumentTypeIds.Select(docTypeId => new User_Documents
                {
                    UserInformationID = userInformation.UserInformationID,
                    DocumentTypeID = docTypeId,
                    CreatedBy = userId,
                    CreatedOn = DateTime.UtcNow,

                }).ToList();

                // Add all User_Documents records
                await _unitOfWork.UserDocumentsRepository.AddBulkAsyn(userDocuments);
                await _unitOfWork.SaveAsync();

                _logger.LogInformation($"Successfully created UserInformation record with ID: {userInformation.UserInformationID} and {userDocuments.Count} associated document types");

                return new ResponseDto<int>
                {
                    IsSuccess = true,
                    Data = userInformation.UserInformationID,
                    Message = "You have successfully added User info"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error creating user information record: {ex.Message}");
                return new ResponseDto<int> { IsSuccess = false, Message = $"Error creating user information record: {ex.Message}" };
            }
        }

        /// <summary>
        /// Gets a list of user information records with their associated document type names
        /// </summary>
        /// <param name="companyId">The company ID to filter users by</param>
        /// <returns>List of user information records with document types</returns>
        public async Task<List<UserInformationResponseDto>> GetUserInformationWithDocumentTypesAsync(int companyId)
        {
            try
            {
                _logger.LogInformation($"Retrieving user information records for company ID: {companyId}");

                // Get all active user information records for the specified company
                var userInfoList = await _unitOfWork.UserInfoRepository
                    .GetManyAsync(ui => !ui.IsDeleted && ui.IsActive && ui.FeatureId == PortFolioCompanyFeatureId && ui.EntityID == companyId);

                // Get categories
                var categoryIds = userInfoList.Select(ui => ui.CategoryID).Distinct().ToList();
                var categories = await _unitOfWork.MUserCategoryRepository
                    .GetManyAsync(c => categoryIds.Contains(c.CategoryID) && !c.IsDeleted);

                // Get user documents with their document types
                var userInfoIds = userInfoList.Select(ui => ui.UserInformationID).ToList();
                var userDocuments = await _unitOfWork.UserDocumentsRepository
                    .GetManyAsync(ud => userInfoIds.Contains(ud.UserInformationID));

                // Get document types
                var documentTypeIds = userDocuments.Select(ud => ud.DocumentTypeID).Distinct().ToList();
                var documentTypes = await _unitOfWork.DataExtractionTypesRepository
                    .GetManyAsync(dt => documentTypeIds.Contains(dt.Id));

                // Combine the data 
                var result = userInfoList.Select(userInfo => new UserInformationResponseDto
                {
                    UserInformationID = userInfo.UserInformationID,
                    Name = userInfo.Name,
                    Email = userInfo.Email,
                    Category = categories.FirstOrDefault(c => c.CategoryID == userInfo.CategoryID)?.Category,
                    Recipient = userInfo.Recipient, 
                    DocumentTypes = userDocuments
                        .Where(ud => ud.UserInformationID == userInfo.UserInformationID)
                        .Select(ud => new DocumentTypeDto
                        {
                            DocumentTypeID = ud.DocumentTypeID,
                            DocumentName = documentTypes.FirstOrDefault(dt => dt.Id == ud.DocumentTypeID)?.DocumentName
                        })
                        .ToList()
                }).ToList();

                _logger.LogInformation($"Retrieved {result.Count} user information records");
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user information records");
                throw;
            }
        }        
        
        /// <summary>
        /// Gets a specific user information record by its ID with associated document types
        /// </summary>
        /// <param name="userInformationId">The ID of the user information record</param>
        /// <returns>The user information record with document types</returns>
        public async Task<UserInformationResponseDto> GetUserInformationByIdAsync(int userInformationId)
        {
            try
            {
                _logger.LogInformation($"Fetching user information for ID: {userInformationId}");
                
                // Get the user information record
                var userInfo = await _unitOfWork.UserInfoRepository
                    .FindFirstAsync(ui => ui.UserInformationID == userInformationId && !ui.IsDeleted);
                
                if (userInfo == null)
                {
                    _logger.LogWarning($"User information record with ID {userInformationId} not found");
                    return null;
                }
                
                // Get category information
                var category = await _unitOfWork.MUserCategoryRepository
                    .FindFirstAsync(c => c.CategoryID == userInfo.CategoryID && !c.IsDeleted);
                
                // Get associated document types for this user
                var userDocuments = await _unitOfWork.UserDocumentsRepository
                    .FindByAsyn(ud => ud.UserInformationID == userInformationId);
                
                // Get document type details
                var documentTypeIds = userDocuments.Select(ud => ud.DocumentTypeID).Distinct().ToList();
                var documentTypes = await _unitOfWork.DataExtractionTypesRepository
                    .FindByAsyn(dt => documentTypeIds.Contains(dt.Id) && !dt.IsDeleted);
                
                // Create and return response DTO
                var result = new UserInformationResponseDto
                {
                    UserInformationID = userInfo.UserInformationID,
                    Name = userInfo.Name,
                    Email = userInfo.Email,
                    Category = category?.Category ?? "--",
                    Recipient = userInfo.Recipient,
                    DocumentTypes = userDocuments.Select(ud => new DocumentTypeDto
                    {
                        DocumentTypeID = ud.DocumentTypeID,
                        DocumentName = documentTypes.FirstOrDefault(dt => dt.Id == ud.DocumentTypeID)?.DocumentName ?? "--"
                    }).ToList()
                };
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving user information for ID: {userInformationId}");
                throw;
            }
        }

        /// <summary>
        /// Updates an existing user information record along with associated document types
        /// </summary>
        /// <param name="model">The data transfer object containing updated user information details</param>
        /// <param name="userId">ID of the user updating this record</param>
        /// <returns>True if update was successful, false otherwise</returns>
        public async Task<ResponseDto<bool>> UpdateUserInformationAsync(UpdateUserInformationDto model, int userId)
        {
            try
            {
                _logger.LogInformation($"Updating user information for ID: {model.UserInformationID}");

                // Get existing user information
                var userInfo = await _unitOfWork.UserInfoRepository
                    .FindFirstAsync(ui => ui.UserInformationID == model.UserInformationID && !ui.IsDeleted);

                if (userInfo == null)
                {
                    _logger.LogWarning($"User information record with ID {model.UserInformationID} not found");
                    return new ResponseDto<bool>
                    {
                        IsSuccess = false,
                        Message = $"User information with ID {model.UserInformationID} not found",
                        Data = false
                    };
                }

                // Duplicate check: Exclude the current record from the check
                var existingUsers = await _unitOfWork.UserInfoRepository.GetManyAsync(ui =>
                    !ui.IsDeleted && ui.IsActive && ui.FeatureId == PortFolioCompanyFeatureId &&
                    ui.EntityID == model.EntityId && ui.Email == model.Email && ui.UserInformationID != model.UserInformationID);

                if (existingUsers != null && existingUsers.Any())
                {
                    var existingUserIds = existingUsers.Select(u => u.UserInformationID).ToList();
                    var existingUserDocs = await _unitOfWork.UserDocumentsRepository.GetManyAsync(ud => existingUserIds.Contains(ud.UserInformationID));
                    var docTypeIdSet = new HashSet<int>(model.DocumentTypeIds);
                    foreach (var userIdVal in existingUserIds)
                    {
                        var docsForUser = existingUserDocs.Where(ud => ud.UserInformationID == userIdVal).Select(ud => ud.DocumentTypeID).ToList();
                        if (docsForUser.Count == docTypeIdSet.Count && !docsForUser.Except(docTypeIdSet).Any() && !docTypeIdSet.Except(docsForUser).Any())
                        {
                            // Duplicate found, do not update
                            _logger.LogWarning("An identical email ID already exists with the same data document type.");
                            return new ResponseDto<bool>
                            {
                                IsSuccess = false,
                                Message = "An identical email ID already exists with the same data document type.",
                                Data = false
                            };
                        }
                    }
                }

                // Update user info fields
                userInfo.EntityID = model.EntityId;
                userInfo.Name = model.Name;
                userInfo.Email = model.Email;
                userInfo.CategoryID = model.CategoryId;
                userInfo.Recipient = model.Recipient;
                userInfo.ModifiedOn = DateTime.UtcNow;
                userInfo.ModifiedBy = userId;

                _unitOfWork.UserInfoRepository.Update(userInfo);

                // Get existing document types for this user
                var existingUserDocuments = await _unitOfWork.UserDocumentsRepository
                    .FindByAsyn(ud => ud.UserInformationID == model.UserInformationID);

                var existingDocTypeIds = existingUserDocuments.Select(ud => ud.DocumentTypeID).ToList();
                var docTypesToRemove = existingUserDocuments.Where(ud => !model.DocumentTypeIds.Contains(ud.DocumentTypeID)).ToList();
                var docTypeIdsToAdd = model.DocumentTypeIds.Where(id => !existingDocTypeIds.Contains(id)).ToList();

                // Delete document types that are no longer needed
                foreach (var docType in docTypesToRemove)
                {
                    _unitOfWork.UserDocumentsRepository.Delete(docType);
                }

                // Add new document types
                foreach (var docTypeId in docTypeIdsToAdd)
                {
                    var newUserDoc = new User_Documents
                    {
                        UserInformationID = model.UserInformationID,
                        DocumentTypeID = docTypeId,
                        CreatedBy = userId,
                        CreatedOn = DateTime.UtcNow
                    };
                    await _unitOfWork.UserDocumentsRepository.AddAsyn(newUserDoc);
                }

                await _unitOfWork.SaveAsync();

                _logger.LogInformation($"Successfully updated user information for ID: {model.UserInformationID}");
                return new ResponseDto<bool>
                {
                    IsSuccess = true,
                    Message = "User information updated successfully",
                    Data = true
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error updating user information for ID: {model.UserInformationID}");
                return new ResponseDto<bool>
                {
                    IsSuccess = false,
                    Message = $"Error updating user information: {ex.Message}",
                    Data = false
                };
            }
        }
        public async Task<CategoryAndDocumentTypeDto> GetCategoriesAndDocumentTypes(int companyId) {
            var categories = await _categoryService.GetCategories();
            var configuredDocTypes = await GetConfiguredDocumentTypes(companyId);

            return new CategoryAndDocumentTypeDto { 
                Categories = categories,
                DocumentTypes = configuredDocTypes
            };
        }

        public async Task<List<DocumentTypeDto>> GetConfiguredDocumentTypes(int companyId)
        {
            // Get all document types that are not deleted
            IEnumerable<DocCollectionFrequencyConfig> docConfigs = await _unitOfWork.DocCollectionConfigRepository.FindAllAsync(x => !x.IsDeleted && x.FeatureId == PortFolioCompanyFeatureId && x.EntityId == companyId);

            if (!docConfigs.Any())
            {
                return new List<DocumentTypeDto>();
            }

            var docTypeIds = docConfigs.Select(x => x.DocTypeID).Distinct().ToList();

                    var documentTypes = await _unitOfWork.DataExtractionTypesRepository.FindAllAsync(
            x => !x.IsDeleted && docTypeIds.Contains(x.Id));

             return documentTypes.Select(dt => new DocumentTypeDto
            {
                DocumentTypeID = dt.Id,
                DocumentName = dt.DocumentName
            }).ToList();
        }

        /// <summary>
        /// Deletes a user and its related document mappings by user information ID
        /// </summary>
        /// <param name="userInformationId">The ID of the user information to delete</param>
        /// <param name="userId">The ID of the user performing the delete</param>
        /// <returns>Status of the delete operation</returns>
        public async Task<ResponseDto<bool>> DeleteUserInformationAsync(int userInformationId, int userId)
        {
            try
            {
                _logger.LogInformation($"Deleting user information for ID: {userInformationId}");

                // Get the user information record
                return await DeleteUserInformation(userInformationId, userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting user information for ID: {userInformationId}");
                return new ResponseDto<bool>
                {
                    IsSuccess = false,
                    Message = $"Error deleting user information: {ex.Message}",
                    Data = false
                };
            }
        }

        private async Task<ResponseDto<bool>> DeleteUserInformation(int userInformationId, int userId)
        {
            var userInfo = await _unitOfWork.UserInfoRepository.FindFirstAsync(ui => ui.UserInformationID == userInformationId && !ui.IsDeleted);
            if (userInfo == null)
            {
                _logger.LogWarning($"User information record with ID {userInformationId} not found");
                return new ResponseDto<bool>
                {
                    IsSuccess = false,
                    Message = $"User information with ID {userInformationId} not found",
                    Data = false
                };
            }

            // Mark user info as deleted (soft delete)
            userInfo.IsDeleted = true;
            userInfo.ModifiedBy = userId;
            userInfo.ModifiedOn = DateTime.UtcNow;
            _unitOfWork.UserInfoRepository.Update(userInfo);

            // Delete all related user-document mappings
            await DeleteUserDocuments(userInformationId);

            await _unitOfWork.SaveAsync();

            _logger.LogInformation($"Successfully deleted user information and related mappings for ID: {userInformationId}");
            return new ResponseDto<bool>
            {
                IsSuccess = true,
                Message = "User information deleted successfully",
                Data = true
            };
        }

        private async Task DeleteUserDocuments(int userInformationId)
        {
            var userDocuments = await _unitOfWork.UserDocumentsRepository.FindByAsyn(ud => ud.UserInformationID == userInformationId);
            foreach (var doc in userDocuments)
            {
                _unitOfWork.UserDocumentsRepository.Delete(doc);
            }
        }
    }
}
