﻿using Contract.Filters;
using Contract.PortfolioCompany;
using DataAccessLayer.DBModel;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;

namespace Contract.KPI
{
    [ExcludeFromCodeCoverage]
    public class KpiModel
    {
        [MaxLength(15)]
        public string KPIType { get; set; }
        public int KpiTypeId { get; set; }
        [SwaggerExclude]
        public FinancialKpiDetails FinancialKpiDetails { get; set; }
        public SectorwiseKpiDetails OperationalKpiDetails { get; set; }
        [SwaggerExclude]
        public M_CompanyKpi CompanyKPIDetails { get; set; }
        [SwaggerExclude]
        public MImpactKpi ImpactKpiDetails { get; set; }
        [SwaggerExclude]
        public M_InvestmentKpi InvestmentKpiDetails { get; set; }
        [SwaggerExclude]
        public M_BalanceSheet_LineItems BalanceSheet_LineItemsDetails { get; set; }
        [SwaggerExclude]
        public M_ProfitAndLoss_LineItems ProfitAndLoss_LineItemDetails { get; set; }
        [SwaggerExclude]
        public M_CashFlow_LineItems CashFlow_LineItemsDetails { get; set; }
        [SwaggerExclude]
        public M_MasterKpis MasterKpiDetails { get; set; }
        public string Synonym { get; set; }

    }
    public class DuplicateKpiModel
    {
        public string KPIType { get; set; }
        [Required]
        public int Id { get; set; }
        public int? UserId { get; set; }
        public int? ModuleId { get; set; }
        public int? SubPageId { get; set; }
    }
    public class KpiQueryModel
    {
        public int KPIId { get; set; }
        public string KPIInfo { get; set; }
        public string KPI { get; set; }
    }
    public class MappedKpisSynonymsModel : BaseModel
    {
        public int ModuleId { get; set; }
        public int MappingId { get; set; }
        public string Synonym { get; set; }
        public bool IsFundKpi { get; set; }
    }
}
