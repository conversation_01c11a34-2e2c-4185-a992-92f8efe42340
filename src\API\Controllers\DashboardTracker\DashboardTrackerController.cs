using API.Helpers;
using Contract.PortfolioCompany;
using DocumentCollection.DashboardTracker.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace API.Controllers.DashboardTracker
{
    [Route("api/")]
    [ApiController]
    [Authorize(Policy = AppSettings.DefaultAuthPolicy)]
    public class DashboardTrackerController : ControllerBase
    {
        private readonly IDashboardTrackerService _dashboardTrackerService;
        private readonly IHelperService _helperService;
        public DashboardTrackerController(IDashboardTrackerService dashboardTrackerService, IHelperService helperService)
        {
            _dashboardTrackerService = dashboardTrackerService;
            _helperService = helperService;
        }

        [HttpGet("dashboard-tracker/get")]
        public async Task<IActionResult> GetCompanies()
        {
     
            var result = await _dashboardTrackerService.GetPortfolioCompanies(
                new PortfolioCompanyFilter
                {
                    CreatedBy = _helperService.GetCurrentUserId(User)
                });

            return Ok(result);
        }
    }
}
