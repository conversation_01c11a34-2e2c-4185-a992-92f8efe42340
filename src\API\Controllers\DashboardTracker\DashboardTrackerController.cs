using API.Helpers;
using Contract.PortfolioCompany;
using DocumentCollection.DashboardTracker.DTOs;
using DocumentCollection.DashboardTracker.Interfaces;
using DocumentCollection.DashboardTracker.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Net;
using System.Threading.Tasks;
using Utility.Resource;

namespace API.Controllers.DashboardTracker
{
    [Route("api/")]
    [ApiController]
    [Authorize(Policy = AppSettings.DefaultAuthPolicy)]
    public class DashboardTrackerController : ControllerBase
    {
        private readonly IDashboardTrackerService _dashboardTrackerService;
        private readonly IHelperService _helperService;
        public DashboardTrackerController(IDashboardTrackerService dashboardTrackerService, IHelperService helperService)
        {
            _dashboardTrackerService = dashboardTrackerService;
            _helperService = helperService;
        }

        [HttpGet("dashboard-tracker/get")]
        public async Task<IActionResult> GetCompanies()
        {

            var result = await _dashboardTrackerService.GetPortfolioCompanies(
                new PortfolioCompanyFilter
                {
                    CreatedBy = _helperService.GetCurrentUserId(User)
                });

            return Ok(result);
        }

        [HttpPost("dashboard-tracker/config/save")]
        public async Task<IActionResult> SaveDashboardTrackerConfig([FromBody] DashboardTrackerConfigDto dto)
        {
            if (!ModelState.IsValid)
            {
                return JsonResponse.Create(HttpStatusCode.InternalServerError, Messages.SomethingWentWrong);
            }

            try
            {
                int userId = _helperService.GetCurrentUserId(User);

                // Set audit fields based on whether it's an insert or update
                if (dto.ID.HasValue && dto.ID.Value > 0)
                {
                    dto.ModifiedBy = userId;
                    dto.ModifiedOn = DateTime.Now;
                }
                else
                {
                    dto.CreatedBy = userId;
                    dto.CreatedOn = DateTime.Now;
                }

                int result = await _dashboardTrackerService.SaveDashboardTrackerConfigAsync(dto);

                if (result > 0)
                {
                    string message = dto.ID.HasValue && dto.ID.Value > 0
                        ? "Dashboard tracker configuration updated successfully"
                        : "Dashboard tracker configuration added successfully";
                    return JsonResponse.Create(HttpStatusCode.OK, message, new { id = result });
                }
                else
                {
                    return JsonResponse.Create(HttpStatusCode.InternalServerError, Messages.SomethingWentWrong);
                }
            }
            catch (Exception ex)
            {
                // Log the exception here if you have logging configured
                return JsonResponse.Create(HttpStatusCode.InternalServerError, Messages.SomethingWentWrong);
            }
        }
    }
}
