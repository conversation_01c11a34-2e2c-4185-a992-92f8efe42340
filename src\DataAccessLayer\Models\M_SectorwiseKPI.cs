﻿using System.Collections.Generic;
using DataAccessLayer.Models;
using DataAccessLayer.Models.Workflow;

namespace DataAccessLayer.DBModel
{
    public partial class M_SectorwiseKPI : BaseModel
    {
        public M_SectorwiseKPI()
        {
            PortfolioCompanyOperationalKPIValues = new HashSet<PortfolioCompanyOperationalKPIValue>();
            PortfolioCompanyOperationalKPIValuesDraft = new HashSet<PortfolioCompanyOperationalKPIValuesDraft>();
        }

        public int SectorwiseOperationalKPIID { get; set; }
        public int SectorId { get; set; }
        public int MethodologyId { get; set; }
        public string Kpi { get; set; }
        public string Description { get; set; }
        public bool IsDeleted { get; set; }
        public string EncryptedSectorwiseKPIID { get; set; }

        public string KpiInfo { get; set; }
        public string Formula { get; set; }
        public string FormulaKPIId { get; set; }
        public bool IsHeader { get; set; }
        public bool IsBoldKPI { get; set; }
        public string Synonym { get; set; }
        public virtual M_Sector Sector { get; set; }
        public virtual ICollection<PortfolioCompanyOperationalKPIValue> PortfolioCompanyOperationalKPIValues { get; set; }
        public virtual ICollection<PortfolioCompanyOperationalKPIValuesDraft> PortfolioCompanyOperationalKPIValuesDraft { get; set; }
    }
}