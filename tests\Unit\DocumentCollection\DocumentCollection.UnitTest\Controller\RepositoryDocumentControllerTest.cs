using API.Controllers.DocumentCollection;
using API.Helpers;
using Contract.Utility;
using DocumentCollection.DTOs;
using DocumentCollection.Interfaces;
using DocumentCollection.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;

namespace Controller
{
    public class RepositoryDocumentControllerTest
    {
        private readonly Mock<IDocumentRepositoryService> _mockRepositoryService;
        private readonly Mock<IRepositoryConfigurationService> _mockConfigService;
        private readonly Mock<IInjectedParameters> _mockInjectedParameters;
        private readonly Mock<IHelperService> _mockHelperService;
        private readonly Mock<ILogger<RepositoryDocumentController>> _mockLogger;
        private readonly RepositoryDocumentController _controller;
        private const int Feature_Id = 14;

        public RepositoryDocumentControllerTest()
        {
            _mockRepositoryService = new Mock<IDocumentRepositoryService>();
            _mockConfigService = new Mock<IRepositoryConfigurationService>();
            _mockInjectedParameters = new Mock<IInjectedParameters>();
            _mockHelperService = new Mock<IHelperService>();
            _mockLogger = new Mock<ILogger<RepositoryDocumentController>>();
            _controller = new RepositoryDocumentController(_mockRepositoryService.Object, _mockConfigService.Object, _mockInjectedParameters.Object, _mockHelperService.Object, _mockLogger.Object);
        }

        [Fact]
        public async Task GetFolderConfig_ValidCompanyId_ReturnsOkResult()
        {
            // Arrange
            string encryptedCompanyId = "encryptedId";
            string decryptedCompanyId = "1";
            var featureId = Feature_Id;
            int companyId = 1;
            var responseDto = new ResponseDto<List<RepositoryTreeModel>> { IsSuccess = true, Data = new List<RepositoryTreeModel>() };
            _mockInjectedParameters.Setup(p => p.Encryption.Decrypt(encryptedCompanyId)).Returns(decryptedCompanyId);
            _mockRepositoryService.Setup(s => s.GetRepositoryData(companyId, featureId)).ReturnsAsync(responseDto);

            // Act
            var result = await _controller.GetFolderConfig(encryptedCompanyId, featureId);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.Equal(responseDto, okResult.Value);
        }

        [Fact]
        public async Task GetFolderConfig_InvalidCompanyId_ReturnsBadRequest()
        {
            // Arrange
            string encryptedCompanyId = "invalidId";
            string decryptedCompanyId = "";
            var featureId = Feature_Id;
            _mockInjectedParameters.Setup(p => p.Encryption.Decrypt(encryptedCompanyId)).Returns(decryptedCompanyId);

            // Act
            var result = await _controller.GetFolderConfig(encryptedCompanyId, featureId);

            // Assert
            Assert.IsType<BadRequestResult>(result);
        }

        [Fact]
        public async Task GetFolderConfig_ValidCompanyIdWithConfigData_ReturnsOkResult()
        {
            // Arrange
            int companyId = 1;
            var featureId = Feature_Id;
            _mockInjectedParameters.Setup(p => p.Encryption.Decrypt(companyId.ToString())).Returns(companyId.ToString());

            List<DocumentConfigurationDto> configdata = new List<DocumentConfigurationDto>
            {
                new DocumentConfigurationDto(){ DoctypeID = 1, DoctypeName ="Type 1" },
                new DocumentConfigurationDto(){ DoctypeID = 2, DoctypeName ="Type 2" },
            };
            _mockConfigService.Setup(s => s.GetRepositoryConfigurationDataByCompany(companyId)).ReturnsAsync(configdata);

            var responseDto = new ResponseDto<List<RepositoryTreeModel>> { IsSuccess = true };
            List<RepositoryTreeModel> folderdata = new List<RepositoryTreeModel>
            {
                new RepositoryTreeModel { Name = "Type 1", Path = "1" },
                new RepositoryTreeModel { Name = "Type 2", Path = "2" },
            };
            responseDto.Data = folderdata;
            _mockRepositoryService.Setup(s => s.GetRepositoryData(companyId, featureId)).ReturnsAsync(responseDto);

            // Act
            var result = await _controller.GetFolderConfig(companyId.ToString(), featureId);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var response = okResult.Value as ResponseDto<List<RepositoryTreeModel>>;
            var resultdata = response?.Data;
            Assert.Equal(responseDto, okResult.Value);
            Assert.Equal(true, response?.IsSuccess);
            Assert.Equal(2, resultdata?.Count);
            Assert.Equal(configdata[0].DoctypeName, resultdata?.First().Name);
            Assert.Equal(configdata[0].DoctypeID.ToString(), resultdata?.First().Path);
            Assert.Equal(configdata[1].DoctypeName, resultdata?.Last().Name);
            Assert.Equal(configdata[1].DoctypeID.ToString(), resultdata?.Last().Path);
        }

        [Fact]
        public async Task DeleteDocuments_ValidCompanyIdAndDocumentIds_ReturnsOkResult()
        {
            // Arrange
            string encryptedCompanyId = "encryptedId";
            string decryptedCompanyId = "1";
            int companyId = 1;
            int userId = 5;
            var deleteRequest = new DocumentDeleteRequestDto
            {
                DocumentIds = new string[] { "doc1", "doc2" },
                Path = "1/2023"
            };

            var responseDto = new ResponseDto<string> { IsSuccess = true, Message = "Document Deleted Successfully" };
            
            _mockInjectedParameters.Setup(p => p.Encryption.Decrypt(encryptedCompanyId)).Returns(decryptedCompanyId);
            _mockHelperService.Setup(h => h.GetCurrentUserId(It.IsAny<System.Security.Claims.ClaimsPrincipal>())).Returns(userId);
            _mockRepositoryService.Setup(s => s.DeleteDocuments(companyId, It.Is<DocumentDeleteRequestDto>(r => 
                r.DocumentIds == deleteRequest.DocumentIds && 
                r.ModifiedBy == userId))).ReturnsAsync(responseDto);

            // Act
            var result = await _controller.DeleteDocuments(encryptedCompanyId, deleteRequest);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.Equal(responseDto, okResult.Value);
            Assert.Equal(userId, deleteRequest.ModifiedBy);
        }

        [Fact]
        public async Task DeleteDocuments_InvalidCompanyId_ReturnsBadRequest()
        {
            // Arrange
            string encryptedCompanyId = "invalidId";
            string decryptedCompanyId = "";
            var deleteRequest = new DocumentDeleteRequestDto
            {
                DocumentIds = new string[] { "doc1", "doc2" },
                Path = "1/2023"
            };
            
            _mockInjectedParameters.Setup(p => p.Encryption.Decrypt(encryptedCompanyId)).Returns(decryptedCompanyId);

            // Act
            var result = await _controller.DeleteDocuments(encryptedCompanyId, deleteRequest);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);
            var badRequestResult = result as BadRequestObjectResult;
            Assert.Equal("Invalid company ID", badRequestResult.Value);
        }

        [Fact]
        public async Task DeleteDocuments_NullDocumentIds_ReturnsBadRequest()
        {
            // Arrange
            string encryptedCompanyId = "encryptedId";
            string decryptedCompanyId = "1";
            var deleteRequest = new DocumentDeleteRequestDto
            {
                DocumentIds = null,
                Path = "1/2023"
            };
            
            _mockInjectedParameters.Setup(p => p.Encryption.Decrypt(encryptedCompanyId)).Returns(decryptedCompanyId);

            // Act
            var result = await _controller.DeleteDocuments(encryptedCompanyId, deleteRequest);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);
            var badRequestResult = result as BadRequestObjectResult;
            Assert.Equal("No documents specified for deletion", badRequestResult.Value);
        }

        [Fact]
        public async Task DeleteDocuments_EmptyDocumentIds_ReturnsBadRequest()
        {
            // Arrange
            string encryptedCompanyId = "encryptedId";
            string decryptedCompanyId = "1";
            var deleteRequest = new DocumentDeleteRequestDto
            {
                DocumentIds = new string[0],
                Path = "1/2023"
            };
            
            _mockInjectedParameters.Setup(p => p.Encryption.Decrypt(encryptedCompanyId)).Returns(decryptedCompanyId);

            // Act
            var result = await _controller.DeleteDocuments(encryptedCompanyId, deleteRequest);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);
            var badRequestResult = result as BadRequestObjectResult;
            Assert.Equal("No documents specified for deletion", badRequestResult.Value);
        }

        [Fact]
        public async Task DeleteDocuments_InvalidModelState_ReturnsBadRequest()
        {
            // Arrange
            string encryptedCompanyId = "encryptedId";
            var deleteRequest = new DocumentDeleteRequestDto
            {
                DocumentIds = new string[] { "doc1", "doc2" },
                Path = "1/2023"
            };
            
            _controller.ModelState.AddModelError("DocumentIds", "Required");

            // Act
            var result = await _controller.DeleteDocuments(encryptedCompanyId, deleteRequest);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);
        }

        [Fact]
        public async Task DeleteDocuments_ValidRequest_SetsModifiedByAndCallsService()
        {
            // Arrange
            string encryptedCompanyId = "encryptedId";
            string decryptedCompanyId = "1";
            int companyId = 1;
            int userId = 10;
            var deleteRequest = new DocumentDeleteRequestDto
            {
                DocumentIds = new string[] { "doc1", "doc2" },
                Path = "1/2023",
                ModifiedBy = 0 // Should be overwritten
            };

            var responseDto = new ResponseDto<string> { IsSuccess = true, Message = "Document Deleted Successfully" };
            
            _mockInjectedParameters.Setup(p => p.Encryption.Decrypt(encryptedCompanyId)).Returns(decryptedCompanyId);
            _mockHelperService.Setup(h => h.GetCurrentUserId(It.IsAny<System.Security.Claims.ClaimsPrincipal>())).Returns(userId);
            _mockRepositoryService.Setup(s => s.DeleteDocuments(companyId, It.IsAny<DocumentDeleteRequestDto>()))
                .ReturnsAsync(responseDto);

            // Act
            await _controller.DeleteDocuments(encryptedCompanyId, deleteRequest);

            // Assert
            Assert.Equal(userId, deleteRequest.ModifiedBy);
            _mockRepositoryService.Verify(s => s.DeleteDocuments(companyId, It.Is<DocumentDeleteRequestDto>(
                req => req.ModifiedBy == userId && req.DocumentIds.Length == 2)), Times.Once);
        }
        [Fact]
        public async Task DownloadDocument_ValidRequest_ReturnsFileResult()
        {
            // Arrange
            string encryptedCompanyId = "encryptedId";
            string decryptedCompanyId = "1";
            int companyId = 1;
            string documentId = "doc-guid";
            string folderPath = "folder/path";
            var fileContent = new byte[] { 1, 2, 3 };
            var memoryStream = new MemoryStream(fileContent);
            var downloadResult = new DownloadDocumentResult
            {
                IsSuccess = true,
                FileStream = memoryStream,
                FileName = "test.pdf",
                ContentType = "application/pdf"
            };

            _mockInjectedParameters.Setup(p => p.Encryption.Decrypt(encryptedCompanyId)).Returns(decryptedCompanyId);
            _mockRepositoryService.Setup(s => s.DownloadDocument(companyId, documentId, folderPath)).ReturnsAsync(downloadResult);

            // Act
            var result = await _controller.DownloadDocument(encryptedCompanyId, documentId, folderPath);

            // Assert
            var fileResult = Assert.IsType<FileStreamResult>(result);
            Assert.Equal("application/pdf", fileResult.ContentType);
            Assert.Equal("test.pdf", fileResult.FileDownloadName);
        }

        [Fact]
        public async Task DownloadDocument_InvalidModelState_ReturnsBadRequest()
        {
            // Arrange
            string encryptedCompanyId = "encryptedId";
            string documentId = "doc-guid";
            string folderPath = "folder/path";
            _controller.ModelState.AddModelError("Test", "Error");

            // Act
            var result = await _controller.DownloadDocument(encryptedCompanyId, documentId, folderPath);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);
        }

        [Fact]
        public async Task DownloadDocument_InvalidCompanyId_ReturnsBadRequest()
        {
            // Arrange
            string encryptedCompanyId = "invalidId";
            string decryptedCompanyId = "";
            string documentId = "doc-guid";
            string folderPath = "folder/path";
            _mockInjectedParameters.Setup(p => p.Encryption.Decrypt(encryptedCompanyId)).Returns(decryptedCompanyId);

            // Act
            var result = await _controller.DownloadDocument(encryptedCompanyId, documentId, folderPath);

            // Assert
            var badRequest = Assert.IsType<BadRequestObjectResult>(result);
            Assert.Equal("Invalid company ID", badRequest.Value);
        }

        [Fact]
        public async Task DownloadDocument_EmptyDocumentId_ReturnsBadRequest()
        {
            // Arrange
            string encryptedCompanyId = "encryptedId";
            string decryptedCompanyId = "1";
            string documentId = "";
            string folderPath = "folder/path";
            _mockInjectedParameters.Setup(p => p.Encryption.Decrypt(encryptedCompanyId)).Returns(decryptedCompanyId);

            // Act
            var result = await _controller.DownloadDocument(encryptedCompanyId, documentId, folderPath);

            // Assert
            var badRequest = Assert.IsType<BadRequestObjectResult>(result);
            Assert.Equal("Invalid document ID", badRequest.Value);
        }

        [Fact]
        public async Task DownloadDocument_FileNotFound_ReturnsNotFound()
        {
            // Arrange
            string encryptedCompanyId = "encryptedId";
            string decryptedCompanyId = "1";
            int companyId = 1;
            string documentId = "doc-guid";
            string folderPath = "folder/path";
            var downloadResult = new DownloadDocumentResult
            {
                IsSuccess = false,
                FileStream = null
            };

            _mockInjectedParameters.Setup(p => p.Encryption.Decrypt(encryptedCompanyId)).Returns(decryptedCompanyId);
            _mockRepositoryService.Setup(s => s.DownloadDocument(companyId, documentId, folderPath)).ReturnsAsync(downloadResult);

            // Act
            var result = await _controller.DownloadDocument(encryptedCompanyId, documentId, folderPath);

            // Assert
            var notFound = Assert.IsType<NotFoundObjectResult>(result);
            Assert.Equal("File not found or could not be downloaded.", notFound.Value);
        }
    }
}