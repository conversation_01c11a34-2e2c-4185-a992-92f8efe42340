﻿using Audit.Enums;
using Contract.Deals;
using Contract.KPI;
using Contract.PortfolioCompany;
using Contract.Utility;
using Dapper;
using DapperRepository;
using DapperRepository.Constants;
using DataAccessLayer.DBModel;
using DataAccessLayer.Models;
using DataAccessLayer.Models.MonthlyReport;
using DataAccessLayer.UnitOfWork;
using Shared;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Utility.Functions;
using Utility.Functions.FormulaHelper;
using Utility.Helpers;

namespace Master;
public class KpiService : IKpiService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IEncryption _encryption;
    private readonly IMemoryCacher _memoryCacher;
    private readonly IGlobalConfigurations _globalConfig;
    private const string _allKPITypesToken = "allKPITypes";
    public readonly IDapperGenericRepository _dapperGenericRepository;

    public KpiService(IDapperGenericRepository dapperGenericRepository, IUnitOfWork unitOfWork, IEncryption encryption, IMemoryCacher memoryCacher, IGlobalConfigurations globalConfig)
    {
        _unitOfWork = unitOfWork;
        _encryption = encryption;
        _memoryCacher = memoryCacher;
        _globalConfig = globalConfig;
        _dapperGenericRepository = dapperGenericRepository;
    }

    #region Add KPI

    public int AddOrUpdateKPI(KpiModel kpiModel, int userId)
    {
        Enum.TryParse(kpiModel.KPIType, out KpiTypeFeature KpiTypeFeature);
        switch (KpiTypeFeature)
        {
            case KpiTypeFeature.Operational:
                if (kpiModel.OperationalKpiDetails.SectorwiseKPIID > 0)
                {
                    kpiModel.OperationalKpiDetails.ModifiedBy = userId;
                    UpdateOperationalKpiDetails(kpiModel);
                }
                else
                {
                    if (_unitOfWork.M_SectorwiseKPIRepository.ExistsAny(x => x.Kpi.ToLower() == kpiModel.OperationalKpiDetails.KPI.ToLower() && !x.IsDeleted))
                        return -1;
                    else
                    {
                        kpiModel.OperationalKpiDetails.CreatedBy = userId;
                        AddOperationalKPI(kpiModel);
                    }
                }
                break;

            case KpiTypeFeature.Company:

                if (kpiModel.CompanyKPIDetails.CompanywiseKPIID > 0)
                {
                    kpiModel.CompanyKPIDetails.ModifiedBy = userId;
                    UpdateCompanyKpiDetails(kpiModel);
                }
                else
                {
                    if (_unitOfWork.M_CompanyKPIRepository.ExistsAny(x => x.Kpi.ToLower() == kpiModel.CompanyKPIDetails.Kpi.ToLower() && !x.IsDeleted))
                        return -1;
                    else
                    {
                        kpiModel.CompanyKPIDetails.CreatedBy = userId;
                        AddCompanyKPI(kpiModel);
                    }
                }
                break;

            case KpiTypeFeature.Investment:
                if (kpiModel.InvestmentKpiDetails.InvestmentKpiId > 0)
                {
                    kpiModel.InvestmentKpiDetails.ModifiedBy = userId;
                    UpdateInvestmentKpiDetails(kpiModel);
                }
                else
                {
                    if (_unitOfWork.M_InvestmentKPIRepository.ExistsAny(x => x.Kpi.ToLower() == kpiModel.InvestmentKpiDetails.Kpi.ToLower() && !x.IsDeleted))
                        return -1;
                    else
                    {
                        kpiModel.InvestmentKpiDetails.CreatedBy = userId;
                        AddInvestmentKPI(kpiModel);
                    }
                }
                break;

            case KpiTypeFeature.Impact:
                if (kpiModel.ImpactKpiDetails.ImpactKpiId > 0)
                {
                    kpiModel.ImpactKpiDetails.ModifiedBy = userId;
                    UpdateImpactKpiDetails(kpiModel);
                }
                else
                {
                    if (_unitOfWork.M_ImpactKPIRepository.ExistsAny(x => x.Kpi.ToLower() == kpiModel.ImpactKpiDetails.Kpi.ToLower() && !x.IsDeleted))
                        return -1;
                    else
                    {
                        kpiModel.ImpactKpiDetails.CreatedBy = userId;
                        AddImpactKPI(kpiModel);
                    }
                }
                break;

            case KpiTypeFeature.BalanceSheet:
                if (kpiModel.BalanceSheet_LineItemsDetails.BalanceSheetLineItemID > 0)
                {
                    kpiModel.BalanceSheet_LineItemsDetails.ModifiedBy = userId;
                    UpdateBalanceSheetKpiDetails(kpiModel);
                }
                else
                {
                    if (_unitOfWork.M_BalanceSheet_LineItemsRepository.ExistsAny(x => x.BalanceSheetLineItemID != kpiModel.BalanceSheet_LineItemsDetails.BalanceSheetLineItemID && x.BalanceSheetLineItem.ToLower() == kpiModel.BalanceSheet_LineItemsDetails.BalanceSheetLineItem.ToLower() && !x.IsDeleted))
                        return -1;
                    else
                    {
                        kpiModel.BalanceSheet_LineItemsDetails.CreatedBy = userId;
                        AddBalanceSheetKPI(kpiModel);
                    }
                }
                break;

            case KpiTypeFeature.ProfitAndLoss:
                if (kpiModel.ProfitAndLoss_LineItemDetails.ProfitAndLossLineItemID > 0)
                {
                    kpiModel.ProfitAndLoss_LineItemDetails.ModifiedBy = userId;
                    UpdateProfitAndLossKpiDetails(kpiModel);
                }
                else
                {
                    if (_unitOfWork.M_ProfitAndLoss_LineItemsRepository.ExistsAny(x => x.ProfitAndLossLineItemID != kpiModel.ProfitAndLoss_LineItemDetails.ProfitAndLossLineItemID && x.ProfitAndLossLineItem.ToLower() == kpiModel.ProfitAndLoss_LineItemDetails.ProfitAndLossLineItem.ToLower() && !x.IsDeleted))
                        return -1;
                    else
                    {
                        kpiModel.ProfitAndLoss_LineItemDetails.CreatedBy = userId;
                        AddProfitAndLossKPI(kpiModel);
                    }
                }
                break;

            case KpiTypeFeature.CashFlow:
                if (kpiModel.CashFlow_LineItemsDetails.CashFlowLineItemID > 0)
                {
                    kpiModel.CashFlow_LineItemsDetails.ModifiedBy = userId;
                    UpdateCashflowKpiDetails(kpiModel);
                }
                else
                {
                    if (_unitOfWork.M_CashFlow_LineItemsRepository.ExistsAny(x => x.CashFlowLineItemID != kpiModel.CashFlow_LineItemsDetails.CashFlowLineItemID && x.CashFlowLineItem.ToLower() == kpiModel.CashFlow_LineItemsDetails.CashFlowLineItem.ToLower() && !x.IsDeleted))
                        return -1;
                    else
                    {
                        kpiModel.CashFlow_LineItemsDetails.CreatedBy = userId;
                        AddCashflowKPI(kpiModel);
                    }
                }
                break;

            case KpiTypeFeature.TradingRecords:
                if (kpiModel?.MasterKpiDetails?.MasterKpiID > 0)
                {
                    kpiModel.MasterKpiDetails.ModifiedBy = userId;
                    UpdateMasterKPIs(kpiModel);
                }
                else
                {
                    if (_unitOfWork.M_MasterKpisRepository.ExistsAny(x => x.KPI.ToLower() == kpiModel.MasterKpiDetails.KPI.ToLower() && !x.IsDeleted))
                        return -1;
                    else
                    {
                        kpiModel.MasterKpiDetails.ModuleID = _unitOfWork.M_KpiModulesRepository.GetFirstOrDefault(x => x.Name.ToLower() == kpiModel.KPIType.ToLower() && !x.IsDeleted).ModuleID;
                        kpiModel.MasterKpiDetails.CreatedBy = userId;
                        AddMasterKPIs(kpiModel);
                    }
                }
                break;
        }
        return 1;
    }

    private void AddCompanyKPI(KpiModel kpiModel)
    {
        M_CompanyKpi kpiDetail = new M_CompanyKpi
        {
            Kpi = kpiModel.CompanyKPIDetails.Kpi,
            KpiInfo = kpiModel.CompanyKPIDetails.KpiInfo,
            Description = kpiModel.CompanyKPIDetails.Description,
            CreatedBy = kpiModel.CompanyKPIDetails.CreatedBy,
            CreatedOn = DateTime.Now,
            IsDeleted = false,
            ParentKpiId = kpiModel.CompanyKPIDetails.ParentKpiId,
            IsHeader = kpiModel.CompanyKPIDetails.IsHeader,
            IsBoldKPI = kpiModel.CompanyKPIDetails.IsBoldKPI,
            Synonym = kpiModel.CompanyKPIDetails.Synonym,
            MethodologyId = (kpiModel.CompanyKPIDetails.KpiInfo != Constants.KpiInfoText) ? kpiModel.CompanyKPIDetails.MethodologyId : 0
        };
        _unitOfWork.M_CompanyKPIRepository.Insert(kpiDetail);
        _unitOfWork.Save();

        if (_encryption != null)
        {
            kpiDetail.EncryptedCompanyKpiId = _encryption.Encrypt(kpiDetail.CompanywiseKPIID.ToString());
            _unitOfWork.Save();
        }
    }

    public async Task<int> CreateKPI(DuplicateKpiModel duplicateKPI)
    {
        DynamicParameters parameters = new();
        parameters.Add(Constants.KpiType, duplicateKPI.KPIType, DbType.String);
        parameters.Add(Constants.KpiId, duplicateKPI.Id, DbType.Int32);
        parameters.Add(Constants.UserDetailId, duplicateKPI.UserId, DbType.Int32);
        parameters.Add(Constants.ModuleId, duplicateKPI.ModuleId, DbType.Int32);
        parameters.Add(Constants.Id, DbType.Int32, direction: ParameterDirection.Output);
        await _dapperGenericRepository.QueryExecuteSpAsync<int>(SqlConstants.QueryBySPCreateDuplicateKPI, parameters);
        return parameters.Get<int>(Constants.Id);
    }

    private void AddImpactKPI(KpiModel kpiModel)
    {
        MImpactKpi kpiDetail = new MImpactKpi
        {
            Kpi = kpiModel.ImpactKpiDetails.Kpi,
            KpiInfo = kpiModel.ImpactKpiDetails.KpiInfo,
            Description = kpiModel.ImpactKpiDetails.Description,
            CreatedBy = kpiModel.ImpactKpiDetails.CreatedBy,
            IsHeader = kpiModel.ImpactKpiDetails.IsHeader,
            IsBoldKPI = kpiModel.ImpactKpiDetails.IsBoldKPI,
            CreatedOn = DateTime.Now,
            IsDeleted = false,
            Synonym = kpiModel.ImpactKpiDetails.Synonym,
            MethodologyId = (kpiModel.ImpactKpiDetails.KpiInfo != Constants.KpiInfoText) ? kpiModel.ImpactKpiDetails.MethodologyId : 0
        };
        _unitOfWork.M_ImpactKPIRepository.Insert(kpiDetail);
        _unitOfWork.Save();

        if (_encryption != null)
        {
            kpiDetail.EncryptedImpactKpiId = _encryption.Encrypt(kpiDetail.ImpactKpiId.ToString());
            _unitOfWork.Save();
        }
    }

    private void AddInvestmentKPI(KpiModel kpiModel)
    {
        M_InvestmentKpi kpiDetail = new()
        {
            Kpi = kpiModel.InvestmentKpiDetails.Kpi,
            KpiInfo = kpiModel.InvestmentKpiDetails.KpiInfo,
            Description = kpiModel.InvestmentKpiDetails.Description,
            CreatedBy = kpiModel.InvestmentKpiDetails.CreatedBy,
            IsHeader = kpiModel.InvestmentKpiDetails.IsHeader,
            IsBoldKPI = kpiModel.InvestmentKpiDetails.IsBoldKPI,
            CreatedOn = DateTime.Now,
            IsDeleted = false,
            MethodologyId = (kpiModel.InvestmentKpiDetails.KpiInfo != Constants.KpiInfoText) ? kpiModel.InvestmentKpiDetails.MethodologyId : 0,
        };
        _unitOfWork.M_InvestmentKPIRepository.Insert(kpiDetail);
        _unitOfWork.Save();

        if (_encryption != null)
        {
            kpiDetail.EncryptedInvestmentKpiId = _encryption.Encrypt(kpiDetail.InvestmentKpiId.ToString());
            _unitOfWork.Save();
        }
    }

    private void AddOperationalKPI(KpiModel kpiModel)
    {
        M_SectorwiseKPI kpiDetail = new M_SectorwiseKPI
        {
            SectorId = kpiModel.OperationalKpiDetails.Sector.SectorID,
            Kpi = kpiModel.OperationalKpiDetails.KPI,

            Description = kpiModel.OperationalKpiDetails.Description,
            CreatedBy = kpiModel.OperationalKpiDetails.CreatedBy,
            CreatedOn = DateTime.Now,
            IsDeleted = false,
            KpiInfo = kpiModel.OperationalKpiDetails.KpiInfo,
            IsHeader = kpiModel.OperationalKpiDetails.IsHeader,
            IsBoldKPI = kpiModel.OperationalKpiDetails.IsBoldKPI,
            Synonym = kpiModel.OperationalKpiDetails.Synonym,
            MethodologyId = (kpiModel.OperationalKpiDetails.KpiInfo != Constants.KpiInfoText) ? kpiModel.OperationalKpiDetails.MethodologyId : 0
        };
        _unitOfWork.M_SectorwiseKPIRepository.Insert(kpiDetail);
        _unitOfWork.Save();

        if (_encryption != null)
        {
            kpiDetail.EncryptedSectorwiseKPIID = _encryption.Encrypt(kpiDetail.SectorwiseOperationalKPIID.ToString());
            _unitOfWork.Save();
        }
    }

    private void AddBalanceSheetKPI(KpiModel kpiModel)
    {
        M_BalanceSheet_LineItems kpiDetail = new M_BalanceSheet_LineItems
        {
            BalanceSheetLineItem = kpiModel.BalanceSheet_LineItemsDetails.BalanceSheetLineItem,
            KPIInfo = kpiModel.BalanceSheet_LineItemsDetails.KPIInfo,
            MethodologyID = (kpiModel.BalanceSheet_LineItemsDetails.KPIInfo != Constants.KpiInfoText) ? kpiModel.BalanceSheet_LineItemsDetails.MethodologyID : 0,
            Description = kpiModel.BalanceSheet_LineItemsDetails.Description,
            CreatedBy = kpiModel.BalanceSheet_LineItemsDetails.CreatedBy,
            CreatedOn = DateTime.Now,
            IsDeleted = false,
            IsActive = true,
            IsCalculatedValue = false,
            Synonym = kpiModel.BalanceSheet_LineItemsDetails.Synonym,
            isHeader = kpiModel.BalanceSheet_LineItemsDetails.isHeader,
            IsBoldKPI = kpiModel.BalanceSheet_LineItemsDetails.IsBoldKPI,
        };
        _unitOfWork.M_BalanceSheet_LineItemsRepository.Insert(kpiDetail);
        _unitOfWork.Save();

        if (_encryption != null)
        {
            kpiDetail.EncryptedBalanceSheetLineItemID = _encryption.Encrypt(kpiDetail.BalanceSheetLineItemID.ToString());
            _unitOfWork.Save();
        }
    }

    private void AddProfitAndLossKPI(KpiModel kpiModel)
    {
        M_ProfitAndLoss_LineItems kpiDetail = new M_ProfitAndLoss_LineItems
        {
            ProfitAndLossLineItem = kpiModel.ProfitAndLoss_LineItemDetails.ProfitAndLossLineItem,
            KPIInfo = kpiModel.ProfitAndLoss_LineItemDetails.KPIInfo,
            Description = kpiModel.ProfitAndLoss_LineItemDetails.Description,
            CreatedBy = kpiModel.ProfitAndLoss_LineItemDetails.CreatedBy,
            MethodologyID = (kpiModel.ProfitAndLoss_LineItemDetails.KPIInfo != Constants.KpiInfoText) ? kpiModel.ProfitAndLoss_LineItemDetails.MethodologyID : 0,
            CreatedOn = DateTime.Now,
            IsActive = true,
            IsCalculatedValue = false,
            isHeader = kpiModel.ProfitAndLoss_LineItemDetails.isHeader,
            IsBoldKPI = kpiModel.ProfitAndLoss_LineItemDetails.IsBoldKPI,
            Synonym = kpiModel.ProfitAndLoss_LineItemDetails.Synonym,
            IsDeleted = false,
        };
        _unitOfWork.M_ProfitAndLoss_LineItemsRepository.Insert(kpiDetail);
        _unitOfWork.Save();

        if (_encryption != null)
        {
            kpiDetail.EncryptedProfitAndLossLineItemID = _encryption.Encrypt(kpiDetail.ProfitAndLossLineItemID.ToString());
            _unitOfWork.Save();
        }
    }

    private void AddCashflowKPI(KpiModel kpiModel)
    {
        M_CashFlow_LineItems kpiDetail = new M_CashFlow_LineItems
        {
            CashFlowLineItem = kpiModel.CashFlow_LineItemsDetails.CashFlowLineItem,
            KPIInfo = kpiModel.CashFlow_LineItemsDetails.KPIInfo,
            MethodologyID = (kpiModel.CashFlow_LineItemsDetails.KPIInfo != Constants.KpiInfoText) ? kpiModel.CashFlow_LineItemsDetails.MethodologyID : 0,
            Description = kpiModel.CashFlow_LineItemsDetails.Description,
            CreatedBy = kpiModel.CashFlow_LineItemsDetails.CreatedBy,
            CreatedOn = DateTime.Now,
            IsDeleted = false,
            IsActive = true,
            IsCalculatedValue = false,
            isHeader = kpiModel.CashFlow_LineItemsDetails.isHeader,
            IsBoldKPI = kpiModel.CashFlow_LineItemsDetails.IsBoldKPI,
            Synonym = kpiModel.CashFlow_LineItemsDetails.Synonym
        };
        _unitOfWork.M_CashFlow_LineItemsRepository.Insert(kpiDetail);
        _unitOfWork.Save();

        if (_encryption != null)
        {
            kpiDetail.EncryptedCashFlowLineItemID = _encryption.Encrypt(kpiDetail.CashFlowLineItemID.ToString());
            _unitOfWork.Save();
        }
    }

    private void AddMasterKPIs(KpiModel kpiModel)
    {
        M_MasterKpis masterKpis = new M_MasterKpis
        {
            KPI = kpiModel.MasterKpiDetails.KPI,
            ModuleID = 1,
            MethodologyID = (kpiModel.MasterKpiDetails.KpiInfo != Constants.KpiInfoText) ? kpiModel.MasterKpiDetails.MethodologyID : 0,
            KpiInfo = kpiModel.MasterKpiDetails.KpiInfo,
            Description = kpiModel.MasterKpiDetails.Description,
            CreatedBy = kpiModel.MasterKpiDetails.CreatedBy,
            CreatedOn = DateTime.Now,
            IsDeleted = false,
            Synonym = kpiModel.MasterKpiDetails.Synonym
        };
        _unitOfWork.M_MasterKpisRepository.Insert(masterKpis);
        _unitOfWork.Save();
    }

    #endregion Add KPI

    public async Task<int> DeleteKPI(DeleteKpiModel kPIModel)
    {
        return await _dapperGenericRepository.QueryExecuteAsync<int>(SqlConstants.QueryByDeleteKPI, new { @KpiType = kPIModel.KpiType, @ModuleId = kPIModel.ModuleId, @KPIId = kPIModel.KPIId });
    }

    public List<CompanyKpiModel> GetCompanyKpi()
    {
        var methodologies = _unitOfWork.M_MethodologyRepository.GetQueryable().Where(x => !x.IsDeleted).ToList();
        List<CompanyKpiModel> listModel = _unitOfWork.M_CompanyKPIRepository.GetQueryable().Where(kpi => !kpi.IsDeleted).Select(kpi => new CompanyKpiModel
        {
            CompanywiseKPIID = kpi.CompanywiseKPIID,
            KPI = kpi.Kpi,
            KpiInfoType = getKpiInfoType(kpi.KpiInfo),
            KpiInfo = kpi.KpiInfo,
            IsHeader = kpi.IsHeader,
            IsBoldKPI = kpi.IsBoldKPI,
            Formula = kpi.Formula,
            MethodologyID = kpi.MethodologyId,
            EncryptedCompanywiseKPIID = kpi.EncryptedCompanyKpiId,
            Description = kpi.Description,
            MethodologyName = GetMethodologyName(kpi.MethodologyId, methodologies),
            Synonym = kpi.Synonym
        }).OrderBy(x => x.KPI).ToList();
        return listModel;
    }

    public List<SectorwiseKpiDetails> GetOperationalKpi()
    {
        var methodologies = _unitOfWork.M_MethodologyRepository.GetQueryable().Where(x => !x.IsDeleted).ToList();
        List<SectorwiseKpiDetails> listModel = _unitOfWork.M_SectorwiseKPIRepository.GetQueryable().Where(kpi => !kpi.IsDeleted).Select(kpi => new SectorwiseKpiDetails
        {
            SectorwiseKPIID = kpi.SectorwiseOperationalKPIID,
            KPI = kpi.Kpi,
            KpiInfo = kpi.KpiInfo,
            KpiInfoType = getKpiInfoType(kpi.KpiInfo),
            IsHeader = kpi.IsHeader,
            IsBoldKPI = kpi.IsBoldKPI,
            MethodologyId = kpi.MethodologyId,
            Formula = kpi.Formula,
            EncryptedSectorwiseKPIID = kpi.EncryptedSectorwiseKPIID,
            Description = kpi.Description,
            MethodologyName = GetMethodologyName(kpi.MethodologyId, methodologies),
            Synonym = kpi.Synonym
        }).OrderBy(x => x.KPI).ToList();
        return listModel;
    }

    private static string GetMethodologyName(int methodologyId, List<M_Methodology> methodologies)
    {
        return methodologies.FirstOrDefault(y => y.MethodologyID == methodologyId)?.MethodologyName;
    }

    public List<KpiTypeModel> GetKPITypes()
    {
        var result = (List<KpiTypeModel>)_memoryCacher.GetValue(_allKPITypesToken);
        if (result == null)
        {
            var kpiTypeList = _unitOfWork.KPITypesRepository.GetMany(x => !x.IsDeleted).Select(x => new KpiTypeModel
            {
                KpiType = x.Kpitype,
                KpiTypeId = x.KpitypeId
            }).ToList();
            _memoryCacher.Add(_allKPITypesToken, kpiTypeList, DateTimeOffset.UtcNow.AddHours(_globalConfig.CacheTimoutHours));
            result = kpiTypeList;
        }
        return result;
    }

    public ImpactKpiListModel GetImpactList()
    {
        ImpactKpiListModel listModel = new();
        var methodologies = _unitOfWork.M_MethodologyRepository.GetQueryable().Where(x => !x.IsDeleted).ToList();
        listModel.ImpactKPIList = _unitOfWork.M_ImpactKPIRepository.GetMany(x => !x.IsDeleted).Select(x => new ImpactKpiDetails
        {
            ImpactKPIId = x.ImpactKpiId,
            KPI = x.Kpi,
            Description = x.Description,
            KpiInfo = x.KpiInfo,
            KpiInfoType = getKpiInfoType(x.KpiInfo),
            IsBoldKPI = x.IsBoldKPI,
            IsHeader = x.IsHeader,
            Formula = x.Formula,
            MethodologyID = x.MethodologyId,
            MethodologyName = methodologies.FirstOrDefault(y => y.MethodologyID == x.MethodologyId)?.MethodologyName,
            Synonym = x.Synonym
        }).OrderBy(x => x.KPI).ToList();
        return listModel;
    }

    public InvestmentKpiListModel GetInvestmentKPIDetails()
    {
        InvestmentKpiListModel listModel = new();
        var methodologies = _unitOfWork.M_MethodologyRepository.GetQueryable().Where(x => !x.IsDeleted).ToList();
        listModel.InvestmentKPIList = _unitOfWork.M_InvestmentKPIRepository.GetMany(x => !x.IsDeleted).Select(x => new InvestmentKpiDetails
        {
            InvestmentKPIId = x.InvestmentKpiId,
            KPI = x.Kpi,
            Description = x.Description,
            IsNumeric = x.IsNumeric,
            MethodologyID = x.MethodologyId,
            KpiInfo = x.KpiInfo,
            IsHeader = x.IsHeader,
            IsBoldKPI = x.IsBoldKPI,
            Formula = x.Formula,
            MethodologyName = methodologies.FirstOrDefault(y => y.MethodologyID == x.MethodologyId)?.MethodologyName,
            KpiInfoType = getKpiInfoType(x.KpiInfo),
            Synonym = x.Synonym
        }).OrderBy(x => x.KPI).ToList();

        return listModel;
    }

    #region Download KPI Template

    public List<KpiTemplate> GetInvestmentKPIList(int portfolioCompanyId)
    {
        var InvestmentKPIValue = _unitOfWork.M_InvestmentKPIRepository.GetMany(x => !x.IsDeleted);
        var MappingPortfolioCompanyKPIValue = _unitOfWork.PortfolioInvestmentKpiMappingRepository.GetManyQueryable(x => x.PortfolioCompanyId == portfolioCompanyId && !x.IsDeleted);
        return (from C in InvestmentKPIValue
                join MC in MappingPortfolioCompanyKPIValue on C.InvestmentKpiId equals MC.KpiId
                select new KpiTemplate
                {
                    LineItem = C.Kpi,
                    Id = C.InvestmentKpiId,
                    DisplayOrder = MC.DisplayOrder,
                    IsHeader = (MC.ParentKPIID == null || MC.ParentKPIID == 0)
                }).OrderBy(x => x.DisplayOrder).ToList();
    }

    public async Task<List<KpiTemplate>> GetMasterKPIList(int portfolioCompanyId, string moduleName)
    {
        var moduleId = _unitOfWork.M_KpiModulesRepository.GetFirstOrDefault(x => !x.IsDeleted && x.Name.ToLower() == moduleName.ToLower())?.ModuleID;
        var mappedKPIList = await _dapperGenericRepository.Query<Mapping_Kpis>(SqlConstants.QueryByMappingKPIs, new { @companyId = portfolioCompanyId, @moduleId = moduleId });
        var masterKPIValue = await _dapperGenericRepository.Query<M_MasterKpis>(SqlConstants.QueryByMasterKpis, new { @moduleId = moduleId });
        return (from C in masterKPIValue
                join MC in mappedKPIList on C.MasterKpiID equals MC.KpiID
                select new KpiTemplate
                {
                    LineItem = C.KPI,
                    Id = C.MasterKpiID,
                    DisplayOrder = MC.DisplayOrder,
                    IsHeader = (MC.ParentKPIID == null || MC.ParentKPIID == 0)
                }).OrderBy(x => x.DisplayOrder).ToList();
    }

    public List<KpiTemplate> GetImpactKPIList(int portfolioCompanyId)
    {
        var ImpactKPIValue = _unitOfWork.M_ImpactKPIRepository.GetMany(x => !x.IsDeleted);
        var ImpactKpiMapping = _unitOfWork.Mapping_ImpactKPI_OrderRepository.GetManyQueryable(x => x.PortfolioCompanyID == portfolioCompanyId && !x.IsDeleted);
        return (from C in ImpactKPIValue
                join MC in ImpactKpiMapping on C.ImpactKpiId equals MC.ImpactKPIID
                select new KpiTemplate
                {
                    LineItem = C.Kpi,
                    Id = C.ImpactKpiId,
                    DisplayOrder = MC.KPIOrder,
                    IsHeader = (MC.ParentKPIID == null || MC.ParentKPIID == 0)
                }).OrderBy(x => x.DisplayOrder).ToList();
    }

    public List<KpiTemplate> GetOperationalKPIList(int portfolioCompanyId)
    {
        var OperationalKPIValue = _unitOfWork.M_SectorwiseKPIRepository.GetMany(x => !x.IsDeleted);
        var OperationalKpiMapping = _unitOfWork.MappingPortfolioOperationalKpi_OrderRepository.GetManyQueryable(x => x.PortfolioCompanyId == portfolioCompanyId && !x.IsDeleted);
        return (from C in OperationalKPIValue
                join MC in OperationalKpiMapping on C.SectorwiseOperationalKPIID equals MC.KpiId
                select new KpiTemplate
                {
                    LineItem = C.Kpi,
                    Id = C.SectorwiseOperationalKPIID,
                    DisplayOrder = MC.DisplayOrder,
                    IsHeader = (MC.ParentKPIID == null || MC.ParentKPIID == 0)
                }).OrderBy(x => x.DisplayOrder).ToList();
    }

    public List<KpiTemplate> GetCompanyKPIList(int portfolioCompanyId)
    {
        var InvestmentKPIValue = _unitOfWork.M_CompanyKPIRepository.GetMany(x => !x.IsDeleted);
        var MappingPortfolioCompanyKPIValue = _unitOfWork.PortfolioCompanyKpiMappingRepository.GetManyQueryable(x => x.PortfolioCompanyId == portfolioCompanyId && !x.IsDeleted);
        return (from C in InvestmentKPIValue
                join MC in MappingPortfolioCompanyKPIValue on C.CompanywiseKPIID equals MC.KpiId
                select new KpiTemplate
                {
                    LineItem = C.Kpi,
                    Id = C.CompanywiseKPIID,
                    DisplayOrder = MC.DisplayOrder,
                    IsHeader = (MC.ParentKPIID == null || MC.ParentKPIID == 0)
                }).OrderBy(x => x.DisplayOrder).ToList();
    }

    public FinancialKpiListModel GetFinancialKPIList(FinancialKpiFilter filter)
    {
        var result = new FinancialKpiListModel();
        var data = _unitOfWork.M_FinancialKPIRepository.GetManyQueryable(x => !x.IsDeleted).OrderByDescending(x => x.FinancialKPIId).AsQueryable();

        if (filter != null && filter.PaginationFilter != null)
        {
            int totalRows = 0;
            if (!string.IsNullOrEmpty(filter.PaginationFilter.GlobalFilter))
            {
                var lowerGlobalFilter = filter.PaginationFilter.GlobalFilter.ToLower();
                data = data.Where(u => u.KPI.ToLower().Contains(lowerGlobalFilter) ||
                   (u.Description != null && u.Description.ToLower().Contains(lowerGlobalFilter)));
            }
            if (!filter.PaginationFilter.FilterWithoutPaging)
                data = data.PagedResult(filter.PaginationFilter.First, filter.PaginationFilter.Rows, filter.PaginationFilter.MultiSortMeta, out totalRows);
            else
                data = data.SortedResult(filter.PaginationFilter.SortField, filter.PaginationFilter.SortOrder == 1);
            result.TotalRecords = totalRows;
        }
        var kpiList = data.OrderBy(x => x.KPI).ToList();
        result.FinancialKPIList = kpiList.Select(kpiDetails => new FinancialKpiDetails
        {
            FinancialKPIId = kpiDetails.FinancialKPIId,
            KPI = kpiDetails.KPI,

            Description = kpiDetails.Description
        }).ToList();
        return result;
    }

    public Dictionary<string, List<KpiTemplate>> GetFinancialKPIList(int portfolioCompanyId)
    {
        Dictionary<string, List<KpiTemplate>> FinancialKPIList = new();
        var ProfitAndLossLineItemsValue = _unitOfWork.M_ProfitAndLoss_LineItemsRepository.GetManyQueryable(x => !x.IsDeleted);
        var MappingyProfitAndLossValue = _unitOfWork.Mapping_CompanyProfitAndLossLineItemsRepository.GetManyQueryable(x => x.PortfolioCompanyID == portfolioCompanyId && !x.IsDeleted);
        var ProfitAndLoss_LineItemsList = (from C in ProfitAndLossLineItemsValue
                                           join MC in MappingyProfitAndLossValue on C.ProfitAndLossLineItemID equals MC.ProfitAndLossLineItemID
                                           select new KpiTemplate
                                           {
                                               LineItem = C.ProfitAndLossLineItem,
                                               Id = C.ProfitAndLossLineItemID,
                                               DisplayOrder = MC.DisplayOrder,
                                               IsHeader = (MC.ParentLineItemID == null || MC.ParentLineItemID == 0)
                                           }).OrderBy(x => x.DisplayOrder).ToList();

        var BalanceSheetLineItemsValue = _unitOfWork.M_BalanceSheet_LineItemsRepository.GetManyQueryable(x => !x.IsDeleted);
        var MappingPortfolioCompanyKPIValue = _unitOfWork.Mapping_CompanyBalanceSheetLineItemsRepository.GetManyQueryable(x => x.PortfolioCompanyID == portfolioCompanyId && !x.IsDeleted);
        var BalanceSheet_LineItemsList = (from C in BalanceSheetLineItemsValue
                                          join MC in MappingPortfolioCompanyKPIValue on C.BalanceSheetLineItemID equals MC.BalanceSheetLineItemID
                                          select new KpiTemplate
                                          {
                                              LineItem = C.BalanceSheetLineItem,
                                              Id = C.BalanceSheetLineItemID,
                                              DisplayOrder = MC.DisplayOrder,
                                              IsHeader = (MC.ParentLineItemID == null || MC.ParentLineItemID == 0)
                                          }).OrderBy(x => x.DisplayOrder).ToList();

        var CashFlowLineItemsValue = _unitOfWork.M_CashFlow_LineItemsRepository.GetManyQueryable(x => !x.IsDeleted);
        var MappingCashFlowValue = _unitOfWork.Mapping_CompanyCashFlowLineItemsRepository.GetManyQueryable(x => x.PortfolioCompanyID == portfolioCompanyId && !x.IsDeleted);
        var CashFlow_LineItemsList = (from C in CashFlowLineItemsValue
                                      join MC in MappingCashFlowValue on C.CashFlowLineItemID equals MC.CashFlowLineItemID
                                      select new KpiTemplate
                                      {
                                          LineItem = C.CashFlowLineItem,
                                          Id = C.CashFlowLineItemID,
                                          DisplayOrder = MC.DisplayOrder,
                                          IsHeader = (MC.ParentLineItemID == null || MC.ParentLineItemID == 0)
                                      }).OrderBy(x => x.DisplayOrder).ToList();

        FinancialKPIList.Add("Profit & Loss", ProfitAndLoss_LineItemsList);
        FinancialKPIList.Add("Balance Sheet", BalanceSheet_LineItemsList);
        FinancialKPIList.Add("CashFlow", CashFlow_LineItemsList);
        return FinancialKPIList;
    }

    #endregion Download KPI Template

    #region GetFinancials

    public BalanceSheetKpiListModel GetBalancesheetKPIList()
    {
        BalanceSheetKpiListModel listModel = new();
        listModel.balanceSheet_LineItems = _unitOfWork.M_BalanceSheet_LineItemsRepository.GetMany(x => !x.IsDeleted).Select(x => new M_BalanceSheet_LineItems
        {
            BalanceSheetLineItemID = x.BalanceSheetLineItemID,
            BalanceSheetLineItem = x.BalanceSheetLineItem,
            Description = x.Description,
            KPIInfo = x.KPIInfo,
            CreatedBy = x.CreatedBy,
            CreatedOn = DateTime.Now,
            MethodologyID = x.MethodologyID,
            isHeader = x.isHeader,
            IsBoldKPI = x.IsBoldKPI,
            Formula = x.Formula,
            FormulaKPIId = x.FormulaKPIId,
            MethodologyName = _unitOfWork.M_MethodologyRepository.GetFirstOrDefault(y => y.MethodologyID == x.MethodologyID)?.MethodologyName,
            KpiInfoType = getKpiInfoType(x.KPIInfo),
            Synonym = x.Synonym
        }).OrderBy(x => x.BalanceSheetLineItem).ToList();

        return listModel;
    }

    public ProfitLossKpiListModel GetProfitLossKPIList()
    {
        ProfitLossKpiListModel listModel = new();
        listModel.profitAndLoss_LineItems = _unitOfWork.M_ProfitAndLoss_LineItemsRepository.GetMany(x => !x.IsDeleted).Select(x => new M_ProfitAndLoss_LineItems
        {
            ProfitAndLossLineItemID = x.ProfitAndLossLineItemID,
            ProfitAndLossLineItem = x.ProfitAndLossLineItem,
            Description = x.Description,
            KPIInfo = x.KPIInfo,
            CreatedBy = x.CreatedBy,
            CreatedOn = DateTime.Now,
            MethodologyID = x.MethodologyID,
            isHeader = x.isHeader,
            IsBoldKPI = x.IsBoldKPI,
            Formula = x.Formula,
            FormulaKPIId = x.FormulaKPIId,
            MethodologyName = _unitOfWork.M_MethodologyRepository.GetFirstOrDefault(y => y.MethodologyID == x.MethodologyID)?.MethodologyName,
            KpiInfoType = getKpiInfoType(x.KPIInfo),
            Synonym = x.Synonym
        }).OrderBy(x => x.ProfitAndLossLineItem).ToList();

        return listModel;
    }

    public CashflowtKpiListModel GetCashflowKPIList()
    {
        CashflowtKpiListModel listModel = new();
        listModel.CashFlow_LineItems = _unitOfWork.M_CashFlow_LineItemsRepository.GetMany(x => !x.IsDeleted).Select(x => new M_CashFlow_LineItems
        {
            CashFlowLineItemID = x.CashFlowLineItemID,
            CashFlowLineItem = x.CashFlowLineItem,
            Description = x.Description,
            KPIInfo = x.KPIInfo,
            CreatedBy = x.CreatedBy,
            CreatedOn = DateTime.Now,
            MethodologyID = x.MethodologyID,
            isHeader = x.isHeader,
            IsBoldKPI = x.IsBoldKPI,
            Formula = x.Formula,
            FormulaKPIId = x.FormulaKPIId,
            MethodologyName = _unitOfWork.M_MethodologyRepository.GetFirstOrDefault(y => y.MethodologyID == x.MethodologyID)?.MethodologyName,
            KpiInfoType = getKpiInfoType(x.KPIInfo),
            Synonym = x.Synonym
        }).OrderBy(x => x.CashFlowLineItem).ToList();

        return listModel;
    }

    #endregion GetFinancials

    #region Get KPI Mapping

    public async Task<List<KpiMappingModel>> GetKPIMapping(int PortfolioCompanyId, string Type, int moduleID)
    {
        return moduleID
        switch
        {
            (int)KpiModuleType.Company => await CompanyKPIMapping(PortfolioCompanyId),
            (int)KpiModuleType.Impact => ImpactKPIMapping(PortfolioCompanyId),
            (int)KpiModuleType.Investment => await InvestmentKPIMapping(PortfolioCompanyId),
            (int)KpiModuleType.BalanceSheet => await BalanceSheetMapping(PortfolioCompanyId),
            (int)KpiModuleType.CashFlow => await CashFlowMapping(PortfolioCompanyId),
            (int)KpiModuleType.ProfitAndLoss => await ProfitAndLossMapping(PortfolioCompanyId),
            (int)KpiModuleType.Operational => await OperationalKPIMapping(PortfolioCompanyId),
            (int)KpiModuleType.TradingRecords or (int)KpiModuleType.CreditKPI or (int)KpiModuleType.CustomTable1 or (int)KpiModuleType.CustomTable2 or
            (int)KpiModuleType.CustomTable3 or (int)KpiModuleType.CustomTable4 or (int)KpiModuleType.OtherKPI1 or (int)KpiModuleType.OtherKPI2 or
            (int)KpiModuleType.OtherKPI3 or (int)KpiModuleType.OtherKPI4 or (int)KpiModuleType.OtherKPI5 or (int)KpiModuleType.OtherKPI6 or
            (int)KpiModuleType.OtherKPI7 or (int)KpiModuleType.OtherKPI8 or (int)KpiModuleType.OtherKPI9 or (int)KpiModuleType.OtherKPI10 =>
            await MasterKPIMapping(PortfolioCompanyId, moduleID),
            (int)KpiModuleType.MonthlyReport => MonthlyReportMapping(PortfolioCompanyId),
            _ => CapTableMapping(PortfolioCompanyId, moduleID)
        };
    }
    public async Task<List<KpiMappingBasedOnModuleId>> GetKPIMapping(int PortfolioCompanyId)
    {
        var result = new List<KpiMappingBasedOnModuleId>();
        var PLData = await ProfitAndLossMapping(PortfolioCompanyId);
        result.Add(new KpiMappingBasedOnModuleId{ ModuleId = (int)KpiModuleType.ProfitAndLoss,KPIMappings = PLData });
        var BSData = await BalanceSheetMapping(PortfolioCompanyId);
        result.Add(new KpiMappingBasedOnModuleId { ModuleId = (int)KpiModuleType.BalanceSheet, KPIMappings = BSData });
        var CFData = await CashFlowMapping(PortfolioCompanyId);
        result.Add(new KpiMappingBasedOnModuleId { ModuleId = (int)KpiModuleType.CashFlow, KPIMappings = CFData });
        return result;
    }

    public async Task<List<KpiMappingModel>> GetUnMappedKpi(int portfolioCompanyId, string type, int moduleId)
    {
        return await _dapperGenericRepository.Query<KpiMappingModel>(SqlConstants.QueryByGetMasterKpiNotMappedList, new { @companyId = portfolioCompanyId, @Type = type, @ModuleId = moduleId });
    }

    private async Task<List<KpiMappingModel>> CompanyKPIMapping(int PortfolioCompanyId)
    {
        var mappedKPIList = await _dapperGenericRepository.Query<MappingPortfolioCompanyKpi>(SqlConstants.QueryByGetMappingCompanyKpi, new { @companyId = PortfolioCompanyId });
        var kpiMasterList = await _dapperGenericRepository.Query<M_CompanyKpi>(SqlConstants.QueryByGetCompanyKpi);
        var mappedList = (from mapping in mappedKPIList
                          join master in kpiMasterList on mapping.KpiId equals master.CompanywiseKPIID
                          where !master.IsDeleted
                          select new KpiMappingModel() { Id = mapping.KpiId }).Select(x => x.Id).ToList();
        var mappedCompanyList = mappedKPIList.Where(x => !x.IsDeleted && mappedList.Contains(x.KpiId) && x.PortfolioCompanyId == PortfolioCompanyId)
            .Select(x => new KpiMappingModel()
            {
                Id = x.KpiId,
                Name = kpiMasterList.Where(y => y.CompanywiseKPIID == x.KpiId).Select(x => x.Kpi).FirstOrDefault(),
                ParentKPIID = x.ParentKPIID,
                DisplayOrder = x.DisplayOrder,
                IsMapped = true,
                IsHeader = kpiMasterList.FirstOrDefault(y => y.CompanywiseKPIID == x.KpiId) != null && kpiMasterList.FirstOrDefault(y => y.CompanywiseKPIID == x.KpiId).IsHeader,
                IsBoldKPI = kpiMasterList.FirstOrDefault(y => y.CompanywiseKPIID == x.KpiId) != null && kpiMasterList.FirstOrDefault(y => y.CompanywiseKPIID == x.KpiId).IsBoldKPI,
                MappingKPIId = x.MappingPortfolioCompanyKpiId,
                IsExtraction = x.IsExtraction,
                Synonym = string.IsNullOrEmpty(x.Synonym) ? kpiMasterList.FirstOrDefault(y => y.CompanywiseKPIID == x.KpiId && !y.IsDeleted).Synonym : x.Synonym,
                Definition = x.Definition,
                KpiInfo = kpiMasterList.Where(y => y.CompanywiseKPIID == x.KpiId && !y.IsDeleted).Select(x => x.KpiInfo).FirstOrDefault(),
                Formula = (string.IsNullOrEmpty(x.Formula) || x.Formula == "||||") ? kpiMasterList.FirstOrDefault(y => y.CompanywiseKPIID == x.KpiId)?.Formula : x.Formula,
            }).ToList();
        var parentList = mappedCompanyList.Where(x => x.ParentKPIID == null || x.ParentKPIID == 0).ToList();
        return parentList.Select(x => new KpiMappingModel()
        {
            Id = x.Id,
            Name = x.Name,
            ParentKPIID = x.ParentKPIID,
            DisplayOrder = x.DisplayOrder,
            IsMapped = x.IsMapped,
            IsHeader = x.IsHeader,
            MappingKPIId = x.MappingKPIId,
            IsBoldKPI = x.IsBoldKPI,
            Formula = x.Formula,
            KpiInfo = x.KpiInfo,
            IsExtraction = x.IsExtraction,
            Synonym = string.IsNullOrEmpty(x.Synonym) ? kpiMasterList.FirstOrDefault(y => y.CompanywiseKPIID == x.Id && !y.IsDeleted).Synonym : x.Synonym,
            Definition = x.Definition,
            Children = mappedCompanyList.Where(y => y.ParentKPIID == x.Id && y.ParentKPIID != null).OrderByDescending(i => i.DisplayOrder.HasValue && i.IsMapped).ThenBy(i => i.DisplayOrder).ToList()
        }).OrderByDescending(i => i.DisplayOrder.HasValue && i.IsMapped).ThenBy(i => i.DisplayOrder).ToList();
    }

    private async Task<List<KpiMappingModel>> MasterKPIMapping(int portfolioCompanyId, int moduleID)
    {
        var mappedKPIList = await _dapperGenericRepository.Query<Mapping_Kpis>(SqlConstants.QueryByMappingKPIs, new { @companyId = portfolioCompanyId, @moduleId = moduleID });
        var kpiMasterList = await _dapperGenericRepository.Query<M_MasterKpis>(SqlConstants.QueryByMasterKpis, new { @moduleId = moduleID });
        var mappedList = (from mapping in mappedKPIList
                          join master in kpiMasterList on mapping.KpiID equals master.MasterKpiID
                          where !master.IsDeleted
                          select new KpiMappingModel() { Id = mapping.KpiID, IsHeader = master.IsHeader, IsBoldKPI = master.IsBoldKPI }).Select(x => x.Id).ToList();
        var mappedCompanyList = mappedKPIList.Where(x => !x.IsDeleted && mappedList.Contains(x.KpiID) && x.PortfolioCompanyID == portfolioCompanyId)
            .Select(x => new KpiMappingModel()
            {
                Id = x.KpiID,
                Name = kpiMasterList.FirstOrDefault(y => y.MasterKpiID == x.KpiID).KPI,
                ParentKPIID = x.ParentKPIID,
                DisplayOrder = x.DisplayOrder,
                IsMapped = true,
                IsHeader = kpiMasterList.FirstOrDefault(y => y.MasterKpiID == x.KpiID) != null && kpiMasterList.FirstOrDefault(y => y.MasterKpiID == x.KpiID).IsHeader,
                IsBoldKPI = kpiMasterList.FirstOrDefault(y => y.MasterKpiID == x.KpiID) != null && kpiMasterList.FirstOrDefault(y => y.MasterKpiID == x.KpiID).IsBoldKPI,
                MappingKPIId = x.Mapping_KpisID,
                IsExtraction = x.IsExtraction,
                Synonym = string.IsNullOrEmpty(x.Synonym) ? kpiMasterList.FirstOrDefault(y => y.MasterKpiID == x.KpiID && !y.IsDeleted).Synonym : x.Synonym,
                Definition = x.Definition,
                KpiInfo = kpiMasterList.Where(y => y.MasterKpiID == x.KpiID && !y.IsDeleted).Select(x => x.KpiInfo).FirstOrDefault(),
                Formula = (string.IsNullOrEmpty(x.Formula) || x.Formula == "||||") ? kpiMasterList.FirstOrDefault(y => y.MasterKpiID == x.KpiID)?.Formula : x.Formula,
            }).ToList();
        var parentList = mappedCompanyList.Where(x => x.ParentKPIID == null || x.ParentKPIID == 0).ToList();
        return parentList.Select(x => new KpiMappingModel()
        {
            Id = x.Id,
            Name = x.Name,
            ParentKPIID = x.ParentKPIID,
            DisplayOrder = x.DisplayOrder,
            IsMapped = x.IsMapped,
            IsHeader = x.IsHeader,
            IsBoldKPI = x.IsBoldKPI,
            MappingKPIId = x.MappingKPIId,
            Formula = x.Formula,
            KpiInfo = x.KpiInfo,
            IsExtraction = x.IsExtraction,
            Synonym = string.IsNullOrEmpty(x.Synonym) ? kpiMasterList.FirstOrDefault(y => y.MasterKpiID == x.Id && !y.IsDeleted).Synonym : x.Synonym,
            Definition = x.Definition,
            Children = mappedCompanyList.Where(y => y.ParentKPIID == x.Id && y.ParentKPIID != null).OrderByDescending(i => i.DisplayOrder.HasValue && i.IsMapped).ThenBy(i => i.DisplayOrder).ToList()
        }).OrderByDescending(i => i.DisplayOrder.HasValue && i.IsMapped).ThenBy(i => i.DisplayOrder).ToList();
    }
    /// <summary>
    /// Retrieves a list of KpiMappingModel objects based on the provided portfolioCompanyId and moduleID.
    /// </summary>
    /// <param name="portfolioCompanyId">The ID of the portfolio company.</param>
    /// <param name="moduleID">The ID of the module.</param>
    /// <returns>A list of KpiMappingModel objects.</returns>
    private List<KpiMappingModel> CapTableMapping(int portfolioCompanyId, int moduleID)
    {
        var masterList = _unitOfWork.MCapTableRepository.GetManyQueryable(x => !x.IsDeleted && x.ModuleId == moduleID).ToList();
        var mappedList = (from mapping in _unitOfWork.MappingCapTableRepository.GetManyQueryable(x => !x.IsDeleted && x.PortfolioCompanyId == portfolioCompanyId && x.ModuleId == moduleID)
                          join master in _unitOfWork.MCapTableRepository.GetManyQueryable(x => !x.IsDeleted && x.ModuleId == moduleID) on mapping.KpiId equals master.KpiId
                          where !master.IsDeleted && mapping.PortfolioCompanyId == portfolioCompanyId && mapping.ModuleId == moduleID && mapping != null && master != null
                          select new KpiMappingModel()
                          {
                              Id = master.KpiId,
                              IsHeader = master.IsHeader,
                              IsBoldKPI = master.IsBoldKpi,
                              Name = master.Kpi,
                              ParentKPIID = mapping.ParentKpiId,
                              DisplayOrder = mapping.DisplayOrder,
                              IsMapped = true,
                              IsExtraction = mapping.IsExtraction,
                              Synonym = string.IsNullOrEmpty(mapping.Synonym) ? master.Synonym : mapping.Synonym,
                              Definition = mapping.Definition,
                              MappingKPIId = mapping.MappingId,
                              Formula = (string.IsNullOrEmpty(mapping.Formula) || mapping.Formula == "||||") ? master.Formula : mapping.Formula,
                              KpiTypeId = master.KpiTypeId ?? 0
                          }).ToList();
        var parentList = mappedList.Where(x => x.ParentKPIID == null || x.ParentKPIID == 0).ToList();
        return [.. parentList.Select(x => new KpiMappingModel()
            {
                Id = x.Id,
                Name = x.Name,
                ParentKPIID = x.ParentKPIID,
                DisplayOrder = x.DisplayOrder,
                IsMapped = x.IsMapped,
                IsHeader = x.IsHeader,
                IsBoldKPI = x.IsBoldKPI,
                MappingKPIId = x.MappingKPIId,
                Formula = x.Formula,
                KpiInfo = x.KpiInfo,
                KpiTypeId = x.KpiTypeId,
                IsExtraction = x.IsExtraction,
                Synonym = string.IsNullOrEmpty(x.Synonym) ? masterList.FirstOrDefault(y => y.KpiId == x.Id && !y.IsDeleted).Synonym : x.Synonym,
                Definition = x.Definition,
                Children = [.. mappedList.Where(y => y.ParentKPIID == x.Id && y.ParentKPIID != null).OrderByDescending(i => i.DisplayOrder.HasValue && i.IsMapped).ThenBy(i => i.DisplayOrder)]
            }).OrderByDescending(i => i.DisplayOrder.HasValue && i.IsMapped).ThenBy(i => i.DisplayOrder)];
    }
    /// <summary>
    /// Retrieves a list of KpiMappingModel objects for generating a monthly report based on the specified portfolio company ID.
    /// </summary>
    /// <param name="portfolioCompanyId">The ID of the portfolio company.</param>
    /// <returns>A list of KpiMappingModel objects representing the monthly report mapping.</returns>
    public List<KpiMappingModel> MonthlyReportMapping(int portfolioCompanyId)
    {
        var mappedList = (from mapping in _unitOfWork.MappingMonthlyReportRepository.GetManyQueryable(x => !x.IsDeleted && x.PortfolioCompanyId == portfolioCompanyId)
                          join master in _unitOfWork.MMonthlyReportRepository.GetManyQueryable(x => !x.IsDeleted) on mapping.KpiId equals master.KpiId
                          where !master.IsDeleted && mapping.PortfolioCompanyId == portfolioCompanyId && mapping != null && master != null
                          select new KpiMappingModel()
                          {
                              Id = master.KpiId,
                              IsHeader = master.IsHeader,
                              IsBoldKPI = master.IsBoldKpi,
                              Name = master.Kpi,
                              ParentKPIID = mapping.ParentKpiId,
                              DisplayOrder = mapping.DisplayOrder,
                              IsMapped = true,
                              MappingKPIId = mapping.MappingId,
                              Formula = (string.IsNullOrEmpty(mapping.Formula) || mapping.Formula == "||||") ? master.Formula : mapping.Formula,
                          }).ToList();
        var parentList = mappedList.Where(x => x.ParentKPIID == null || x.ParentKPIID == 0).ToList();
        return [.. parentList.Select(x => new KpiMappingModel()
            {
                Id = x.Id,
                Name = x.Name,
                ParentKPIID = x.ParentKPIID,
                DisplayOrder = x.DisplayOrder,
                IsMapped = x.IsMapped,
                IsHeader = x.IsHeader,
                IsBoldKPI = x.IsBoldKPI,
                MappingKPIId = x.MappingKPIId,
                Formula = x.Formula,
                KpiInfo = x.KpiInfo,
                Children = [.. mappedList.Where(y => y.ParentKPIID == x.Id && y.ParentKPIID != null).OrderByDescending(i => i.DisplayOrder.HasValue && i.IsMapped).ThenBy(i => i.DisplayOrder)]
            }).OrderByDescending(i => i.DisplayOrder.HasValue && i.IsMapped).ThenBy(i => i.DisplayOrder)];
    }
    private async Task<List<KpiMappingModel>> BalanceSheetMapping(int PortfolioCompanyId)
    {
        var mappedKPIList = await _dapperGenericRepository.Query<Mapping_CompanyBalanceSheetLineItems>(SqlConstants.QueryByGetMappingBalanceSheet, new { @companyId = PortfolioCompanyId });
        var kpiMasterList = await _dapperGenericRepository.Query<M_BalanceSheet_LineItems>(SqlConstants.QueryByGetBalanceSheet);
        var mappedList = (from mapping in mappedKPIList
                          join master in kpiMasterList on mapping.BalanceSheetLineItemID equals master.BalanceSheetLineItemID
                          where !master.IsDeleted
                          select new KpiMappingModel() { Id = mapping.BalanceSheetLineItemID, IsBoldKPI = master.IsBoldKPI, IsHeader = master.isHeader }).Select(x => x.Id).ToList();
        var mappedCompanyList = mappedKPIList.Where(x => !x.IsDeleted && mappedList.Contains(x.BalanceSheetLineItemID) && x.PortfolioCompanyID == PortfolioCompanyId)
            .Select(x => new KpiMappingModel()
            {
                Id = x.BalanceSheetLineItemID,
                Name = kpiMasterList.Where(y => y.BalanceSheetLineItemID == x.BalanceSheetLineItemID).Select(x => x.BalanceSheetLineItem).FirstOrDefault(),
                ParentKPIID = x.ParentLineItemID,
                DisplayOrder = x.DisplayOrder,
                IsMapped = true,
                IsExtraction = x.IsExtraction,
                Synonym = string.IsNullOrEmpty(x.Synonym) ? kpiMasterList.FirstOrDefault(y => y.BalanceSheetLineItemID == x.BalanceSheetLineItemID && !y.IsDeleted).Synonym : x.Synonym,
                Definition = x.Definition,
                KpiInfo = kpiMasterList.FirstOrDefault(y => y.BalanceSheetLineItemID == x.BalanceSheetLineItemID)?.KPIInfo,
                IsHeader = kpiMasterList.FirstOrDefault(y => y.BalanceSheetLineItemID == x.BalanceSheetLineItemID) != null && kpiMasterList.FirstOrDefault(y => y.BalanceSheetLineItemID == x.BalanceSheetLineItemID).isHeader,
                IsBoldKPI = kpiMasterList.FirstOrDefault(y => y.BalanceSheetLineItemID == x.BalanceSheetLineItemID) != null && kpiMasterList.FirstOrDefault(y => y.BalanceSheetLineItemID == x.BalanceSheetLineItemID).IsBoldKPI,
                MappingKPIId = x.CompanyBalanceSheetLineItemMappingID,
                Formula = (string.IsNullOrEmpty(x.Formula) || x.Formula == "||||") ? kpiMasterList.FirstOrDefault(y => y.BalanceSheetLineItemID == x.BalanceSheetLineItemID)?.Formula : x.Formula,
                MethodologyID = kpiMasterList.FirstOrDefault(y => y.BalanceSheetLineItemID == x.BalanceSheetLineItemID)?.MethodologyID
                ,
            }).ToList();
        var parentList = mappedCompanyList.Where(x => x.ParentKPIID == null || x.ParentKPIID == 0).ToList();
        return parentList.Select(x => new KpiMappingModel()
        {
            Id = x.Id,
            Name = x.Name,
            ParentKPIID = x.ParentKPIID,
            DisplayOrder = x.DisplayOrder,
            IsMapped = x.IsMapped,
            IsHeader = x.IsHeader,
            IsBoldKPI = x.IsBoldKPI,
            MappingKPIId = x.MappingKPIId,
            KpiInfo = x.KpiInfo,
            Formula = x.Formula,
            IsExtraction = x.IsExtraction,
            Synonym = string.IsNullOrEmpty(x.Synonym) ? kpiMasterList.FirstOrDefault(y => y.BalanceSheetLineItemID == x.Id && !y.IsDeleted).Synonym : x.Synonym,
            Definition = x.Definition,
            Children = mappedCompanyList.Where(y => y.ParentKPIID == x.Id && y.ParentKPIID != null).OrderByDescending(i => i.DisplayOrder.HasValue && i.IsMapped).ThenBy(i => i.DisplayOrder).ToList(),
            MethodologyID = x.MethodologyID
        }).OrderByDescending(i => i.DisplayOrder.HasValue && i.IsMapped).ThenBy(i => i.DisplayOrder).ToList();
    }

    private async Task<List<KpiMappingModel>> ProfitAndLossMapping(int PortfolioCompanyId)
    {
        var mappedKPIList = await _dapperGenericRepository.Query<Mapping_CompanyProfitAndLossLineItems>(SqlConstants.QueryByGetMappingProfitAndLoss, new { @companyId = PortfolioCompanyId });
        var kpiMasterList = await _dapperGenericRepository.Query<M_ProfitAndLoss_LineItems>(SqlConstants.QueryByGetProfitAndLoss);
        var mappedList = (from mapping in mappedKPIList
                          join master in kpiMasterList on mapping.ProfitAndLossLineItemID equals master.ProfitAndLossLineItemID
                          where !master.IsDeleted
                          select new KpiMappingModel() { Id = mapping.ProfitAndLossLineItemID, IsBoldKPI = master.IsBoldKPI, IsHeader = master.IsBoldKPI }).Select(x => x.Id).ToList();
        var mappedCompanyList = mappedKPIList.Where(x => !x.IsDeleted && mappedList.Contains(x.ProfitAndLossLineItemID) && x.PortfolioCompanyID == PortfolioCompanyId)
            .Select(x => new KpiMappingModel()
            {
                Id = x.ProfitAndLossLineItemID,
                Name = kpiMasterList.Where(y => y.ProfitAndLossLineItemID == x.ProfitAndLossLineItemID).Select(x => x.ProfitAndLossLineItem).FirstOrDefault(),
                ParentKPIID = x.ParentLineItemID,
                DisplayOrder = x.DisplayOrder,
                IsMapped = true,
                IsExtraction = x.IsExtraction,
                Synonym = string.IsNullOrEmpty(x.Synonym) ? kpiMasterList.FirstOrDefault(y => y.ProfitAndLossLineItemID == x.ProfitAndLossLineItemID && !y.IsDeleted).Synonym : x.Synonym,
                Definition = x.Definition,
                KpiInfo = kpiMasterList.FirstOrDefault(y => y.ProfitAndLossLineItemID == x.ProfitAndLossLineItemID)?.KPIInfo,
                IsHeader = kpiMasterList.FirstOrDefault(y => y.ProfitAndLossLineItemID == x.ProfitAndLossLineItemID) != null && kpiMasterList.FirstOrDefault(y => y.ProfitAndLossLineItemID == x.ProfitAndLossLineItemID).isHeader,
                IsBoldKPI = kpiMasterList.FirstOrDefault(y => y.ProfitAndLossLineItemID == x.ProfitAndLossLineItemID) != null && kpiMasterList.FirstOrDefault(y => y.ProfitAndLossLineItemID == x.ProfitAndLossLineItemID).IsBoldKPI,
                MappingKPIId = x.CompanyProfitAndLossLineItemMappingID,
                Formula = (string.IsNullOrEmpty(x.Formula) || x.Formula == "||||") ? kpiMasterList.FirstOrDefault(y => y.ProfitAndLossLineItemID == x.ProfitAndLossLineItemID)?.Formula : x.Formula,
                MethodologyID = kpiMasterList.FirstOrDefault(y => y.ProfitAndLossLineItemID == x.ProfitAndLossLineItemID)?.MethodologyID
            }).ToList();
        var parentList = mappedCompanyList.Where(x => x.ParentKPIID == null || x.ParentKPIID == 0).ToList();
        return parentList.Select(x => new KpiMappingModel()
        {
            Id = x.Id,
            Name = x.Name,
            ParentKPIID = x.ParentKPIID,
            DisplayOrder = x.DisplayOrder,
            IsMapped = x.IsMapped,
            IsHeader = x.IsHeader,
            MappingKPIId = x.MappingKPIId,
            IsBoldKPI = x.IsBoldKPI,
            Formula = x.Formula,
            KpiInfo = x.KpiInfo,
            MethodologyID = x.MethodologyID,
            IsExtraction = x.IsExtraction,
            Synonym = string.IsNullOrEmpty(x.Synonym) ? kpiMasterList.FirstOrDefault(y => y.ProfitAndLossLineItemID == x.Id && !y.IsDeleted).Synonym : x.Synonym,
            Definition = x.Definition,
            Children = mappedCompanyList.Where(y => y.ParentKPIID == x.Id && y.ParentKPIID != null).OrderByDescending(i => i.DisplayOrder.HasValue && i.IsMapped).ThenBy(i => i.DisplayOrder).ToList()
        }).OrderByDescending(i => i.DisplayOrder.HasValue && i.IsMapped).ThenBy(i => i.DisplayOrder).ToList();
    }

    private async Task<List<KpiMappingModel>> CashFlowMapping(int PortfolioCompanyId)
    {
        var mappedKPIList = await _dapperGenericRepository.Query<Mapping_CompanyCashFlowLineItems>(SqlConstants.QueryByGetMappingCashflow, new { @companyId = PortfolioCompanyId });
        var kpiMasterList = await _dapperGenericRepository.Query<M_CashFlow_LineItems>(SqlConstants.QueryByGetCashflow);
        var mappedList = (from mapping in mappedKPIList
                          join master in kpiMasterList on mapping.CashFlowLineItemID equals master.CashFlowLineItemID
                          where !master.IsDeleted
                          select new KpiMappingModel() { Id = mapping.CashFlowLineItemID, IsBoldKPI = master.IsBoldKPI, IsHeader = master.isHeader }).Select(x => x.Id).ToList();
        var mappedCompanyList = mappedKPIList.Where(x => !x.IsDeleted && mappedList.Contains(x.CashFlowLineItemID) && x.PortfolioCompanyID == PortfolioCompanyId)
            .Select(x => new KpiMappingModel()
            {
                Id = x.CashFlowLineItemID,
                Name = kpiMasterList.Where(y => y.CashFlowLineItemID == x.CashFlowLineItemID).Select(x => x.CashFlowLineItem).FirstOrDefault(),
                ParentKPIID = x.ParentLineItemID,
                DisplayOrder = x.DisplayOrder,
                IsMapped = true,
                IsExtraction = x.IsExtraction,
                Synonym = string.IsNullOrEmpty(x.Synonym) ? kpiMasterList.FirstOrDefault(y => y.CashFlowLineItemID == x.CashFlowLineItemID && !y.IsDeleted).Synonym : x.Synonym,
                Definition = x.Definition,
                KpiInfo = kpiMasterList.FirstOrDefault(y => y.CashFlowLineItemID == x.CashFlowLineItemID)?.KPIInfo,
                IsHeader = kpiMasterList.FirstOrDefault(y => y.CashFlowLineItemID == x.CashFlowLineItemID) != null && kpiMasterList.FirstOrDefault(y => y.CashFlowLineItemID == x.CashFlowLineItemID).isHeader,
                IsBoldKPI = kpiMasterList.FirstOrDefault(y => y.CashFlowLineItemID == x.CashFlowLineItemID) != null && kpiMasterList.FirstOrDefault(y => y.CashFlowLineItemID == x.CashFlowLineItemID).IsBoldKPI,
                MappingKPIId = x.CompanyCashFlowLineItemMappingID,
                Formula = (string.IsNullOrEmpty(x.Formula) || x.Formula == "||||") ? kpiMasterList.FirstOrDefault(y => y.CashFlowLineItemID == x.CashFlowLineItemID)?.Formula : x.Formula,
                MethodologyID = kpiMasterList.FirstOrDefault(y => y.CashFlowLineItemID == x.CashFlowLineItemID).MethodologyID
            }).ToList();
        var parentList = mappedCompanyList.Where(x => x.ParentKPIID == null || x.ParentKPIID == 0).ToList();
        return parentList.Select(x => new KpiMappingModel()
        {
            Id = x.Id,
            Name = x.Name,
            ParentKPIID = x.ParentKPIID,
            DisplayOrder = x.DisplayOrder,
            IsMapped = x.IsMapped,
            IsHeader = x.IsHeader,
            IsBoldKPI = x.IsBoldKPI,
            MappingKPIId = x.MappingKPIId,
            KpiInfo = x.KpiInfo,
            Formula = x.Formula,
            MethodologyID = x.MethodologyID,
            IsExtraction = x.IsExtraction,
            Synonym = string.IsNullOrEmpty(x.Synonym) ? kpiMasterList.FirstOrDefault(y => y.CashFlowLineItemID == x.Id && !y.IsDeleted).Synonym : x.Synonym,
            Definition = x.Definition,
            Children = mappedCompanyList.Where(y => y.ParentKPIID == x.Id && y.ParentKPIID != null).OrderByDescending(i => i.DisplayOrder.HasValue && i.IsMapped).ThenBy(i => i.DisplayOrder).ToList()
        }).OrderByDescending(i => i.DisplayOrder.HasValue && i.IsMapped).ThenBy(i => i.DisplayOrder).ToList();
    }

    private List<KpiMappingModel> ImpactKPIMapping(int PortfolioCompanyId)
    {
        var mappedKPIList = _unitOfWork.Mapping_ImpactKPI_OrderRepository.GetManyQueryable(x => x.PortfolioCompanyID == PortfolioCompanyId && !x.IsDeleted);
        var kpiMasterList = _unitOfWork.M_ImpactKPIRepository.GetMany(x => !x.IsDeleted);
        var mappedList = (from mapping in mappedKPIList
                          join master in kpiMasterList on mapping.ImpactKPIID equals master.ImpactKpiId
                          where !master.IsDeleted
                          select new KpiMappingModel() { Id = (int)mapping.ImpactKPIID, IsBoldKPI = false, IsHeader = false }).Select(x => x.Id).ToList();
        var mappedCompanyList = _unitOfWork.Mapping_ImpactKPI_OrderRepository.GetMany(x => !x.IsDeleted && mappedList.Contains((int)x.ImpactKPIID) && x.PortfolioCompanyID == PortfolioCompanyId)
            .Select(x => new KpiMappingModel()
            {
                Id = (int)x.ImpactKPIID,
                Name = kpiMasterList.Where(y => y.ImpactKpiId == x.ImpactKPIID).Select(x => x.Kpi).FirstOrDefault(),
                ParentKPIID = x.ParentKPIID,
                DisplayOrder = x.KPIOrder,
                IsMapped = true,
                IsExtraction = x.IsExtraction,
                Synonym = string.IsNullOrEmpty(x.Synonym) ? kpiMasterList.FirstOrDefault(y => y.ImpactKpiId == x.ImpactKPIID && !y.IsDeleted).Synonym : x.Synonym,
                Definition = x.Definition,
                IsHeader = kpiMasterList.FirstOrDefault(y => y.ImpactKpiId == x.ImpactKPIID) != null && kpiMasterList.FirstOrDefault(y => y.ImpactKpiId == x.ImpactKPIID).IsHeader,
                IsBoldKPI = kpiMasterList.FirstOrDefault(y => y.ImpactKpiId == x.ImpactKPIID) != null && kpiMasterList.FirstOrDefault(y => y.ImpactKpiId == x.ImpactKPIID).IsBoldKPI,
                Formula = (string.IsNullOrEmpty(x.Formula) || x.Formula == "||||") ? kpiMasterList.FirstOrDefault(y => y.ImpactKpiId == x.ImpactKPIID)?.Formula : x.Formula,
                MappingKPIId = x.ImpactKPIMappingID
            }).ToList();
        var parentList = mappedCompanyList.Where(x => x.ParentKPIID == null || x.ParentKPIID == 0).ToList();
        return parentList.Select(x => new KpiMappingModel()
        {
            Id = x.Id,
            Name = x.Name,
            ParentKPIID = x.ParentKPIID,
            DisplayOrder = x.DisplayOrder,
            IsMapped = x.IsMapped,
            IsHeader = x.IsHeader,
            IsBoldKPI = x.IsBoldKPI,
            MappingKPIId = x.MappingKPIId,
            Formula = x.Formula,
            IsExtraction = x.IsExtraction,
            Synonym = string.IsNullOrEmpty(x.Synonym) ? kpiMasterList.FirstOrDefault(y => y.ImpactKpiId == x.Id && !y.IsDeleted).Synonym : x.Synonym,
            Definition = x.Definition,
            Children = mappedCompanyList.Where(y => y.ParentKPIID == x.Id && y.ParentKPIID != null).OrderByDescending(i => i.DisplayOrder.HasValue && i.IsMapped).ThenBy(i => i.DisplayOrder).ToList()
        }).OrderByDescending(i => i.DisplayOrder.HasValue && i.IsMapped).ThenBy(i => i.DisplayOrder).ToList();
    }

    private async Task<List<KpiMappingModel>> InvestmentKPIMapping(int PortfolioCompanyId)
    {
        var mappedKPIList = await _dapperGenericRepository.Query<MappingPortfolioInvestmentKpi>(SqlConstants.QueryByGetMappingInvestmentKpi, new { @companyId = PortfolioCompanyId });
        var kpiMasterList = await _dapperGenericRepository.Query<M_InvestmentKpi>(SqlConstants.QueryByGetInvestmentKpi);
        var mappedList = (from mapping in mappedKPIList
                          join master in kpiMasterList on mapping.KpiId equals master.InvestmentKpiId
                          where !master.IsDeleted
                          select new KpiMappingModel() { Id = mapping.KpiId }).Select(x => x.Id).ToList();
        var mappedCompanyList = mappedKPIList.Where(x => !x.IsDeleted && mappedList.Contains(x.KpiId) && x.PortfolioCompanyId == PortfolioCompanyId)
            .Select(x => new KpiMappingModel()
            {
                Id = x.KpiId,
                Name = kpiMasterList.Where(y => y.InvestmentKpiId == x.KpiId && !y.IsDeleted).Select(x => x.Kpi).FirstOrDefault(),
                ParentKPIID = x.ParentKPIID,
                DisplayOrder = x.DisplayOrder,
                IsMapped = true,
                IsExtraction = x.IsExtraction,
                Synonym = string.IsNullOrEmpty(x.Synonym) ? kpiMasterList.FirstOrDefault(y => y.InvestmentKpiId == x.KpiId && !y.IsDeleted).Synonym : x.Synonym,
                Definition = x.Definition,
                IsHeader = kpiMasterList.FirstOrDefault(y => y.InvestmentKpiId == x.KpiId) != null && kpiMasterList.FirstOrDefault(y => y.InvestmentKpiId == x.KpiId).IsHeader,
                IsBoldKPI = kpiMasterList.FirstOrDefault(y => y.InvestmentKpiId == x.KpiId) != null && kpiMasterList.FirstOrDefault(y => y.InvestmentKpiId == x.KpiId).IsBoldKPI,
                MappingKPIId = x.MappingPortfolioInvestmentKpiId,
                Formula = (string.IsNullOrEmpty(x.Formula) || x.Formula == "||||") ? kpiMasterList?.FirstOrDefault(y => y.InvestmentKpiId == x.KpiId).Formula : x.Formula,
                KpiInfo = kpiMasterList.Where(y => y.InvestmentKpiId == x.KpiId && !y.IsDeleted).Select(x => x.KpiInfo).FirstOrDefault(),
            });
        var parentList = mappedCompanyList.Where(x => x.ParentKPIID == null || x.ParentKPIID == 0).ToList();
        return parentList.Select(x => new KpiMappingModel()
        {
            Id = x.Id,
            Name = x.Name,
            ParentKPIID = x.ParentKPIID,
            DisplayOrder = x.DisplayOrder,
            IsMapped = x.IsMapped,
            IsHeader = x.IsHeader,
            IsBoldKPI = x.IsBoldKPI,
            MappingKPIId = x.MappingKPIId,
            Formula = x.Formula,
            KpiInfo = x.KpiInfo,
            IsExtraction = x.IsExtraction,
            Synonym = string.IsNullOrEmpty(x.Synonym) ? kpiMasterList.FirstOrDefault(y => y.InvestmentKpiId == x.Id && !y.IsDeleted).Synonym : x.Synonym,
            Definition = x.Definition,
            Children = mappedCompanyList.Where(y => y.ParentKPIID == x.Id && y.ParentKPIID != null).OrderByDescending(i => i.DisplayOrder.HasValue && x.IsMapped).ThenBy(i => i.DisplayOrder).ToList()
        }).OrderByDescending(i => i.DisplayOrder.HasValue && i.IsMapped).ThenBy(i => i.DisplayOrder).ToList();
    }

    private async Task<List<KpiMappingModel>> OperationalKPIMapping(int PortfolioCompanyId)
    {
        var mappedKPIList = await _dapperGenericRepository.Query<MappingPortfolioOperationalKpi>(SqlConstants.QueryByGetMappingOperationalKpi, new { @companyId = PortfolioCompanyId });
        var kpiMasterList = await _dapperGenericRepository.Query<M_SectorwiseKPI>(SqlConstants.QueryByGetOperationalKpi);
        var mappedList = (from mapping in mappedKPIList
                          join master in kpiMasterList on mapping.KpiId equals master.SectorwiseOperationalKPIID
                          where !master.IsDeleted
                          select new KpiMappingModel() { Id = mapping.KpiId }).Select(x => x.Id).ToList();
        var mappedCompanyList = mappedKPIList.Where(x => !x.IsDeleted && mappedList.Contains(x.KpiId) && x.PortfolioCompanyId == PortfolioCompanyId)
            .Select(x => new KpiMappingModel()
            {
                Id = x.KpiId,
                Name = kpiMasterList.Where(y => y.SectorwiseOperationalKPIID == x.KpiId && !y.IsDeleted).Select(x => x.Kpi).FirstOrDefault(),
                ParentKPIID = x.ParentKPIID,
                DisplayOrder = x.DisplayOrder,
                IsMapped = true,
                IsExtraction = x.IsExtraction,
                Synonym = string.IsNullOrEmpty(x.Synonym) ? kpiMasterList.FirstOrDefault(y => y.SectorwiseOperationalKPIID == x.KpiId && !y.IsDeleted).Synonym : x.Synonym,
                Definition = x.Definition,
                IsHeader = kpiMasterList?.FirstOrDefault(y => y.SectorwiseOperationalKPIID == x.KpiId) != null && kpiMasterList.FirstOrDefault(y => y.SectorwiseOperationalKPIID == x.KpiId).IsHeader,
                IsBoldKPI = kpiMasterList?.FirstOrDefault(y => y.SectorwiseOperationalKPIID == x.KpiId) != null && kpiMasterList.FirstOrDefault(y => y.SectorwiseOperationalKPIID == x.KpiId).IsBoldKPI,
                MappingKPIId = x.MappingPortfolioOperationalKpiId,
                KpiInfo = kpiMasterList.Where(y => y.SectorwiseOperationalKPIID == x.KpiId && !y.IsDeleted).Select(x => x.KpiInfo).FirstOrDefault(),
                Formula = (string.IsNullOrEmpty(x.Formula) || x.Formula == "||||") ? kpiMasterList?.FirstOrDefault(y => y.SectorwiseOperationalKPIID == x.KpiId).Formula : x.Formula,
            }).ToList();
        var parentList = mappedCompanyList?.Where(x => x.ParentKPIID == null || x.ParentKPIID == 0).ToList();
        return parentList.Select(x => new KpiMappingModel()
        {
            Id = x.Id,
            Name = x.Name,
            ParentKPIID = x.ParentKPIID,
            DisplayOrder = x.DisplayOrder,
            IsMapped = x.IsMapped,
            IsHeader = x.IsHeader,
            IsBoldKPI = x.IsBoldKPI,
            MappingKPIId = x.MappingKPIId,
            Formula = x.Formula,
            KpiInfo = x.KpiInfo,
            IsExtraction = x.IsExtraction,
            Synonym = string.IsNullOrEmpty(x.Synonym) ? kpiMasterList.FirstOrDefault(y => y.SectorwiseOperationalKPIID == x.Id && !y.IsDeleted).Synonym : x.Synonym,
            Definition = x.Definition,
            Children = mappedCompanyList.Where(y => y.ParentKPIID == x.Id && y.ParentKPIID != null).OrderByDescending(i => i.DisplayOrder.HasValue && x.IsMapped).ThenBy(i => i.DisplayOrder).ToList()
        }).OrderByDescending(i => i.DisplayOrder.HasValue && i.IsMapped).ThenBy(i => i.DisplayOrder).ToList();
    }

    #endregion Get KPI Mapping

    #region Update KPI Mapping

    public async Task<bool> UpdateKPIMapping(int PortfolioCompanyId, string Type, List<KpiMappingModel> model, int userId, int moduleID)
    {
        return moduleID

            switch
        {
            (int)KpiModuleType.Operational => await UpdateOperationalKPIMapping(PortfolioCompanyId, model, userId),
            (int)KpiModuleType.Company => await UpdateCompanyKPI(PortfolioCompanyId, model, userId),
            (int)KpiModuleType.Impact => UpdateImpactKPI(PortfolioCompanyId, model, userId),
            (int)KpiModuleType.Investment => await UpdateInvestmentKPI(PortfolioCompanyId, model, userId),
            (int)KpiModuleType.BalanceSheet => await UpdateBalanceSheet(PortfolioCompanyId, model, userId),
            (int)KpiModuleType.CashFlow => await UpdateCashFlow(PortfolioCompanyId, model, userId),
            (int)KpiModuleType.ProfitAndLoss => await UpdateProfitAndLoss(PortfolioCompanyId, model, userId),
            (int)KpiModuleType.TradingRecords or (int)KpiModuleType.CreditKPI or (int)KpiModuleType.CustomTable1 or (int)KpiModuleType.CustomTable2 or
            (int)KpiModuleType.CustomTable3 or (int)KpiModuleType.CustomTable4 or (int)KpiModuleType.OtherKPI1 or (int)KpiModuleType.OtherKPI2 or
            (int)KpiModuleType.OtherKPI3 or (int)KpiModuleType.OtherKPI4 or (int)KpiModuleType.OtherKPI5 or (int)KpiModuleType.OtherKPI6 or
            (int)KpiModuleType.OtherKPI7 or (int)KpiModuleType.OtherKPI8 or (int)KpiModuleType.OtherKPI9 or (int)KpiModuleType.OtherKPI10 => await UpdateMasterKPI(PortfolioCompanyId, model, userId, moduleID),
            (int)KpiModuleType.MonthlyReport => await UpdateMonthlyReportTable(PortfolioCompanyId, model, userId),
            _ => await UpdateCapTable(PortfolioCompanyId, model, userId, moduleID)
        };
    }

    private async Task<bool> UpdateInvestmentKPI(int PortfolioCompanyId, List<KpiMappingModel> kPIMappingModels, int userId)
    {
        List<MappingPortfolioInvestmentKpi> mappingPortfolioInvestmentKpis = new();
        List<MappingPortfolioInvestmentKpi> mappingPortfolionvestmentKpisUpdate = new();
        var lastKpis = await _dapperGenericRepository.QueryFirstAsync<MappingPortfolioInvestmentKpi>(SqlConstants.QueryByInvestmentKpiLast, new { @companyId = PortfolioCompanyId });
        var lastDisplayOrder = lastKpis?.DisplayOrder + 1 ?? 1;
        int displayOrder = 1;
        if (lastDisplayOrder != 1)
            displayOrder = lastDisplayOrder;
        foreach (var model in kPIMappingModels)
        {
            if (model.IsMapped && model.MappingKPIId == 0)
            {
                var investmentKpi = new MappingPortfolioInvestmentKpi()
                {
                    PortfolioCompanyId = PortfolioCompanyId,
                    KpiId = model.Id,
                    ParentKPIID = null,
                    DisplayOrder = displayOrder,
                    CreatedBy = userId,
                    KpiTypeId = 2,
                    CreatedOn = DateTime.Now,
                    IsDeleted = false
                };
                mappingPortfolioInvestmentKpis.Add(investmentKpi);
                displayOrder++;
            }
            else if (!model.IsMapped && model.MappingKPIId > 0)
            {
                MappingPortfolioInvestmentKpi mappingPortfolioInvestmentKpi = await _dapperGenericRepository.QueryFirstAsync<MappingPortfolioInvestmentKpi>(SqlConstants.QueryByMappingInvestmentFirst, new { @mappingId = model.MappingKPIId });
                if (mappingPortfolioInvestmentKpi.DisplayOrder != (displayOrder)) mappingPortfolioInvestmentKpi.DisplayOrder = displayOrder;
                mappingPortfolioInvestmentKpi.IsDeleted = true;
                mappingPortfolioInvestmentKpi.ModifiedOn = DateTime.Now;
                mappingPortfolioInvestmentKpi.ModifiedBy = userId;
                mappingPortfolioInvestmentKpi.IsExtraction = model.IsExtraction;
                mappingPortfolioInvestmentKpi.Synonym = model.Synonym;
                mappingPortfolioInvestmentKpi.Definition = model.Definition;
                mappingPortfolionvestmentKpisUpdate.Add(mappingPortfolioInvestmentKpi);
                displayOrder++;
            }
            else if (model.IsMapped && model.MappingKPIId > 0)
            {
                MappingPortfolioInvestmentKpi mappingPortfolioInvestmentKpi = await _dapperGenericRepository.QueryFirstAsync<MappingPortfolioInvestmentKpi>(SqlConstants.QueryByMappingInvestmentFirst, new { @mappingId = model.MappingKPIId });
                if (mappingPortfolioInvestmentKpi.DisplayOrder != (displayOrder)) mappingPortfolioInvestmentKpi.DisplayOrder = displayOrder;
                mappingPortfolioInvestmentKpi.ModifiedOn = DateTime.Now;
                mappingPortfolioInvestmentKpi.ModifiedBy = userId;
                mappingPortfolioInvestmentKpi.ParentKPIID = null;
                mappingPortfolioInvestmentKpi.IsExtraction = model.IsExtraction;
                mappingPortfolioInvestmentKpi.Synonym = model.Synonym;
                mappingPortfolioInvestmentKpi.Definition = model.Definition;
                mappingPortfolionvestmentKpisUpdate.Add(mappingPortfolioInvestmentKpi);
                displayOrder++;
            }

            if (model.Children != null && model.Children.Count > 0)
            {
                foreach (var child in model.Children)
                {
                    if (child.IsMapped && child.MappingKPIId == 0)
                    {
                        var mappingKpi = new MappingPortfolioInvestmentKpi()
                        {
                            PortfolioCompanyId = PortfolioCompanyId,
                            KpiId = child.Id,
                            ParentKPIID = model.Id,
                            DisplayOrder = displayOrder,
                            CreatedBy = userId,
                            KpiTypeId = 2,
                            CreatedOn = DateTime.Now,
                            IsDeleted = false,
                            IsExtraction = child.IsExtraction,
                            Synonym = child.Synonym,
                            Definition = child.Definition
                        };
                        mappingPortfolioInvestmentKpis.Add(mappingKpi);
                        displayOrder++;
                    }
                    else if (child.MappingKPIId > 0)
                    {
                        MappingPortfolioInvestmentKpi mappingPortfolioInvestmentKpi = await _dapperGenericRepository.QueryFirstAsync<MappingPortfolioInvestmentKpi>(SqlConstants.QueryByMappingInvestmentFirst, new { @mappingId = child.MappingKPIId });
                        if (mappingPortfolioInvestmentKpi.DisplayOrder != (displayOrder)) mappingPortfolioInvestmentKpi.DisplayOrder = displayOrder;
                        if (!child.IsMapped) mappingPortfolioInvestmentKpi.IsDeleted = true;
                        mappingPortfolioInvestmentKpi.ParentKPIID = model.Id;
                        mappingPortfolioInvestmentKpi.ModifiedOn = DateTime.Now;
                        mappingPortfolioInvestmentKpi.ModifiedBy = userId;
                        mappingPortfolioInvestmentKpi.IsExtraction = child.IsExtraction;
                        mappingPortfolioInvestmentKpi.Synonym = child.Synonym;
                        mappingPortfolioInvestmentKpi.Definition = child.Definition;
                        mappingPortfolionvestmentKpisUpdate.Add(mappingPortfolioInvestmentKpi);
                        displayOrder++;
                    }
                }
            }
        }
        if (mappingPortfolioInvestmentKpis != null && mappingPortfolioInvestmentKpis.Count > 0)
        {
            await _unitOfWork.PortfolioInvestmentKpiMappingRepository.AddBulkAsyn(mappingPortfolioInvestmentKpis);
            await _unitOfWork.SaveAsync();
        }
        if (mappingPortfolionvestmentKpisUpdate != null && mappingPortfolionvestmentKpisUpdate.Count > 0)
        {
            _unitOfWork.PortfolioInvestmentKpiMappingRepository.UpdateBulk(mappingPortfolionvestmentKpisUpdate);
            await _unitOfWork.SaveAsync();
        }
        return true;
    }

    private async Task<bool> UpdateCompanyKPI(int PortfolioCompanyId, List<KpiMappingModel> kPIMappingModels, int userId)
    {
        List<MappingPortfolioCompanyKpi> mappingKpis = new();
        List<MappingPortfolioCompanyKpi> mappingKpisUpdate = new();
        var lastKpis = await _dapperGenericRepository.QueryFirstAsync<MappingPortfolioCompanyKpi>(SqlConstants.QueryByCompanyKpiLast, new { @companyId = PortfolioCompanyId });
        var lastDisplayOrder = lastKpis?.DisplayOrder + 1 ?? 1;
        int displayOrder = 1;
        if (lastDisplayOrder != 1)
            displayOrder = lastDisplayOrder;
        foreach (var model in kPIMappingModels)
        {
            if (model.IsMapped && model.MappingKPIId == 0)
            {
                var companyKpi = new MappingPortfolioCompanyKpi()
                {
                    PortfolioCompanyId = PortfolioCompanyId,
                    KpiId = model.Id,
                    ParentKPIID = null,
                    DisplayOrder = displayOrder,
                    CreatedBy = userId,
                    KpiTypeId = 1,
                    CreatedOn = DateTime.Now,
                    IsDeleted = false,
                    IsExtraction = model.IsExtraction,
                    Synonym = model.Synonym,
                    Definition = model.Definition
                };
                mappingKpis.Add(companyKpi);
                displayOrder++;
            }
            else if (!model.IsMapped && model.MappingKPIId > 0)
            {
                MappingPortfolioCompanyKpi mappingKpi = await _dapperGenericRepository.QueryFirstAsync<MappingPortfolioCompanyKpi>(SqlConstants.QueryByMappingCompanyKpiFirst, new { @mappingId = model.MappingKPIId });
                if (mappingKpi.DisplayOrder != (displayOrder)) mappingKpi.DisplayOrder = displayOrder;
                mappingKpi.IsDeleted = true;
                mappingKpi.ModifiedOn = DateTime.Now;
                mappingKpi.ModifiedBy = userId;
                mappingKpi.IsExtraction = model.IsExtraction;
                mappingKpi.Synonym = model.Synonym;
                mappingKpi.Definition = model.Definition;
                mappingKpisUpdate.Add(mappingKpi);
                displayOrder++;
            }
            else if (model.IsMapped && model.MappingKPIId > 0)
            {
                MappingPortfolioCompanyKpi mappingKpi = await _dapperGenericRepository.QueryFirstAsync<MappingPortfolioCompanyKpi>(SqlConstants.QueryByMappingCompanyKpiFirst, new { @mappingId = model.MappingKPIId });
                if (mappingKpi.DisplayOrder != (displayOrder)) mappingKpi.DisplayOrder = displayOrder;
                mappingKpi.ModifiedOn = DateTime.Now;
                mappingKpi.ModifiedBy = userId;
                mappingKpi.ParentKPIID = null;
                mappingKpi.IsExtraction = model.IsExtraction;
                mappingKpi.Synonym = model.Synonym;
                mappingKpi.Definition = model.Definition;
                mappingKpisUpdate.Add(mappingKpi);
                displayOrder++;
            }
            if (model.Children != null && model.Children.Count > 0)
            {
                foreach (var child in model.Children)
                {
                    if (child.IsMapped && child.MappingKPIId == 0)
                    {
                        var mappingKpi = new MappingPortfolioCompanyKpi()
                        {
                            PortfolioCompanyId = PortfolioCompanyId,
                            KpiId = child.Id,
                            ParentKPIID = model.Id,
                            DisplayOrder = displayOrder,
                            CreatedBy = userId,
                            KpiTypeId = 1,
                            CreatedOn = DateTime.Now,
                            IsDeleted = false,
                            IsExtraction = child.IsExtraction,
                            Synonym = child.Synonym,
                            Definition = child.Definition
                        };
                        mappingKpis.Add(mappingKpi);
                        displayOrder++;
                    }
                    else if (child.MappingKPIId > 0)
                    {
                        MappingPortfolioCompanyKpi mappingKpi = await _dapperGenericRepository.QueryFirstAsync<MappingPortfolioCompanyKpi>(SqlConstants.QueryByMappingCompanyKpiFirst, new { @mappingId = child.MappingKPIId });
                        if (mappingKpi.DisplayOrder != (displayOrder)) mappingKpi.DisplayOrder = displayOrder;
                        if (!child.IsMapped) mappingKpi.IsDeleted = true;
                        mappingKpi.ParentKPIID = model.Id;
                        mappingKpi.ModifiedOn = DateTime.Now;
                        mappingKpi.ModifiedBy = userId;
                        mappingKpi.IsExtraction = child.IsExtraction;
                        mappingKpi.Synonym = child.Synonym;
                        mappingKpi.Definition = child.Definition;
                        mappingKpisUpdate.Add(mappingKpi);
                        displayOrder++;
                    }
                }
            }
        }
        if (mappingKpis?.Count > 0)
        {
            await _unitOfWork.PortfolioCompanyKpiMappingRepository.AddBulkAsyn(mappingKpis);
            await _unitOfWork.SaveAsync();
        }
        if (mappingKpisUpdate?.Count > 0)
        {
            _unitOfWork.PortfolioCompanyKpiMappingRepository.UpdateBulk(mappingKpisUpdate);
            await _unitOfWork.SaveAsync();
        }
        return true;
    }

    private bool UpdateImpactKPI(int PortfolioCompanyId, List<KpiMappingModel> kPIMappingModels, int userId)
    {
        List<Mapping_ImpactKPI_Order> mappingKpis = new();
        List<Mapping_ImpactKPI_Order> mappingKpisUpdate = new();
        var lastDisplayOrder = _unitOfWork.Mapping_ImpactKPI_OrderRepository.GetLastOrDefault(x => x.PortfolioCompanyID == PortfolioCompanyId && !x.IsDeleted)?.KPIOrder + 1 ?? 1;
        int displayOrder = 1;
        if (lastDisplayOrder != 1)
            displayOrder = lastDisplayOrder;
        foreach (var model in kPIMappingModels)
        {
            if (model.IsMapped && model.MappingKPIId == 0)
            {
                var mappingKpi = new Mapping_ImpactKPI_Order()
                {
                    PortfolioCompanyID = PortfolioCompanyId,
                    ImpactKPIID = model.Id,
                    ParentKPIID = null,
                    KPIOrder = displayOrder,
                    CreatedBy = userId,
                    CreatedOn = DateTime.Now,
                    IsDeleted = false,
                    IsExtraction = model.IsExtraction,
                    Synonym = model.Synonym,
                    Definition = model.Definition
                };
                mappingKpis.Add(mappingKpi);
                displayOrder++;
            }
            else if (!model.IsMapped && model.MappingKPIId > 0)
            {
                Mapping_ImpactKPI_Order mappingKpi = _unitOfWork.Mapping_ImpactKPI_OrderRepository.GetFirstOrDefault(s => s.ImpactKPIMappingID == model.MappingKPIId);
                if (mappingKpi.KPIOrder != displayOrder) mappingKpi.KPIOrder = displayOrder;
                mappingKpi.IsDeleted = true;
                mappingKpi.ModifiedOn = DateTime.Now;
                mappingKpi.ModifiedBy = userId;
                mappingKpi.IsExtraction = model.IsExtraction;
                mappingKpi.Synonym = model.Synonym;
                mappingKpi.Definition = model.Definition;
                mappingKpisUpdate.Add(mappingKpi);
                displayOrder++;
            }
            else if (model.IsMapped && model.MappingKPIId > 0)
            {
                Mapping_ImpactKPI_Order mappingKpi = _unitOfWork.Mapping_ImpactKPI_OrderRepository.GetFirstOrDefault(s => s.ImpactKPIMappingID == model.MappingKPIId);
                if (mappingKpi.KPIOrder != displayOrder) mappingKpi.KPIOrder = displayOrder;
                mappingKpi.ModifiedOn = DateTime.Now;
                mappingKpi.ModifiedBy = userId;
                mappingKpi.ParentKPIID = null;
                mappingKpi.IsExtraction = model.IsExtraction;
                mappingKpi.Synonym = model.Synonym;
                mappingKpi.Definition = model.Definition;
                mappingKpisUpdate.Add(mappingKpi);
                displayOrder++;
            }

            if (model.Children != null && model.Children.Count > 0)
            {
                foreach (var child in model.Children)
                {
                    if (child.IsMapped && child.MappingKPIId == 0)
                    {
                        var mappingKpi = new Mapping_ImpactKPI_Order()
                        {
                            PortfolioCompanyID = PortfolioCompanyId,
                            ImpactKPIID = child.Id,
                            ParentKPIID = model.Id,
                            KPIOrder = displayOrder,
                            CreatedBy = userId,
                            CreatedOn = DateTime.Now,
                            IsDeleted = false,
                            IsExtraction = child.IsExtraction,
                            Synonym = child.Synonym,
                            Definition = child.Definition
                        };
                        mappingKpis.Add(mappingKpi);
                        displayOrder++;
                    }
                    else if (child.MappingKPIId > 0)
                    {
                        Mapping_ImpactKPI_Order mappingKpi = _unitOfWork.Mapping_ImpactKPI_OrderRepository.GetFirstOrDefault(s => s.ImpactKPIMappingID == child.MappingKPIId);
                        if (mappingKpi.KPIOrder != (displayOrder)) mappingKpi.KPIOrder = displayOrder;
                        if (!child.IsMapped) mappingKpi.IsDeleted = true;
                        mappingKpi.ParentKPIID = model.Id;
                        mappingKpi.ModifiedOn = DateTime.Now;
                        mappingKpi.ModifiedBy = userId;
                        mappingKpi.IsExtraction = child.IsExtraction;
                        mappingKpi.Synonym = child.Synonym;
                        mappingKpi.Definition = child.Definition;
                        mappingKpisUpdate.Add(mappingKpi);
                        displayOrder++;
                    }
                }
            }
        }
        if (mappingKpis != null && mappingKpis.Count > 0)
        {
            _unitOfWork.Mapping_ImpactKPI_OrderRepository.InsertBulk(mappingKpis);
            _unitOfWork.Save();
        }
        if (mappingKpisUpdate != null && mappingKpisUpdate.Count > 0)
        {
            _unitOfWork.Mapping_ImpactKPI_OrderRepository.UpdateBulk(mappingKpisUpdate);
            _unitOfWork.Save();
        }
        return true;
    }

    private async Task<bool> UpdateProfitAndLoss(int PortfolioCompanyId, List<KpiMappingModel> kPIMappingModels, int userId)
    {
        List<Mapping_CompanyProfitAndLossLineItems> mappingKpis = new();
        List<Mapping_CompanyProfitAndLossLineItems> mappingKpisUpdate = new();
        var lastKpis = await _dapperGenericRepository.QueryFirstAsync<Mapping_CompanyProfitAndLossLineItems>(SqlConstants.QueryByProfitLossLast, new { @companyId = PortfolioCompanyId });
        var lastDisplayOrder = lastKpis?.DisplayOrder + 1 ?? 1;
        int displayOrder = 1;
        if (lastDisplayOrder != 1)
            displayOrder = lastDisplayOrder;
        foreach (var model in kPIMappingModels)
        {
            if (model.IsMapped && model.MappingKPIId == 0)
            {
                var mappingKpi = new Mapping_CompanyProfitAndLossLineItems()
                {
                    PortfolioCompanyID = PortfolioCompanyId,
                    ProfitAndLossLineItemID = model.Id,
                    ParentLineItemID = null,
                    DisplayOrder = displayOrder,
                    CreatedBy = userId,
                    CreatedOn = DateTime.Now,
                    IsDeleted = false,
                    SegmentType = null,
                    IsActive = true,
                    IsExtraction = model.IsExtraction,
                    Synonym = model.Synonym,
                    Definition = model.Definition
                };
                mappingKpis.Add(mappingKpi);
                displayOrder++;
            }
            else if (!model.IsMapped && model.MappingKPIId > 0)
            {
                Mapping_CompanyProfitAndLossLineItems mappingKpi = await _dapperGenericRepository.QueryFirstAsync<Mapping_CompanyProfitAndLossLineItems>(SqlConstants.QueryByMappingProfitLossFirst, new { @mappingId = model.MappingKPIId });
                if (mappingKpi.DisplayOrder != (displayOrder)) mappingKpi.DisplayOrder = displayOrder;
                mappingKpi.IsDeleted = true;
                mappingKpi.ModifiedOn = DateTime.Now;
                mappingKpi.ModifiedBy = userId;
                mappingKpi.IsExtraction = model.IsExtraction;
                mappingKpi.Synonym = model.Synonym;
                mappingKpi.Definition = model.Definition;
                mappingKpisUpdate.Add(mappingKpi);
                displayOrder++;
            }
            else if (model.IsMapped && model.MappingKPIId > 0)
            {
                Mapping_CompanyProfitAndLossLineItems mappingKpi = await _dapperGenericRepository.QueryFirstAsync<Mapping_CompanyProfitAndLossLineItems>(SqlConstants.QueryByMappingProfitLossFirst, new { @mappingId = model.MappingKPIId });
                if (mappingKpi.DisplayOrder != (displayOrder)) mappingKpi.DisplayOrder = displayOrder;
                mappingKpi.ModifiedOn = DateTime.Now;
                mappingKpi.ModifiedBy = userId;
                mappingKpi.ParentLineItemID = null;
                mappingKpi.IsExtraction = model.IsExtraction;
                mappingKpi.Synonym = model.Synonym;
                mappingKpi.Definition = model.Definition;
                mappingKpisUpdate.Add(mappingKpi);
                displayOrder++;
            }

            if (model.Children != null && model.Children.Count > 0)
            {
                foreach (var child in model.Children)
                {
                    if (child.IsMapped && child.MappingKPIId == 0)
                    {
                        var mappingKpi = new Mapping_CompanyProfitAndLossLineItems()
                        {
                            PortfolioCompanyID = PortfolioCompanyId,
                            ProfitAndLossLineItemID = child.Id,
                            ParentLineItemID = model.Id,
                            DisplayOrder = displayOrder,
                            CreatedBy = userId,
                            CreatedOn = DateTime.Now,
                            IsDeleted = false,
                            SegmentType = null,
                            IsActive = true,
                            IsExtraction = child.IsExtraction,
                            Synonym = child.Synonym,
                            Definition = child.Definition
                        };
                        mappingKpis.Add(mappingKpi);
                        displayOrder++;
                    }
                    else if (child.MappingKPIId > 0)
                    {
                        Mapping_CompanyProfitAndLossLineItems mappingKpi = await _dapperGenericRepository.QueryFirstAsync<Mapping_CompanyProfitAndLossLineItems>(SqlConstants.QueryByMappingProfitLossFirst, new { @mappingId = child.MappingKPIId });
                        if (mappingKpi.DisplayOrder != (displayOrder)) mappingKpi.DisplayOrder = displayOrder;
                        if (!child.IsMapped) mappingKpi.IsDeleted = true;
                        mappingKpi.ParentLineItemID = model.Id;
                        mappingKpi.ModifiedOn = DateTime.Now;
                        mappingKpi.ModifiedBy = userId;
                        mappingKpi.IsExtraction = child.IsExtraction;
                        mappingKpi.Synonym = child.Synonym;
                        mappingKpi.Definition = child.Definition;
                        mappingKpisUpdate.Add(mappingKpi);
                        displayOrder++;
                    }
                }
            }
        }
        if (mappingKpis != null && mappingKpis.Count > 0)
        {
            await _unitOfWork.Mapping_CompanyProfitAndLossLineItemsRepository.AddBulkAsyn(mappingKpis);
            await _unitOfWork.SaveAsync();
        }
        if (mappingKpisUpdate != null && mappingKpisUpdate.Count > 0)
        {
            _unitOfWork.Mapping_CompanyProfitAndLossLineItemsRepository.UpdateBulk(mappingKpisUpdate);
            await _unitOfWork.SaveAsync();
        }
        return true;
    }

    private async Task<bool> UpdateBalanceSheet(int PortfolioCompanyId, List<KpiMappingModel> kPIMappingModels, int userId)
    {
        List<Mapping_CompanyBalanceSheetLineItems> mappingKpis = new();
        List<Mapping_CompanyBalanceSheetLineItems> mappingKpisUpdate = new();
        var lastKpis = await _dapperGenericRepository.QueryFirstAsync<Mapping_CompanyBalanceSheetLineItems>(SqlConstants.QueryByBalanceSheetLast, new { @companyId = PortfolioCompanyId });
        var lastDisplayOrder = lastKpis?.DisplayOrder + 1 ?? 1;
        int displayOrder = 1;
        if (lastDisplayOrder != 1)
            displayOrder = lastDisplayOrder;
        foreach (var model in kPIMappingModels)
        {
            if (model.IsMapped && model.MappingKPIId == 0)
            {
                var mappingKpi = new Mapping_CompanyBalanceSheetLineItems()
                {
                    PortfolioCompanyID = PortfolioCompanyId,
                    BalanceSheetLineItemID = model.Id,
                    ParentLineItemID = null,
                    DisplayOrder = displayOrder,
                    CreatedBy = userId,
                    CreatedOn = DateTime.Now,
                    IsDeleted = false,
                    IsActive = true,
                    SegmentType = null,
                    IsExtraction = model.IsExtraction,
                    Synonym = model.Synonym,
                    Definition = model.Definition
                };
                mappingKpis.Add(mappingKpi);
                displayOrder++;
            }
            else if (!model.IsMapped && model.MappingKPIId > 0)
            {
                Mapping_CompanyBalanceSheetLineItems mappingKpi = await _dapperGenericRepository.QueryFirstAsync<Mapping_CompanyBalanceSheetLineItems>(SqlConstants.QueryByMappingBalanceSheetFirst, new { @mappingId = model.MappingKPIId });
                if (mappingKpi.DisplayOrder != (displayOrder)) mappingKpi.DisplayOrder = displayOrder;
                mappingKpi.IsDeleted = true;
                mappingKpi.ModifiedOn = DateTime.Now;
                mappingKpi.ModifiedBy = userId;
                mappingKpi.IsExtraction = model.IsExtraction;
                mappingKpi.Synonym = model.Synonym;
                mappingKpi.Definition = model.Definition;
                mappingKpisUpdate.Add(mappingKpi);
                displayOrder++;
            }
            else if (model.IsMapped && model.MappingKPIId > 0)
            {
                Mapping_CompanyBalanceSheetLineItems mappingKpi = await _dapperGenericRepository.QueryFirstAsync<Mapping_CompanyBalanceSheetLineItems>(SqlConstants.QueryByMappingBalanceSheetFirst, new { @mappingId = model.MappingKPIId });
                if (mappingKpi.DisplayOrder != (displayOrder)) mappingKpi.DisplayOrder = displayOrder;
                mappingKpi.ModifiedOn = DateTime.Now;
                mappingKpi.ModifiedBy = userId;
                mappingKpi.ParentLineItemID = null;
                mappingKpi.IsExtraction = model.IsExtraction;
                mappingKpi.Synonym = model.Synonym;
                mappingKpi.Definition = model.Definition;
                mappingKpisUpdate.Add(mappingKpi);
                displayOrder++;
            }

            if (model.Children != null && model.Children.Count > 0)
            {
                foreach (var child in model.Children)
                {
                    if (child.IsMapped && child.MappingKPIId == 0)
                    {
                        var mappingKpi = new Mapping_CompanyBalanceSheetLineItems()
                        {
                            PortfolioCompanyID = PortfolioCompanyId,
                            BalanceSheetLineItemID = child.Id,
                            ParentLineItemID = model.Id,
                            DisplayOrder = displayOrder,
                            CreatedBy = userId,
                            CreatedOn = DateTime.Now,
                            IsDeleted = false,
                            IsActive = true,
                            SegmentType = null,
                            IsExtraction = child.IsExtraction,
                            Synonym = child.Synonym,
                            Definition = child.Definition
                        };
                        mappingKpis.Add(mappingKpi);
                        displayOrder++;
                    }
                    else if (child.MappingKPIId > 0)
                    {
                        Mapping_CompanyBalanceSheetLineItems mappingKpi = await _dapperGenericRepository.QueryFirstAsync<Mapping_CompanyBalanceSheetLineItems>(SqlConstants.QueryByMappingBalanceSheetFirst, new { @mappingId = child.MappingKPIId });
                        if (mappingKpi.DisplayOrder != (displayOrder)) mappingKpi.DisplayOrder = displayOrder;
                        if (!child.IsMapped) mappingKpi.IsDeleted = true;
                        mappingKpi.ParentLineItemID = model.Id;
                        mappingKpi.ModifiedOn = DateTime.Now;
                        mappingKpi.ModifiedBy = userId;
                        mappingKpi.IsExtraction = child.IsExtraction;
                        mappingKpi.Synonym = child.Synonym;
                        mappingKpi.Definition = child.Definition;
                        mappingKpisUpdate.Add(mappingKpi);
                        displayOrder++;
                    }
                }
            }
        }
        if (mappingKpis != null && mappingKpis.Count > 0)
        {
            await _unitOfWork.Mapping_CompanyBalanceSheetLineItemsRepository.AddBulkAsyn(mappingKpis);
            await _unitOfWork.SaveAsync();
        }
        if (mappingKpisUpdate != null && mappingKpisUpdate.Count > 0)
        {
            _unitOfWork.Mapping_CompanyBalanceSheetLineItemsRepository.UpdateBulk(mappingKpisUpdate);
            await _unitOfWork.SaveAsync();
        }
        return true;
    }

    private async Task<bool> UpdateCashFlow(int PortfolioCompanyId, List<KpiMappingModel> kPIMappingModels, int userId)
    {
        List<Mapping_CompanyCashFlowLineItems> mappingKpis = new();
        List<Mapping_CompanyCashFlowLineItems> mappingKpisUpdate = new();
        var lastKpis = await _dapperGenericRepository.QueryFirstAsync<Mapping_CompanyCashFlowLineItems>(SqlConstants.QueryByCashflowLast, new { @companyId = PortfolioCompanyId });
        var lastDisplayOrder = lastKpis?.DisplayOrder + 1 ?? 1;
        int displayOrder = 1;
        if (lastDisplayOrder != 1)
            displayOrder = lastDisplayOrder;
        foreach (var model in kPIMappingModels)
        {
            if (model.IsMapped && model.MappingKPIId == 0)
            {
                var mappingKpi = new Mapping_CompanyCashFlowLineItems()
                {
                    PortfolioCompanyID = PortfolioCompanyId,
                    CashFlowLineItemID = model.Id,
                    ParentLineItemID = 0,
                    DisplayOrder = displayOrder,
                    CreatedBy = userId,
                    CreatedOn = DateTime.Now,
                    IsDeleted = false,
                    SegmentType = null,
                    IsActive = true,
                    IsExtraction = model.IsExtraction,
                    Synonym = model.Synonym,
                    Definition = model.Definition,
                    EncryptedCompanyCashFlowLineItemMappingID = null
                };
                mappingKpis.Add(mappingKpi);
                displayOrder++;
            }
            else if (!model.IsMapped && model.MappingKPIId > 0)
            {
                Mapping_CompanyCashFlowLineItems mappingKpi = await _dapperGenericRepository.QueryFirstAsync<Mapping_CompanyCashFlowLineItems>(SqlConstants.QueryByCashflowFirst, new { @mappingId = model.MappingKPIId });
                if (mappingKpi.DisplayOrder != (displayOrder)) mappingKpi.DisplayOrder = displayOrder;
                mappingKpi.IsDeleted = true;
                mappingKpi.ModifiedOn = DateTime.Now;
                mappingKpi.ModifiedBy = userId;
                mappingKpi.IsExtraction = model.IsExtraction;
                mappingKpi.Synonym = model.Synonym;
                mappingKpi.Definition = model.Definition;
                mappingKpisUpdate.Add(mappingKpi);
                displayOrder++;
            }
            else if (model.IsMapped && model.MappingKPIId > 0)
            {
                Mapping_CompanyCashFlowLineItems mappingKpi = await _dapperGenericRepository.QueryFirstAsync<Mapping_CompanyCashFlowLineItems>(SqlConstants.QueryByCashflowFirst, new { @mappingId = model.MappingKPIId });
                if (mappingKpi.DisplayOrder != (displayOrder)) mappingKpi.DisplayOrder = displayOrder;
                mappingKpi.ModifiedOn = DateTime.Now;
                mappingKpi.ModifiedBy = userId;
                mappingKpi.ParentLineItemID = null;
                mappingKpi.IsExtraction = model.IsExtraction;
                mappingKpi.Synonym = model.Synonym;
                mappingKpi.Definition = model.Definition;
                mappingKpisUpdate.Add(mappingKpi);
                displayOrder++;
            }

            if (model.Children != null && model.Children.Count > 0)
            {
                foreach (var child in model.Children)
                {
                    if (child.IsMapped && child.MappingKPIId == 0)
                    {
                        var mappingKpi = new Mapping_CompanyCashFlowLineItems()
                        {
                            PortfolioCompanyID = PortfolioCompanyId,
                            CashFlowLineItemID = child.Id,
                            ParentLineItemID = model.Id,
                            DisplayOrder = displayOrder,
                            CreatedBy = userId,
                            CreatedOn = DateTime.Now,
                            IsDeleted = false,
                            SegmentType = null,
                            IsActive = true,
                            IsExtraction = child.IsExtraction,
                            Synonym = child.Synonym,
                            Definition = child.Definition,
                            EncryptedCompanyCashFlowLineItemMappingID = null
                        };
                        mappingKpis.Add(mappingKpi);
                        displayOrder++;
                    }
                    else if (child.MappingKPIId > 0)
                    {
                        Mapping_CompanyCashFlowLineItems mappingKpi = await _dapperGenericRepository.QueryFirstAsync<Mapping_CompanyCashFlowLineItems>(SqlConstants.QueryByCashflowFirst, new { @mappingId = child.MappingKPIId });
                        if (mappingKpi.DisplayOrder != (displayOrder)) mappingKpi.DisplayOrder = displayOrder;
                        if (!child.IsMapped) mappingKpi.IsDeleted = true;
                        mappingKpi.ParentLineItemID = model.Id;
                        mappingKpi.ModifiedOn = DateTime.Now;
                        mappingKpi.ModifiedBy = userId;
                        mappingKpi.IsExtraction = child.IsExtraction;
                        mappingKpi.Synonym = child.Synonym;
                        mappingKpi.Definition = child.Definition;
                        mappingKpisUpdate.Add(mappingKpi);
                        displayOrder++;
                    }
                }
            }
        }
        if (mappingKpis != null && mappingKpis.Count > 0)
        {
            await _unitOfWork.Mapping_CompanyCashFlowLineItemsRepository.AddBulkAsyn(mappingKpis);
            await _unitOfWork.SaveAsync();
        }
        if (mappingKpisUpdate != null && mappingKpisUpdate.Count > 0)
        {
            _unitOfWork.Mapping_CompanyCashFlowLineItemsRepository.UpdateBulk(mappingKpisUpdate);
            await _unitOfWork.SaveAsync();
        }
        return true;
    }

    private async Task<bool> UpdateOperationalKPIMapping(int PortfolioCompanyId, List<KpiMappingModel> kPIMappingModels, int userId)
    {
        try
        {
            List<MappingPortfolioOperationalKpi> mappingKpis = new();
            List<MappingPortfolioOperationalKpi> mappingKpisUpdate = new();
            var lastKpis = await _dapperGenericRepository.QueryFirstAsync<MappingPortfolioOperationalKpi>(SqlConstants.QueryByOperationalLast, new { @companyId = PortfolioCompanyId });
            var lastDisplayOrder = lastKpis?.DisplayOrder + 1 ?? 1;
            int displayOrder = 1;
            if (lastDisplayOrder != 1)
                displayOrder = lastDisplayOrder;
            foreach (var model in kPIMappingModels)
            {
                if (model.IsMapped && model.MappingKPIId == 0)
                {
                    var companyKpi = new MappingPortfolioOperationalKpi()
                    {
                        PortfolioCompanyId = PortfolioCompanyId,
                        KpiId = model.Id,
                        ParentKPIID = null,
                        DisplayOrder = displayOrder,
                        CreatedBy = userId,
                        KpiTypeId = 1,
                        CreatedOn = DateTime.Now,
                        IsDeleted = false,
                        IsExtraction = model.IsExtraction,
                        Synonym = model.Synonym,
                        Definition = model.Definition
                    };
                    mappingKpis.Add(companyKpi);
                    displayOrder++;
                }
                else if (!model.IsMapped && model.MappingKPIId > 0)
                {
                    MappingPortfolioOperationalKpi mappingKpi = await _dapperGenericRepository.QueryFirstAsync<MappingPortfolioOperationalKpi>(SqlConstants.QueryByMappingOperationalKpiFirst, new { @mappingId = model.MappingKPIId });
                    if (mappingKpi.DisplayOrder != (displayOrder)) mappingKpi.DisplayOrder = displayOrder;
                    mappingKpi.IsDeleted = true;
                    mappingKpi.ModifiedOn = DateTime.Now;
                    mappingKpi.ModifiedBy = userId;
                    mappingKpi.IsExtraction = model.IsExtraction;
                    mappingKpi.Synonym = model.Synonym;
                    mappingKpi.Definition = model.Definition;
                    mappingKpisUpdate.Add(mappingKpi);
                    displayOrder++;
                }
                else if (model.IsMapped && model.MappingKPIId > 0)
                {
                    MappingPortfolioOperationalKpi mappingKpi = await _dapperGenericRepository.QueryFirstAsync<MappingPortfolioOperationalKpi>(SqlConstants.QueryByMappingOperationalKpiFirst, new { @mappingId = model.MappingKPIId });
                    if (mappingKpi.DisplayOrder != (displayOrder)) mappingKpi.DisplayOrder = displayOrder;
                    mappingKpi.IsExtraction = model.IsExtraction;
                    mappingKpi.Synonym = model.Synonym;
                    mappingKpi.Definition = model.Definition;
                    mappingKpi.ModifiedOn = DateTime.Now;
                    mappingKpi.ModifiedBy = userId;
                    mappingKpi.ParentKPIID = null;
                    mappingKpisUpdate.Add(mappingKpi);
                    displayOrder++;
                }
                if (model.Children != null && model.Children.Count > 0)
                {
                    foreach (var child in model.Children)
                    {
                        if (child.IsMapped && child.MappingKPIId == 0)
                        {
                            var mappingKpi = new MappingPortfolioOperationalKpi()
                            {
                                PortfolioCompanyId = PortfolioCompanyId,
                                KpiId = child.Id,
                                ParentKPIID = model.Id,
                                DisplayOrder = displayOrder,
                                CreatedBy = userId,
                                KpiTypeId = 1,
                                CreatedOn = DateTime.Now,
                                IsDeleted = false,
                                IsExtraction = child.IsExtraction,
                                Synonym = child.Synonym,
                                Definition = child.Definition
                            };
                            mappingKpis.Add(mappingKpi);
                            displayOrder++;
                        }
                        else if (child.MappingKPIId > 0)
                        {
                            MappingPortfolioOperationalKpi mappingKpi = await _dapperGenericRepository.QueryFirstAsync<MappingPortfolioOperationalKpi>(SqlConstants.QueryByMappingOperationalKpiFirst, new { @mappingId = child.MappingKPIId });
                            if (mappingKpi.DisplayOrder != (displayOrder)) mappingKpi.DisplayOrder = displayOrder;
                            if (!child.IsMapped) mappingKpi.IsDeleted = true;
                            mappingKpi.IsExtraction = child.IsExtraction;
                            mappingKpi.Synonym = child.Synonym;
                            mappingKpi.Definition = child.Definition;
                            mappingKpi.ParentKPIID = model.Id;
                            mappingKpi.ModifiedOn = DateTime.Now;
                            mappingKpi.ModifiedBy = userId;
                            mappingKpisUpdate.Add(mappingKpi);
                            displayOrder++;
                        }
                    }
                }
            }
            if (mappingKpis != null && mappingKpis.Count > 0)
            {
                await _unitOfWork.MappingPortfolioOperationalKpi_OrderRepository.AddBulkAsyn(mappingKpis);
                await _unitOfWork.SaveAsync();
            }
            if (mappingKpisUpdate != null && mappingKpisUpdate.Count > 0)
            {
                _unitOfWork.MappingPortfolioOperationalKpi_OrderRepository.UpdateBulk(mappingKpisUpdate);
                await _unitOfWork.SaveAsync();
            }
            return true;
        }
        catch
        {
            return false;
        }
    }
    private async Task<bool> UpdateMasterKPI(int portfolioCompanyId, List<KpiMappingModel> kPIMappingModels, int userId, int moduleID)
    {
        List<Mapping_Kpis> mappingKpis = new();
        List<Mapping_Kpis> mappingKpisUpdate = new();
        var mappedList = await _dapperGenericRepository.Query<Mapping_Kpis>(SqlConstants.QueryByMappingKPIs, new { @companyId = portfolioCompanyId, @moduleId = moduleID });
        var lastKpis = await _dapperGenericRepository.QueryFirstAsync<Mapping_Kpis>(SqlConstants.QueryByMappingKPIsLast, new { @companyId = portfolioCompanyId, @moduleId = moduleID });
        var lastDisplayOrder = lastKpis?.DisplayOrder + 1 ?? 1;
        int displayOrder = 1;
        if (lastDisplayOrder != 1)
            displayOrder = lastDisplayOrder;
        foreach (var model in kPIMappingModels)
        {
            if (model.IsMapped && model.MappingKPIId == 0)
            {
                var mapping_Kpis = new Mapping_Kpis()
                {
                    PortfolioCompanyID = portfolioCompanyId,
                    KpiID = model.Id,
                    ParentKPIID = null,
                    DisplayOrder = displayOrder,
                    CreatedBy = userId,
                    KPITypeID = 1,
                    CreatedOn = DateTime.Now,
                    IsDeleted = false,
                    ModuleID = moduleID,
                    IsExtraction = model.IsExtraction,
                    Synonym = model.Synonym,
                    Definition = model.Definition
                };
                mappingKpis.Add(mapping_Kpis);
                displayOrder++;
            }
            else if (!model.IsMapped && model.MappingKPIId > 0)
            {
                Mapping_Kpis mappingKpi = mappedList.FirstOrDefault(s => s.Mapping_KpisID == model.MappingKPIId);
                if (mappingKpi.DisplayOrder != (displayOrder)) mappingKpi.DisplayOrder = displayOrder;
                mappingKpi.IsDeleted = true;
                mappingKpi.ModifiedOn = DateTime.Now;
                mappingKpi.ModifiedBy = userId;
                mappingKpi.ModuleID = moduleID;
                mappingKpi.IsExtraction = model.IsExtraction;
                mappingKpi.Synonym = model.Synonym;
                mappingKpi.Definition = model.Definition;
                mappingKpisUpdate.Add(mappingKpi);
                displayOrder++;
            }
            else if (model.IsMapped && model.MappingKPIId > 0)
            {
                Mapping_Kpis mappingKpi = mappedList.FirstOrDefault(s => s.Mapping_KpisID == model.MappingKPIId);
                if (mappingKpi.DisplayOrder != (displayOrder)) mappingKpi.DisplayOrder = displayOrder;
                mappingKpi.ModifiedOn = DateTime.Now;
                mappingKpi.ModifiedBy = userId;
                mappingKpi.ModuleID = moduleID;
                mappingKpi.ParentKPIID = null;
                mappingKpi.IsExtraction = model.IsExtraction;
                mappingKpi.Synonym = model.Synonym;
                mappingKpi.Definition = model.Definition;
                mappingKpisUpdate.Add(mappingKpi);
                displayOrder++;
            }
            if (model.Children != null && model.Children.Count > 0)
            {
                foreach (var child in model.Children)
                {
                    if (child.IsMapped && child.MappingKPIId == 0)
                    {
                        var mappingKpi = new Mapping_Kpis()
                        {
                            PortfolioCompanyID = portfolioCompanyId,
                            KpiID = child.Id,
                            ParentKPIID = model.Id,
                            DisplayOrder = displayOrder,
                            CreatedBy = userId,
                            KPITypeID = 1,
                            CreatedOn = DateTime.Now,
                            IsDeleted = false,
                            ModuleID = moduleID,
                            IsExtraction = child.IsExtraction,
                            Synonym = child.Synonym,
                            Definition = child.Definition
                        };
                        mappingKpis.Add(mappingKpi);
                        displayOrder++;
                    }
                    else if (child.MappingKPIId > 0)
                    {
                        Mapping_Kpis mappingKpi = mappedList.FirstOrDefault(s => s.Mapping_KpisID == child.MappingKPIId);
                        if (mappingKpi.DisplayOrder != (displayOrder)) mappingKpi.DisplayOrder = displayOrder;
                        if (!child.IsMapped) mappingKpi.IsDeleted = true;
                        mappingKpi.ParentKPIID = model.Id;
                        mappingKpi.ModifiedOn = DateTime.Now;
                        mappingKpi.ModifiedBy = userId;
                        mappingKpi.ModuleID = moduleID;
                        mappingKpi.IsExtraction = child.IsExtraction;
                        mappingKpi.Synonym = child.Synonym;
                        mappingKpi.Definition = child.Definition;
                        mappingKpisUpdate.Add(mappingKpi);
                        displayOrder++;
                    }
                }
            }
        }
        if (mappingKpis != null && mappingKpis.Count > 0)
        {
            await _unitOfWork.Mapping_KpisRepository.AddBulkAsyn(mappingKpis);
            await _unitOfWork.SaveAsync();
        }
        if (mappingKpisUpdate != null && mappingKpisUpdate.Count > 0)
        {
            _unitOfWork.Mapping_KpisRepository.UpdateBulk(mappingKpisUpdate);
            await _unitOfWork.SaveAsync();
        }
        return true;
    }
    private MappingCapTable CreateMappingKpi(int portfolioCompanyId, int kpiId, int? parentKpiId, int displayOrder, int userId, int moduleId)
    {
        return new MappingCapTable()
        {
            PortfolioCompanyId = portfolioCompanyId,
            KpiId = kpiId,
            ParentKpiId = parentKpiId,
            DisplayOrder = displayOrder,
            CreatedBy = userId,
            CreatedOn = DateTime.Now,
            IsDeleted = false,
            ModuleId = moduleId
        };
    }
    /// <summary>
    /// Represents a mapping for a monthly report.
    /// </summary>
    private MappingMonthlyReport CreateMappingMonthlyReportKpi(int portfolioCompanyId, int kpiId, int? parentKpiId, int displayOrder, int userId)
    {
        return new MappingMonthlyReport()
        {
            PortfolioCompanyId = portfolioCompanyId,
            KpiId = kpiId,
            ParentKpiId = parentKpiId,
            DisplayOrder = displayOrder,
            CreatedBy = userId,
            CreatedOn = DateTime.UtcNow,
            IsDeleted = false
        };
    }
    private void UpdateMappingKpi(MappingCapTable mappingKpi, int displayOrder, int userId, int moduleId, bool isDeleted = false, int? parentKpiId = null)
    {
        if (mappingKpi.DisplayOrder != displayOrder) mappingKpi.DisplayOrder = displayOrder;
        mappingKpi.IsDeleted = isDeleted;
        mappingKpi.ModifiedOn = DateTime.Now;
        mappingKpi.ModifiedBy = userId;
        mappingKpi.ModuleId = moduleId;
        mappingKpi.ParentKpiId = parentKpiId;
    }
    /// <summary>
    /// Updates the mapping of a monthly report KPI.
    /// </summary>
    /// <param name="mappingKpi">The mapping of the monthly report KPI.</param>
    /// <param name="displayOrder">The new display order for the mapping.</param>
    /// <param name="userId">The ID of the user performing the update.</param>
    /// <param name="isDeleted">Indicates whether the mapping is deleted.</param>
    /// <param name="parentKpiId">The ID of the parent KPI.</param>
    private void UpdateMappingMonthlyReportKpi(MappingMonthlyReport mappingKpi, int displayOrder, int userId, bool isDeleted = false, int? parentKpiId = null)
    {
        if (mappingKpi.DisplayOrder != displayOrder) mappingKpi.DisplayOrder = displayOrder;
        mappingKpi.IsDeleted = isDeleted;
        mappingKpi.ModifiedOn = DateTime.Now;
        mappingKpi.ModifiedBy = userId;
        mappingKpi.ParentKpiId = parentKpiId;
    }
    private async Task<bool> UpdateCapTable(int portfolioCompanyId, List<KpiMappingModel> kPIMappingModels, int userId, int moduleID)
    {
        List<MappingCapTable> mappingKpis = [];
        List<MappingCapTable> mappingKpisUpdate = [];
        var mappedList = await _unitOfWork.MappingCapTableRepository.FindAllAsync(x => !x.IsDeleted && x.PortfolioCompanyId == portfolioCompanyId && x.ModuleId == moduleID);
        var lastKpis = mappedList?.OrderByDescending(x => x.MappingId)?.FirstOrDefault(x => !x.IsDeleted && x.PortfolioCompanyId == portfolioCompanyId && x.ModuleId == moduleID);
        var lastDisplayOrder = lastKpis?.DisplayOrder + 1 ?? 1;
        int displayOrder = 1;
        if (lastDisplayOrder != 1)
            displayOrder = lastDisplayOrder;
        foreach (var model in kPIMappingModels)
        {
            if (model.IsMapped && model.MappingKPIId == 0)
            {
                var mapping_Kpis = CreateMappingKpi(portfolioCompanyId, model.Id, null, displayOrder, userId, moduleID);
                mapping_Kpis.IsExtraction = model.IsExtraction;
                mapping_Kpis.Synonym = model.Synonym;
                mapping_Kpis.Definition = model.Definition;
                mappingKpis.Add(mapping_Kpis);
                displayOrder++;
            }
            else if (!model.IsMapped && model.MappingKPIId > 0)
            {
                MappingCapTable mappingKpi = mappedList.FirstOrDefault(s => s.MappingId == model.MappingKPIId);
                UpdateMappingKpi(mappingKpi, displayOrder, userId, moduleID, true);
                mappingKpi.IsExtraction = model.IsExtraction;
                mappingKpi.Synonym = model.Synonym;
                mappingKpi.Definition = model.Definition;
                mappingKpisUpdate.Add(mappingKpi);
                displayOrder++;
            }
            else if (model.IsMapped && model.MappingKPIId > 0)
            {
                MappingCapTable mappingKpi = mappedList.FirstOrDefault(s => s.MappingId == model.MappingKPIId);
                UpdateMappingKpi(mappingKpi, displayOrder, userId, moduleID, false, null);
                mappingKpi.IsExtraction = model.IsExtraction;
                mappingKpi.Synonym = model.Synonym;
                mappingKpi.Definition = model.Definition;
                mappingKpisUpdate.Add(mappingKpi);
                displayOrder++;
            }
            if (model.Children != null && model.Children.Count > 0)
            {
                foreach (var child in model.Children)
                {
                    if (child.IsMapped && child.MappingKPIId == 0)
                    {
                        var mappingKpi = CreateMappingKpi(portfolioCompanyId, child.Id, model.Id, displayOrder, userId, moduleID);
                        mappingKpi.IsExtraction = child.IsExtraction;
                        mappingKpi.Synonym = child.Synonym;
                        mappingKpi.Definition = child.Definition;
                        mappingKpis.Add(mappingKpi);
                        displayOrder++;
                    }
                    else if (child.MappingKPIId > 0)
                    {
                        MappingCapTable mappingKpi = mappedList.FirstOrDefault(s => s.MappingId == child.MappingKPIId);
                        UpdateMappingKpi(mappingKpi, displayOrder, userId, moduleID, !child.IsMapped, model.Id);
                        mappingKpi.IsExtraction = child.IsExtraction;
                        mappingKpi.Synonym = child.Synonym;
                        mappingKpi.Definition = child.Definition;
                        mappingKpisUpdate.Add(mappingKpi);
                        displayOrder++;
                    }
                }
            }
        }
        if (mappingKpis != null && mappingKpis.Count > 0)
        {
            await _unitOfWork.MappingCapTableRepository.AddBulkAsyn(mappingKpis);
            await _unitOfWork.SaveAsync();
        }
        if (mappingKpisUpdate != null && mappingKpisUpdate.Count > 0)
        {
            _unitOfWork.MappingCapTableRepository.UpdateBulk(mappingKpisUpdate);
            await _unitOfWork.SaveAsync();
        }
        return true;
    }
    /// <summary>
    /// Updates the monthly report table with the provided data.
    /// </summary>
    /// <param name="portfolioCompanyId">The ID of the portfolio company.</param>
    /// <param name="kPIMappingModels">The list of KPI mapping models.</param>
    /// <param name="userId">The ID of the user performing the update.</param>
    /// <returns>A task representing the asynchronous operation. The task result is a boolean indicating the success of the update.</returns>
    private async Task<bool> UpdateMonthlyReportTable(int portfolioCompanyId, List<KpiMappingModel> kPIMappingModels, int userId)
    {
        List<MappingMonthlyReport> mappingKpis = [];
        List<MappingMonthlyReport> mappingKpisUpdate = [];
        var mappedList = await _unitOfWork.MappingMonthlyReportRepository.FindAllAsync(x => !x.IsDeleted && x.PortfolioCompanyId == portfolioCompanyId);
        var lastKpis = mappedList?.OrderByDescending(x => x.MappingId)?.FirstOrDefault(x => !x.IsDeleted && x.PortfolioCompanyId == portfolioCompanyId);
        var lastDisplayOrder = lastKpis?.DisplayOrder + 1 ?? 1;
        int displayOrder = 1;
        if (lastDisplayOrder != 1)
            displayOrder = lastDisplayOrder;
        foreach (var model in kPIMappingModels)
        {
            if (model.IsMapped && model.MappingKPIId == 0)
            {
                MappingMonthlyReport mapping_Kpis = CreateMappingMonthlyReportKpi(portfolioCompanyId, model.Id, null, displayOrder, userId);
                mappingKpis.Add(mapping_Kpis);
                displayOrder++;
            }
            else if (!model.IsMapped && model.MappingKPIId > 0)
            {
                MappingMonthlyReport mappingKpi = mappedList.FirstOrDefault(s => s.MappingId == model.MappingKPIId);
                UpdateMappingMonthlyReportKpi(mappingKpi, displayOrder, userId, true);
                mappingKpisUpdate.Add(mappingKpi);
                displayOrder++;
            }
            else if (model.IsMapped && model.MappingKPIId > 0)
            {
                MappingMonthlyReport mappingKpi = mappedList.FirstOrDefault(s => s.MappingId == model.MappingKPIId);
                UpdateMappingMonthlyReportKpi(mappingKpi, displayOrder, userId, false, null);
                mappingKpisUpdate.Add(mappingKpi);
                displayOrder++;
            }
            if (model.Children != null && model.Children.Count > 0)
            {
                foreach (var child in model.Children)
                {
                    if (child.IsMapped && child.MappingKPIId == 0)
                    {
                        MappingMonthlyReport mappingKpi = CreateMappingMonthlyReportKpi(portfolioCompanyId, child.Id, model.Id, displayOrder, userId);
                        mappingKpis.Add(mappingKpi);
                        displayOrder++;
                    }
                    else if (child.MappingKPIId > 0)
                    {
                        MappingMonthlyReport mappingKpi = mappedList.FirstOrDefault(s => s.MappingId == child.MappingKPIId);
                        UpdateMappingMonthlyReportKpi(mappingKpi, displayOrder, userId, !child.IsMapped, model.Id);
                        mappingKpisUpdate.Add(mappingKpi);
                        displayOrder++;
                    }
                }
            }
        }
        if (mappingKpis != null && mappingKpis.Count > 0)
        {
            await _unitOfWork.MappingMonthlyReportRepository.AddBulkAsyn(mappingKpis);
            await _unitOfWork.SaveAsync();
        }
        if (mappingKpisUpdate != null && mappingKpisUpdate.Count > 0)
        {
            _unitOfWork.MappingMonthlyReportRepository.UpdateBulk(mappingKpisUpdate);
            await _unitOfWork.SaveAsync();
        }
        return true;
    }
    #endregion Update KPI Mapping

    #region Update KPI Details

    private void UpdateCompanyKpiDetails(KpiModel kpiModel)
    {
        M_CompanyKpi kpiDetail = _unitOfWork.M_CompanyKPIRepository.GetFirstOrDefault(x => x.CompanywiseKPIID == kpiModel.CompanyKPIDetails.CompanywiseKPIID);
        kpiDetail.Kpi = kpiModel.CompanyKPIDetails.Kpi;
        kpiDetail.KpiInfo = kpiModel.CompanyKPIDetails.KpiInfo;
        kpiDetail.Description = kpiModel.CompanyKPIDetails.Description;
        kpiDetail.ModifiedBy = kpiModel.CompanyKPIDetails.ModifiedBy;
        kpiDetail.IsHeader = kpiModel.CompanyKPIDetails.IsHeader;
        kpiDetail.IsBoldKPI = kpiModel.CompanyKPIDetails.IsBoldKPI;
        kpiDetail.ModifiedOn = DateTime.Now;
        kpiDetail.Synonym = kpiModel.CompanyKPIDetails.Synonym;
        kpiDetail.MethodologyId = (kpiModel.CompanyKPIDetails.KpiInfo != Constants.KpiInfoText) ? kpiModel.CompanyKPIDetails.MethodologyId : 0;
        _unitOfWork.M_CompanyKPIRepository.Update(kpiDetail);
        _unitOfWork.Save();
    }

    private void UpdateImpactKpiDetails(KpiModel kpiModel)
    {
        MImpactKpi kpiDetail = _unitOfWork.M_ImpactKPIRepository.GetFirstOrDefault(x => x.ImpactKpiId == kpiModel.ImpactKpiDetails.ImpactKpiId);
        kpiDetail.Kpi = kpiModel.ImpactKpiDetails.Kpi;
        kpiDetail.KpiInfo = kpiModel.ImpactKpiDetails.KpiInfo;
        kpiDetail.Description = kpiModel.ImpactKpiDetails.Description;
        kpiDetail.IsBoldKPI = kpiModel.ImpactKpiDetails.IsBoldKPI;
        kpiDetail.IsHeader = kpiModel.ImpactKpiDetails.IsHeader;
        kpiDetail.ModifiedBy = kpiModel.ImpactKpiDetails.ModifiedBy;
        kpiDetail.ModifiedOn = DateTime.Now;
        kpiDetail.Synonym = kpiModel.ImpactKpiDetails.Synonym;
        kpiDetail.MethodologyId = (kpiModel.ImpactKpiDetails.KpiInfo != Constants.KpiInfoText) ? kpiModel.ImpactKpiDetails.MethodologyId : 0;
        _unitOfWork.M_ImpactKPIRepository.Update(kpiDetail);
        _unitOfWork.Save();
    }

    private void UpdateInvestmentKpiDetails(KpiModel kpiModel)
    {
        M_InvestmentKpi kpiDetail = _unitOfWork.M_InvestmentKPIRepository.GetFirstOrDefault(x => x.InvestmentKpiId == kpiModel.InvestmentKpiDetails.InvestmentKpiId);
        kpiDetail.Kpi = kpiModel.InvestmentKpiDetails.Kpi;
        kpiDetail.KpiInfo = kpiModel.InvestmentKpiDetails.KpiInfo;
        kpiDetail.Description = kpiModel.InvestmentKpiDetails.Description;
        kpiDetail.ModifiedBy = kpiModel.InvestmentKpiDetails.ModifiedBy;
        kpiDetail.IsBoldKPI = kpiModel.InvestmentKpiDetails.IsBoldKPI;
        kpiDetail.IsHeader = kpiModel.InvestmentKpiDetails.IsHeader;
        kpiDetail.MethodologyId = (kpiModel.InvestmentKpiDetails.KpiInfo != Constants.KpiInfoText) ? kpiModel.InvestmentKpiDetails.MethodologyId : 0;
        kpiDetail.ModifiedOn = DateTime.Now;
        kpiDetail.Synonym = kpiModel.InvestmentKpiDetails.Synonym;
        _unitOfWork.M_InvestmentKPIRepository.Update(kpiDetail);
        _unitOfWork.Save();
    }

    private void UpdateCashflowKpiDetails(KpiModel kpiModel)
    {
        M_CashFlow_LineItems kpiDetail = _unitOfWork.M_CashFlow_LineItemsRepository.GetFirstOrDefault(x => x.CashFlowLineItemID == kpiModel.CashFlow_LineItemsDetails.CashFlowLineItemID);
        kpiDetail.CashFlowLineItem = kpiModel.CashFlow_LineItemsDetails.CashFlowLineItem;
        kpiDetail.KPIInfo = kpiModel.CashFlow_LineItemsDetails.KPIInfo;
        kpiDetail.Description = kpiModel.CashFlow_LineItemsDetails.Description;
        kpiDetail.ModifiedBy = kpiModel.CashFlow_LineItemsDetails.ModifiedBy;
        kpiDetail.MethodologyID = (kpiModel.CashFlow_LineItemsDetails.KPIInfo != Constants.KpiInfoText) ? kpiModel.CashFlow_LineItemsDetails.MethodologyID : 0;
        kpiDetail.ModifiedOn = DateTime.Now;
        kpiDetail.isHeader = kpiModel.CashFlow_LineItemsDetails.isHeader;
        kpiDetail.IsBoldKPI = kpiModel.CashFlow_LineItemsDetails.IsBoldKPI;
        kpiDetail.Synonym = kpiModel.CashFlow_LineItemsDetails.Synonym;
        _unitOfWork.M_CashFlow_LineItemsRepository.Update(kpiDetail);
        _unitOfWork.Save();
    }

    private void UpdateMasterKPIs(KpiModel kpiModel)
    {
        M_MasterKpis masterKpis = _unitOfWork.M_MasterKpisRepository.GetFirstOrDefault(x => x.MasterKpiID == kpiModel.MasterKpiDetails.MasterKpiID);
        masterKpis.KPI = kpiModel.MasterKpiDetails.KPI;
        masterKpis.KpiInfo = kpiModel.MasterKpiDetails.KpiInfo;
        masterKpis.Description = kpiModel.MasterKpiDetails.Description;
        masterKpis.ModifiedBy = kpiModel.MasterKpiDetails.ModifiedBy;
        masterKpis.ModifiedOn = DateTime.Now;
        masterKpis.MethodologyID = (kpiModel.MasterKpiDetails.KpiInfo != Constants.KpiInfoText) ? kpiModel.MasterKpiDetails.MethodologyID : 0;
        masterKpis.Synonym = kpiModel.MasterKpiDetails.Synonym;
        _unitOfWork.M_MasterKpisRepository.Update(masterKpis);
        _unitOfWork.Save();
    }

    private void UpdateProfitAndLossKpiDetails(KpiModel kpiModel)
    {
        M_ProfitAndLoss_LineItems kpiDetail = _unitOfWork.M_ProfitAndLoss_LineItemsRepository.GetFirstOrDefault(x => x.ProfitAndLossLineItemID == kpiModel.ProfitAndLoss_LineItemDetails.ProfitAndLossLineItemID);
        kpiDetail.ProfitAndLossLineItem = kpiModel.ProfitAndLoss_LineItemDetails.ProfitAndLossLineItem;
        kpiDetail.ModifiedBy = kpiModel.ProfitAndLoss_LineItemDetails.ModifiedBy;
        kpiDetail.KPIInfo = kpiModel.ProfitAndLoss_LineItemDetails.KPIInfo;
        kpiDetail.Description = kpiModel.ProfitAndLoss_LineItemDetails.Description;
        kpiDetail.ModifiedBy = kpiModel.ProfitAndLoss_LineItemDetails.ModifiedBy;
        kpiDetail.ModifiedOn = DateTime.Now;
        kpiDetail.isHeader = kpiModel.ProfitAndLoss_LineItemDetails.isHeader;
        kpiDetail.IsBoldKPI = kpiModel.ProfitAndLoss_LineItemDetails.IsBoldKPI;
        kpiDetail.Synonym = kpiModel.ProfitAndLoss_LineItemDetails.Synonym;
        kpiDetail.MethodologyID = (kpiModel.ProfitAndLoss_LineItemDetails.KPIInfo != Constants.KpiInfoText) ? kpiModel.ProfitAndLoss_LineItemDetails.MethodologyID : 0;
        _unitOfWork.M_ProfitAndLoss_LineItemsRepository.Update(kpiDetail);
        _unitOfWork.Save();
    }

    private void UpdateBalanceSheetKpiDetails(KpiModel kpiModel)
    {
        M_BalanceSheet_LineItems kpiDetail = _unitOfWork.M_BalanceSheet_LineItemsRepository.GetFirstOrDefault(x => x.BalanceSheetLineItemID == kpiModel.BalanceSheet_LineItemsDetails.BalanceSheetLineItemID);
        kpiDetail.BalanceSheetLineItem = kpiModel.BalanceSheet_LineItemsDetails.BalanceSheetLineItem;
        kpiDetail.KPIInfo = kpiModel.BalanceSheet_LineItemsDetails.KPIInfo;
        kpiDetail.Description = kpiModel.BalanceSheet_LineItemsDetails.Description;
        kpiDetail.ModifiedOn = DateTime.Now;
        kpiDetail.ModifiedBy = kpiModel.BalanceSheet_LineItemsDetails.ModifiedBy;
        kpiDetail.IsBoldKPI = kpiModel.BalanceSheet_LineItemsDetails.IsBoldKPI;
        kpiDetail.isHeader = kpiModel.BalanceSheet_LineItemsDetails.isHeader;
        kpiDetail.Synonym = kpiModel.BalanceSheet_LineItemsDetails.Synonym;
        kpiDetail.MethodologyID = (kpiModel.BalanceSheet_LineItemsDetails.KPIInfo != Constants.KpiInfoText) ? kpiModel.BalanceSheet_LineItemsDetails.MethodologyID : 0;
        _unitOfWork.M_BalanceSheet_LineItemsRepository.Update(kpiDetail);
        _unitOfWork.Save();
    }

    private void UpdateOperationalKpiDetails(KpiModel kpiModel)
    {
        M_SectorwiseKPI kpiDetail = _unitOfWork.M_SectorwiseKPIRepository.GetFirstOrDefault(x => x.SectorwiseOperationalKPIID == kpiModel.OperationalKpiDetails.SectorwiseKPIID);
        kpiDetail.SectorId = kpiModel.OperationalKpiDetails.Sector.SectorID;
        kpiDetail.Kpi = kpiModel.OperationalKpiDetails.KPI;
        kpiDetail.KpiInfo = kpiModel.OperationalKpiDetails.KpiInfo;
        kpiDetail.Description = kpiModel.OperationalKpiDetails.Description;
        kpiDetail.ModifiedOn = DateTime.Now;
        kpiDetail.ModifiedBy = kpiModel.OperationalKpiDetails.ModifiedBy;
        kpiDetail.IsBoldKPI = kpiModel.OperationalKpiDetails.IsBoldKPI;
        kpiDetail.IsHeader = kpiModel.OperationalKpiDetails.IsHeader;
        kpiDetail.Synonym = kpiModel.OperationalKpiDetails.Synonym;
        kpiDetail.MethodologyId = (kpiModel.OperationalKpiDetails.KpiInfo != Constants.KpiInfoText) ? kpiModel.OperationalKpiDetails.MethodologyId : 0;
        _unitOfWork.M_SectorwiseKPIRepository.Update(kpiDetail);
        _unitOfWork.Save();
    }

    #endregion Update KPI Details

    public static string getKpiInfoType(string kpiInfo)
    {
        if (kpiInfo == "$")
        {
            return "Currency";
        }
        else if (kpiInfo == "#")
        {
            return "Number";
        }
        else
        {
            return kpiInfo ?? "";
        }
    }

    public async Task<KpiExcelDataModel> GetMappedTradingRecords(int fundId)
    {
        KpiExcelDataModel kpiExcelDataModel = new();
        var data = await _dapperGenericRepository.Query<DealQueryModel>(SqlConstants.DealPCListQueryWithFundId, new { FundID = fundId });
        if (data != null && data.Any())
        {
            var companyIdList = data.Select(x => x.PortfolioCompanyID).ToList();
            IDictionary<int?, List<int>> consistantKpis = new Dictionary<int?, List<int>>();
            IDictionary<int?, List<int>> inconsistantkpis = new Dictionary<int?, List<int>>();
            kpiExcelDataModel.CompIds = companyIdList;
            foreach (var compId in companyIdList)
            {
                var tradingRecordsLineitems = await _unitOfWork.Mapping_KpisRepository.FindAllAsync(x => !x.IsDeleted && x.ModuleID == 1 && x.PortfolioCompanyID == compId);
                var iteratedCompaniesKpiIds = tradingRecordsLineitems.OrderBy(x => x.DisplayOrder).Select(x => x.KpiID).ToList();
                ValidateKpis(iteratedCompaniesKpiIds, compId, consistantKpis, inconsistantkpis);
            }
            kpiExcelDataModel = await CheckIfAnyInConsistantKpis(consistantKpis, inconsistantkpis, kpiExcelDataModel, (int)KpiModuleType.TradingRecords, "Trading Records");
        }
        else
        {
            kpiExcelDataModel.status = new Status() { Code = "error", Message = "Selected fund has no associated companies" };
        }
        return kpiExcelDataModel;
    }

    public async Task<KpiExcelDataModel> GetMappedInvestmentKpis(int decryptedFundId, KpiExcelDataModel fundDataModels)
    {
        if (fundDataModels.CompIds is object)
        {
            var companyIdList = fundDataModels.CompIds;
            if (!fundDataModels.CompIds.Any())
            {
                var data = await _dapperGenericRepository.Query<DealQueryModel>(SqlConstants.DealFundPCListQueryWithFundId, new { fundID = decryptedFundId });
                if (data != null && data.Any())
                {
                    fundDataModels.status = new Status() { Code = "error", Message = "Selected fund has no associated companies" };
                    return fundDataModels;
                }
                companyIdList = data.Select(x => x.PortfolioCompanyID).ToList();
            }
            IDictionary<int?, List<int>> consistantKpis = new Dictionary<int?, List<int>>();
            IDictionary<int?, List<int>> inconsistantkpis = new Dictionary<int?, List<int>>();
            foreach (var compId in companyIdList)
            {
                var investmentKpis = await _dapperGenericRepository.Query<MappingPortfolioInvestmentKpi>(SqlConstants.QueryByGetMappingInvestmentKpi, new { @companyId = compId });
                var iteratedCompaniesKpiIds = investmentKpis.Where(x => x.DisplayOrder != null).OrderBy(x => x.DisplayOrder).Select(x => x.KpiId).ToList();
                ValidateKpis(iteratedCompaniesKpiIds, compId, consistantKpis, inconsistantkpis);
            }
            fundDataModels = await CheckIfAnyInConsistantKpis(consistantKpis, inconsistantkpis, fundDataModels, (int)KpiModuleType.Investment, "Investment KPI");
        }
        return fundDataModels;
    }

    public async Task<KpiExcelDataModel> GetMappedOperationalKpis(int fundId, KpiExcelDataModel kpiExcelDataModel)
    {
        if (kpiExcelDataModel.CompIds is object)
        {
            var companyIdList = kpiExcelDataModel.CompIds;
            if (!kpiExcelDataModel.CompIds.Any())
            {
                var data = await _dapperGenericRepository.Query<DealQueryModel>(SqlConstants.DealFundPCListQueryWithFundId, new { fundID = fundId });
                if (data != null && data.Any())
                {
                    kpiExcelDataModel.status = new Status() { Code = "error", Message = "Selected fund has no associated companies" };
                    return kpiExcelDataModel;
                }
                companyIdList = data.Select(x => x.PortfolioCompanyID).ToList();
            }
            IDictionary<int?, List<int>> consistantKpis = new Dictionary<int?, List<int>>();
            IDictionary<int?, List<int>> inconsistantkpis = new Dictionary<int?, List<int>>();
            foreach (var compId in companyIdList)
            {
                var operationalKpis = await _dapperGenericRepository.Query<MappingPortfolioOperationalKpi>(SqlConstants.QueryByMappedOperationalKpis, new { @companyId = compId });
                operationalKpis = operationalKpis.OrderBy(x => x.DisplayOrder).ToList();
                var iteratedCompaniesKpiIds = operationalKpis.Select(x => x.KpiId).ToList();
                ValidateKpis(iteratedCompaniesKpiIds, compId, consistantKpis, inconsistantkpis);
            }
            kpiExcelDataModel = await CheckIfAnyInConsistantKpis(consistantKpis, inconsistantkpis, kpiExcelDataModel, (int)KpiModuleType.Operational, "Operational KPI");
        }
        return kpiExcelDataModel;
    }

    public async Task<List<KpiFormulaModel>> GetKpisByType(string type, int CompanyId, int moduleId)
    {
        return await _dapperGenericRepository.Query<KpiFormulaModel>(SqlConstants.QueryByKpisByType, new { @KpiType = type, @PortfolioCompanyId = CompanyId, @ModuleId = moduleId });
    }

    public async Task<int> UpdateFormulaByKPIId(KpiFormula kpiFormula)
    {
        return await _dapperGenericRepository.QueryExecuteAsync<int>(SqlConstants.QueryByUpdateFormulaById, new { @KpiType = kpiFormula.KpiType, @KpiId = kpiFormula.KpiId, @Formula = kpiFormula.Formula, @FormulaKpiId = kpiFormula.FormulaKpiId, @MappingId = kpiFormula.MappingId, @ModifiedBy = kpiFormula.ModifiedBy });
    }

    public async Task<int> CopyKPIToCompanies(CopyToKpiQueryModel copyToKPIQueryModel)
    {
        return await _dapperGenericRepository.QueryExecuteAsync<int>(SqlConstants.QueryByCopyToCompanies, new
        {
            @KpiType = copyToKPIQueryModel.KpiType,
            @CompanyId = copyToKPIQueryModel.CompanyId,
            @UserId = copyToKPIQueryModel.UserId,
            @CompanyIds = copyToKPIQueryModel.CompanyIds,
            @ModuleId = copyToKPIQueryModel.ModuleId
        });
    }

    private async Task<KpiExcelDataModel> CheckIfAnyInConsistantKpis(IDictionary<int?, List<int>> consistantKpis, IDictionary<int?,
        List<int>> inconsistantkpis, KpiExcelDataModel kpiExcelDataModel, int moduleId, string kpiType)
    {
        string sheetCode = Constants.FundPcSheetName;
        if (inconsistantkpis.Any())
        {
            var compIds = inconsistantkpis.Keys.ToList();
            var compData = await _unitOfWork.PortfolioCompanyDetailRepository.FindByAsyn(x => !x.IsDeleted && compIds.Contains(x.PortfolioCompanyId));
            var compNames = string.Join(", ", compData.Select(x => x.CompanyName).ToList());
            kpiExcelDataModel.status = new Status()
            {
                Code = "error",
                Message = $"The {kpiType} mapping for the following company(s) are inconsistent. {compNames} .Please check the KPI mapping and submit again."
            };
            return kpiExcelDataModel;
        }
        List<int> kpiIds = consistantKpis.FirstOrDefault().Value.ToList();
        switch (moduleId)
        {
            case (int)KpiModuleType.TradingRecords:
                kpiExcelDataModel = await GetMappedFundTradingRecords(kpiExcelDataModel, kpiIds);
                break;

            case (int)KpiModuleType.Investment:
                kpiExcelDataModel = await GetMappedFundInvestmentRecords(kpiExcelDataModel, kpiIds);
                break;

            case (int)KpiModuleType.Operational:
                kpiExcelDataModel = await GetMappedFundOperationalRecords(kpiExcelDataModel, kpiIds);
                sheetCode = Constants.FundOperationalKpiSheetName;
                break;
        }

        kpiExcelDataModel.status = new Status()
        {
            Code = kpiExcelDataModel.kpiDataModels.Any() ? "ok" : "error",
            Message = kpiExcelDataModel.kpiDataModels.Any() ? "Kpis are mapped to companies" : "Kpis are not mapped to any companies",
            SheetCode = sheetCode
        };
        if (moduleId == (int)KpiModuleType.Operational && !kpiExcelDataModel.kpiDataModels.Any())
        {
            kpiExcelDataModel.status.Code = "not found";
        }
        return kpiExcelDataModel;
    }

    private async Task<KpiExcelDataModel> GetMappedFundOperationalRecords(KpiExcelDataModel kpiExcelDataModel, List<int> kpiIds)
    {
        List<M_SectorwiseKPI> kpisData = new();
        var kpiDetails = await _unitOfWork.M_SectorwiseKPIRepository.FindAllAsync(x => !x.IsDeleted && kpiIds.Contains(x.SectorwiseOperationalKPIID));
        foreach (var id in kpiIds)
        {
            kpisData.Add(kpiDetails.FirstOrDefault(x => x.SectorwiseOperationalKPIID == id));
        }
        if (kpiExcelDataModel.kpiDataModels != null)
        {
            kpiExcelDataModel.kpiDataModels.AddRange(kpisData.Select(x => new KpiDataModel() { Id = x.SectorwiseOperationalKPIID, Name = x.Kpi, Prefix = Constants.OperationalKPIPrefix }).ToList());
        }
        else
        {
            kpiExcelDataModel.kpiDataModels = kpisData.Select(x => new KpiDataModel() { Id = x.SectorwiseOperationalKPIID, Name = x.Kpi, Prefix = Constants.OperationalKPIPrefix }).ToList();
        }
        return kpiExcelDataModel;
    }

    private async Task<KpiExcelDataModel> GetMappedFundInvestmentRecords(KpiExcelDataModel kpiExcelDataModel, List<int> kpiIds)
    {
        var kpiData = await _dapperGenericRepository.Query<M_InvestmentKpi>(SqlConstants.QueryByGetInvestmentKpis);
        kpiData = (from kpiId in kpiIds join kpiMaster in kpiData on kpiId equals kpiMaster.InvestmentKpiId select kpiMaster)?.ToList();
        if (kpiExcelDataModel.kpiDataModels != null)
        {
            kpiExcelDataModel.kpiDataModels.AddRange(kpiData.Select(x => new KpiDataModel() { Id = x.InvestmentKpiId, Name = x.Kpi, Prefix = Constants.InvestmentKPIPrefix }).ToList());
        }
        else
        {
            kpiExcelDataModel.kpiDataModels = kpiData?.Select(x => new KpiDataModel() { Id = x.InvestmentKpiId, Name = x.Kpi, Prefix = Constants.InvestmentKPIPrefix }).ToList();
        }
        return kpiExcelDataModel;
    }

    private async Task<KpiExcelDataModel> GetMappedFundTradingRecords(KpiExcelDataModel kpiExcelDataModel, List<int> kpiIds)
    {
        List<M_MasterKpis> kpisData = new();
        var kpiDetails = await _unitOfWork.M_MasterKpisRepository.FindAllAsync(x => !x.IsDeleted && kpiIds.Contains(x.MasterKpiID));
        foreach (var id in kpiIds)
        {
            kpisData.Add(kpiDetails.FirstOrDefault(x => x.MasterKpiID == id));
        }
        if (kpiExcelDataModel.kpiDataModels != null)
        {
            kpiExcelDataModel.kpiDataModels.AddRange(kpisData.Select(x => new KpiDataModel() { Id = x.MasterKpiID, Name = x.KPI, Prefix = Constants.TradingKPIPrefix }).ToList());
        }
        else
        {
            kpiExcelDataModel.kpiDataModels = kpisData.Select(x => new KpiDataModel() { Id = x.MasterKpiID, Name = x.KPI, Prefix = Constants.TradingKPIPrefix }).ToList();
        }
        return kpiExcelDataModel;
    }

    private void ValidateKpis(List<int> iteratedCompaniesKpiIds, int? compId, IDictionary<int?,
        List<int>> consistantKpis, IDictionary<int?, List<int>> inconsistantkpis)
    {
        if (consistantKpis.Count == 0)
        {
            consistantKpis.Add(new KeyValuePair<int?, List<int>>(compId, iteratedCompaniesKpiIds));
        }
        else
        {
            var consistantCompKpiIds = consistantKpis.FirstOrDefault().Value;
            if (iteratedCompaniesKpiIds.Count == consistantCompKpiIds.Count && iteratedCompaniesKpiIds.All(consistantCompKpiIds.Contains))
            {
                consistantKpis.Add(new KeyValuePair<int?, List<int>>(compId, iteratedCompaniesKpiIds));
            }
            else
            {
                if (iteratedCompaniesKpiIds.Count > consistantCompKpiIds.Count)
                {
                    foreach (var element in consistantKpis)
                        inconsistantkpis.Add(element);

                    consistantKpis.Clear();
                    consistantKpis.Add(new KeyValuePair<int?, List<int>>(compId, iteratedCompaniesKpiIds));
                }
                else
                {
                    inconsistantkpis.Add(new KeyValuePair<int?, List<int>>(compId, iteratedCompaniesKpiIds));
                }
            }
        }
    }

    public string GetKpiFormula(KpiFormula formula)
    {
        StringBuilder sb = new StringBuilder();
        if (formula is not null)
        {
            string inputModel = FormulaHelper.RawFormulaSplit(formula.Formula);
            string inputFormula = inputModel.Replace("^^", string.Empty);
            char[] alphabets = { '-', '+', '*', '/', '(', ')' };
            List<string> result = inputFormula.SplitAndKeep(alphabets).ToList();
            if (result is not null)
            {
                int index = 0;
                result.ForEach(x =>
                {
                    if (index == 0)
                    {
                        sb.Append("<div data-element='kpi-empty' class='d-inline -block element-empty' id='" + index + ".1'>&nbsp</div>");
                        sb.Append("<div data-element='kpi-empty' class='d-inline -block element-empty' id='" + index + ".2' >&nbsp</div>");
                    }
                    if (!alphabets.Any(x.Contains))
                    {
                        if (x.Contains("_"))
                        {
                            var kpi = GetKpiValues(x);
                            sb.Append("<div data-element='kpi' contenteditable='false' (click)='setKPIElement($event)' class='custom-formula-div d-inline-block m-1 pr-2 kpi-formula-btn' id='" + index + "' data-attr='" + x + "'" +
                                "title='" + kpi + "'>" + kpi + "</div>");
                        }
                        else
                        {
                            sb.Append("<div data-element='kpi' tabindex='0' contenteditable='false' (click)='setSymbolElement($event)' class='text-center custom-formula-div pr-2 pl-2 d-inline-block m-1' id='" + index + "'  data-attr='" + x + "' title='" + x + "'>" + x + "</div>");
                        }
                    }
                    else
                    {
                        string value = x.Equals("*") ? "x" : x;
                        sb.Append("<div data-element='kpi' tabindex='0' contenteditable='false' (click)='setSymbolElement($event)' class='text-center custom-formula-div pr-2 pl-2 d-inline-block formula-symbol m-1'id='" + index + "' data-attr='" + value + "' title='" + value + "'>" + value + "</div>");
                    }
                    sb.Append("<div data-element='kpi-empty' class='d-inline -block element-empty' id='" + index + ".3'>&nbsp</div>");
                    sb.Append("<div data-element='kpi-empty' class='d-inline -block element-empty' id='" + index + ".4'>&nbsp</div>");
                    index++;
                });
            }
        }
        return sb.ToString();
    }

    public string GetKpiValues(string formulaValue)
    {
        string[] splitValues = formulaValue.Split("_");
        string type = splitValues[0];
        _ = int.TryParse(splitValues[1], out int num);
        var kpi = "";
        if (num > 0)
        {
            switch (type)
            {
                case Constants.PROFIT_LOSS_KPI_PREFIX:
                    kpi = _dapperGenericRepository.QueryFirst<FormulaMaster>(SqlConstants.QueryByspGetKpiByFormulaKpiId, new { KpiId = num, Module = Constants.FormulaPL }).Kpi;
                    break;

                case Constants.BALANCE_SHEET_KPI_PREFIX:
                    kpi = _dapperGenericRepository.QueryFirst<FormulaMaster>(SqlConstants.QueryByspGetKpiByFormulaKpiId, new { KpiId = num, Module = Constants.FormulaBS }).Kpi;
                    break;

                case Constants.CASHFLOW_KPI_PREFIX:
                    kpi = _dapperGenericRepository.QueryFirst<FormulaMaster>(SqlConstants.QueryByspGetKpiByFormulaKpiId, new { KpiId = num, Module = Constants.FormulaCF }).Kpi;
                    break;

                case Constants.TRADING_RECORDS_PREFIX:
                    kpi = _dapperGenericRepository.QueryFirst<FormulaMaster>(SqlConstants.QueryByspGetKpiByFormulaKpiId, new { KpiId = num, Module = Constants.FormulaTradingRecords }).Kpi;
                    break;

                case Constants.INVESTMENT_KPI_PREFIX:
                    kpi = _dapperGenericRepository.QueryFirst<FormulaMaster>(SqlConstants.QueryByspGetKpiByFormulaKpiId, new { KpiId = num, Module = Constants.FormulaInvestment }).Kpi;
                    break;

                case Constants.OPERATIONAL_KPI_PREFIX:
                    kpi = _dapperGenericRepository.QueryFirst<FormulaMaster>(SqlConstants.QueryByspGetKpiByFormulaKpiId, new { KpiId = num, Module = Constants.FormulaOperational }).Kpi;
                    break;

                case Constants.CREDIT_KPI_PREFIX:
                    kpi = _dapperGenericRepository.QueryFirst<FormulaMaster>(SqlConstants.QueryByspGetKpiByFormulaKpiId, new { KpiId = num, Module = Constants.FormulaCreditKPI }).Kpi;
                    break;

                case Constants.IMPACT_KPI_PREFIX:
                    kpi = _dapperGenericRepository.QueryFirst<FormulaMaster>(SqlConstants.QueryByspGetKpiByFormulaKpiId, new { KpiId = num, Module = Constants.FormulaImpact }).Kpi;
                    break;

                case Constants.COMPANY_KPI_PREFIX:
                    kpi = _dapperGenericRepository.QueryFirst<FormulaMaster>(SqlConstants.QueryByspGetKpiByFormulaKpiId, new { KpiId = num, Module = Constants.FormulaCompany }).Kpi;
                    break;
                default:
                    if (type.Contains(Constants.Cap_KPI_PREFIX))
                    {
                        kpi = _dapperGenericRepository.QueryFirst<FormulaMaster>(SqlConstants.QueryByspGetKpiByFormulaKpiId, new { KpiId = num, Module = Constants.FormulaCapTable })?.Kpi;
                    }
                    break;

            }
        }

        return Regex.Replace(kpi, @"[ ]{2,}", "", RegexOptions.None, TimeSpan.FromSeconds(5));
    }
    /// <summary>
    /// UpdateCompLevelSynonyms
    /// </summary>
    /// <param name="mappedKpisSynonymData"></param>
    /// <returns></returns>
    public async Task<int> UpdateCompLevelSynonyms(MappedKpisSynonymsModel mappedKpisSynonymData)
    {
        if (mappedKpisSynonymData.IsFundKpi)
        {
            return await UpdateFundKpisSynonyms(mappedKpisSynonymData);
        }
        return mappedKpisSynonymData.ModuleId switch
        {
            (int)KpiModuleType.CreditKPI or (int)KpiModuleType.TradingRecords
            or (int)KpiModuleType.CustomTable1 or (int)KpiModuleType.CustomTable2 or (int)KpiModuleType.CustomTable3 or (int)KpiModuleType.CustomTable4
            or (int)KpiModuleType.OtherKPI1 or (int)KpiModuleType.OtherKPI2 or (int)KpiModuleType.OtherKPI3 or (int)KpiModuleType.OtherKPI4 or (int)KpiModuleType.OtherKPI5
            or (int)KpiModuleType.OtherKPI6 or (int)KpiModuleType.OtherKPI7 or (int)KpiModuleType.OtherKPI8 or (int)KpiModuleType.OtherKPI9 or (int)KpiModuleType.OtherKPI10
            => await UpdateMasterKpisSynonyms(mappedKpisSynonymData),
            (int)KpiModuleType.ProfitAndLoss => await UpdateProfitAndLossSynonyms(mappedKpisSynonymData),
            (int)KpiModuleType.BalanceSheet => await UpdateBalanceSheetSynonyms(mappedKpisSynonymData),
            (int)KpiModuleType.CashFlow => await UpdateCashFlowSynonyms(mappedKpisSynonymData),
            (int)KpiModuleType.Investment => await UpdateInvestmentKpisSynonyms(mappedKpisSynonymData),
            (int)KpiModuleType.Impact => await UpdateImpactKpisSynonyms(mappedKpisSynonymData),
            (int)KpiModuleType.Operational => await UpdateOperationalKpisSynonyms(mappedKpisSynonymData),
            (int)KpiModuleType.Company => await UpdateCompanyKpisSynonyms(mappedKpisSynonymData),
            (int)KpiModuleType.CapTable1 or (int)KpiModuleType.CapTable2 or (int)KpiModuleType.CapTable3 or (int)KpiModuleType.CapTable4 or (int)KpiModuleType.CapTable5 or
            (int)KpiModuleType.CapTable6 or(int) KpiModuleType.CapTable7 or(int) KpiModuleType.CapTable8 or(int) KpiModuleType.CapTable9 or(int) KpiModuleType.CapTable10 or
            (int)KpiModuleType.OtherCapTable1 or (int)KpiModuleType.OtherCapTable2 or (int)KpiModuleType.OtherCapTable3 or (int)KpiModuleType.OtherCapTable4 or (int)KpiModuleType.OtherCapTable5 or
            (int)KpiModuleType.OtherCapTable6 or (int)KpiModuleType.OtherCapTable7 or (int)KpiModuleType.OtherCapTable8 or (int)KpiModuleType.OtherCapTable9 or (int)KpiModuleType.OtherCapTable10 
            => await UpdateCapTableSynonyms(mappedKpisSynonymData),
            _ => 0,
        };
    }
    /// <summary>
    /// UpdateMasterKpisSynonyms
    /// </summary>
    /// <param name="mappedKpisSynonymData"></param>
    /// <returns></returns>
    private async Task<int> UpdateMasterKpisSynonyms(MappedKpisSynonymsModel mappedKpisSynonymData)
    {
        var existingData = await _unitOfWork.Mapping_KpisRepository.FindFirstAsync(x => x.Mapping_KpisID == mappedKpisSynonymData.MappingId && !x.IsDeleted);
        if (existingData != null)
        {
            existingData.Synonym = mappedKpisSynonymData.Synonym;
            existingData.ModifiedBy = mappedKpisSynonymData.ModifiedBy;
            existingData.ModifiedOn = DateTime.UtcNow;
            _unitOfWork.Mapping_KpisRepository.Update(existingData);
            _unitOfWork.Save();
            return 1;
        }
        return 0;
    }
    /// <summary>
    /// UpdateInvestmentKpisSynonyms
    /// </summary>
    /// <param name="mappedKpisSynonymData"></param>
    /// <returns></returns>
    private async Task<int> UpdateInvestmentKpisSynonyms(MappedKpisSynonymsModel mappedKpisSynonymData)
    {
        var existingData = await _unitOfWork.PortfolioInvestmentKpiMappingRepository.FindFirstAsync(x => x.MappingPortfolioInvestmentKpiId == mappedKpisSynonymData.MappingId && !x.IsDeleted);
        if (existingData != null)
        {
            existingData.Synonym = mappedKpisSynonymData.Synonym;
            existingData.ModifiedBy = mappedKpisSynonymData.ModifiedBy;
            existingData.ModifiedOn = DateTime.UtcNow;
            _unitOfWork.PortfolioInvestmentKpiMappingRepository.Update(existingData);
            _unitOfWork.Save();
            return 1;
        }
        return 0;
    }
    /// <summary>
    /// UpdateImpactKpisSynonyms
    /// </summary>
    /// <param name="mappedKpisSynonymData"></param>
    /// <returns></returns>
    private async Task<int> UpdateImpactKpisSynonyms(MappedKpisSynonymsModel mappedKpisSynonymData)
    {
        var existingData = await _unitOfWork.Mapping_ImpactKPI_OrderRepository.FindFirstAsync(x => x.ImpactKPIMappingID == mappedKpisSynonymData.MappingId && !x.IsDeleted);
        if (existingData != null)
        {
            existingData.Synonym = mappedKpisSynonymData.Synonym;
            existingData.ModifiedBy = mappedKpisSynonymData.ModifiedBy;
            existingData.ModifiedOn = DateTime.UtcNow;
            _unitOfWork.Mapping_ImpactKPI_OrderRepository.Update(existingData);
            _unitOfWork.Save();
            return 1;
        }
        return 0;
    }
    /// <summary>
    /// UpdateOperationalKpisSynonyms
    /// </summary>
    /// <param name="mappedKpisSynonymData"></param>
    /// <returns></returns>
    private async Task<int> UpdateOperationalKpisSynonyms(MappedKpisSynonymsModel mappedKpisSynonymData)
    {
        var existingData = await _unitOfWork.MappingPortfolioOperationalKpi_OrderRepository.FindFirstAsync(x => x.MappingPortfolioOperationalKpiId == mappedKpisSynonymData.MappingId && !x.IsDeleted);
        if (existingData != null)
        {
            existingData.Synonym = mappedKpisSynonymData.Synonym;
            existingData.ModifiedBy = mappedKpisSynonymData.ModifiedBy;
            existingData.ModifiedOn = DateTime.UtcNow;
            _unitOfWork.MappingPortfolioOperationalKpi_OrderRepository.Update(existingData);
            _unitOfWork.Save();
            return 1;
        }
        return 0;
    }
    /// <summary>
    /// UpdateCompanyKpisSynonyms
    /// </summary>
    /// <param name="mappedKpisSynonymData"></param>
    /// <returns></returns>
    private async Task<int> UpdateCompanyKpisSynonyms(MappedKpisSynonymsModel mappedKpisSynonymData)
    {
        var existingData = await _unitOfWork.PortfolioCompanyKpiMappingRepository.FindFirstAsync(x => x.MappingPortfolioCompanyKpiId == mappedKpisSynonymData.MappingId && !x.IsDeleted);
        if (existingData != null)
        {
            existingData.Synonym = mappedKpisSynonymData.Synonym;
            existingData.ModifiedBy = mappedKpisSynonymData.ModifiedBy;
            existingData.ModifiedOn = DateTime.UtcNow;
            _unitOfWork.PortfolioCompanyKpiMappingRepository.Update(existingData);
            _unitOfWork.Save();
            return 1;
        }
        return 0;
    }
    /// <summary>
    /// UpdateProfitAndLossSynonyms
    /// </summary>
    /// <param name="mappedKpisSynonymData"></param>
    /// <returns></returns>
    private async Task<int> UpdateProfitAndLossSynonyms(MappedKpisSynonymsModel mappedKpisSynonymData)
    {
        var existingData = await _unitOfWork.Mapping_CompanyProfitAndLossLineItemsRepository.FindFirstAsync(x => x.CompanyProfitAndLossLineItemMappingID == mappedKpisSynonymData.MappingId && !x.IsDeleted);
        if (existingData != null)
        {
            existingData.Synonym = mappedKpisSynonymData.Synonym;
            existingData.ModifiedBy = mappedKpisSynonymData.ModifiedBy;
            existingData.ModifiedOn = DateTime.UtcNow;
            _unitOfWork.Mapping_CompanyProfitAndLossLineItemsRepository.Update(existingData);
            _unitOfWork.Save();
            return 1;
        }
        return 0;
    }
    /// <summary>
    /// UpdateBalanceSheetSynonyms
    /// </summary>
    /// <param name="mappedKpisSynonymData"></param>
    /// <returns></returns>
    private async Task<int> UpdateBalanceSheetSynonyms(MappedKpisSynonymsModel mappedKpisSynonymData)
    {
        var existingData = await _unitOfWork.Mapping_CompanyBalanceSheetLineItemsRepository.FindFirstAsync(x => x.CompanyBalanceSheetLineItemMappingID == mappedKpisSynonymData.MappingId && !x.IsDeleted);
        if (existingData != null)
        {
            existingData.Synonym = mappedKpisSynonymData.Synonym;
            existingData.ModifiedBy = mappedKpisSynonymData.ModifiedBy;
            existingData.ModifiedOn = DateTime.UtcNow;
            _unitOfWork.Mapping_CompanyBalanceSheetLineItemsRepository.Update(existingData);
            _unitOfWork.Save();
            return 1;
        }
        return 0;
    }
    /// <summary>
    /// UpdateCashFlowSynonyms
    /// </summary>
    /// <param name="mappedKpisSynonymData"></param>
    /// <returns></returns>
    private async Task<int> UpdateCashFlowSynonyms(MappedKpisSynonymsModel mappedKpisSynonymData)
    {
        var existingData = await _unitOfWork.Mapping_CompanyCashFlowLineItemsRepository.FindFirstAsync(x => x.CompanyCashFlowLineItemMappingID == mappedKpisSynonymData.MappingId && !x.IsDeleted);
        if (existingData != null)
        {
            existingData.Synonym = mappedKpisSynonymData.Synonym;
            existingData.ModifiedBy = mappedKpisSynonymData.ModifiedBy;
            existingData.ModifiedOn = DateTime.UtcNow;
            _unitOfWork.Mapping_CompanyCashFlowLineItemsRepository.Update(existingData);
            _unitOfWork.Save();
            return 1;
        }
        return 0;
    }
    /// <summary>
    /// UpdateCapTableSynonyms
    /// </summary>
    /// <param name="mappedKpisSynonymData"></param>
    /// <returns></returns>
    private async Task<int> UpdateCapTableSynonyms(MappedKpisSynonymsModel mappedKpisSynonymData)
    {
        var existingData = await _unitOfWork.MappingCapTableRepository.FindFirstAsync(x => x.MappingId == mappedKpisSynonymData.MappingId && !x.IsDeleted);
        if (existingData != null)
        {
            existingData.Synonym = mappedKpisSynonymData.Synonym;
            existingData.ModifiedBy = mappedKpisSynonymData.ModifiedBy;
            existingData.ModifiedOn = DateTime.UtcNow;
            _unitOfWork.MappingCapTableRepository.Update(existingData);
            _unitOfWork.Save();
            return 1;
        }
        return 0;
    }
    /// <summary>
    /// UpdateFundKpisSynonyms
    /// </summary>
    /// <param name="mappedKpisSynonymData"></param>
    /// <returns></returns>
    private async Task<int> UpdateFundKpisSynonyms(MappedKpisSynonymsModel mappedKpisSynonymData)
    {
        var existingData = await _unitOfWork.MappingFundSectionKpiRepository.FindFirstAsync(x => x.MappingFundSectionKpiId == mappedKpisSynonymData.MappingId && !x.IsDeleted);
        if (existingData != null)
        {
            existingData.Synonym = mappedKpisSynonymData.Synonym;
            existingData.ModifiedBy = mappedKpisSynonymData.ModifiedBy;
            existingData.ModifiedOn = DateTime.UtcNow;
            _unitOfWork.MappingFundSectionKpiRepository.Update(existingData);
            _unitOfWork.Save();
            return 1;
        }
        return 0;
    }

    /// <summary>
    /// Retrieves all mapped KPIs for the specified company IDs.
    /// </summary>
    /// <param name="companyIds">A comma-separated string of company IDs.</param>
    /// <returns>A task that represents the asynchronous operation, containing a list of <see cref="AllKpiMappingModel"/>.</returns>
    public async Task<List<AllKpiMappingModel>> GetAllMappedKPIs(string companyIds)
    {
        if(string.IsNullOrWhiteSpace(companyIds))
            return new List<AllKpiMappingModel>();
        var allKpis = await _dapperGenericRepository.Query<AllKpiMappingModel>(SqlConstants.ProcGetAllMappedKpi, new { @companyIds = companyIds.Trim() });
        return allKpis;
    }
}
