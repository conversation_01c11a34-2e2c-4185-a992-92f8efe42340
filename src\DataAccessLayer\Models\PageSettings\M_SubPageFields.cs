﻿
using System.ComponentModel.DataAnnotations;
namespace DataAccessLayer.DBModel
{
    public partial class M_SubPageFields : PageSettingBaseModel
    {
        [Key]
        public int FieldID { get; set; }       
        public int SubPageID { get; set; }
        public string EncryptedSubPageID { get; set; }
        public bool IsMandatory { get; set; }
        public int? DataTypeId { get; set; }
        public bool IsListData { get; set; }
        public bool ShowOnList { get; set; }
        public bool IsChart { get; set; }
        public bool IsHighLight { get; set; }
        public bool IsPcLink { get; set; }
        public bool IsTrend { get; set; }

    }
}




