using EmailConfiguration.Helpers;
using DataAccessLayer.UnitOfWork;
using DataAccessLayer.GenericRepository;
using DataAccessLayer.Models.EmailNotifications;
using Moq;
using Xunit;

namespace EmailConfiguration.UnitTest.Helpers
{
    /// <summary>
    /// Unit tests for EmailReminderDataHelper
    /// </summary>
    public class EmailReminderDataHelperTests
    {
        private readonly Mock<IUnitOfWork> _unitOfWorkMock;
        private readonly Mock<IGenericRepository<EmailReminderSchedule>> _scheduleRepoMock;

        public EmailReminderDataHelperTests()
        {
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _scheduleRepoMock = new Mock<IGenericRepository<EmailReminderSchedule>>();
            _unitOfWorkMock.Setup(u => u.EmailReminderScheduleRepository).Returns(_scheduleRepoMock.Object);
        }

        [Fact]
        public async Task GetScheduleStatisticsAsync_NoSchedules_ReturnsZeroValues()
        {
            // Arrange
            var configId = 1;
            var schedules = new List<EmailReminderSchedule>();

            _scheduleRepoMock.Setup(r => r.GetManyAsync(It.IsAny<Func<EmailReminderSchedule, bool>>()))
                .ReturnsAsync(schedules);

            // Act
            var result = await EmailReminderDataHelper.GetScheduleStatisticsAsync(_unitOfWorkMock.Object, configId);

            // Assert
            Assert.Equal(0, result.totalSent);
            Assert.Equal(0, result.nonSentandnonPending);
            Assert.Null(result.lastSentDate);
            Assert.Null(result.nextScheduledDate);
        }

        [Fact]
        public async Task GetScheduleStatisticsAsync_OnlySentSchedules_ReturnsCorrectStatistics()
        {
            // Arrange
            var configId = 1;
            var schedules = new List<EmailReminderSchedule>
            {
                new EmailReminderSchedule 
                { 
                    Id = 1, 
                    ReminderConfigId = configId, 
                    Status = ReminderStatus.Sent, // Sent
                    SentDate = new DateTime(2024, 1, 1),
                    IsActive = true,
                    IsDeleted = false
                },
                new EmailReminderSchedule 
                { 
                    Id = 2, 
                    ReminderConfigId = configId, 
                    Status = ReminderStatus.Sent, // Sent
                    SentDate = new DateTime(2024, 1, 5),
                    IsActive = true,
                    IsDeleted = false
                },
                new EmailReminderSchedule 
                { 
                    Id = 3, 
                    ReminderConfigId = configId, 
                    Status = ReminderStatus.Sent, // Sent
                    SentDate = new DateTime(2024, 1, 3),
                    IsActive = true,
                    IsDeleted = false
                }
            };

            _scheduleRepoMock.Setup(r => r.GetManyAsync(It.IsAny<Func<EmailReminderSchedule, bool>>()))
                .ReturnsAsync(schedules);

            // Act
            var result = await EmailReminderDataHelper.GetScheduleStatisticsAsync(_unitOfWorkMock.Object, configId);

            // Assert
            Assert.Equal(3, result.totalSent);
            Assert.Equal(0, result.nonSentandnonPending);
            Assert.Equal(new DateTime(2024, 1, 5), result.lastSentDate); // Latest sent date
            Assert.Null(result.nextScheduledDate); // No pending schedules
        }

        [Fact]
        public async Task GetScheduleStatisticsAsync_OnlyPendingSchedules_ReturnsCorrectStatistics()
        {
            // Arrange
            var configId = 1;
            var schedules = new List<EmailReminderSchedule>
            {
                new EmailReminderSchedule 
                { 
                    Id = 1, 
                    ReminderConfigId = configId, 
                    Status = ReminderStatus.Pending, // Pending
                    ReminderDate = new DateTime(2024, 1, 15),
                    IsActive = true,
                    IsDeleted = false
                },
                new EmailReminderSchedule 
                { 
                    Id = 2, 
                    ReminderConfigId = configId, 
                    Status = ReminderStatus.Pending, // Pending
                    ReminderDate = new DateTime(2024, 1, 10),
                    IsActive = true,
                    IsDeleted = false
                },
                new EmailReminderSchedule 
                { 
                    Id = 3, 
                    ReminderConfigId = configId, 
                    Status = ReminderStatus.Pending, // Pending
                    ReminderDate = new DateTime(2024, 1, 20),
                    IsActive = true,
                    IsDeleted = false
                }
            };

            _scheduleRepoMock.Setup(r => r.GetManyAsync(It.IsAny<Func<EmailReminderSchedule, bool>>()))
                .ReturnsAsync(schedules);

            // Act
            var result = await EmailReminderDataHelper.GetScheduleStatisticsAsync(_unitOfWorkMock.Object, configId);

            // Assert
            Assert.Equal(0, result.totalSent);
            Assert.Equal(0, result.nonSentandnonPending);
            Assert.Null(result.lastSentDate);
            Assert.Equal(new DateTime(2024, 1, 10), result.nextScheduledDate); // Earliest pending date
        }

        [Fact]
        public async Task GetScheduleStatisticsAsync_MixedSchedules_ReturnsCorrectStatistics()
        {
            // Arrange
            var configId = 1;
            var schedules = new List<EmailReminderSchedule>
            {
                new EmailReminderSchedule 
                { 
                    Id = 1, 
                    ReminderConfigId = configId, 
                    Status = ReminderStatus.Sent, // Sent
                    SentDate = new DateTime(2024, 1, 1),
                    IsActive = true,
                    IsDeleted = false
                },
                new EmailReminderSchedule 
                { 
                    Id = 2, 
                    ReminderConfigId = configId, 
                    Status = ReminderStatus.Sent, // Sent
                    SentDate = new DateTime(2024, 1, 5),
                    IsActive = true,
                    IsDeleted = false
                },
                new EmailReminderSchedule 
                { 
                    Id = 3, 
                    ReminderConfigId = configId, 
                    Status = ReminderStatus.Pending, // Pending
                    ReminderDate = new DateTime(2024, 1, 15),
                    IsActive = true,
                    IsDeleted = false
                },
                new EmailReminderSchedule 
                { 
                    Id = 4, 
                    ReminderConfigId = configId, 
                    Status = ReminderStatus.Pending, // Pending
                    ReminderDate = new DateTime(2024, 1, 10),
                    IsActive = true,
                    IsDeleted = false
                },
                new EmailReminderSchedule 
                { 
                    Id = 5, 
                    ReminderConfigId = configId, 
                    Status = ReminderStatus.Failed, // Failed
                    ReminderDate = new DateTime(2024, 1, 8),
                    IsActive = true,
                    IsDeleted = false
                }
            };

            _scheduleRepoMock.Setup(r => r.GetManyAsync(It.IsAny<Func<EmailReminderSchedule, bool>>()))
                .ReturnsAsync(schedules);

            // Act
            var result = await EmailReminderDataHelper.GetScheduleStatisticsAsync(_unitOfWorkMock.Object, configId);

            // Assert
            Assert.Equal(2, result.totalSent); // Only sent schedules
            Assert.Equal(1, result.nonSentandnonPending);
            Assert.Equal(new DateTime(2024, 1, 5), result.lastSentDate); // Latest sent date
            Assert.Equal(new DateTime(2024, 1, 10), result.nextScheduledDate); // Earliest pending date
        }

        [Fact]
        public async Task GetScheduleStatisticsAsync_SentWithoutSentDate_ExcludedFromCount()
        {
            // Arrange
            var configId = 1;
            var schedules = new List<EmailReminderSchedule>
            {
                new EmailReminderSchedule 
                { 
                    Id = 1, 
                    ReminderConfigId = configId, 
                    Status = ReminderStatus.Sent, // Sent
                    SentDate = new DateTime(2024, 1, 1),
                    IsActive = true,
                    IsDeleted = false
                },
                new EmailReminderSchedule 
                { 
                    Id = 2, 
                    ReminderConfigId = configId, 
                    Status = ReminderStatus.Sent, // Sent but no SentDate
                    SentDate = null,
                    IsActive = true,
                    IsDeleted = false
                }
            };

            _scheduleRepoMock.Setup(r => r.GetManyAsync(It.IsAny<Func<EmailReminderSchedule, bool>>()))
                .ReturnsAsync(schedules);

            // Act
            var result = await EmailReminderDataHelper.GetScheduleStatisticsAsync(_unitOfWorkMock.Object, configId);

            // Assert
            Assert.Equal(1, result.totalSent); // Only schedules with SentDate
            Assert.Equal(0, result.nonSentandnonPending);
            Assert.Equal(new DateTime(2024, 1, 1), result.lastSentDate);
            Assert.Null(result.nextScheduledDate);
        }

        [Fact]
        public async Task GetScheduleStatisticsAsync_InactiveSchedules_ExcludedFromPending()
        {
            // Arrange
            var configId = 1;
            var schedules = new List<EmailReminderSchedule>
            {
                new EmailReminderSchedule 
                { 
                    Id = 1, 
                    ReminderConfigId = configId, 
                    Status = ReminderStatus.Pending, // Pending
                    ReminderDate = new DateTime(2024, 1, 10),
                    IsActive = true,
                    IsDeleted = false
                },
                new EmailReminderSchedule 
                { 
                    Id = 2, 
                    ReminderConfigId = configId, 
                    Status = ReminderStatus.Pending, // Pending but inactive
                    ReminderDate = new DateTime(2024, 1, 5),
                    IsActive = false,
                    IsDeleted = false
                }
            };

            _scheduleRepoMock.Setup(r => r.GetManyAsync(It.IsAny<Func<EmailReminderSchedule, bool>>()))
                .ReturnsAsync(schedules.Where(i=> i.IsActive).ToList());

            // Act
            var result = await EmailReminderDataHelper.GetScheduleStatisticsAsync(_unitOfWorkMock.Object, configId);

            // Assert
            Assert.Equal(0, result.totalSent);
            Assert.Equal(0, result.nonSentandnonPending);
            Assert.Null(result.lastSentDate);
            Assert.Equal(new DateTime(2024, 1, 10), result.nextScheduledDate); // Only active pending schedules
        }
    }
}
