﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using DataAccessLayer.DBModel;

namespace DataAccessLayer.Models
{
    public class MCapTable : BaseModel
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int KpiId { get; set; }
        public int ModuleId { get; set; }
        public string Kpi { get; set; }
        public string KpiInfo { get; set; }
        public string Description { get; set; }
        [DefaultValue(false)]
        [DatabaseGenerated(DatabaseGeneratedOption.Computed)]
        public bool IsDeleted { get; set; }
        public int? MethodologyID { get; set; }
        [DefaultValue(false)]
        public bool IsBoldKpi { get; set; }
        [DefaultValue(false)]
        public bool IsHeader { get; set; }
        public string Formula { get; set; }
        public string FormulaKPIId { get; set; }
        public int? KpiTypeId { get; set; }
        public bool IsOverrule { get; set; }
        public string Synonym { get; set; }
    }
}
