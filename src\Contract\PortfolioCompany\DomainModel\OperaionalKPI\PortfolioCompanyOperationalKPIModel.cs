﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Contract.Sector;

namespace Contract.PortfolioCompany
{
    public class SectorwiseKpiDetails : BaseModel
    {
        public int SectorwiseKPIID { get; set; }
        public int SectorID { get; set; }

        [MaxLength(200)]
        public string KPI { get; set; }

        [MaxLength(20)]
        public string KpiInfo { get; set; }

        [MaxLength(500)]
        public string Description { get; set; }
        public bool IsDeleted { get; set; }
        public SectorModel Sector { get; set; }
        public bool IsMapped { get; set; }
        public int? ParentKpiId { get; set; }
        public int MethodologyId { get; set; }
        public string MethodologyName { get; set; }
        public string SectorName { get; set; }
        [MaxLength(500)]
        public string EncryptedSectorwiseKPIID { get; set; }
        [NotMapped]
        public string KpiInfoType { get; set; }
        public string Formula { get; set; }
        public string FormulaKPIId { get; set; }
        public bool IsBoldKPI { get; set; }
        public bool IsHeader { get; set; }
        public string Synonym { get; set; }
    }
}