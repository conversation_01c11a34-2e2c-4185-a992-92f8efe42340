using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace EmailConfiguration.DTOs
{
    /// <summary>
    /// DTO for email reminder response
    /// </summary>
    public class EmailRemainderDefaultDataResponseDto
    {
        /// <summary>
        /// Unique identifier for the reminder
        /// </summary>
        public string ReminderId { get; set; }

        /// <summary>
        /// List of Portfolio Company IDs
        /// </summary>
        [Required]
        [MinLength(1, ErrorMessage = "At least one Portfolio Company ID is required")]
        public List<EntityDetails> PortfolioCompanys { get; set; }

        /// <summary>
        /// List of Document Type IDs
        /// </summary>
        [Required]
        [MinLength(1, ErrorMessage = "At least one DocumentTypes is required")]
        public List<EntityDetails> DocumentTypes { get; set; }

        /// <summary>
        /// List of To Reciepients
        /// </summary>
        [Required]
        [MinLength(1, ErrorMessage = "At least one recipient is required")]
        public List<EmailRecipientsDto> ToRecipients { get; set; }

        /// <summary>
        /// List of Cc Reciepients
        /// </summary>
        public List<EmailRecipientsDto> CcReciepients { get; set; }
    }
}
