using DapperRepository;
using DataAccessLayer.Models.DataExtraction;
using DataAccessLayer.Models.DocumentCollection;
using DataAccessLayer.UnitOfWork;
using DocumentCollection.DTOs;
using DocumentCollection.Models;
using DocumentCollection.Services;
using Moq;
using System.Linq.Expressions;

namespace Services
{
    public class RepositoryConfigServiceTest
    {
        private readonly Mock<IUnitOfWork> _unitOfWorkMock;
        private readonly Mock<IDapperGenericRepository> _dapperGenericRepositoryMock;
        private readonly RepositoryConfigService _service;

        public RepositoryConfigServiceTest()
        {
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _dapperGenericRepositoryMock = new Mock<IDapperGenericRepository>();
            _service = new RepositoryConfigService(_unitOfWorkMock.Object, _dapperGenericRepositoryMock.Object);
        }

        [Fact]
        public async Task GetDocumentConfigurationByCompanies_ShouldReturnConfigurations()
        {
            // Arrange             
            var entityIds = new List<int> { 1, 2, 3 };
            var featureId = 14;
            var configs = new List<DocCollectionFrequencyConfig>
            {
               new DocCollectionFrequencyConfig { EntityId = 1, DocTypeID = 1, FrequencyId = 1, From = null, To = null },
               new DocCollectionFrequencyConfig { EntityId = 2, DocTypeID = 1, FrequencyId = 2, From = "2023", To = "2025" },
               new DocCollectionFrequencyConfig { EntityId = 3, DocTypeID = 1, FrequencyId = 2, From = "2024", To = "2025" }
            };
            _unitOfWorkMock.Setup(u => u.DocCollectionConfigRepository.FindAllAsync(It.IsAny<Expression<Func<DocCollectionFrequencyConfig, bool>>>()))
               .ReturnsAsync(configs);

            // Act  
            var result = await _service.GetRepositoryConfigurationByCompanies(entityIds, featureId);

            // Assert  
            Assert.Null(result.Data);
            Assert.Equal("Company having different configuration", result.Message);
        }

        [Fact]
        public async Task GetRepositoryConfigurationByCompanies_ShouldReturnEmpty_WhenNoConfigsFound()
        {
            // Arrange
            var entityIds = new List<int> { 1, 2, 3 };
            var featureId = 14;
            var configs = new List<DocCollectionFrequencyConfig>();
            _unitOfWorkMock.Setup(u => u.DocCollectionConfigRepository.FindAllAsync(It.IsAny<Expression<Func<DocCollectionFrequencyConfig, bool>>>())).ReturnsAsync(configs);

            // Act
            var result = await _service.GetRepositoryConfigurationByCompanies(entityIds, featureId);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.NotNull(result.Data);
            Assert.Empty((List<DocumentConfigurationDto>)result.Data);
        }

        [Fact]
        public async Task GetDocumentConfigurationByCompany_ShouldReturnConfigurations()
        {
            // Arrange
            var companyId = 1;
            var featureId = 14;
            var configs = new List<DocCollectionFrequencyConfig>
                {
                    new DocCollectionFrequencyConfig { EntityId = 1, DocTypeID = 1, FrequencyId = 1, From = null, To = null }
                };
            _unitOfWorkMock.Setup(u => u.DocCollectionConfigRepository.FindAllAsync(It.IsAny<Expression<Func<DocCollectionFrequencyConfig, bool>>>()))
                .ReturnsAsync(configs);

            // Act
            var result = await _service.GetRepositoryConfigurationByCompany(companyId, featureId);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.NotNull(result.Data);
            Assert.Single((List<DocumentConfigurationDto>)result.Data);
        }

        [Fact]
        public async Task GetRepositoryConfigurationByCompany_ShouldReturnEmpty_WhenNoConfigsFound()
        {
            // Arrange
            var companyId = 1;
            var featureId = 14;
            var configs = new List<DocCollectionFrequencyConfig>();
            _unitOfWorkMock.Setup(u => u.DocCollectionConfigRepository.FindAllAsync(It.IsAny<Expression<Func<DocCollectionFrequencyConfig, bool>>>())).ReturnsAsync(configs);

            // Act
            var result = await _service.GetRepositoryConfigurationByCompany(companyId, featureId);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.NotNull(result.Data);
            Assert.Empty((List<DocumentConfigurationDto>)result.Data);
        }

        [Fact]
        public async Task UpdateDocumentConfigurations_ShouldUpdateAndInsertConfigurations()
        {
            // Arrange
            var configurations = new DocumentConfigurationModel
            {
                Ids = new List<int> { 1 },
                Configuration = new List<FolderFrequencyConfigModel>
                    {
                        new FolderFrequencyConfigModel
                        {
                            DoctypeID = 1,
                            AnnualConfig = null,
                            QuarterConfig = null,
                            MonthConfig = null
                        }
                    }
            };
            var existingConfigs = new List<DocCollectionFrequencyConfig>();
            _unitOfWorkMock.Setup(u => u.DocCollectionConfigRepository.FindAllAsync(It.IsAny<Expression<Func<DocCollectionFrequencyConfig, bool>>>()))
                .ReturnsAsync(existingConfigs);

            // Act
            var result = await _service.UpdateRepositoryConfigurations(configurations, 1);

            // Assert
            Assert.True(result.IsSuccess);
            _unitOfWorkMock.Verify(u => u.DocCollectionConfigRepository.AddBulkAsyn(It.IsAny<List<DocCollectionFrequencyConfig>>()), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Fact]
        public async Task UpdateRepositoryConfigurations_ShouldReturnError_WhenExceptionThrown()
        {
            // Arrange
            var configurations = new DocumentConfigurationModel
            {
                Ids = new List<int> { 1 },
                Configuration = new List<FolderFrequencyConfigModel>
                {
                    new FolderFrequencyConfigModel
                    {
                        DoctypeID = 1,
                        AnnualConfig = new RangeConfigModel { From = DateTime.Now.ToString(), To = DateTime.Now.AddMonths(1).ToString() }
                    }
                }
            };
            _unitOfWorkMock.Setup(u => u.DocCollectionConfigRepository.FindAllAsync(It.IsAny<Expression<Func<DocCollectionFrequencyConfig, bool>>>())).ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _service.UpdateRepositoryConfigurations(configurations, 1);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal("Failed to update configurations: Database error", result.Message);
        }

        [Fact]
        public async Task GetRepositoryConfigurationDataByCompany_ShouldReturnConfigurations()
        {
            // Arrange
            var companyId = 1;
            var configs = new List<DocCollectionFrequencyConfig>
                {
                    new DocCollectionFrequencyConfig { EntityId = 1, DocTypeID = 1, FrequencyId = 1, From = null, To = null }
                };
            var docTypes = new List<DataExtractionTypes>
                {
                    new DataExtractionTypes { Id = 1, DocumentName = "DocType1" }
                };
            _unitOfWorkMock.Setup(u => u.DocCollectionConfigRepository.FindAllAsync(It.IsAny<Expression<Func<DocCollectionFrequencyConfig, bool>>>()))
                .ReturnsAsync(configs);
            _unitOfWorkMock.Setup(u => u.DataExtractionTypesRepository.FindAllAsync(It.IsAny<Expression<Func<DataExtractionTypes, bool>>>()))
                .ReturnsAsync(docTypes);

            // Act
            var result = await _service.GetRepositoryConfigurationDataByCompany(companyId);

            // Assert
            Assert.NotNull(result);
            Assert.Single(result);
            Assert.Equal("DocType1", result.First().DoctypeName);
        }

        [Fact]
        public async Task GetRepositoryConfigurationByCompanies_ShouldReturnError_WhenExceptionThrown()
        {
            // Arrange
            var companyIds = new List<int> { 1, 2, 3 };
            var featureId = 14;
            _unitOfWorkMock.Setup(u => u.DocCollectionConfigRepository.FindAllAsync(It.IsAny<Expression<Func<DocCollectionFrequencyConfig, bool>>>())).ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _service.GetRepositoryConfigurationByCompanies(companyIds, featureId);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal("Failed to get document configurations: Database error", result.Message);
        }

        [Fact]
        public async Task GetRepositoryConfigurationByCompany_ShouldReturnError_WhenExceptionThrown()
        {
            // Arrange
            var companyId = 1;
            var featureId = 14;
            _unitOfWorkMock.Setup(u => u.DocCollectionConfigRepository.FindAllAsync(It.IsAny<Expression<Func<DocCollectionFrequencyConfig, bool>>>())).ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _service.GetRepositoryConfigurationByCompany(companyId, featureId);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal("Failed to get document configurations: Database error", result.Message);
        }

        [Fact]
        public async Task GetPorfolioCompaniesList_ShouldReturnDistinctCompanies()
        {
            // Arrange
            var userId = 1;
            var companies = new List<PortfolioCompanyModel>
            {
                new PortfolioCompanyModel { PortfolioCompanyID = 1, CompanyName = "Company A" },
                new PortfolioCompanyModel { PortfolioCompanyID = 2, CompanyName = "Company B" },
                new PortfolioCompanyModel { PortfolioCompanyID = 1, CompanyName = "Company A" } // Duplicate
            };

            var dapperGenericRepositoryMock = new Mock<IDapperGenericRepository>();
            dapperGenericRepositoryMock
                .Setup(repo => repo.Query<PortfolioCompanyModel>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(companies);

            var unitOfWorkMock = new Mock<IUnitOfWork>();
            var service = new RepositoryConfigService(unitOfWorkMock.Object, dapperGenericRepositoryMock.Object);

            // Act
            var result = await service.GetPortfolioCompaniesList(userId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count); // Should return only distinct companies
            Assert.Contains(result, c => c.PortfolioCompanyID == 1 && c.CompanyName == "Company A");
            Assert.Contains(result, c => c.PortfolioCompanyID == 2 && c.CompanyName == "Company B");
        }
        [Fact]
        public async Task GetDocumentConfigurationByCompanies_multiple_annual_frequency()
        {
            // Arrange
            var companyIds = new List<int> { 1, 2, 3 };
            var featureId = 14;
            var configs = new List<DocCollectionFrequencyConfig>
                {
                    new DocCollectionFrequencyConfig { EntityId = 1, DocTypeID = 1, FrequencyId = 1, From = null, To = null },
                    new DocCollectionFrequencyConfig { EntityId = 2, DocTypeID = 1, FrequencyId = 2, From = "2023", To = "2025" },
                    new DocCollectionFrequencyConfig { EntityId = 3, DocTypeID = 1, FrequencyId = 2, From = "2024", To = "2025" }
                };
            _unitOfWorkMock.Setup(u => u.DocCollectionConfigRepository.FindAllAsync(It.IsAny<Expression<Func<DocCollectionFrequencyConfig, bool>>>()))
                .ReturnsAsync(configs);

            // Act
            var result = await _service.GetRepositoryConfigurationByCompanies(companyIds, featureId);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Null(result.Data);
        }
        [Fact]
        public async Task GetDocumentConfigurationByCompanies_multiple_quarter_frequency()
        {
            // Arrange
            var companyIds = new List<int> { 1, 2, 3 };
            var featureId = 14;
            var configs = new List<DocCollectionFrequencyConfig>
                {
                    new DocCollectionFrequencyConfig { EntityId = 1, DocTypeID = 1, FrequencyId = 1, From = null, To = null },
                    new DocCollectionFrequencyConfig { EntityId = 2, DocTypeID = 1, FrequencyId = 3, From = "Q1/2023", To = "Q1/2025" },
                    new DocCollectionFrequencyConfig { EntityId = 3, DocTypeID = 1, FrequencyId = 3, From = "Q1/2024", To = "Q1/2025" }
                };
            _unitOfWorkMock.Setup(u => u.DocCollectionConfigRepository.FindAllAsync(It.IsAny<Expression<Func<DocCollectionFrequencyConfig, bool>>>()))
                .ReturnsAsync(configs);

            // Act
            var result = await _service.GetRepositoryConfigurationByCompanies(companyIds, featureId);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Null(result.Data);
        }
        [Fact]
        public async Task GetDocumentConfigurationByCompanies_multiple_monthly_frequency()
        {
            // Arrange
            var companyIds = new List<int> { 1, 2, 3 };
            var featureId = 14;
            var configs = new List<DocCollectionFrequencyConfig>
                {
                    new DocCollectionFrequencyConfig { EntityId = 1, DocTypeID = 1, FrequencyId = 1, From = "2023", To = "2024" },
                    new DocCollectionFrequencyConfig { EntityId = 2, DocTypeID = 1, FrequencyId = 4, From = "Jan/2023", To = "Jan/2024" },
                    new DocCollectionFrequencyConfig { EntityId = 3, DocTypeID = 1, FrequencyId = 4, From = "Jan/2023", To = "Jan/2024" }
                };
            _unitOfWorkMock.Setup(u => u.DocCollectionConfigRepository.FindAllAsync(It.IsAny<Expression<Func<DocCollectionFrequencyConfig, bool>>>()))
                .ReturnsAsync(configs);

            // Act
            var result = await _service.GetRepositoryConfigurationByCompanies(companyIds, featureId);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.NotNull(result.Data);
        }

        [Fact]
        public void HasCompanyWithoutConfiguration_ShouldReturnTrue_WhenCompanyHasNoConfig()
        {
            // Arrange
            var companyIds = new List<int> { 1, 2, 3 };
            var allConfigs = new List<DocCollectionFrequencyConfig>
             {
                 new DocCollectionFrequencyConfig { EntityId = 1 },
                 new DocCollectionFrequencyConfig { EntityId = 2 }
             };

            // Act
            var result = _service.HasCompanyWithoutConfiguration(companyIds, allConfigs);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public void HasCompanyWithoutConfiguration_ShouldReturnFalse_WhenAllCompaniesHaveConfig()
        {
            // Arrange
            var companyIds = new List<int> { 1, 2 };
            var allConfigs = new List<DocCollectionFrequencyConfig>
                 {
                     new DocCollectionFrequencyConfig { EntityId = 1 },
                     new DocCollectionFrequencyConfig { EntityId = 2 }
                 };

            // Act
            var result = _service.HasCompanyWithoutConfiguration(companyIds, allConfigs);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void HasCompanyWithoutConfiguration_ShouldReturnTrue_WhenConfigsAreEmpty()
        {
            // Arrange
            var companyIds = new List<int> { 1, 2, 3 };
            var allConfigs = new List<DocCollectionFrequencyConfig>();

            // Act
            var result = _service.HasCompanyWithoutConfiguration(companyIds, allConfigs);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public void HasCompanyWithoutConfiguration_ShouldReturnFalse_WhenCompanyIdsAreEmpty()
        {
            // Arrange
            var companyIds = new List<int>();
            var allConfigs = new List<DocCollectionFrequencyConfig>
                 {
                     new DocCollectionFrequencyConfig { EntityId = 1 },
                     new DocCollectionFrequencyConfig { EntityId = 2 }
                 };

            // Act
            var result = _service.HasCompanyWithoutConfiguration(companyIds, allConfigs);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task GetFundList_ShouldReturnFunds()
        {
            // Arrange
            var userId = 1;
            var funds = new List<FundDataModel>
            {
                new FundDataModel { FundID = 1, FundName = "Fund A" },
                new FundDataModel { FundID = 2, FundName = "Fund B" }
            };
            _dapperGenericRepositoryMock.Setup(repo => repo.Query<FundDataModel>(It.IsAny<string>(), It.IsAny<object>())).ReturnsAsync(funds);

            // Act
            var result = await _service.GetFundList(userId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            Assert.Contains(result, f => f.FundID == 1 && f.FundName == "Fund A");
            Assert.Contains(result, f => f.FundID == 2 && f.FundName == "Fund B");
        }

        [Fact]
        public async Task GetFundList_ShouldReturnEmpty_WhenNoFundsFound()
        {
            // Arrange
            var userId = 1;
            var funds = new List<FundDataModel>();
            _dapperGenericRepositoryMock.Setup(repo => repo.Query<FundDataModel>(It.IsAny<string>(), It.IsAny<object>())).ReturnsAsync(funds);

            // Act
            var result = await _service.GetFundList(userId);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public async Task GetFundList_ShouldThrowException_WhenRepositoryThrows()
        {
            // Arrange
            var userId = 1;
            _dapperGenericRepositoryMock.Setup(repo => repo.Query<FundDataModel>(It.IsAny<string>(), It.IsAny<object>())).ThrowsAsync(new Exception("Database error"));

            // Act & Assert
            var ex = await Assert.ThrowsAsync<Exception>(() => _service.GetFundList(userId));
            Assert.Equal("Database error", ex.Message);
        }       
    }
}