using EmailConfiguration.Helpers;
using EmailConfiguration.DTOs;
using DataAccessLayer.Models.EmailNotifications;
using DataAccessLayer.UnitOfWork;
using DataAccessLayer.GenericRepository;
using Moq;
using Xunit;

namespace EmailConfiguration.UnitTest.Helpers
{
    /// <summary>
    /// Basic unit tests for EmailReminderRecipientHelper that compile and run successfully
    /// </summary>
    public class EmailReminderRecipientHelperBasicTests
    {
        [Fact]
        public void CreateEmailReminderRecipient_EmailRecipient_ReturnsCorrectEntity()
        {
            // Arrange
            var reminderId = Guid.NewGuid();
            var recipient = new EmailRecipientsDto
            {
                InternalID = 123,
                Email = "<EMAIL>",
                Name = "Test User"
            };
            var recipientType = RecipientTypes.To;
            var userId = 1;

            // Act
            var result = EmailReminderRecipientHelper.CreateEmailReminderRecipient(reminderId, recipient, recipientType, userId);

            // Assert
            Assert.Equal(reminderId, result.ReminderId);
            Assert.Equal(recipientType, result.RecipientType);
            Assert.Equal(123, result.RecipientId);
            Assert.Equal("<EMAIL>", result.EmailAddress);
            Assert.False(result.IsGroupMember);
            Assert.Null(result.GroupID);
            Assert.Equal(userId, result.CreatedBy);
            Assert.False(result.IsDeleted);
        }

        [Fact]
        public void CreateEmailReminderRecipient_GroupRecipient_ReturnsCorrectEntity()
        {
            // Arrange
            var reminderId = Guid.NewGuid();
            var recipient = new EmailRecipientsDto
            {
                InternalID = 456,
                Email = "", // Empty email indicates group
                Name = "Test Group"
            };
            var recipientType = RecipientTypes.Cc;
            var userId = 1;

            // Act
            var result = EmailReminderRecipientHelper.CreateEmailReminderRecipient(reminderId, recipient, recipientType, userId);

            // Assert
            Assert.Equal(reminderId, result.ReminderId);
            Assert.Equal(recipientType, result.RecipientType);
            Assert.Null(result.RecipientId);
            Assert.Null(result.EmailAddress);
            Assert.True(result.IsGroupMember);
            Assert.Equal(456, result.GroupID);
            Assert.Equal(userId, result.CreatedBy);
            Assert.False(result.IsDeleted);
        }

        [Fact]
        public void CreateEmailReminderRecipients_ValidRecipients_ReturnsCorrectEntities()
        {
            // Arrange
            var reminderId = Guid.NewGuid();
            var recipients = new List<EmailRecipientsDto>
            {
                new EmailRecipientsDto { InternalID = 1, Email = "<EMAIL>", Name = "User 1" },
                new EmailRecipientsDto { InternalID = 2, Email = "<EMAIL>", Name = "User 2" }
            };
            var recipientType = RecipientTypes.To;
            var userId = 1;

            // Act
            var result = EmailReminderRecipientHelper.CreateEmailReminderRecipients(reminderId, recipients, recipientType, userId);

            // Assert
            Assert.Equal(2, result.Count);
            Assert.All(result, r => Assert.Equal(reminderId, r.ReminderId));
            Assert.All(result, r => Assert.Equal(recipientType, r.RecipientType));
            Assert.All(result, r => Assert.Equal(userId, r.CreatedBy));
        }

        [Fact]
        public void CreateEmailReminderRecipients_NullRecipients_ReturnsEmptyList()
        {
            // Arrange
            var reminderId = Guid.NewGuid();
            List<EmailRecipientsDto>? recipients = null;
            var recipientType = RecipientTypes.To;
            var userId = 1;

            // Act
            var result = EmailReminderRecipientHelper.CreateEmailReminderRecipients(reminderId, recipients, recipientType, userId);

            // Assert
            Assert.Empty(result);
        }

        [Fact]
        public void CreateEmailReminderRecipients_EmptyRecipients_ReturnsEmptyList()
        {
            // Arrange
            var reminderId = Guid.NewGuid();
            var recipients = new List<EmailRecipientsDto>();
            var recipientType = RecipientTypes.To;
            var userId = 1;

            // Act
            var result = EmailReminderRecipientHelper.CreateEmailReminderRecipients(reminderId, recipients, recipientType, userId);

            // Assert
            Assert.Empty(result);
        }

        [Fact]
        public async Task RemoveExistingRecipientsAsync_ValidReminderId_CallsDeleteForEachRecipient()
        {
            // Arrange
            var unitOfWorkMock = new Mock<IUnitOfWork>();
            var repositoryMock = new Mock<IGenericRepository<EmailReminderRecipients>>();
            var reminderId = Guid.NewGuid();

            var existingRecipients = new List<EmailReminderRecipients>
            {
                new EmailReminderRecipients { ReminderId = reminderId, RecipientType = RecipientTypes.To },
                new EmailReminderRecipients { ReminderId = reminderId, RecipientType = RecipientTypes.Cc }
            };

            repositoryMock.Setup(r => r.GetManyAsync(It.IsAny<Func<EmailReminderRecipients, bool>>()))
                .ReturnsAsync(existingRecipients);

            unitOfWorkMock.Setup(u => u.EmailReminderRecipientsRepository).Returns(repositoryMock.Object);

            // Act
            await EmailReminderRecipientHelper.RemoveExistingRecipientsAsync(unitOfWorkMock.Object, reminderId);

            // Assert
            repositoryMock.Verify(r => r.Delete(It.IsAny<EmailReminderRecipients>()), Times.Exactly(2));
        }

        [Fact]
        public async Task AddRecipientsAsync_ValidRecipients_CallsAddForEachRecipient()
        {
            // Arrange
            var unitOfWorkMock = new Mock<IUnitOfWork>();
            var repositoryMock = new Mock<IGenericRepository<EmailReminderRecipients>>();

            var recipients = new List<EmailReminderRecipients>
            {
                new EmailReminderRecipients { RecipientType = RecipientTypes.To },
                new EmailReminderRecipients { RecipientType = RecipientTypes.Cc }
            };

            unitOfWorkMock.Setup(u => u.EmailReminderRecipientsRepository).Returns(repositoryMock.Object);

            // Act
            await EmailReminderRecipientHelper.AddRecipientsAsync(unitOfWorkMock.Object, recipients);

            // Assert
            repositoryMock.Verify(r => r.AddAsyn(It.IsAny<EmailReminderRecipients>()), Times.Exactly(2));
        }

        [Fact]
        public void MapToEmailRecipientDto_ValidRecipients_ReturnsCorrectMapping()
        {
            // Arrange
            var recipients = new List<EmailReminderRecipients>
            {
                new EmailReminderRecipients
                {
                    RecipientType = RecipientTypes.To,
                    RecipientId = 1,
                    EmailAddress = "<EMAIL>",
                    IsGroupMember = false,
                    GroupID = null
                },
                new EmailReminderRecipients
                {
                    RecipientType = RecipientTypes.Cc,
                    RecipientId = null,
                    EmailAddress = null,
                    IsGroupMember = true,
                    GroupID = 5
                },
                new EmailReminderRecipients
                {
                    RecipientType = RecipientTypes.To,
                    RecipientId = 2,
                    EmailAddress = "<EMAIL>",
                    IsGroupMember = false,
                    GroupID = null
                }
            };

            // Act
            var result = EmailReminderRecipientHelper.MapToEmailRecipientDto(recipients, RecipientTypes.To);

            // Assert
            Assert.Equal(2, result.Count);
            Assert.All(result, r => Assert.False(r.IsGroupMember));
            Assert.Contains(result, r => r.RecipientId == 1 && r.EmailAddress == "<EMAIL>");
            Assert.Contains(result, r => r.RecipientId == 2 && r.EmailAddress == "<EMAIL>");
        }

        [Fact]
        public void MapToEmailRecipientDetailsDto_EmailRecipient_ReturnsCorrectMapping()
        {
            // Arrange
            var recipient = new EmailReminderRecipients
            {
                RecipientId = 1,
                EmailAddress = "<EMAIL>",
                IsGroupMember = false,
                GroupID = null
            };
            var groups = new List<EmailNotificationGroup>();

            // Act
            var result = EmailReminderRecipientHelper.MapToEmailRecipientDetailsDto(recipient, groups);

            // Assert
            Assert.Equal(1, result.RecipientId);
            Assert.Equal("<EMAIL>", result.EmailAddress);
            Assert.False(result.IsGroupMember);
            Assert.Null(result.GroupID);
            Assert.Null(result.Name);
        }

        [Fact]
        public void MapToEmailRecipientDetailsDto_GroupRecipient_ReturnsCorrectMapping()
        {
            // Arrange
            var recipient = new EmailReminderRecipients
            {
                RecipientId = null,
                EmailAddress = null,
                IsGroupMember = true,
                GroupID = 5
            };
            var groups = new List<EmailNotificationGroup>
            {
                new EmailNotificationGroup { GroupId = 5, GroupName = "Test Group" }
            };

            // Act
            var result = EmailReminderRecipientHelper.MapToEmailRecipientDetailsDto(recipient, groups);

            // Assert
            Assert.Null(result.RecipientId);
            Assert.Null(result.EmailAddress);
            Assert.True(result.IsGroupMember);
            Assert.Equal(5, result.GroupID);
            Assert.Equal("Test Group", result.Name);
        }

        [Fact]
        public void MapToEmailRecipientDetailsDto_GroupRecipientNotFound_ReturnsUnknownGroup()
        {
            // Arrange
            var recipient = new EmailReminderRecipients
            {
                RecipientId = null,
                EmailAddress = null,
                IsGroupMember = true,
                GroupID = 999
            };
            var groups = new List<EmailNotificationGroup>
            {
                new EmailNotificationGroup { GroupId = 5, GroupName = "Test Group" }
            };

            // Act
            var result = EmailReminderRecipientHelper.MapToEmailRecipientDetailsDto(recipient, groups);

            // Assert
            Assert.Equal("Unknown Group", result.Name);
        }

        [Fact]
        public void GetGroupIdsFromRecipients_ValidRecipients_ReturnsUniqueGroupIds()
        {
            // Arrange
            var recipients = new List<EmailReminderRecipients>
            {
                new EmailReminderRecipients { IsGroupMember = true, GroupID = 1 },
                new EmailReminderRecipients { IsGroupMember = false, GroupID = null },
                new EmailReminderRecipients { IsGroupMember = true, GroupID = 2 },
                new EmailReminderRecipients { IsGroupMember = true, GroupID = 1 }, // Duplicate
                new EmailReminderRecipients { IsGroupMember = true, GroupID = 3 }
            };

            // Act
            var result = EmailReminderRecipientHelper.GetGroupIdsFromRecipients(recipients);

            // Assert
            Assert.Equal(3, result.Count);
            Assert.Contains(1, result);
            Assert.Contains(2, result);
            Assert.Contains(3, result);
        }        
    }
}
