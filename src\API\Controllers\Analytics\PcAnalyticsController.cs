﻿using API.Helpers;
using Contract.Funds;
using Contract.FxRates;
using DataAnalytic.Models;
using DataAnalytic.Services;
using Master;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Linq;
using System.Threading.Tasks;

namespace API.Controllers.Analytics
{
    /// <summary>
    /// constructor to initialize PcAnalyticsController
    /// </summary>
    /// <param name="pcAnalyticsService"></param>
    [Route("api")]
    [ApiController]
    [Authorize(Policy = AppSettings.DefaultAuthPolicy)]
    public class PcAnalyticsController(IPcAnalyticsService pcAnalyticsService, IPageDetailsConfigurationService pageDetailConfigService, IHelperService helperService, ILogger<PcAnalyticsController> logger) : ControllerBase
    {
        private readonly IPcAnalyticsService _pcAnalyticsService = pcAnalyticsService;
        private readonly IPageDetailsConfigurationService _pageDetailConfigService = pageDetailConfigService;
        private readonly IHelperService _helperService = helperService;
        private readonly ILogger<PcAnalyticsController> _logger = logger;

        /// <summary>
        /// Endpoint to retrieve portfolio company details by section based on the provided filter criteria.
        /// </summary>
        /// <param name="analyticsPcFilter">The filter criteria for retrieving portfolio company details.</param>
        /// <returns>An IActionResult containing the portfolio company details or a NoContent result if no data is found.</returns>
        [HttpPost("data-analytics/portfolio-company")]
        public async Task<IActionResult> GetPortfolioCompanyDetailsBySection(DataAnalyticsPcFilter analyticsPcFilter)
        {
            var userId = _helperService.GetCurrentUserId(User);
            _logger.LogInformation("Retrieving portfolio company details by section for user ID: {UserId} with filter: {@Filter}", userId, analyticsPcFilter);
            var result = await _pcAnalyticsService.GetPortfolioCompanyDetailsBySection(userId, analyticsPcFilter);
            if (result == null || !result.Any())
            {
                _logger.LogInformation("No portfolio company details found for user ID: {UserId} with the provided filter.", userId);
                return NoContent();
            }
            _logger.LogInformation("Portfolio company details retrieved successfully for user ID: {UserId}.", userId);
            return Ok(result);
        }
        /// <summary>
        /// Endpoint to retrieve portfolio company details for the dashboard based on the provided filter criteria.
        /// </summary>
        /// <param name="analyticsPcFilter">The filter criteria for retrieving portfolio company details.</param>
        /// <returns>An IActionResult containing the portfolio company details or a NoContent result if no data is found.</returns>
        [HttpPost("data-analytics/bi/portfolio-company")]
        public async Task<IActionResult> GetPortfolioCompanyDetailsForDashboard(DataAnalyticsBiFilter analyticsPcFilter)
        {
            _logger.LogInformation("Retrieving portfolio company details for dashboard with filter: {@Filter}", analyticsPcFilter);
            var result = await _pcAnalyticsService.GetPcAnalyticsDetails(analyticsPcFilter);
            if (result == null || !result.Any())
            {
                _logger.LogInformation("No portfolio company details found for the provided filter.");
                return NoContent();
            }
            _logger.LogInformation("Portfolio company details retrieved successfully.");
            return Ok(result);
        }
        /// <summary>
        /// Endpoint to retrieve static fields for data analytics.
        /// </summary>
        /// <returns>An IActionResult containing the static fields or a NoContent result if no data is found.</returns>
        [HttpGet("data-analytics/static-field")]
        public async Task<IActionResult> GetStaticFields()
        {
            _logger.LogInformation("Retrieving static fields for data analytics.");
            var staticFields = await _pcAnalyticsService.GetStaticFields();
            if (staticFields == null)
            {
                _logger.LogInformation("No static fields found for data analytics.");
                return NoContent();
            }
            _logger.LogInformation("Static fields retrieved successfully for data analytics.");
            return Ok(staticFields);
        }
        /// <summary>
        /// GetFixedStaticFileds
        /// </summary>
        /// <returns>Company name, Fund name and Investor name subpagefieldmodels</returns>
        [HttpGet("data-analytics/fixed-static-fields")]
        public async Task<IActionResult> GetFixedStaticFields()
        {
            _logger.LogInformation("Retrieving fixed static fields for data analytics.");
            var staticFields = await _pcAnalyticsService.GetFixedStaticFields();
            if (staticFields == null)
            {
                _logger.LogInformation("No fixed static fields found for data analytics.");
                return NoContent();
            }
            _logger.LogInformation("Fixed static fields retrieved successfully for data analytics.");
            return Ok(staticFields);
        }
        /// <summary>
        /// GetFilterTabs
        /// </summary>
        /// <returns>Company name, Fund name and Investor name subpagefieldmodels</returns>
        [HttpGet("data-analytics/filter-tabs/page-config")]
        public async Task<IActionResult> GetFilterTabs()
        {
            const int sectionId = (int)PageConfigurationSubFeature.DataAnalytics;
            _logger.LogInformation("Retrieving filter tabs configuration for section ID: {SectionId}", sectionId);
            var staticFields = await _pageDetailConfigService.GetActiveFieldsBySubPageId(sectionId);
            if (staticFields == null)
            {
                _logger.LogInformation("No filter tabs configuration found for section ID: {SectionId}", sectionId);
                return NoContent();
            }
            _logger.LogInformation("Filter tabs configuration retrieved successfully for section ID: {SectionId}", sectionId);
            return Ok(staticFields);
        }
        /// <summary>
        /// Handles HTTP POST requests to retrieve the master KPI list based on the provided filter.
        /// </summary>
        /// <param name="filter">An instance of <see cref="DataAnalyticsPcFilter"/> containing the filter criteria for the KPI list.</param>
        /// <returns>
        /// An <see cref="IActionResult"/> containing an HTTP 200 OK response with the master KPI list if the request is successful.
        /// </returns>
        /// <remarks>
        /// This endpoint is mapped to the route "data-analytics/kpiMaster".
        /// </remarks>
        [HttpPost("data-analytics/kpiMaster")]
        public async Task<IActionResult> GetKpiMaster(DataAnalyticsPcFilter filter)
        {
            _logger.LogInformation("Retrieving KPI master list with filter: {@Filter}", filter);
            var result = await _pcAnalyticsService.GetMasterKpiList(filter);
            if (result == null || !result.Any())
            {
                _logger.LogInformation("No KPI master list found for the provided filter.");
                return NoContent();
            }
            _logger.LogInformation("KPI master list retrieved successfully.");
            return Ok(result);
        }
        /// <summary>
        /// Endpoint to retrieve KPI mappings based on the provided filter criteria.
        /// </summary>
        /// <param name="filter">The filter criteria containing sections and company IDs.</param>
        /// <returns>An IActionResult containing the KPI mappings or a NoContent result if no data is found.</returns>
        [HttpPost("data-analytics/kpiMapping")]
        public async Task<IActionResult> GetKpiMapping(DataAnalyticsPcFilter filter)
        {
            _logger.LogInformation("Retrieving KPI mappings with filter: {@Filter}", filter);
            var result = await _pcAnalyticsService.GetMappingKpiList(filter);
            if (result == null || !result.Any())
            {
                _logger.LogInformation("No KPI mappings found for the provided filter.");
                return NoContent();
            }
            _logger.LogInformation("KPI mappings retrieved successfully.");
            return Ok(result);
        }
        /// <summary>
        /// Endpoint to retrieve a list of companies that the current user has access to view.
        /// </summary>
        /// <returns>An IActionResult containing the list of companies or a NoContent result if no data is found.</returns>
        [HttpPost("data-analytics/company-list")]
        public async Task<IActionResult> GetCompanyList()
        {
            var userId = _helperService.GetCurrentUserId(User);
            _logger.LogInformation("Retrieving company list for user ID: {UserId}", userId);
            var result = await _pcAnalyticsService.GetCompaniesByAccess(userId);
            if (result == null || !result.Any())
            {
                _logger.LogInformation("No companies found for user ID: {UserId}", userId);
                return NoContent();
            }
            _logger.LogInformation("Company list retrieved successfully for user ID: {UserId}", userId);
            return Ok(result);
        }
        [HttpPost("data-analytics/fxrates")]
        public async Task<ActionResult> GetFxRates(FxRateFilterModel filter)
        {
            if (filter == null)
                return BadRequest("Filter parameters are required.");

            var result = await _pcAnalyticsService.GetFxRates(filter);
            return Ok(result);
        }
    }
}
