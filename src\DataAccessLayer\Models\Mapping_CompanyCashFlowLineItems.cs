using DataAccessLayer.Models;
using System.Collections.Generic;

namespace DataAccessLayer.DBModel
{
    public partial class Mapping_CompanyCashFlowLineItems : BaseModel
    {
        public int CompanyCashFlowLineItemMappingID { get; set; }
        public int CashFlowLineItemID { get; set; }
        public int? ParentLineItemID { get; set; }
        public int? DisplayOrder { get; set; }
        public int PortfolioCompanyID { get; set; }
        public bool IsDeleted { get; set; }
        public bool IsActive { get; set; }
        public string SegmentType { get; set; }
        public string EncryptedCompanyCashFlowLineItemMappingID { get; set; }
        public string Formula { get; set; }
        public string FormulaKPIId { get; set; }
        public bool IsExtraction { get; set; }
        public string Synonym { get; set; }
        public string Definition { get; set; }
    }
}
