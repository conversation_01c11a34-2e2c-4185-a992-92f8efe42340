﻿using Contract.Documents;
using Contract.Utility;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PortfolioCompany.Interfaces
{
    public interface IDataExtraction
    {        
        Task<List<SourceTypes>> GetSourceTypes();
        Task<DocumentsInformationDto> AddDocumentTypeAsync(DocumentsInformationDto documentType);
        Task<List<DocumentsInformationDto>> GetDataExtractionTypes(int featureId);
    }
}
