using Moq;
using Microsoft.Extensions.Logging;
using EmailConfiguration.Interfaces;
using EmailConfiguration.DTOs;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using API.Controllers.EmailNotification;
using API.Helpers;
using ReportService;

namespace API.Tests.Controllers.EmailNotification
{
    public class UserInformationControllerTests
    {
        private readonly Mock<IUserInformationService> _userInfoServiceMock;
        private readonly Mock<ILogger<UserInformationController>> _loggerMock;
        private readonly Mock<IHelperService> _helperServiceMock;
        private readonly UserInformationController _controller;

        public UserInformationControllerTests()
        {
            _userInfoServiceMock = new Mock<IUserInformationService>();
            _loggerMock = new Mock<ILogger<UserInformationController>>();
            _helperServiceMock = new Mock<IHelperService>();
            _controller = new UserInformationController(_userInfoServiceMock.Object, _loggerMock.Object, _helperServiceMock.Object);
        }

        [Fact]
        public async Task DeleteUserInformation_ReturnsOk_WhenDeleteSuccessful()
        {
            // Arrange
            int userInformationId = 1;
            int userId = 123;
            _helperServiceMock.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _userInfoServiceMock.Setup(s => s.DeleteUserInformationAsync(userInformationId, userId))
                .ReturnsAsync(new ResponseDto<bool> { IsSuccess = true, Message = "Deleted", Data = true });

            // Act
            var result = await _controller.DeleteUserInformation(userInformationId);

            // Assert
            var okResult = result as OkObjectResult;
            Assert.NotNull(okResult);
            var response = okResult.Value as ResponseDto<bool>;
            Assert.NotNull(response);
            Assert.True(response.IsSuccess);
        }

        [Fact]
        public async Task DeleteUserInformation_ReturnsNotFound_WhenUserNotFound()
        {
            // Arrange
            int userInformationId = 2;
            int userId = 123;
            _helperServiceMock.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _userInfoServiceMock.Setup(s => s.DeleteUserInformationAsync(userInformationId, userId))
                .ReturnsAsync(new ResponseDto<bool> { IsSuccess = false, Message = "Not found", Data = false });

            // Act
            var result = await _controller.DeleteUserInformation(userInformationId);

           // Assert
            var notFoundResult = result as NotFoundObjectResult;
            Assert.NotNull(notFoundResult);
            var response = notFoundResult.Value as ResponseDto<bool>;
            Assert.NotNull(response);
            Assert.False(response.IsSuccess);
        }

        [Fact]
        public async Task DeleteUserInformation_ReturnsBadRequest_WhenModelStateInvalid()
        {
            // Arrange
            _controller.ModelState.AddModelError("key", "error");

            // Act
            var result = await _controller.DeleteUserInformation(1);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);
        }
    }
}
