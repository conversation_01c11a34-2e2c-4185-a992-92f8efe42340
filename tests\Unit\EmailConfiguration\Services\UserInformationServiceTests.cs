﻿using Contract.Account;
using DataAccessLayer.GenericRepository;
using DataAccessLayer.Models.DataExtraction;
using DataAccessLayer.Models.DocumentCollection;
using DataAccessLayer.Models.EmailNotifications;
using DataAccessLayer.UnitOfWork;
using EmailConfiguration.DTOs;
using EmailConfiguration.Interfaces;
using EmailConfiguration.Services;
using Microsoft.Extensions.Logging;
using Moq;
using System.Linq.Expressions;

namespace EmailConfiguration.UnitTest.Services
{
    public class UserInformationServiceTests
    {

        private readonly Mock<IUnitOfWork> _unitOfWorkMock;
        private readonly Mock<ILogger<UserInformationService>> _loggerMock;
        private readonly Mock<ICategoryService> _categoryServiceMock;
        private readonly Mock<IGenericRepository<UserInformation>> _userInfoRepositoryMock;
        private readonly Mock<IGenericRepository<User_Documents>> _userDocumentsRepositoryMock;
        private readonly Mock<IGenericRepository<M_UserCategory>> _userCategoryRepositoryMock;
        private readonly Mock<IGenericRepository<DataExtractionTypes>> _dataExtractionTypesRepositoryMock;
        private readonly Mock<IGenericRepository<DocCollectionFrequencyConfig>> _docCollectionConfigRepositoryMock;

        private readonly UserInformationService _userInformationService;
        private readonly int _portfolioCompanyFeatureId = (int)Features.PortfolioCompany;

        public UserInformationServiceTests()
        {
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _loggerMock = new Mock<ILogger<UserInformationService>>();
            _categoryServiceMock = new Mock<ICategoryService>();
            _userInfoRepositoryMock = new Mock<IGenericRepository<UserInformation>>();
            _userDocumentsRepositoryMock = new Mock<IGenericRepository<User_Documents>>();
            _userCategoryRepositoryMock = new Mock<IGenericRepository<M_UserCategory>>();
            _dataExtractionTypesRepositoryMock = new Mock<IGenericRepository<DataExtractionTypes>>();
            _docCollectionConfigRepositoryMock = new Mock<IGenericRepository<DocCollectionFrequencyConfig>>();

            _unitOfWorkMock.Setup(uow => uow.UserInfoRepository).Returns(_userInfoRepositoryMock.Object);
            _unitOfWorkMock.Setup(uow => uow.UserDocumentsRepository).Returns(_userDocumentsRepositoryMock.Object);
            _unitOfWorkMock.Setup(uow => uow.MUserCategoryRepository).Returns(_userCategoryRepositoryMock.Object);
            _unitOfWorkMock.Setup(uow => uow.DataExtractionTypesRepository).Returns(_dataExtractionTypesRepositoryMock.Object);
            _unitOfWorkMock.Setup(uow => uow.DocCollectionConfigRepository).Returns(_docCollectionConfigRepositoryMock.Object);

            _userInformationService = new UserInformationService(
                _unitOfWorkMock.Object,
                _loggerMock.Object,
                _categoryServiceMock.Object
            );
        }

        [Fact]
        public async Task CreateUserInformationWithDocumentTypesAsync_EmptyEmail_ShouldReturnErrorResponse()
        {
            // Arrange
            var userId = 1;
            var model = new CreateUserInformationDto
            {
                Name = "John Doe",
                Email = "",
                CategoryId = 1,
                EntityId = 10,
                DocumentTypeIds = new List<int> { 1, 2, 3 }
            };

            // Act
            var result = await _userInformationService.CreateUserInformationWithDocumentTypesAsync(model, userId);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Contains("Email cannot be null or empty", result.Message);
            _userInfoRepositoryMock.Verify(repo => repo.AddAsyn(It.IsAny<UserInformation>()), Times.Never);
        }

        [Fact]
        public async Task CreateUserInformationWithDocumentTypesAsync_InvalidCategoryId_ShouldReturnErrorResponse()
        {
            // Arrange
            var userId = 1;
            var model = new CreateUserInformationDto
            {
                Name = "John Doe",
                Email = "<EMAIL>",
                CategoryId = 0, // Invalid
                EntityId = 10,
                DocumentTypeIds = new List<int> { 1, 2, 3 }
            };

            // Act
            var result = await _userInformationService.CreateUserInformationWithDocumentTypesAsync(model, userId);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Contains("Category ID must be greater than zero", result.Message);
            _userInfoRepositoryMock.Verify(repo => repo.AddAsyn(It.IsAny<UserInformation>()), Times.Never);
        }

        [Fact]
        public async Task CreateUserInformationWithDocumentTypesAsync_EmptyDocTypeIds_ShouldReturnErrorResponse()
        {
            // Arrange
            var userId = 1;
            var model = new CreateUserInformationDto
            {
                Name = "John Doe",
                Email = "<EMAIL>",
                CategoryId = 1,
                EntityId = 10,
                DocumentTypeIds = new List<int>() // Empty
            };

            // Act
            var result = await _userInformationService.CreateUserInformationWithDocumentTypesAsync(model, userId);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Contains("At least one document type ID must be provided", result.Message);
            _userInfoRepositoryMock.Verify(repo => repo.AddAsyn(It.IsAny<UserInformation>()), Times.Never);
        }        

        [Fact]
        public async Task GetCategoriesAndDocumentTypes_ShouldCombineCategoriesAndDocTypes()
        {
            // Arrange
            int companyId = 10;

            var categories = new List<UserCategoryDto>
            {
                new UserCategoryDto { CategoryId = 1, Category = "Admin" },
                new UserCategoryDto { CategoryId = 2, Category = "User" }
            };

            var documentTypes = new List<DocumentTypeDto>
            {
                new DocumentTypeDto { DocumentTypeID = 100, DocumentName = "Financial Report" },
                new DocumentTypeDto { DocumentTypeID = 200, DocumentName = "Legal Document" }
            };

            _categoryServiceMock.Setup(service => service.GetCategories())
                .ReturnsAsync(categories);

            var docConfigs = new List<DocCollectionFrequencyConfig>
            {
                new DocCollectionFrequencyConfig
                {
                    Id = 1,
                    DocTypeID = 100,
                    FeatureId = _portfolioCompanyFeatureId,
                    EntityId = companyId,
                    IsDeleted = false
                },
                new DocCollectionFrequencyConfig
                {
                    Id = 2,
                    DocTypeID = 200,
                    FeatureId = _portfolioCompanyFeatureId,
                    EntityId = companyId,
                    IsDeleted = false
                }
            };

            var docTypes = new List<DataExtractionTypes>
            {
                new DataExtractionTypes { Id = 100, DocumentName = "Financial Report", IsDeleted = false },
                new DataExtractionTypes { Id = 200, DocumentName = "Legal Document", IsDeleted = false }
            };

            _docCollectionConfigRepositoryMock.Setup(repo => repo.FindAllAsync(
                It.IsAny<Expression<Func<DocCollectionFrequencyConfig, bool>>>()))
                .ReturnsAsync(docConfigs);

            _dataExtractionTypesRepositoryMock.Setup(repo => repo.FindAllAsync(
                It.IsAny<Expression<Func<DataExtractionTypes, bool>>>()))
                .ReturnsAsync(docTypes);

            // Act
            var result = await _userInformationService.GetCategoriesAndDocumentTypes(companyId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Categories.Count);
            Assert.Equal(2, result.DocumentTypes.Count);
            Assert.Equal("Admin", result.Categories[0].Category);
            Assert.Equal("Financial Report", result.DocumentTypes[0].DocumentName);
        }

        [Fact]
        public async Task GetUserInformationByIdAsync_ValidId_ReturnsUserInformationWithDocumentTypes()
        {
            // Arrange
            int userInformationId = 1;
            
            var userInfo = new UserInformation
            {
                UserInformationID = userInformationId,
                Name = "John Doe",
                Email = "<EMAIL>",
                CategoryID = 2,
                Recipient = "Internal",
                FeatureId = _portfolioCompanyFeatureId,
                IsDeleted = false
            };

            var category = new M_UserCategory
            {
                CategoryID = 2,
                Category = "Admin",
                IsDeleted = false
            };

            var userDocuments = new List<User_Documents>
            {
                new User_Documents 
                {
                    UserDocumentID = 101,
                    UserInformationID = userInformationId,
                    DocumentTypeID = 10,                    
                },
                new User_Documents
                {
                    UserDocumentID = 102,
                    UserInformationID = userInformationId,
                    DocumentTypeID = 20,                    
                }
            };

            var documentTypes = new List<DataExtractionTypes>
            {
                new DataExtractionTypes { Id = 10, DocumentName = "Financial Report", IsDeleted = false },
                new DataExtractionTypes { Id = 20, DocumentName = "Legal Document", IsDeleted = false }
            };

            _userInfoRepositoryMock.Setup(repo => repo.FindFirstAsync(
                It.IsAny<Expression<Func<UserInformation, bool>>>()))
                .ReturnsAsync(userInfo);

            _userCategoryRepositoryMock.Setup(repo => repo.FindFirstAsync(
                It.IsAny<Expression<Func<M_UserCategory, bool>>>()))
                .ReturnsAsync(category);

            _userDocumentsRepositoryMock.Setup(repo => repo.FindByAsyn(
                It.IsAny<Expression<Func<User_Documents, bool>>>()))
                .ReturnsAsync(userDocuments);

            _dataExtractionTypesRepositoryMock.Setup(repo => repo.FindByAsyn(
                It.IsAny<Expression<Func<DataExtractionTypes, bool>>>()))
                .ReturnsAsync(documentTypes);

            // Act
            var result = await _userInformationService.GetUserInformationByIdAsync(userInformationId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(userInformationId, result.UserInformationID);
            Assert.Equal("John Doe", result.Name);
            Assert.Equal("<EMAIL>", result.Email);
            Assert.Equal("Admin", result.Category);
            Assert.Equal("Internal", result.Recipient);
            Assert.Equal(2, result.DocumentTypes.Count);
            Assert.Equal(10, result.DocumentTypes[0].DocumentTypeID);
            Assert.Equal("Financial Report", result.DocumentTypes[0].DocumentName);
            Assert.Equal(20, result.DocumentTypes[1].DocumentTypeID);
            Assert.Equal("Legal Document", result.DocumentTypes[1].DocumentName);

            _userInfoRepositoryMock.Verify(repo => repo.FindFirstAsync(
                It.IsAny<Expression<Func<UserInformation, bool>>>()), Times.Once);
            _userDocumentsRepositoryMock.Verify(repo => repo.FindByAsyn(
                It.IsAny<Expression<Func<User_Documents, bool>>>()), Times.Once);
        }

        [Fact]
        public async Task GetUserInformationByIdAsync_UserNotFound_ReturnsNull()
        {
            // Arrange
            int userInformationId = 999;
            
            _userInfoRepositoryMock.Setup(repo => repo.FindFirstAsync(
                It.IsAny<Expression<Func<UserInformation, bool>>>()))
                .ReturnsAsync((UserInformation)null);

            // Act
            var result = await _userInformationService.GetUserInformationByIdAsync(userInformationId);

            // Assert
            Assert.Null(result);
            _userInfoRepositoryMock.Verify(repo => repo.FindFirstAsync(
                It.IsAny<Expression<Func<UserInformation, bool>>>()), Times.Once);
        }

        [Fact]
        public async Task UpdateUserInformationAsync_ValidModel_UpdatesUserAndDocumentTypes()
        {
            // Arrange
            int userId = 5;
            var model = new UpdateUserInformationDto
            {
                UserInformationID = 1,
                Name = "Updated Name",
                Email = "<EMAIL>",
                CategoryId = 3,
                Recipient = "External",
                EntityId = 10,
                DocumentTypeIds = new List<int> { 30, 40 }
            };

            var existingUserInfo = new UserInformation
            {
                UserInformationID = 1,
                Name = "Original Name",
                Email = "<EMAIL>",
                CategoryID = 2,
                Recipient = "Internal",
                EntityID = 10,
                IsDeleted = false
            };

            var existingUserDocuments = new List<User_Documents>
            {
                new User_Documents 
                {
                    UserDocumentID = 101,
                    UserInformationID = 1,
                    DocumentTypeID = 20,                    
                },
                new User_Documents
                {
                    UserDocumentID = 102,
                    UserInformationID = 1,
                    DocumentTypeID = 30,                    
                }
            };

            _userInfoRepositoryMock.Setup(repo => repo.FindFirstAsync(
                It.IsAny<Expression<Func<UserInformation, bool>>>()))
                .ReturnsAsync(existingUserInfo);

            _userDocumentsRepositoryMock.Setup(repo => repo.FindByAsyn(
                It.IsAny<Expression<Func<User_Documents, bool>>>()))
                .ReturnsAsync(existingUserDocuments);

            _unitOfWorkMock.Setup(uow => uow.SaveAsync()).ReturnsAsync(1);

            // Act
            var result = await _userInformationService.UpdateUserInformationAsync(model, userId);

            // Assert
            Assert.True(result.IsSuccess);
            
            _userInfoRepositoryMock.Verify(repo => repo.Update(
                It.Is<UserInformation>(ui => 
                    ui.Name == "Updated Name" &&
                    ui.Email == "<EMAIL>" &&
                    ui.CategoryID == 3 &&
                    ui.Recipient == "External")), Times.Once);

            // Verify document deletion (one document should be deleted - the one with ID 20)
            _userDocumentsRepositoryMock.Verify(repo => repo.Delete(
                It.Is<User_Documents>(ud => ud.DocumentTypeID == 20)), Times.Once);

            // Verify document addition (one document should be added - with ID 40)
            _userDocumentsRepositoryMock.Verify(repo => repo.AddAsyn(
                It.Is<User_Documents>(ud => 
                    ud.UserInformationID == 1 && 
                    ud.DocumentTypeID == 40)), Times.Once);
            
            _unitOfWorkMock.Verify(uow => uow.SaveAsync(), Times.Once);
        }

        [Fact]
        public async Task UpdateUserInformationAsync_UserNotFound_ReturnsFalse()
        {
            // Arrange
            int userId = 5;
            var model = new UpdateUserInformationDto
            {
                UserInformationID = 999,
                Name = "Updated Name",
                Email = "<EMAIL>",
                CategoryId = 3,
                Recipient = "External",
                EntityId = 10,
                DocumentTypeIds = new List<int> { 30, 40 }
            };

            _userInfoRepositoryMock.Setup(repo => repo.FindFirstAsync(
                It.IsAny<Expression<Func<UserInformation, bool>>>()))
                .ReturnsAsync((UserInformation)null);

            // Act
            var result = await _userInformationService.UpdateUserInformationAsync(model, userId);

            // Assert
            Assert.False(result.IsSuccess);
            _userInfoRepositoryMock.Verify(repo => repo.FindFirstAsync(
                It.IsAny<Expression<Func<UserInformation, bool>>>()), Times.Once);
            _userDocumentsRepositoryMock.Verify(repo => repo.FindByAsyn(
                It.IsAny<Expression<Func<User_Documents, bool>>>()), Times.Never);
            _unitOfWorkMock.Verify(uow => uow.SaveAsync(), Times.Never);
        }

        [Fact]
        public async Task DeleteUserInformationAsync_UserExists_DeletesUserAndDocuments_ReturnsSuccess()
        {
            // Arrange
            int userInformationId = 1;
            int userId = 123;
            var userInfo = new UserInformation { UserInformationID = userInformationId, IsDeleted = false };
            var userDocs = new List<User_Documents> { new User_Documents { UserInformationID = userInformationId, DocumentTypeID = 1 } };
            _userInfoRepositoryMock.Setup(r => r.FindFirstAsync(It.IsAny<Expression<Func<UserInformation, bool>>>()))
                .ReturnsAsync(userInfo);
            _userDocumentsRepositoryMock.Setup(r => r.FindByAsyn(It.IsAny<Expression<Func<User_Documents, bool>>>()))
                .ReturnsAsync(userDocs);
            _unitOfWorkMock.Setup(u => u.SaveAsync()).ReturnsAsync(1);

            // Act
            var result = await _userInformationService.DeleteUserInformationAsync(userInformationId, userId);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.True(result.Data);
            Assert.Equal("User information deleted successfully", result.Message);
            _userInfoRepositoryMock.Verify(r => r.Update(It.Is<UserInformation>(u => u.IsDeleted)), Times.Once);
            _userDocumentsRepositoryMock.Verify(r => r.Delete(It.IsAny<User_Documents>()), Times.Exactly(userDocs.Count));
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Fact]
        public async Task DeleteUserInformationAsync_UserNotFound_ReturnsFailure()
        {
            // Arrange
            int userInformationId = 2;
            int userId = 123;
            _userInfoRepositoryMock.Setup(r => r.FindFirstAsync(It.IsAny<Expression<Func<UserInformation, bool>>>()))
                .ReturnsAsync((UserInformation?)null);

            // Act
            var result = await _userInformationService.DeleteUserInformationAsync(userInformationId, userId);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.False(result.Data);
            Assert.Contains("not found", result.Message);
            _userInfoRepositoryMock.Verify(r => r.Update(It.IsAny<UserInformation>()), Times.Never);
            _userDocumentsRepositoryMock.Verify(r => r.Delete(It.IsAny<User_Documents>()), Times.Never);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Never);
        }

        [Fact]
        public async Task DeleteUserInformationAsync_ExceptionThrown_ReturnsFailure()
        {
            // Arrange
            int userInformationId = 3;
            int userId = 123;
            _userInfoRepositoryMock.Setup(r => r.FindFirstAsync(It.IsAny<Expression<Func<UserInformation, bool>>>()))
                .ThrowsAsync(new Exception("db error"));

            // Act
            var result = await _userInformationService.DeleteUserInformationAsync(userInformationId, userId);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.False(result.Data);
            Assert.Contains("Error deleting user information", result.Message);
        }
    }
}
