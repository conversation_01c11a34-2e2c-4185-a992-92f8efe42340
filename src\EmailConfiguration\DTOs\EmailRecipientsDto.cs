namespace EmailConfiguration.DTOs
{
    /// <summary>
    /// DTO for email recipients information
    /// </summary>
    public class EmailRecipientsDto
    {
        /// <summary>
        /// Internal user ID for internal recipients
        /// </summary>
        public int InternalID { get; set; }

        /// <summary>
        /// Display name of the recipient
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Email address of the recipient
        /// </summary>
        public string Email { get; set; }
    }
}
