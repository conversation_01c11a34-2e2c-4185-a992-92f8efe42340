namespace DataAccessLayer.DBModel
{
    public partial class Mapping_CompanyBalanceSheetLineItems : BaseModel
    {
        public int CompanyBalanceSheetLineItemMappingID { get; set; }
        public int BalanceSheetLineItemID { get; set; }
        public int? ParentLineItemID { get; set; }
        public int? DisplayOrder { get; set; }
        public int PortfolioCompanyID { get; set; }
        public bool IsDeleted { get; set; }
        public bool IsActive { get; set; }
        public string SegmentType { get; set; }
        public string EncryptedCompanyBalanceSheetLineItemMappingID { get; set; }
        public string Formula { get; set; }
        public string FormulaKPIId { get; set; }
        public bool IsExtraction { get; set; }
        public string Synonym { get; set; }
        public string Definition { get; set; }
    }

}