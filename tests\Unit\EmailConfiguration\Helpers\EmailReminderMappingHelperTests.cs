using EmailConfiguration.Helpers;
using EmailConfiguration.DTOs;
using DataAccessLayer.Models.EmailNotifications;
using Xunit;

namespace EmailConfiguration.UnitTest.Helpers
{
    /// <summary>
    /// Unit tests for EmailReminderMappingHelper
    /// </summary>
    public class EmailReminderMappingHelperTests
    {
        [Fact]
        public void MapToDetailsDto_WithoutConfig_ReturnsDefaultValues()
        {
            // Arrange
            var reminderId = Guid.NewGuid();
            var emailReminder = new EmailReminder
            {
                ReminderId = reminderId,
                Subject = "Test Subject",
                EmailBody = "Test Body"
            };

            var recipients = new List<EmailReminderRecipients>
            {
                new EmailReminderRecipients
                {
                    ReminderId = reminderId,
                    RecipientType = RecipientTypes.To,
                    RecipientId = 1,
                    EmailAddress = "<EMAIL>"
                }
            };

            var groups = new List<EmailNotificationGroup>();

            // Act
            var result = EmailReminderMappingHelper.MapToDetailsDto(emailReminder, recipients, groups);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(reminderId, result.ReminderId);
            Assert.Equal("Test Subject", result.Subject);
            Assert.Equal("Test Body", result.MessageBody);
            Assert.Equal(0, result.TotalNumberOfReminders);
            Assert.Equal(0, result.TotalRemindersSent);
            Assert.Null(result.LastReminderSentDate);
            Assert.Null(result.NextReminderScheduledDate);
        }

        [Fact]
        public void MapToDetailsDto_WithConfig_ReturnsConfigValues()
        {
            // Arrange
            var reminderId = Guid.NewGuid();
            var emailReminder = new EmailReminder
            {
                ReminderId = reminderId,
                Subject = "Test Subject",
                EmailBody = "Test Body"
            };

            var config = new EmailReminderConfig
            {
                Id = 1,
                ReminderId = reminderId,
                TotalRemindersPerCycle = 5
            };

            var recipients = new List<EmailReminderRecipients>();
            var groups = new List<EmailNotificationGroup>();

            var totalSent = 3;
            var lastSentDate = new DateTime(2024, 1, 5);
            var nextScheduledDate = new DateTime(2024, 1, 10);

            // Act
            var result = EmailReminderMappingHelper.MapToDetailsDto(
                emailReminder, 
                recipients, 
                groups, 
                config, 
                totalSent, 
                lastSentDate, 
                nextScheduledDate);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(reminderId, result.ReminderId);
            Assert.Equal("Test Subject", result.Subject);
            Assert.Equal("Test Body", result.MessageBody);
            Assert.Equal(5, result.TotalNumberOfReminders);
            Assert.Equal(3, result.TotalRemindersSent);
            Assert.Equal(new DateTime(2024, 1, 5), result.LastReminderSentDate);
            Assert.Equal(new DateTime(2024, 1, 10), result.NextReminderScheduledDate);
        }

        [Fact]
        public void MapToDetailsDto_WithNullConfig_ReturnsZeroForTotalReminders()
        {
            // Arrange
            var reminderId = Guid.NewGuid();
            var emailReminder = new EmailReminder
            {
                ReminderId = reminderId,
                Subject = "Test Subject",
                EmailBody = "Test Body"
            };

            var recipients = new List<EmailReminderRecipients>();
            var groups = new List<EmailNotificationGroup>();

            var totalSent = 2;
            var lastSentDate = new DateTime(2024, 1, 3);
            var nextScheduledDate = new DateTime(2024, 1, 8);

            // Act
            var result = EmailReminderMappingHelper.MapToDetailsDto(
                emailReminder, 
                recipients, 
                groups, 
                null, // null config
                totalSent, 
                lastSentDate, 
                nextScheduledDate);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(0, result.TotalNumberOfReminders); // Should be 0 when config is null
            Assert.Equal(2, result.TotalRemindersSent);
            Assert.Equal(new DateTime(2024, 1, 3), result.LastReminderSentDate);
            Assert.Equal(new DateTime(2024, 1, 8), result.NextReminderScheduledDate);
        }

        [Fact]
        public void MapToDetailsDto_WithNullDates_ReturnsNullDates()
        {
            // Arrange
            var reminderId = Guid.NewGuid();
            var emailReminder = new EmailReminder
            {
                ReminderId = reminderId,
                Subject = "Test Subject",
                EmailBody = "Test Body"
            };

            var config = new EmailReminderConfig
            {
                Id = 1,
                ReminderId = reminderId,
                TotalRemindersPerCycle = 3
            };

            var recipients = new List<EmailReminderRecipients>();
            var groups = new List<EmailNotificationGroup>();

            var totalSent = 0;
            DateTime? lastSentDate = null;
            DateTime? nextScheduledDate = null;

            // Act
            var result = EmailReminderMappingHelper.MapToDetailsDto(
                emailReminder, 
                recipients, 
                groups, 
                config, 
                totalSent, 
                lastSentDate, 
                nextScheduledDate);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(3, result.TotalNumberOfReminders);
            Assert.Equal(0, result.TotalRemindersSent);
            Assert.Null(result.LastReminderSentDate);
            Assert.Null(result.NextReminderScheduledDate);
        }

        [Fact]
        public void MapToDetailsDto_WithToAndCcRecipients_MapsCorrectly()
        {
            // Arrange
            var reminderId = Guid.NewGuid();
            var emailReminder = new EmailReminder
            {
                ReminderId = reminderId,
                Subject = "Test Subject",
                EmailBody = "Test Body"
            };

            var recipients = new List<EmailReminderRecipients>
            {
                new EmailReminderRecipients
                {
                    ReminderId = reminderId,
                    RecipientType = RecipientTypes.To,
                    RecipientId = 1,
                    EmailAddress = "<EMAIL>"
                },
                new EmailReminderRecipients
                {
                    ReminderId = reminderId,
                    RecipientType = RecipientTypes.To,
                    RecipientId = 2,
                    EmailAddress = "<EMAIL>"
                },
                new EmailReminderRecipients
                {
                    ReminderId = reminderId,
                    RecipientType = RecipientTypes.Cc,
                    RecipientId = 3,
                    EmailAddress = "<EMAIL>"
                }
            };

            var groups = new List<EmailNotificationGroup>();

            var config = new EmailReminderConfig
            {
                Id = 1,
                ReminderId = reminderId,
                TotalRemindersPerCycle = 4
            };

            // Act
            var result = EmailReminderMappingHelper.MapToDetailsDto(
                emailReminder, 
                recipients, 
                groups, 
                config, 
                1, 
                new DateTime(2024, 1, 1), 
                new DateTime(2024, 1, 5));

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.ToRecipients.Count);
            Assert.Equal(1, result.CcRecipients.Count);
            Assert.Equal(4, result.TotalNumberOfReminders);
            Assert.Equal(1, result.TotalRemindersSent);
            Assert.Equal(new DateTime(2024, 1, 1), result.LastReminderSentDate);
            Assert.Equal(new DateTime(2024, 1, 5), result.NextReminderScheduledDate);
        }

        [Fact]
        public void MapToDetailsDto_WithEmptyRecipients_ReturnsEmptyLists()
        {
            // Arrange
            var reminderId = Guid.NewGuid();
            var emailReminder = new EmailReminder
            {
                ReminderId = reminderId,
                Subject = "Test Subject",
                EmailBody = "Test Body"
            };

            var recipients = new List<EmailReminderRecipients>();
            var groups = new List<EmailNotificationGroup>();

            var config = new EmailReminderConfig
            {
                Id = 1,
                ReminderId = reminderId,
                TotalRemindersPerCycle = 2
            };

            // Act
            var result = EmailReminderMappingHelper.MapToDetailsDto(
                emailReminder, 
                recipients, 
                groups, 
                config, 
                0, 
                null, 
                new DateTime(2024, 1, 10));

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result.ToRecipients);
            Assert.Empty(result.CcRecipients);
            Assert.Equal(2, result.TotalNumberOfReminders);
            Assert.Equal(0, result.TotalRemindersSent);
            Assert.Null(result.LastReminderSentDate);
            Assert.Equal(new DateTime(2024, 1, 10), result.NextReminderScheduledDate);
        }
    }
}
