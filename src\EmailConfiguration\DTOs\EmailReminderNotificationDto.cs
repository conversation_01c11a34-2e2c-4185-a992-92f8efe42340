using System;
using System.Collections.Generic;

namespace EmailConfiguration.DTOs
{
    /// <summary>
    /// DTO for email reminder notification data
    /// </summary>
    public class EmailReminderNotificationDto
    {
        public int ScheduleId { get; set; }
        public Guid ReminderId { get; set; }
        public string Subject { get; set; }
        public string EmailBody { get; set; }
        public DateTime ReminderDate { get; set; }
        public int ScheduleOccurrence { get; set; }
        public List<EmailRecipientDto> ToRecipients { get; set; } = new List<EmailRecipientDto>();
        public List<EmailRecipientDto> CcRecipients { get; set; } = new List<EmailRecipientDto>();
    }

    /// <summary>
    /// DTO for email recipient information
    /// </summary>
    public class EmailRecipientDto
    {
        public int? RecipientId { get; set; }
        public string EmailAddress { get; set; }
        public bool IsGroupMember { get; set; }
        public int? GroupID { get; set; }
    }
}
