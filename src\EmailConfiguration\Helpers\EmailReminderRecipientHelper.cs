using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using DataAccessLayer.Models.EmailNotifications;
using DataAccessLayer.UnitOfWork;
using EmailConfiguration.DTOs;

namespace EmailConfiguration.Helpers
{
    /// <summary>
    /// Helper class for email reminder recipient operations
    /// </summary>
    public static class EmailReminderRecipientHelper
    {
        /// <summary>
        /// Creates an EmailReminderRecipients entity from DTO
        /// </summary>
        /// <param name="reminderId">Reminder ID</param>
        /// <param name="recipient">Recipient DTO</param>
        /// <param name="recipientType">Type of recipient (To or Cc)</param>
        /// <param name="userId">User ID for audit</param>
        /// <returns>EmailReminderRecipients entity</returns>
        public static EmailReminderRecipients CreateEmailReminderRecipient(Guid reminderId, EmailRecipientsDto recipient,
            DataAccessLayer.Models.EmailNotifications.RecipientTypes recipientType, int userId)
        {
            return new EmailReminderRecipients
            {
                ReminderId = reminderId,
                RecipientType = recipientType,
                RecipientId = !string.IsNullOrEmpty(recipient.Email) ? recipient.InternalID : null,
                EmailAddress = !string.IsNullOrEmpty(recipient.Email) ? recipient.Email : null,
                IsGroupMember = string.IsNullOrEmpty(recipient.Email),
                GroupID = string.IsNullOrEmpty(recipient.Email) ? recipient.InternalID : null,
                CreatedBy = userId,
                CreatedOn = DateTime.UtcNow,
                IsDeleted = false
            };
        }

        /// <summary>
        /// Creates multiple EmailReminderRecipients entities from DTOs
        /// </summary>
        /// <param name="reminderId">Reminder ID</param>
        /// <param name="recipients">List of recipient DTOs</param>
        /// <param name="recipientType">Type of recipient (To or Cc)</param>
        /// <param name="userId">User ID for audit</param>
        /// <returns>List of EmailReminderRecipients entities</returns>
        public static List<EmailReminderRecipients> CreateEmailReminderRecipients(Guid reminderId,
            IEnumerable<EmailRecipientsDto> recipients, DataAccessLayer.Models.EmailNotifications.RecipientTypes recipientType, int userId)
        {
            return recipients?.Select(recipient => CreateEmailReminderRecipient(reminderId, recipient, recipientType, userId))
                .ToList() ?? new List<EmailReminderRecipients>();
        }

        /// <summary>
        /// Removes existing recipients for a reminder
        /// </summary>
        /// <param name="unitOfWork">Unit of work instance</param>
        /// <param name="reminderId">Reminder ID</param>
        /// <returns>Task</returns>
        public static async Task RemoveExistingRecipientsAsync(IUnitOfWork unitOfWork, Guid reminderId)
        {
            var existingRecipients = await unitOfWork.EmailReminderRecipientsRepository
                .GetManyAsync(x => x.ReminderId == reminderId);

            foreach (var recipient in existingRecipients)
            {
                unitOfWork.EmailReminderRecipientsRepository.Delete(recipient);
            }
        }

        /// <summary>
        /// Adds recipients to the repository
        /// </summary>
        /// <param name="unitOfWork">Unit of work instance</param>
        /// <param name="recipients">List of recipients to add</param>
        /// <returns>Task</returns>
        public static async Task AddRecipientsAsync(IUnitOfWork unitOfWork, IEnumerable<EmailReminderRecipients> recipients)
        {
            foreach (var recipient in recipients)
            {
                await unitOfWork.EmailReminderRecipientsRepository.AddAsyn(recipient);
            }
        }

        /// <summary>
        /// Maps EmailReminderRecipients to EmailRecipientDto
        /// </summary>
        /// <param name="recipients">Email reminder recipients</param>
        /// <param name="recipientType">Type of recipient to filter (To or Cc)</param>
        /// <returns>List of EmailRecipientDto</returns>
        public static List<EmailRecipientDto> MapToEmailRecipientDto(IEnumerable<EmailReminderRecipients> recipients, DataAccessLayer.Models.EmailNotifications.RecipientTypes recipientType)
        {
            return recipients
                .Where(x => x.RecipientType == recipientType)
                .Select(x => new EmailRecipientDto
                {
                    RecipientId = x.RecipientId,
                    EmailAddress = x.EmailAddress,
                    IsGroupMember = x.IsGroupMember,
                    GroupID = x.GroupID
                })
                .ToList();
        }

        /// <summary>
        /// Maps EmailReminderRecipients to EmailRecipientDetailsDto with group information
        /// </summary>
        /// <param name="recipient">Email reminder recipient</param>
        /// <param name="groups">Available groups</param>
        /// <returns>EmailRecipientDetailsDto</returns>
        public static EmailRecipientDetailsDto MapToEmailRecipientDetailsDto(EmailReminderRecipients recipient, 
            IEnumerable<EmailNotificationGroup> groups)
        {
            var recipientDetails = new EmailRecipientDetailsDto
            {
                RecipientId = recipient.RecipientId,
                EmailAddress = recipient.EmailAddress,
                IsGroupMember = recipient.IsGroupMember,
                GroupID = recipient.GroupID
            };

            if (recipient.IsGroupMember && recipient.GroupID.HasValue)
            {
                var group = groups.FirstOrDefault(g => g.GroupId == recipient.GroupID.Value);
                recipientDetails.Name = group?.GroupName ?? "Unknown Group";
            }

            return recipientDetails;
        }

        /// <summary>
        /// Gets group IDs from recipients
        /// </summary>
        /// <param name="recipients">Email reminder recipients</param>
        /// <returns>List of unique group IDs</returns>
        public static List<int> GetGroupIdsFromRecipients(IEnumerable<EmailReminderRecipients> recipients)
        {
            return recipients
                .Where(r => r.GroupID.HasValue && r.IsGroupMember)
                .Select(r => r.GroupID.Value)
                .Distinct()
                .ToList();
        }
    }
}
