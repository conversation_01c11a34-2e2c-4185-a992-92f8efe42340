using System.Net;
using API.Filters.CustomAuthorization;
using API.Helpers;
using Contract.Account;
using Contract.Fund.Models;
using Contract.KPI;
using Contract.Utility;
using Fund;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Utility.Resource;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Contract.PortfolioCompany.DomainModel.PortfolioCompanyKPI;
using Shared;

namespace API.Controllers.Fund
{
    [Route("api")]
    [Authorize(Policy = AppSettings.DefaultAuthPolicy)]
    [ApiController]
    public class FundKpiController(
        IFundKpiService fundKpiService,
        IHelperService userService,
        IEncryption encryption,
        ILogger<FundKpiController> logger) : ControllerBase
    {
        private readonly IFundKpiService _fundKpiService = fundKpiService;
        private readonly IHelperService _userService = userService;
        private readonly IEncryption _encryption = encryption;
        private readonly ILogger<FundKpiController> _logger = logger;

        /// <summary>
        /// Get Fund KPI List by Module ID
        /// </summary>
        /// <param name="moduleId">Module ID</param>
        /// <returns>List of Fund KPIs</returns>
        [HttpPost("fund/kpi/list/{moduleId}")]
        public async Task<IActionResult> GetFundKpiList(int moduleId)
        {
            _logger.LogInformation("Getting fund KPI list for moduleId: {ModuleId}", moduleId);
            var result = await _fundKpiService.GetFundKPIList(moduleId);
            _logger.LogInformation("Retrieved {Count} KPIs for moduleId: {ModuleId}", result?.Count() ?? 0, moduleId);
            return Ok(result);
        }

        /// <summary>
        /// Add or Update Fund KPI
        /// </summary>
        /// <param name="kpiModel">Fund KPI Model</param>
        /// <returns>Success status</returns>
        [HttpPost("fund/kpi/addorupdate")]
        public async Task<IActionResult> AddOrUpdateFundKpi(FundKpiModel kpiModel)
        {
            _logger.LogInformation("Adding or updating fund KPI with ID: {KpiId}", kpiModel.FundSectionKpiId);
            int userId = _userService.GetCurrentUserId(User);
            var result = await _fundKpiService.AddOrUpdateFundKPI(kpiModel, userId);
            if (result == 1)
            {
                _logger.LogInformation("Successfully added/updated fund KPI with ID: {KpiId}", kpiModel.FundSectionKpiId);
                return Ok(true);
            }
            else if (result == -1)
            {
                _logger.LogError("Failed to add/update fund KPI with ID: {KpiId}, result: -1", kpiModel.FundSectionKpiId);
                return Ok(new { status = -1, message = "" });
            }
            else
            {
                _logger.LogError("Failed to add/update fund KPI with ID: {KpiId}, result: {Result}", kpiModel.FundSectionKpiId, result);
                return Ok(false);
            }
        }

        /// <summary>
        /// Get Unmapped Fund KPIs
        /// </summary>
        /// <param name="fundId">Fund ID</param>
        /// <param name="moduleId">Module ID</param>
        /// <returns>List of unmapped KPI mapping models</returns>
        [HttpGet("fund/kpi/unmapped/{fundId}/{moduleId}")]
        public async Task<IActionResult> GetUnmappedFundKpi(string fundId, int moduleId)
        {
            _logger.LogInformation("Getting unmapped fund KPIs for fundId: {FundId}, moduleId: {ModuleId}", fundId, moduleId);
            _= int.TryParse(_encryption.Decrypt(fundId), out int id);
            var result = await _fundKpiService.GetUnMappedFundKpi(id, moduleId);
            _logger.LogInformation("Retrieved {Count} unmapped KPIs for fundId: {FundId}, moduleId: {ModuleId}", result?.Count ?? 0, fundId, moduleId);
            return Ok(result);
        }

        /// <summary>
        /// Get Fund KPI Mapping
        /// </summary>
        /// <param name="fundId">Fund ID</param>
        /// <param name="moduleId">Module ID</param>
        /// <returns>List of KPI mapping models</returns>
        [HttpGet("fund/kpi/mapping/{fundId}/{moduleId}")]
        public async Task<IActionResult> GetFundKpiMapping(string fundId, int moduleId)
        {
            _logger.LogInformation("Getting fund KPI mappings for fundId: {FundId}, moduleId: {ModuleId}", fundId, moduleId);
            _ = int.TryParse(_encryption.Decrypt(fundId), out int id);
            var result = await _fundKpiService.FundKPIMapping(id, moduleId);
            if (result == null)
            {
                _logger.LogInformation("No KPI mappings found for fundId: {FundId}, moduleId: {ModuleId}", fundId, moduleId);
                return JsonResponse.Create(HttpStatusCode.OK, Messages.NoRecordFound);
            }
            _logger.LogInformation("Retrieved {Count} KPI mappings for fundId: {FundId}, moduleId: {ModuleId}", result?.Count ?? 0, fundId, moduleId);
            return JsonResponse.Create(HttpStatusCode.OK, result);
        }

        /// <summary>
        /// Create duplicate Fund KPI
        /// </summary>
        /// <param name="duplicateKPI">Duplicate KPI model</param>
        /// <returns>Result status</returns>
        [HttpPost("fund/kpi/duplicate")]
        public async Task<IActionResult> CreateDuplicateFundKpi(DuplicateKpiModel duplicateKPI)
        {
            _logger.LogInformation("Creating duplicate fund KPI from KPI ID: {KpiId}", duplicateKPI.Id);
            duplicateKPI.UserId = _userService.GetCurrentUserId(User);
            var result = await _fundKpiService.CreateFundKpi(duplicateKPI);
            if (result == 0)
            {
                _logger.LogError("Failed to create duplicate fund KPI from KPI ID: {KpiId}", duplicateKPI.Id);
                return BadRequest();
            }
            _logger.LogInformation("Successfully created duplicate fund KPI from KPI ID: {KpiId}, new KPI ID: {NewKpiId}", duplicateKPI.Id, result);
            return Ok(result);
        }

        /// <summary>
        /// Update Fund KPI Mapping
        /// </summary>
        /// <param name="model">KPI Mapping update request model</param>
        /// <returns>Success status</returns>
        [HttpPost("fund/kpi/mapping/{fundId}/{moduleId}")]
        [UserFeatureAuthorize((int)Features.KPIsMapping)]
        public async Task<IActionResult> UpdateFundKpiMapping(string fundId,int moduleId,KpiMappingQueryModel kpiMappingQueryModel)
        {            _logger.LogInformation("UpdateFundKpiMapping started for fundId: {FundId}, moduleId: {ModuleId}", fundId, moduleId);
            _= int.TryParse(_encryption.Decrypt(fundId), out int id);
            int userId = _userService.GetCurrentUserId(User);
            _logger.LogInformation("UpdateFundKpiMapping - Processing update for decrypted fundId: {FundId}, userId: {UserId}", id, userId);
            var result = await _fundKpiService.UpdateFundKPIMapping(id, kpiMappingQueryModel.KPIMappings, userId, moduleId);
            if (kpiMappingQueryModel?.CompanyIds?.Any() == true)
            {
                _logger.LogInformation("UpdateFundKpiMapping - Copying KPIs to {CompanyCount} companies", kpiMappingQueryModel.CompanyIds.Count());
                await _fundKpiService.CopyFundKpiToCompanies(new CopyToKpiQueryModel()
                {
                    CompanyId = id,
                    CompanyIds = kpiMappingQueryModel.CompanyIds,
                    UserId = userId,
                    ModuleId = moduleId
                });
            }
            _logger.LogInformation("UpdateFundKpiMapping completed successfully for fundId: {FundId}", fundId);
            return JsonResponse.Create(HttpStatusCode.OK, "Done");
        }

        /// <summary>
        /// Delete Fund KPI
        /// </summary>
        /// <param name="kPIModel">KPI Delete model</param>
        /// <returns>Success status</returns>
        [HttpPost("fund/kpi/delete")]
        public async Task<IActionResult> DeleteFundKpi(DeleteKpiModel kPIModel)
        {
            _logger.LogInformation("DeleteFundKpi started with KPIId: {KPIId}, KpiType: {KpiType}", kPIModel.KPIId, kPIModel.KpiType);
            var result = await _fundKpiService.DeleteFundKPI(kPIModel);
            if (result > 0)
            {
                _logger.LogInformation("DeleteFundKpi completed successfully. Deleted {Count} records", result);
            }
            else
            {
                _logger.LogWarning("DeleteFundKpi completed but no records were deleted");
            }
            return Ok(result > 0);
        }

        /// <summary>
        /// GetFundKPIsResponse
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost("fund/kpi/values")]
        [UserFeatureAuthorize((int)Features.Fund)]
        public async Task<IActionResult> GetFundKPIsResponse(PcKPIsFilterType filter)
        {
            return await GetFundKpiTableResponse(filter);
        }

        /// <summary>
        /// GetFundKPIsPageConfigResponse
        /// </summary>
        /// <returns></returns>
        [HttpGet("fund/kpi/pageconfig")]
        public async Task<IActionResult> GetFundKPIsPageConfigResponse()
        {
            return Ok( await _fundKpiService.GetPageConfigSubSectionFields());
        }

        [HttpGet("fund/kpi/tabList")]
        public async Task<IActionResult> GetFundKPIsTabListResponse()
        {
            return Ok(await _fundKpiService.GetTabList());
        }

        /// <summary>
        /// GetFundKpiTableResponse
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        private async Task<IActionResult> GetFundKpiTableResponse(PcKPIsFilterType filter)
        {
            PcKPIsResponse result = await FundKpiValues(filter);
            return SetReturnValues(filter, result);
        }

        /// <summary>
        /// SetReturnValues
        /// </summary>
        /// <param name="filter"></param>
        /// <param name="result"></param>
        /// <returns></returns>
        private IActionResult SetReturnValues(PcKPIsFilterType filter, PcKPIsResponse? result)
        {
            if (result?.Rows?.Any() == true)
            {
                SetReturnResult(filter, result);
                return Ok(result);
            }
            return NoContent();
        }
        /// <summary>
        /// Gets the PC KPI values for the specified filter.
        /// </summary>
        /// <param name="filter">The filter.</param>
        /// <returns>The PC KPI values.</returns>
        private async Task<PcKPIsResponse> FundKpiValues(PcKPIsFilterType filter)
        {
            return await _fundKpiService.GetFundKpiValues(filter);
        }

        /// <summary>
        /// SetReturnResult
        /// </summary>
        /// <param name="filter"></param>
        /// <param name="result"></param>
        private static void SetReturnResult(PcKPIsFilterType filter, PcKPIsResponse result)
        {
            result.IsMonthly = filter.IsMonthly;
            result.IsQuarterly = filter.IsQuarterly;
            result.IsAnnually = filter.IsAnnually;
        }
    }
}
