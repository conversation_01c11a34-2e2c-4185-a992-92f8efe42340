using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Diagnostics.CodeAnalysis;
using DataAccessLayer.DBModel;

namespace DataAccessLayer.Models.EmailNotifications
{
    [Table("EmailReminderSchedule")]
    [ExcludeFromCodeCoverage]
    public class EmailReminderSchedule : BaseCommonModel
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Required]
        public int ReminderConfigId { get; set; }

        [Required]
        public DateTime ReminderDate { get; set; }

        [Required]
        public int ScheduleOccurrence { get; set; } // Occurrence number in the cycle

        [Required]
        public ReminderStatus Status { get; set; } = ReminderStatus.Pending; // 0=Pending, 1=Sent, 2=Failed , 3= skipped

        public DateTime? SentDate { get; set; }

        [Column(TypeName = "nvarchar(MAX)")]
        public string Error { get; set; }

        [Required]
        public bool IsActive { get; set; } = true;
    }

    public enum ReminderStatus
    {
        Pending = 0,
        Sent = 1,
        Failed = 2,
        Skipped = 3
    }
}
