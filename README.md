# FolioSure
This is the core version of FolioSure which is used for client demos. Currently application is built based on Monolithic architecture.
Moving to micro service architecture in progress.

### Environment Details
  - User Interface: Angular 16, Node JS
  - API: .NET 8 version
  - Database: MS SQL Server 2016  

### Tools

- sudo lsof -i :5001
- kill -9 <PID>

#### Reference 
https://dev.to/htissink/versioning-asp-net-core-apis-with-swashbuckle-making-space-potatoes-v-x-x-x-3po7- 

### git

- git submodule deinit <path_to_submodule>
- git rm <path_to_submodule>
- git commit-m "Removed submodule "
- rm -rf .git/modules/<path_to_submodule>

Changes:
- Fixed download issue of KPI's
- Upgraded to .NET 8
- Removed duplicate functions from before and after install files.

Enhancements to consider
- Encrypted ID is good concept but how ever the data being pushed into db without object model does not have capability to have encrypted id being generated.
- Only one Stock Exchange is being listed
- Taxonomy of Company Financial does have any child line item.

Download Chromium for revealBI export
https://playwright.azureedge.net/builds/chromium/1028/chromium-win64.zip
