﻿using EmailConfiguration.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace EmailConfiguration.Interfaces
{
    public interface IUserInformationService
    {
        /// <summary>
        /// Creates a new user information record along with associated document types
        /// </summary>
        /// <param name="model">The data transfer object containing all user information details</param>
        /// <param name="userId">ID of the user creating this record</param>
        /// <returns>The ID of the newly created user information record</returns>
        Task<ResponseDto<int>> CreateUserInformationWithDocumentTypesAsync(CreateUserInformationDto model, int userId);

        /// <summary>
        /// Gets a list of user information records with their associated document type names
        /// </summary>
        /// <param name="filter">Filter criteria for the query</param>
        /// <returns>List of user information records with document types</returns>
        Task<List<UserInformationResponseDto>> GetUserInformationWithDocumentTypesAsync(int CompanyId);

        /// <summary>
        /// Gets a specific user information record by its ID with associated document types
        /// </summary>
        /// <param name="userInformationId">The ID of the user information record</param>
        /// <returns>The user information record with document types</returns>
        Task<UserInformationResponseDto> GetUserInformationByIdAsync(int userInformationId);

        /// <summary>
        /// Updates an existing user information record along with associated document types
        /// </summary>
        /// <param name="model">The data transfer object containing updated user information details</param>
        /// <param name="userId">ID of the user updating this record</param>
        /// <returns>True if update was successful, false otherwise</returns>
        Task<ResponseDto<bool>> UpdateUserInformationAsync(UpdateUserInformationDto model, int userId);

        /// <summary>
        /// Deletes a user and its related document mappings by user information ID
        /// </summary>
        /// <param name="userInformationId">The ID of the user information to delete</param>
        /// <param name="userId">The ID of the user performing the delete</param>
        /// <returns>Status of the delete operation</returns>
        Task<ResponseDto<bool>> DeleteUserInformationAsync(int userInformationId, int userId);
        /// <summary>
        /// Gets configured DocumentTypes and Categories for the given portfolioCompanyId
        /// </summary>
        /// <param name="companyId"></param>
        /// <returns></returns>
        Task<CategoryAndDocumentTypeDto> GetCategoriesAndDocumentTypes(int companyId);
    }
}
