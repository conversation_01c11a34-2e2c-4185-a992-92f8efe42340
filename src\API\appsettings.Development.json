{"ConnectionStrings": {"DefaultDBConnection": "server=BAN-L42NWW93\\MARIELA;database=foliosure_test_june;MultipleActiveResultSets=True;Integrated Security=True;TrustServerCertificate=True"}, "TokenConfigurations": {"Audience": "ExempleAudience", "Issuer": "Exemple<PERSON><PERSON>uer", "Minutes": 90, "FinalExpiration": 30, "IdleSession": 30, "SecretKey": "124523621452125942"}, "AWS": {"SecretKey": "nprd/foliosure/pod/dev"}, "IdentityServerConfig": {"Audience": "beat-foliosure-pod-pec-localhost-client-id_services", "Issuer": "https://test.beatapps.net/identity/test/beat/sts", "jwk_uri": "https://test.beatapps.net/identity/test/beat/sts/.well-known/openid-configuration/jwks"}, "Environment": "DEV", "ClientCode": "dev", "Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Debug", "Microsoft.AspNetCore": "Debug", "System": "Debug"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "File", "Args": {"path": ".\\Logs\\PEC-FS-.log", "restrictedToMinimumLevel": "Verbose", "outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {ElasticApmTraceId} {ElasticApmTransactionId} {Message:lj} <s:{SourceContext}>{NewLine}{Exception}", "buffered": false, "fileSizeLimitBytes": 10000000, "shared": true, "rollingInterval": "Day", "rollOnFileSizeLimit": true}}]}, "ElasticApm": {"SecretToken": "", "ServerUrls": "https://tools.beatapps.net/APM", "ServiceName": "bds-beat-foliosure-dev-api"}, "ServiceConfiguration": {"AWSS3": {"BucketName": "beat-foliosure-vault", "KeyPrefix": "feature/"}}, "AllowedHosts": "", "AllowedOrigins": "https://*.dev.beatapps.net,https://*.beatfoliosure.com,http://localhost:4200,http://localhost,http://localhost:5001,https://*.test.beatapps.net,https://localhost:4200", "CspTrustedDomains": ["https://*.beatapps.net", "https://*.beatfoliosure.com", "http://localhost:4200", "http://localhost"], "CurrencyApiConfigurations": {"AccessKey": "8a8d566b8fadd3f475e995651d6f03c2", "StartDate": "2020-01-01", "EndDate": "2021-07-17", "FromCurrency": "USD,EUR,GBP,AUD,CAD,HKD,SGD,YEN,CHF,RMB,INR,NGN,EGP,KES", "ToCurrency": "USD,EUR,GBP,AUD,CAD,HKD,SGD,YEN,CHF,RMB,INR,NGN,EGP,KES", "BaseUrl": ""}, "JwtOption": {"TokenCancellationMode": true, "ExpiryTimeInMinute": 60}, "IsWorkflow": true, "IsPendoEnabled": false, "ServiceUrl": "https://localhost:5001/", "RevealBi": "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "envSettings": {"environment": "<PERSON>", "nprdOrPrd": "nprd", "enableSMTPSecretManager": false, "DEFAULT_REGION": "eu-west-1"}, "EmailConfiguration": {"Email": "<EMAIL>", "Password": "", "UserName": "", "Host": "email-smtp.eu-west-1.amazonaws.com", "Port": "587", "EnvirnmentEmailKey": "beatflow@notification", "EnableSSL": true, "UseDefaultCredentials": false}, "SessionIdleTime": {"IdleTimeout": 1140, "WarningTimeout": 60, "IntervalTime": 1200}, "ChromiumInstallPath": "E:/Foliosure/chrome-win/chrome.exe", "ChromiumDownloadPath": "E:/Foliosure/download", "ExcludeEndPointList": ["/api/portfolio-company/add", "/api/report/get/adhoc", "/services/api/report/get/adhoc", "/api/portfolio-company/commentary/save", "/services/api/portfolio-company/commentary/save", "/api/portfolio-company/commentary/save/custom", "/services/api/portfolio-company/commentary/save/custom", "/api/portfolio-company/commentary-section/get", "/services/api/portfolio-company/commentary-section/get", "/api/add/foot-note", "/services/add/foot-note", "/api/update/foot-note", "/services/update/foot-note", "/api/workflow/commentarydraft/save", "/services/api/workflow/commentarydraft/save", "/api/workflow/updateStatusCommentry", "/services/api/workflow/updateStatusCommentry", "/tools/renderhtml/", "/api/v1/InvestmentCompany/investment-company/commentries", "/api/v1/InvestmentCompany/footnotes/save", "/api/update-email-reminders"], "ExcludeUserList": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "AllowedFileExtensions": [".pdf", ".xls", ".xlsx"], "MaxFileSizeInMB": 20}