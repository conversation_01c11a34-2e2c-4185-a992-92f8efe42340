﻿using API.Contracts;
using API.Controllers.Workflow;
using API.Helpers;
using Contract.Deals;
using Contract.Groups;
using Contract.PortfolioCompany;
using Contract.Utility;
using DapperRepository;
using DataAccessLayer.UnitOfWork;
using Groups;
using Master;
using Microsoft.Extensions.Logging;
using PortfolioCompany;
using Report.Download;
using Utility.Services;

namespace Workflow.UnitTest.Controllers.Workflow
{
    public class AccessControllerFixtures
    {
        public Mock<IInjectedParameters> InjectedParameters;
        public Mock<IEncryption> Encryption;
        public Mock<IUnitOfWork> UnitOfWork;
        public Mock<ILogger> Logger;
        public Mock<IPortfolioCompanyService> _IPortfolioCompanyService;
        public readonly PortfolioCompanyService _PortfolioCompanyService;
        public Mock<IAccessService> iAccess;
        public readonly AccessService accessService;
        public Mock<IHelperService> helperService;
        public Mock<IDealService> dealService;
        public Mock<ILogger> logger;
        public Mock<ILogger<AccessController>> controllerLogger;
        public Mock<IGroupService> iGroupService;
        public GroupService groupService;
        public AccessController controller;
        public Mock<IGlobalConfigurations> GlobalConfigurations = new();
        public  Mock<IDapperGenericRepository> dapperGenericRepository;
        public  Mock<IInternalReportCommon> _common;
        public  Mock<ICommonResult> _commonResult;
        public Mock<IPageDetailsConfigurationService> MockPageDetailsConfigurationService = new();
        public AccessControllerFixtures()
        {
            InjectedParameters = new Mock<IInjectedParameters>();
            UnitOfWork = new Mock<IUnitOfWork>();
            helperService = new();
            logger = new();
            Encryption = new Mock<IEncryption>();
            Logger = new Mock<ILogger>();
            controllerLogger = new Mock<ILogger<AccessController>>();
            dapperGenericRepository = new Mock<IDapperGenericRepository>();
            _common = new Mock<IInternalReportCommon>();
            _commonResult = new Mock<ICommonResult>();
            dealService = new Mock<IDealService>();
            InjectedParameters.Setup(S => S.UnitOfWork).Returns(UnitOfWork.Object);
            InjectedParameters.Setup(S => S.Logger).Returns(Logger.Object);
            GlobalConfigurations.Setup(S => S.GetValueByKey("HashKey")).Returns("D,zMM3X45}&QE7FZ)9Z#Lkj");
            Encryption.Setup(S => S.Encrypt(It.IsAny<string>())).Returns("66F63D87E53A6F638CBDBE6B3B83AEA3");
            InjectedParameters.Setup(S => S.Encryption).Returns(new Encryption(GlobalConfigurations.Object));
            Mock<IDapperGenericRepository> dappermock = new Mock<IDapperGenericRepository>();
            _PortfolioCompanyService = new PortfolioCompanyService(dappermock.Object, dealService.Object);
            _PortfolioCompanyService.InjectedParameters = InjectedParameters.Object;
            iAccess = new Mock<IAccessService>();
            iGroupService = new Mock<IGroupService>();
            accessService = new AccessService(InjectedParameters.Object, dapperGenericRepository.Object, _common.Object);
            groupService = new GroupService(UnitOfWork.Object, Encryption.Object, dapperGenericRepository.Object);
            controller = new AccessController(helperService.Object, accessService, controllerLogger.Object, Encryption.Object, _commonResult.Object);
        }

    }
}
