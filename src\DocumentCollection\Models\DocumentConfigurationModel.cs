﻿namespace DocumentCollection.Models
{
    public class DocumentConfigurationModel
    {
        /// <summary>
        /// /// Gets or sets the list of company IDs for which the document configuration is applicable.
        /// </summary>
        public required List<int> Ids { get; set; }

        /// <summary>
        /// /// Gets or sets the list of folder frequency configurations.
        /// Each configuration specifies the frequency settings for a specific document type.
        /// </summary>
        public required List<FolderFrequencyConfigModel> Configuration { get; set; }

        public int FeatureId { get; set; }
    }
}