﻿using Contract.BulkUpload;
using Contract.Configuration;
using Contract.Investor;
using Contract.PortfolioCompany;
using Contract.Reports;
using Contract.Utility;
using DataAccessLayer.DBModel;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;

namespace Contract.Deals
{
    public interface IDealService : IFundHoldingServices
    {

        Task<int> SaveDeal(DealModel dealModel);

        Task<int> AddDealList(List<BulkUploadDealValidationDetails> dealModelList);

        Task<DealListModel> GetDeals(DealFilter filter);

        Task<DealListModel> GetDealsWithMinimalProperties(DealFilter filter);

        Task<DealListModel> GetDuplicateDeals(List<DealModel> checkDuplicateDealName);

        Task<DealModel> GetDealById(int dealId);
        Task<List<PageFieldValueModelTrackRecord>> GetDealTradingRecordDynamicColumns();
        Task<List<IDictionary<string, object>>> DealsTrackRecordDynamicData(PortfolioCompanyFundHoldingListModel fundHoldingListModel, PortfolioCompanyFundHoldingFilter fundHoldingFilter);
        Task<DataTable> GetFundHoldingDataTable(PortfolioCompanyFundHoldingListModel result, PortfolioCompanyFundHoldingFilterExt filter, string header);
        Task<DealListQueryModel> GetDealsListByQuery(DealFilter filter);
        Task<SelectionList> GetCommonDealTrackRecordLatestQuarter();
        Task<string> GetCommonDealTrackRecordQuarterYearCount(ReportQueryModel model);
        Task<List<DealTrackRecordModel>> GetAllFundsByInvestors();
        Task<List<FundInvestorsModel>> GetAllDeals();
        DataTable GetDealTransactionTable(DateTime toDate, DateTime fromDate, string quarter, int year);
        Task<List<DealsList>> GetRepositoryDeals();
        Task<List<SubFeatureAccessPermissionsModel>> GetDealPermissions(int userId, int dealId, int featureId);
        Task<List<Mapping_GroupFeature>> GetDealsPermissions(int userId, int featureId);
        Task<List<PageFieldValueModelTrackRecord>> GetDealHoldingDynamicRecord(int dealId);
    }
}