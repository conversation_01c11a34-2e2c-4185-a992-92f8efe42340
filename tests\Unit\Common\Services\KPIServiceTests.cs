using System;
using System.Collections.Generic;
using System.Composition;
using System.Linq;
using System.Threading.Tasks;
using Contract.KPI;
using Contract.PortfolioCompany;
using Contract.Sector;
using Contract.Utility;
using DataAccessLayer.DBModel;
using DataAccessLayer.Models;
using DataAccessLayer.UnitOfWork;
using Utility.Helpers;
using Moq;
using Xunit;
using Master;
using DapperRepository;
using System.Linq.Expressions;
using Dapper;
using System.Data;
using DapperRepository.Constants;
using PortfolioCompany;
using Notification;



namespace Master.UnitTests
{
    public class KpiServiceUnitTest
    {
        readonly KpiService KpiService;
        readonly Mock<IDapperGenericRepository> dapperGenericRepository = new();
        readonly Mock<IEncryption> _encryption = new();
        readonly Mock<IMemoryCacher> _memoryCacher = new();
        readonly Mock<IGlobalConfigurations> _globalConfig = new();
        public readonly Mock<IUnitOfWork> _UnitOfWork = new();
        public KpiServiceUnitTest()
        {
            KpiService = new KpiService(dapperGenericRepository.Object,_UnitOfWork.Object,_encryption.Object,_memoryCacher.Object,_globalConfig.Object);
        }

        [Fact]
        public void ShouldAbleToAddOrUpdateKPI()
        {
            var kpiModel = new KpiModel();
            var userId = 0;
            var actual = KpiService.AddOrUpdateKPI(kpiModel, userId);
            Assert.NotNull(actual);
        }

        [Fact]
        public void ShouldAbleToGetMappedOperationalKpis()
        {
            var res = new List<MappingPortfolioOperationalKpi>();
            var obj = new MappingPortfolioOperationalKpi() { KpiId = 1, DisplayOrder = 1, PortfolioCompanyId=1, IsDeleted=false };
            res.Add(obj);
            _UnitOfWork.Setup(s => s.MappingPortfolioOperationalKpi_OrderRepository.FindAllAsync(It.IsAny<Expression<Func<MappingPortfolioOperationalKpi, bool>>>())).Returns(Task.FromResult(res));
            var opKpi = new M_SectorwiseKPI()
            {
              SectorwiseOperationalKPIID = 1,
              IsDeleted = false,
            };
            var res1 = new List<M_SectorwiseKPI>();
            res1.Add(opKpi);
            _UnitOfWork.Setup(s => s.M_SectorwiseKPIRepository.FindAllAsync(It.IsAny<Expression<Func<M_SectorwiseKPI, bool>>>())).Returns(Task.FromResult(res1));
            var fundId = 0;
            var compIds = new List<int?>();
            compIds.Add(1);
            var kpiExcelDataModel = new KpiExcelDataModel()
            {
                CompIds = compIds
            };
            var actual = KpiService.GetMappedOperationalKpis(fundId, kpiExcelDataModel);
            Assert.NotNull(actual);
        }

        [Fact]
        public void ShouldAbleToErrorInGetMappedOperationalKpis()
        {
            var fundId = 0;
            var compIds = new List<int?>();
            var kpiExcelDataModel = new KpiExcelDataModel()
            {
                CompIds = compIds
            };
            var actual = KpiService.GetMappedOperationalKpis(fundId, kpiExcelDataModel);
            Assert.NotNull(actual);
        }

        [Fact]
        public void ShouldAbleToGetMappedOperationalKpisForMultipleCompanies()
        {
            var res = new List<MappingPortfolioOperationalKpi>();
            var obj = new MappingPortfolioOperationalKpi() { KpiId = 1, DisplayOrder = 1, PortfolioCompanyId = 1, IsDeleted = false };
            res.Add(obj);
            _UnitOfWork.Setup(s => s.MappingPortfolioOperationalKpi_OrderRepository.FindAllAsync(It.IsAny<Expression<Func<MappingPortfolioOperationalKpi, bool>>>())).Returns(Task.FromResult(res));
            var opKpi = new M_SectorwiseKPI()
            {
                SectorwiseOperationalKPIID = 1,
                IsDeleted = false,
            };
            var res1 = new List<M_SectorwiseKPI>();
            res1.Add(opKpi);
            _UnitOfWork.Setup(s => s.M_SectorwiseKPIRepository.FindAllAsync(It.IsAny<Expression<Func<M_SectorwiseKPI, bool>>>())).Returns(Task.FromResult(res1));
            var fundId = 0;
            var compIds = new List<int?>();
            compIds.Add(1);
            compIds.Add(2);
            var kpiExcelDataModel = new KpiExcelDataModel()
            {
                CompIds = compIds
            };
            var actual = KpiService.GetMappedOperationalKpis(fundId, kpiExcelDataModel);
            Assert.NotNull(actual);
        }

        [Fact]
        public void ShouldAbleToGetMappedInvestmentKpis()
        {
            var res = new List<MappingPortfolioInvestmentKpi>();
            var obj = new MappingPortfolioInvestmentKpi() { KpiId = 1, DisplayOrder = 1, PortfolioCompanyId = 1, IsDeleted = false };
            res.Add(obj);
            _UnitOfWork.Setup(s => s.PortfolioInvestmentKpiMappingRepository.FindAllAsync(It.IsAny<Expression<Func<MappingPortfolioInvestmentKpi, bool>>>())).Returns(Task.FromResult(res));
            var opKpi = new M_InvestmentKpi()
            {
                InvestmentKpiId = 1,
                IsDeleted = false,
            };
            var res1 = new List<M_InvestmentKpi>();
            res1.Add(opKpi);
            _UnitOfWork.Setup(s => s.M_InvestmentKPIRepository.FindAllAsync(It.IsAny<Expression<Func<M_InvestmentKpi, bool>>>())).Returns(Task.FromResult(res1));
            var fundId = 0;
            var compIds = new List<int?>();
            compIds.Add(1);
            var kpiExcelDataModel = new KpiExcelDataModel()
            {
                CompIds = compIds
            };
            var actual = KpiService.GetMappedInvestmentKpis(fundId, kpiExcelDataModel);
            Assert.NotNull(actual);
        }

        [Fact]
        public void ShouldAbleToGetMappedTradingRecordsKpis()
        {
            var res = new List<Mapping_Kpis>();
            var obj = new Mapping_Kpis() { KpiID = 1, DisplayOrder = 1, PortfolioCompanyID = 1, IsDeleted = false };
            res.Add(obj);
            _UnitOfWork.Setup(s => s.Mapping_KpisRepository.FindAllAsync(It.IsAny<Expression<Func<Mapping_Kpis, bool>>>())).Returns(Task.FromResult(res));
            var opKpi = new M_MasterKpis()
            {
                MasterKpiID = 1,
                IsDeleted = false,
            };
            var res1 = new List<M_MasterKpis>();
            res1.Add(opKpi);
            _UnitOfWork.Setup(s => s.M_MasterKpisRepository.FindAllAsync(It.IsAny<Expression<Func<M_MasterKpis, bool>>>())).Returns(Task.FromResult(res1));
            var fundId = 1;
            var actual = KpiService.GetMappedTradingRecords(fundId);
            Assert.NotNull(actual);
        }

        [Fact]
        public void ShouldAbleToGetMappedTradingRecords()
        {
            var fundId = 0;
            var actual = KpiService.GetMappedTradingRecords(fundId);
            Assert.NotNull(actual);
        }

        [Fact]
        public async Task ShouldCreateKPI()
        {
            // Arrange
            int userId = 2;
            DuplicateKpiModel duplicateKPI = new DuplicateKpiModel
            {
                KPIType = "Some KPI Type",
                Id = 123,
                UserId = 456
            };

            DynamicParameters parameters = new DynamicParameters();
            parameters.Add("@KpiType", duplicateKPI.KPIType, DbType.String);
            parameters.Add("@KpiId", duplicateKPI.Id, DbType.Int32);
            parameters.Add("@UserId", duplicateKPI.UserId, DbType.Int32);
            parameters.Add("@Id", DbType.Int32, direction: ParameterDirection.Output);

            dapperGenericRepository.Setup(x => x.QueryExecuteSpAsync<int>(SqlConstants.QueryBySPCreateDuplicateKPI, parameters))
            .Returns(Task.FromResult(userId));
            
            // Act
            var result = await KpiService.CreateKPI(duplicateKPI);

            // Assert
            Assert.Equal(parameters.Get<int>("@Id"), result);
            
        }

        [Fact]
        public void ShouldReturnCompanyKpiList()
        {
            // Arrange
            var methodologies = new List<M_Methodology>
            {
                new M_Methodology { MethodologyID = 1, MethodologyName = "Methodology 1", IsDeleted = false },
                new M_Methodology { MethodologyID = 2, MethodologyName = "Methodology 2", IsDeleted = false }
            };

            var companyKpiList = new List<M_CompanyKpi>
            {
                new M_CompanyKpi
                {
                    CompanywiseKPIID = 1,
                    Kpi = "KPI 1",
                    KpiInfo = "KPI Info 1",
                    IsHeader = false,
                    IsBoldKPI = true,
                    Formula = "Formula 1",
                    MethodologyId = 1,
                    EncryptedCompanyKpiId = "Encrypted ID 1",
                    Description = "Description 1"
                },
                new M_CompanyKpi
                {
                    CompanywiseKPIID = 2,
                    Kpi = "KPI 2",
                    KpiInfo = "KPI Info 2",
                    IsHeader = true,
                    IsBoldKPI = false,
                    Formula = "Formula 2",
                    MethodologyId = 2,
                    EncryptedCompanyKpiId = "Encrypted ID 2",
                    Description = "Description 2"
                }
            };

            _UnitOfWork.Setup(uow => uow.M_MethodologyRepository.GetQueryable()).Returns(methodologies.AsQueryable());
            _UnitOfWork.Setup(uow => uow.M_CompanyKPIRepository.GetQueryable()).Returns(companyKpiList.AsQueryable());

            // Act
            var result = KpiService.GetCompanyKpi();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);

            var firstKpi = result[0];
            Assert.Equal(1, firstKpi.CompanywiseKPIID);
            Assert.Equal("KPI 1", firstKpi.KPI);
            Assert.Equal("KPI Info 1", firstKpi.KpiInfo);          
            Assert.False(firstKpi.IsHeader);
            Assert.True(firstKpi.IsBoldKPI);
            Assert.Equal("Formula 1", firstKpi.Formula);
            Assert.Equal(1, firstKpi.MethodologyID);
            Assert.Equal("Encrypted ID 1", firstKpi.EncryptedCompanywiseKPIID);
            Assert.Equal("Description 1", firstKpi.Description);
            Assert.Equal("Methodology 1", firstKpi.MethodologyName);

            var secondKpi = result[1];
            Assert.Equal(2, secondKpi.CompanywiseKPIID);
            Assert.Equal("KPI 2", secondKpi.KPI);
            Assert.Equal("KPI Info 2", secondKpi.KpiInfo);
            Assert.True(secondKpi.IsHeader);
            Assert.False(secondKpi.IsBoldKPI);
            Assert.Equal("Formula 2", secondKpi.Formula);
            Assert.Equal(2, secondKpi.MethodologyID);
            Assert.Equal("Encrypted ID 2", secondKpi.EncryptedCompanywiseKPIID);
            Assert.Equal("Description 2", secondKpi.Description);
            Assert.Equal("Methodology 2", secondKpi.MethodologyName);
        }

        [Fact]
        public void GetOperationalKpi_ReturnsListOfSectorwiseKpiDetails()
        {
            // Arrange
            var methodologies = new List<M_Methodology>
            {
                new M_Methodology { MethodologyID = 1, MethodologyName = "Methodology 1" },
                new M_Methodology { MethodologyID = 2, MethodologyName = "Methodology 2" }
            };
            var sectorwiseKpis = new List<M_SectorwiseKPI>
            {
                new M_SectorwiseKPI
                {
                    SectorwiseOperationalKPIID = 1,
                    Kpi = "KPI 1",
                    KpiInfo = "KPI Info 1",
                    IsHeader = false,
                    IsBoldKPI = true,
                    MethodologyId = 1,
                    Formula = "Formula 1",
                    EncryptedSectorwiseKPIID = "Encrypted ID 1",
                    Description = "Description 1"
                },
                new M_SectorwiseKPI
                {
                    SectorwiseOperationalKPIID = 2,
                    Kpi = "KPI 2",
                    KpiInfo = "KPI Info 2",
                    IsHeader = true,
                    IsBoldKPI = false,
                    MethodologyId = 2,
                    Formula = "Formula 2",
                    EncryptedSectorwiseKPIID = "Encrypted ID 2",
                    Description = "Description 2"
                }
            };

            _UnitOfWork.Setup(x => x.M_MethodologyRepository.GetQueryable()).Returns(methodologies.AsQueryable());
            _UnitOfWork.Setup(x => x.M_SectorwiseKPIRepository.GetQueryable()).Returns(sectorwiseKpis.AsQueryable());

            // Act
            var result = KpiService.GetOperationalKpi();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);

            var firstKpi = result[0];
            Assert.Equal(1, firstKpi.SectorwiseKPIID);
            Assert.Equal("KPI 1", firstKpi.KPI);
            Assert.Equal("KPI Info 1", firstKpi.KpiInfo);           
            Assert.False(firstKpi.IsHeader);
            Assert.True(firstKpi.IsBoldKPI);
            Assert.Equal(1, firstKpi.MethodologyId);
            Assert.Equal("Formula 1", firstKpi.Formula);
            Assert.Equal("Encrypted ID 1", firstKpi.EncryptedSectorwiseKPIID);
            Assert.Equal("Description 1", firstKpi.Description);
            Assert.Equal("Methodology 1", firstKpi.MethodologyName);

            var secondKpi = result[1];
            Assert.Equal(2, secondKpi.SectorwiseKPIID);
            Assert.Equal("KPI 2", secondKpi.KPI);
            Assert.Equal("KPI Info 2", secondKpi.KpiInfo);            
            Assert.True(secondKpi.IsHeader);
            Assert.False(secondKpi.IsBoldKPI);
            Assert.Equal(2, secondKpi.MethodologyId);
            Assert.Equal("Formula 2", secondKpi.Formula);
            Assert.Equal("Encrypted ID 2", secondKpi.EncryptedSectorwiseKPIID);
            Assert.Equal("Description 2", secondKpi.Description);
            Assert.Equal("Methodology 2", secondKpi.MethodologyName);
        }

        [Fact]
        public void ShouldReturnInvestmentKPIList()
        {
            // Arrange
            int portfolioCompanyId = 1;

            var investmentKPIValue = new List<M_InvestmentKpi>
            {
                new M_InvestmentKpi { InvestmentKpiId = 1, Kpi = "KPI 1" },
                new M_InvestmentKpi { InvestmentKpiId = 2, Kpi = "KPI 2" },
                new M_InvestmentKpi { InvestmentKpiId = 3, Kpi = "KPI 3" }
            };

            var mappingPortfolioCompanyKPIValue = new List<MappingPortfolioInvestmentKpi>
            {
                new MappingPortfolioInvestmentKpi { KpiId = 1, PortfolioCompanyId = 1, DisplayOrder = 1, ParentKPIID = null },
                new MappingPortfolioInvestmentKpi { KpiId = 2, PortfolioCompanyId = 1, DisplayOrder = 2, ParentKPIID = null },
                new MappingPortfolioInvestmentKpi { KpiId = 3, PortfolioCompanyId = 1, DisplayOrder = 3, ParentKPIID = null }
            };

            _UnitOfWork.Setup(uow => uow.M_InvestmentKPIRepository.GetMany(It.IsAny<Func<M_InvestmentKpi, bool>>()))
                .Returns(investmentKPIValue);

            _UnitOfWork.Setup(uow => uow.PortfolioInvestmentKpiMappingRepository.GetManyQueryable(It.IsAny<Func<MappingPortfolioInvestmentKpi, bool>>()))
                .Returns(mappingPortfolioCompanyKPIValue.AsQueryable());

            // Act
            var result = KpiService.GetInvestmentKPIList(portfolioCompanyId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(3, result.Count);

            Assert.Equal("KPI 1", result[0].LineItem);
            Assert.Equal(1, result[0].Id);
            Assert.Equal(1, result[0].DisplayOrder);
            Assert.True(result[0].IsHeader);

            Assert.Equal("KPI 2", result[1].LineItem);
            Assert.Equal(2, result[1].Id);
            Assert.Equal(2, result[1].DisplayOrder);
            Assert.True(result[1].IsHeader);

            Assert.Equal("KPI 3", result[2].LineItem);
            Assert.Equal(3, result[2].Id);
            Assert.Equal(3, result[2].DisplayOrder);
            Assert.True(result[2].IsHeader);
        }

        [Fact]
        public async Task GetMasterKPIList_WithValidInputs_ReturnsKpiTemplateList()
        {
            // Arrange
            int portfolioCompanyId = 1;
            string moduleName = "ModuleName";
            int moduleId = 1;

            var kpiModule = new M_KpiModules { ModuleID = moduleId, Name = moduleName, IsDeleted = false };
            _UnitOfWork.Setup(uow => uow.M_KpiModulesRepository.GetFirstOrDefault(It.IsAny<Func<M_KpiModules, bool>>()))
                .Returns(kpiModule);

            var mappedKPIList = new List<Mapping_Kpis> { new Mapping_Kpis { KpiID = 1, DisplayOrder = 1, ParentKPIID = null } };
            dapperGenericRepository.Setup(repo => repo.Query<Mapping_Kpis>(SqlConstants.QueryByMappingKPIs, It.IsAny<object>()))
                .ReturnsAsync(mappedKPIList);

            var masterKPIValue = new List<M_MasterKpis> { new M_MasterKpis { MasterKpiID = 1, KPI = "KPI" } };
            dapperGenericRepository.Setup(repo => repo.Query<M_MasterKpis>(SqlConstants.QueryByMasterKpis, It.IsAny<object>()))
                .ReturnsAsync(masterKPIValue);

            // Act
            var result = await KpiService.GetMasterKPIList(portfolioCompanyId, moduleName);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(1, result.Count);
            Assert.Equal("KPI", result[0].LineItem);
            Assert.Equal(1, result[0].Id);
            Assert.Equal(1, result[0].DisplayOrder);
            Assert.True(result[0].IsHeader);
        }

        [Fact]
        public void ShouldReturnOperationalKPIList()
        {
            // Arrange
            int portfolioCompanyId = 1;

            var operationalKPIValue = new List<M_SectorwiseKPI>
                {
                    new M_SectorwiseKPI
                    {
                        SectorwiseOperationalKPIID = 1,
                        Kpi = "KPI 1"
                    },
                    new M_SectorwiseKPI
                    {
                        SectorwiseOperationalKPIID = 2,
                        Kpi = "KPI 2"
                    }
                };

            var operationalKpiMapping = new List<MappingPortfolioOperationalKpi>
                    {
                        new MappingPortfolioOperationalKpi
                        {
                            KpiId = 1,
                            DisplayOrder = 1,
                            ParentKPIID = null
                        },
                        new MappingPortfolioOperationalKpi
                        {
                            KpiId = 2,
                            DisplayOrder = 2,
                            ParentKPIID = null
                        }
                    };

            _UnitOfWork.Setup(x => x.M_SectorwiseKPIRepository.GetMany(It.IsAny<Func<M_SectorwiseKPI, bool>>()))
            .Returns(operationalKPIValue.AsQueryable());

            _UnitOfWork.Setup(x => x.MappingPortfolioOperationalKpi_OrderRepository.GetManyQueryable(It.IsAny<Func<MappingPortfolioOperationalKpi, bool>>()))
            .Returns(operationalKpiMapping.AsQueryable());

            // Act
            var result = KpiService.GetOperationalKPIList(portfolioCompanyId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            Assert.Equal("KPI 1", result[0].LineItem);
            Assert.Equal(1, result[0].Id);
            Assert.Equal(1, result[0].DisplayOrder);
            Assert.True(result[0].IsHeader);
            Assert.Equal("KPI 2", result[1].LineItem);
            Assert.Equal(2, result[1].Id);
            Assert.Equal(2, result[1].DisplayOrder);
            Assert.True(result[1].IsHeader);
        }

        [Fact]
        public void ShouldReturnCompanyKPIList()
        {
            // Arrange
            int portfolioCompanyId = 1;

            var investmentKPIValue = new List<M_CompanyKpi>
                     {
                        new M_CompanyKpi
                        {
                            CompanywiseKPIID = 1,
                            Kpi = "KPI 1"
                        },
                        new M_CompanyKpi
                        {
                            CompanywiseKPIID = 2,
                            Kpi = "KPI 2"
                        }
                    };

            var mappingPortfolioCompanyKPIValue = new List<MappingPortfolioCompanyKpi>
             {
                    new MappingPortfolioCompanyKpi
                    {
                        KpiId = 1,
                        DisplayOrder = 1,
                        ParentKPIID = null
                    },
                    new MappingPortfolioCompanyKpi
                    {
                        KpiId = 2,
                        DisplayOrder = 2,
                        ParentKPIID = null
                    }
             };

            _UnitOfWork.Setup(x => x.M_CompanyKPIRepository.GetMany(It.IsAny<Func<M_CompanyKpi, bool>>()))
            .Returns(investmentKPIValue.AsQueryable());

            _UnitOfWork.Setup(x => x.PortfolioCompanyKpiMappingRepository.GetManyQueryable(It.IsAny<Func<MappingPortfolioCompanyKpi, bool>>())).Returns(mappingPortfolioCompanyKPIValue.AsQueryable());
                        
            // Act
            var result = KpiService.GetCompanyKPIList(portfolioCompanyId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            Assert.Equal("KPI 1", result[0].LineItem);
            Assert.Equal(1, result[0].Id);
            Assert.Equal(1, result[0].DisplayOrder);
            Assert.True(result[0].IsHeader);
            Assert.Equal("KPI 2", result[1].LineItem);
            Assert.Equal(2, result[1].Id);
            Assert.Equal(2, result[1].DisplayOrder);
            Assert.True(result[1].IsHeader);
        }

        [Fact]
        public void GetFinancialKPIList_WithFilter_ReturnsFilteredKPIList()
        {
            // Arrange
            var filter = new FinancialKpiFilter
            {
                PaginationFilter = new PaginationFilter
                {
                    GlobalFilter = "filter",
                    First = 0,
                    Rows = 10,
                    MultiSortMeta = null,
                    FilterWithoutPaging = false,
                    SortField = null,
                    SortOrder = 0
                }
            };

            var financialKPIs = new List<MFinancialKpi>
            {
                new MFinancialKpi
                {
                    FinancialKPIId = 1,
                    KPI = "KPI 1",
                    Description = "Description 1"
                },
                new MFinancialKpi
                {
                    FinancialKPIId = 2,
                    KPI = "KPI 2",
                    Description = "Description 2"
                }
            };

            _UnitOfWork.Setup(uow => uow.M_FinancialKPIRepository.GetManyQueryable(It.IsAny<Func<MFinancialKpi, bool>>()))
                .Returns(financialKPIs.AsQueryable());

            // Act
            var result = KpiService.GetFinancialKPIList(filter);

            // Assert
            Assert.NotNull(result);     
        }

        [Fact]
        public void GetFinancialKPIList_ShouldReturnFinancialKPIList()
        {
            // Arrange
            int portfolioCompanyId = 1;

            var profitAndLossLineItems = new List<M_ProfitAndLoss_LineItems>
            {
                new M_ProfitAndLoss_LineItems { ProfitAndLossLineItemID = 1, ProfitAndLossLineItem = "Revenue" },
                new M_ProfitAndLoss_LineItems { ProfitAndLossLineItemID = 2, ProfitAndLossLineItem = "Expenses" }
            };

            var mappingProfitAndLoss = new List<Mapping_CompanyProfitAndLossLineItems>
            {
                new Mapping_CompanyProfitAndLossLineItems { ProfitAndLossLineItemID = 1, DisplayOrder = 1 },
                new Mapping_CompanyProfitAndLossLineItems { ProfitAndLossLineItemID = 2, DisplayOrder = 2 }
            };

            var balanceSheetLineItems = new List<M_BalanceSheet_LineItems>
            {
                new M_BalanceSheet_LineItems { BalanceSheetLineItemID = 1, BalanceSheetLineItem = "Assets" },
                new M_BalanceSheet_LineItems { BalanceSheetLineItemID = 2, BalanceSheetLineItem = "Liabilities" }
            };

            var mappingBalanceSheet = new List<Mapping_CompanyBalanceSheetLineItems>
            {
                new Mapping_CompanyBalanceSheetLineItems { BalanceSheetLineItemID = 1, DisplayOrder = 1 },
                new Mapping_CompanyBalanceSheetLineItems { BalanceSheetLineItemID = 2, DisplayOrder = 2 }
            };

            var cashFlowLineItems = new List<M_CashFlow_LineItems>
            {
                new M_CashFlow_LineItems { CashFlowLineItemID = 1, CashFlowLineItem = "Operating Activities" },
                new M_CashFlow_LineItems { CashFlowLineItemID = 2, CashFlowLineItem = "Investing Activities" }
            };

            var mappingCashFlow = new List<Mapping_CompanyCashFlowLineItems>
            {
                new Mapping_CompanyCashFlowLineItems { CashFlowLineItemID = 1, DisplayOrder = 1 },
                new Mapping_CompanyCashFlowLineItems { CashFlowLineItemID = 2, DisplayOrder = 2 }
            };

            _UnitOfWork.Setup(uow => uow.M_ProfitAndLoss_LineItemsRepository.GetManyQueryable(It.IsAny<Func<M_ProfitAndLoss_LineItems, bool>>()))
                .Returns(profitAndLossLineItems.AsQueryable());

            _UnitOfWork.Setup(uow => uow.Mapping_CompanyProfitAndLossLineItemsRepository.GetManyQueryable(It.IsAny<Func<Mapping_CompanyProfitAndLossLineItems, bool>>()))
                .Returns(mappingProfitAndLoss.AsQueryable());

            _UnitOfWork.Setup(uow => uow.M_BalanceSheet_LineItemsRepository.GetManyQueryable(It.IsAny<Func<M_BalanceSheet_LineItems, bool>>()))
                .Returns(balanceSheetLineItems.AsQueryable());

            _UnitOfWork.Setup(uow => uow.Mapping_CompanyBalanceSheetLineItemsRepository.GetManyQueryable(It.IsAny<Func<Mapping_CompanyBalanceSheetLineItems, bool>>()))
                .Returns(mappingBalanceSheet.AsQueryable());

            _UnitOfWork.Setup(uow => uow.M_CashFlow_LineItemsRepository.GetManyQueryable(It.IsAny<Func<M_CashFlow_LineItems, bool>>()))
                .Returns(cashFlowLineItems.AsQueryable());

            _UnitOfWork.Setup(uow => uow.Mapping_CompanyCashFlowLineItemsRepository.GetManyQueryable(It.IsAny<Func<Mapping_CompanyCashFlowLineItems, bool>>()))
                .Returns(mappingCashFlow.AsQueryable());

            // Act
            var result = KpiService.GetFinancialKPIList(portfolioCompanyId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(3, result.Count);

            Assert.True(result.ContainsKey("Profit & Loss"));
            Assert.Equal(2, result["Profit & Loss"].Count);

            Assert.True(result.ContainsKey("Balance Sheet"));
            Assert.Equal(2, result["Balance Sheet"].Count);

            Assert.True(result.ContainsKey("CashFlow"));
            Assert.Equal(2, result["CashFlow"].Count);
        }


        [Fact]
        public async Task ShouldReturnUnMappedKpi()
        {
            // Arrange
            int portfolioCompanyId = 129;
            string type = "someType";

            List<KpiMappingModel> expected = new List<KpiMappingModel>()
                {
                    new KpiMappingModel
                    {
                        Id = 1,
                        Formula="sdsfsf",
                        KpiInfo="sfedfd",
                        Name="test"
                    },
                    new KpiMappingModel
                    {
                        Id =2,
                        Formula="sdsfsf",
                        KpiInfo="sfedfd",
                        Name="test"
                    }
                };

            dapperGenericRepository.Setup(S => S.Query<KpiMappingModel>(SqlConstants.QueryByGetMasterKpiNotMappedList, It.IsAny<object>())).ReturnsAsync(expected);

            // Act
            List<KpiMappingModel> actual = await KpiService.GetUnMappedKpi(portfolioCompanyId, type,0);

            // Assert
            Assert.Equal(expected, actual);
        }

        [Fact]
        public async Task UpdateKPIMapping_ShouldCallUpdateOperationalKPIMapping_WhenTypeIsOperationalKPI()
        {
            // Arrange
            int portfolioCompanyId = 1;
            string type = "Operational KPI";
            List<KpiMappingModel> model = new List<KpiMappingModel>();
            int userId = 1;
            int moduleId = 1;

          // Act
            var result = await KpiService.UpdateKPIMapping(portfolioCompanyId, type, model, userId, moduleId);

            // Assert
          
            Assert.True(result);
        }

        [Fact]
        public async Task UpdateKPIMapping_ShouldCallUpdateCompanyKPI_WhenTypeIsCompanyKPI()
        {
            // Arrange
            int portfolioCompanyId = 1;
            string type = "Company KPI";
            List<KpiMappingModel> model = new List<KpiMappingModel>();
            int userId = 1;
            int moduleId = 1;

           
            // Act
            var result = await KpiService.UpdateKPIMapping(portfolioCompanyId, type, model, userId, moduleId);

            // Assert
        
            Assert.True(result);
        }

        [Fact]
        public async Task UpdateKPIMapping_ShouldCallUpdateMasterKPI_WhenTypeIsNotRecognized()
        {
            // Arrange
            int portfolioCompanyId = 1;
            string type = "Unknown KPI";
            List<KpiMappingModel> model = new List<KpiMappingModel>();
            int userId = 1;
            int moduleId = 1;
                     
            // Act
            var result = await KpiService.UpdateKPIMapping(portfolioCompanyId, type, model, userId, moduleId);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task GetKpisByType_ReturnsKpiList()
        {
            // Arrange
            string type = "someType";
            int companyId = 1;
            List<KpiFormulaModel> expectedKpis = new List<KpiFormulaModel>
            {
                new KpiFormulaModel { KpiId=1,KPIInfo = "test", Formula = "KPI 1" },
                new KpiFormulaModel { KpiId = 2,KPIInfo = "test", Formula = "KPI 1"}
            };

            dapperGenericRepository.Setup(S => S.Query<KpiFormulaModel>(SqlConstants.QueryByKpisByType, It.IsAny<object>())).ReturnsAsync(expectedKpis);

            // Act
            var result = await KpiService.GetKpisByType(type, companyId,0);

            // Assert
            Assert.Equal(expectedKpis, result);
        }

        [Fact]
        public async Task UpdateFormulaByKPIId_ShouldReturnUpdatedRowCount()
        {
            // Arrange
            var kpiFormula = new KpiFormula
            {
                KpiType = "Type",
                KpiId = 1,
                Formula = "Formula",
                FormulaKpiId = "2",
                MappingId = 3,
                ModifiedBy = 2
            };

            var expectedRowCount = 1;
            dapperGenericRepository.Setup(x => x.QueryExecuteAsync<int>(SqlConstants.QueryByUpdateFormulaById, It.IsAny<object>()))
                .ReturnsAsync(expectedRowCount);

            // Act
            var actualRowCount = await KpiService.UpdateFormulaByKPIId(kpiFormula);

            // Assert
            Assert.Equal(expectedRowCount, actualRowCount);
        }

        [Fact]
        public async Task CopyKPIToCompanies_ReturnsInt()
        {
            // Arrange
            var copyToKPIQueryModel = new CopyToKpiQueryModel
            {
                KpiType = "SomeKpiType",
                CompanyId = 1,
                UserId = 1,
           
            };
           
            // Act
            var result = await KpiService.CopyKPIToCompanies(copyToKPIQueryModel);

            // Assert
            Assert.IsType<int>(result);
        }

        [Fact]
        public void ShouldReturnValidKpiFormula()
        {
            // Arrange
            var formula = new KpiFormula
            {
                Formula = "A + B * C"
            };

            // Act
            var result = KpiService.GetKpiFormula(formula);

            // Assert
            Assert.NotNull(result);
        }
        [Fact]
        public async Task GetAllMappedKPIs_ReturnsMappedKPIsList()
        {
            // Arrange
            var companyIds = "1,2";
            var mappedKPIs = new List<AllKpiMappingModel>
            {
                new AllKpiMappingModel { PortfolioCompanyId = 1, KpiId = 10, KpiName = "KPI 1" },
                new AllKpiMappingModel { PortfolioCompanyId = 2, KpiId = 20, KpiName = "KPI 2" }
            };
            dapperGenericRepository.Setup(x => x.Query<AllKpiMappingModel>(SqlConstants.ProcGetAllMappedKpi, It.IsAny<object>())).ReturnsAsync(mappedKPIs);

            // Mock the repository or method as needed if GetAllMappedKPIs uses any dependencies
            // Here, we assume GetAllMappedKPIs is implemented and returns a list
            // If it uses _UnitOfWork or dapperGenericRepository, set up mocks accordingly

            // Act
            var result = await KpiService.GetAllMappedKPIs(companyIds);

            // Assert
            Assert.NotNull(result);
            // The actual implementation may return an empty list or a list based on mocks
            // Here, we just check that the result is a list (not null)
        }

        [Fact]
        public async Task GetAllMappedKPIs_WithEmptyCompanyIds_ReturnsEmptyList()
        {
            // Arrange
            var companyIds = string.Empty;

            // Act
            var result = await KpiService.GetAllMappedKPIs(companyIds);

            // Assert
            Assert.Empty(result);
        }

        [Fact]
        public async Task GetAllMappedKPIs_WithNullCompanyIds_ReturnsEmptyList()
        {
            // Arrange
            string companyIds = null;

            // Act
            var result = await KpiService.GetAllMappedKPIs(companyIds);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }
    }


}
