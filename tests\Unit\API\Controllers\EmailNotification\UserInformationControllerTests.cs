using Xunit;
using Moq;
using Microsoft.Extensions.Logging;
using EmailConfiguration.Interfaces;
using API.Controllers.EmailNotification;
using EmailConfiguration.DTOs;
using Microsoft.AspNetCore.Mvc;
using API.Helpers;
using System.Threading.Tasks;
using System.Security.Claims;

namespace API.Tests.Controllers.EmailNotification
{
    public class UserInformationControllerTests
    {
        private readonly Mock<IUserInformationService> _userInfoServiceMock;
        private readonly Mock<ILogger<UserInformationController>> _loggerMock;
        private readonly Mock<IHelperService> _helperServiceMock;
        private readonly UserInformationController _controller;

        public UserInformationControllerTests()
        {
            _userInfoServiceMock = new Mock<IUserInformationService>();
            _loggerMock = new Mock<ILogger<UserInformationController>>();
            _helperServiceMock = new Mock<IHelperService>();
            _controller = new UserInformationController(_userInfoServiceMock.Object, _loggerMock.Object, _helperServiceMock.Object);
        }

        [Fact]
        public async Task DeleteUserInformation_ReturnsOk_WhenDeleteSuccessful()
        {
            // Arrange
            int userInformationId = 1;
            int userId = 123;
            _helperServiceMock.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _userInfoServiceMock.Setup(s => s.DeleteUserInformationAsync(userInformationId, userId))
                .ReturnsAsync(new ResponseDto<bool> { IsSuccess = true, Message = "Deleted", Data = true });

            // Act
            var result = await _controller.DeleteUserInformation(userInformationId);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.True(((dynamic)okResult.Value).Success);
        }

        [Fact]
        public async Task DeleteUserInformation_ReturnsNotFound_WhenUserNotFound()
        {
            // Arrange
            int userInformationId = 2;
            int userId = 123;
            _helperServiceMock.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _userInfoServiceMock.Setup(s => s.DeleteUserInformationAsync(userInformationId, userId))
                .ReturnsAsync(new ResponseDto<bool> { IsSuccess = false, Message = "Not found", Data = false });

            // Act
            var result = await _controller.DeleteUserInformation(userInformationId);

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result);
            Assert.False(((dynamic)notFoundResult.Value).Success);
        }

        [Fact]
        public async Task DeleteUserInformation_ReturnsBadRequest_WhenModelStateInvalid()
        {
            // Arrange
            _controller.ModelState.AddModelError("key", "error");

            // Act
            var result = await _controller.DeleteUserInformation(1);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);
        }
    }
}
