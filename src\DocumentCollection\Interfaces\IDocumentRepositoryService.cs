﻿using DocumentCollection.DTOs;
using DocumentCollection.Models;

namespace DocumentCollection.Interfaces
{
    public interface IDocumentRepositoryService
    {
        /// <summary>
        /// Gets the document repository data for a specific company.
        /// </summary>
        /// <param name="companyid"></param>
        /// <param name="featureId"></param>
        /// <returns></returns>
        Task<ResponseDto<List<RepositoryTreeModel>>> GetRepositoryData(int companyid, int featureId);

         /// <summary>
        /// upload files for a specific company to the specifc folder path.
        /// </summary>
        /// <param name="companyid"></param>
        /// <returns></returns>
        Task<ResponseDto<string>> UploadDocuments(int companyId, DocumentUploadRequestDto uploadRequest);

        /// <summary>
        /// Gets documents available in the specifc folder path
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="folderPath"></param>
        /// <param name="featureId"></param>
        /// <returns></returns>
        Task<ResponseDto<List<DocumentsInfoDto>>> GetDocuments(int companyId, string folderPath, int featureId);
        string? ValidateUploadedFiles(DocumentUploadRequestDto uploadRequest);
        Task<ResponseDto<string>> DeleteDocuments(int companyId, DocumentDeleteRequestDto deletRequest);
        Task<DownloadDocumentResult> DownloadDocument(int companyId, string documentId, string folderPath);        
    }
}
