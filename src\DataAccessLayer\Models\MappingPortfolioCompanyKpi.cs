﻿
using DataAccessLayer.DBModel;
namespace DataAccessLayer.Models
{
    public partial class MappingPortfolioCompanyKpi : BaseModel
    {
        public int MappingPortfolioCompanyKpiId { get; set; }
        public int KpiTypeId { get; set; }
        public int PortfolioCompanyId { get; set; }
        public int KpiId { get; set; }
        public int? ParentKPIID { get; set; }
        public int? DisplayOrder { get; set; }
        public string Description { get; set; }
        public bool IsDeleted { get; set; }
        public string EncryptedMappingKpiId { get; set; }
        public string Formula { get; set; }
        public string FormulaKPIId { get; set; }
        public bool IsExtraction { get; set; }
        public string Synonym { get; set; }
        public string Definition { get; set; }
    }
}
