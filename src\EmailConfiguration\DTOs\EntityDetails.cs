namespace EmailConfiguration.DTOs
{
    /// <summary>
    /// DTO for entity details with ID and name
    /// </summary>
    public class EntityDetails
    {
        /// <summary>
        /// Unique identifier of the entity
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Display name of the entity
        /// </summary>
        public string Name { get; set; }
    }
}
