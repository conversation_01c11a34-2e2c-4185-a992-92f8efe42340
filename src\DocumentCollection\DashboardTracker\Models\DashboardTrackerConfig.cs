using System;

namespace DocumentCollection.DashboardTracker.Models
{
    public class DashboardTrackerConfig
    {
        public int ID { get; set; }
        public int FieldType { get; set; }
        public int DataType { get; set; }
        public string Name { get; set; }
        public int? FrequencyType { get; set; }
        public string StartPeriod { get; set; }
        public string EndPeriod { get; set; }
        public int? MapTo { get; set; }
        public bool IsActive { get; set; }
        public bool IsDeleted { get; set; }
        public DateTime CreatedOn { get; set; }
        public int CreatedBy { get; set; }
        public DateTime? ModifiedOn { get; set; }
        public int? ModifiedBy { get; set; }
    }
}
