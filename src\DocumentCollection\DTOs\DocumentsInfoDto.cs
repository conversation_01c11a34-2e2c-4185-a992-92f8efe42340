﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DocumentCollection.DTOs
{
    public class DocumentsInfoDto
    {
        public string DocumentName { get; set; } = string.Empty;
        public string DocumentType { get; set; } = string.Empty;
        public string DocumentSize { get; set; } = string.Empty;
        public string DocumentS3Path { get; set; } = string.Empty;
        public string DocumentUploadType {  get; set; } = string.Empty;
        public string DocumentId { get; set; } = string.Empty;
        public DateTime DocumentCreatedDate { get; set; } = DateTime.Now;
    }
}
