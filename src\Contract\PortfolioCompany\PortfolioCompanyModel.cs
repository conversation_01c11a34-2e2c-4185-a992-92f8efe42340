﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Contract.Configuration;
using Contract.Currency;
using Contract.Deals;
using Contract.Filters;
using Contract.Funds;
using Contract.MasterMapping;
using Contract.Sector;
using Contract.StockExchange;
using DataAccessLayer.DBModel;

namespace Contract.PortfolioCompany
{
    public class PortfolioCompanyModel : BaseModel
    {
        public PortfolioCompanyModel()
        {
            PortfolioCompanyProfitabilityList = new List<PortfolioCompanyProfitabilityModel>();
        }
        public int PortfolioCompanyID { get; set; }

        [MaxLength(500)]
        public string CompanyName { get; set; }

        [MaxLength(500)]
        public string CompanyLegalName { get; set; }

        [MaxLength(500)]
        public string Website { get; set; }
        public int? YearFounded { get; set; }

        public string BusinessDescription { get; set; }

        public string Status { get; set; }

        public int? SectorID { get; set; }
        public int ReportingCurrencyID { get; set; }
        public int? SubSectorID { get; set; }

        public int? FinancialStatusID { get; set; }
        public int? OwnershipStatusID { get; set; }
        public DateTime? LastUpdatedOn { get; set; }
        public int? InvestmentStageID { get; set; }

        [MaxLength(1000)]
        public string CurrentActiveInvestors { get; set; }

        [MaxLength(50)]
        public string TotalKnownFunding { get; set; }
        public string TotalKnownFundingDetail { get; set; }
        public int? TotalKnownFundingCurrencyID { get; set; }

        [MaxLength(50)]
        public string TotalKnownFundingUnit { get; set; }

        [MaxLength(50)]
        public string MostRecentFundingStage { get; set; }
        public DateTime? MostRecentFundingDate { get; set; }
        public int? MostRecentFundingCurrencyID { get; set; }

        [MaxLength(50)]
        public string MostRecentFunding { get; set; }

        [MaxLength(50)]
        public string MostRecentFundingUnit { get; set; }
        public string MostRecentFundingDetail { get; set; }

        [MaxLength(50)]
        public string PostMoneyValuation { get; set; }
        public string PostMoneyValuationDetail { get; set; }
        public int? PostMoneyValuationCurrencyID { get; set; }

        [MaxLength(50)]
        public string PostMoneyValuationUnit { get; set; }

        [MaxLength(50)]
        public string CurrentStake { get; set; }

        [MaxLength(1000)]
        public string StockExchange_Ticker { get; set; }
        public int? HeadquarterID { get; set; }
        public int? CompanyGroupId { get; set; }

        public bool? IsDeleted { get; set; }
        public bool? IsActive { get; set; }
        public string FinancialYearEnd { get; set; }
        public string ImagePath { get; set; }
        public string MasterCompanyName { get; set; }
        public DateTime? InvestmentDate { get; set; }
        public string GroupName { get; set; }
        [SwaggerExclude]
        public FundUploadModel FundDetail { get; set; }
        [SwaggerExclude]
        public DealCompanyModel DealDetail { get; set; }
        public string DealCurom { get; set; }
        [SwaggerExclude]
        public CurrencyModel ReportingCurrencyDetail { get; set; }
        [SwaggerExclude]
        public SectorModel SectorDetail { get; set; }
        [SwaggerExclude]
        public FinancialStatusModel FinancialStatusDetail { get; set; }
        [SwaggerExclude]
        public OwnershipStatusModel OwnershipStatusDetail { get; set; }
        [SwaggerExclude]
        public InvestmentStageModel InvestmentStageDetail { get; set; }
        [SwaggerExclude]
        public SubSectorModel SubSectorDetail { get; set; }
        [SwaggerExclude]
        public List<LocationModel> Headquarters { get; set; }
        [SwaggerExclude]
        public List<LocationModel> GeographicLocations { get; set; }
        [SwaggerExclude]
        public List<PortfolioCompanyEmployeeModel> PCEmployees { get; set; }
        [SwaggerExclude]
        public StockExchangeModel StockExchangeDetail { get; set; }

        [MaxLength(500)]
        public string EncryptedPortfolioCompanyId { get; set; }
        [SwaggerExclude]

        public List<PortfolioCompanyProfitabilityModel> PortfolioCompanyProfitabilityList { get; set; }
        [SwaggerExclude]
        public CommentaryDetailsModel CommentaryDetails { get; set; }
        [SwaggerExclude]
        public CommentaryData CommentaryData { get; set; }
        [SwaggerExclude]
        public PcInvestmentKpiQuarterlyValueListModel InvestmentKpiDetails { get; set; }
        [SwaggerExclude]
        public List<PcImpactKpiQuarterlyValueTreeListModel> LpReportImpactKpiData { get; set; }
        [SwaggerExclude]
        public List<LPReportProfitabilityData> LPReportProfitabilityData { get; set; }
        public string Companylogo { get; set; }
        public bool? StaticDataPermission { get; set; }
        public bool? IsActiveWorkflowPresent { get; set; }
        [SwaggerExclude]
        public List<KeyValuePair<string, string>> SignificantEventsSectionDataLogos { get; set; }
        [SwaggerExclude]
        public PcMasterKpiValueListModel TradingRecordsData { get; set; }
        [SwaggerExclude]
        public PcMasterKpiValueListModel CreditKPIData { get; set; }
        [SwaggerExclude]
        public MappingPCGeographicLocationDraft PCGeographicLocationDraft { get; set; }
        [SwaggerExclude]
        public MappingPCEmployeeDraft PCEmployeeDraft { get; set; }
        public int? WorkflowRequestId { get; set; }
        public bool? SectionStatus { get; set; }
        [SwaggerExclude]
        public PortfolioCompanyOperationalKpiQuarterListTranposeModel PortfolioCompanyOperationalKPIQuarterList { get; set; }
        [SwaggerExclude]
        public System.Data.DataSet LPReportPortfolioCompanyKPI { get; set; }
        public int? WorkflowMappingId { get; set; }
        [SwaggerExclude]
        public List<PCProfitAndLossValuesTreeListModel> ProfitAndLossData { get; set; }
        [SwaggerExclude]
        public List<PCBalanceSheetValuesTreeListModel> BalanceSheetData { get; set; }
        [SwaggerExclude]
        public List<PCCashFlowValuesTreeListModel> CashFlowData { get; set; }
        [SwaggerExclude]
        public FundReportDetailsList FundReportDetailsList { get; set; }
        public bool? IsWorkflow { get; set; }
        [SwaggerExclude]
        public CurrencyModel FundReportingCurrency { get; set; }
        [SwaggerExclude]
        public List<PageFieldValueModel> CustomFieldValueList { get; set; }
        public List<CustomPortfolioGroupList> CustomPortfolioGroupList { get; set; }

    }
    public class PortfolioCompanyQueryModel
    {
        public int PortfolioCompanyID { get; set; }
        public string CompanyName { get; set; }
        public string FundName { get; set; }
        public string DealCustomID { get; set; }
        public string EncryptedDealID { get; set; }
        public string EncryptedFundID { get; set; }
        public string Website { get; set; }
        public int? SectorID { get; set; }
        public string Sector { get; set; }
        public int ReportingCurrencyID { get; set; }
        public int? SubSectorID { get; set; }
        public string MasterCompanyName { get; set; }
        public string FinancialYearEnd { get; set; }
        public string StockExchange_Ticker { get; set; }
        public string Status { get; set; }
        public DateTime? InvestmentDate { get; set; }
        public string CompanyLegalName { get; set; }
        public CurrencyModel ReportingCurrencyDetail { get; set; }
        public SectorModel SectorDetail { get; set; }
        public SubSectorModel SubSectorDetail { get; set; }
        public LocationModel GeographicLocations { get; set; }
        public string EncryptedPortfolioCompanyId { get; set; }
        public int WorkflowRequestId { get; set; }
        public string DraftName { get; set; }
        public int? GroupId { get; set; }
        public string GroupName { get; set; }

    }
    public class CompanyDataModel
    {
        public int CompanyId { get; set; }
        public string Name { get; set; }
        public int RowId { get; set; }
        public string Quarter { get; set; }
        public string QuarterAndYear { get; set; }
        public int? Year { get; set; }
    }
    public class CompanyListModel
    {
        public int PortfolioCompanyID { get; set; }
        public string CompanyName { get; set; }
        public string EncryptedPortfolioCompanyId { get; set; }
    }
    public class IngestionCompanyModel
    {
        public CompanyListModel CompanyListModel { get; set; }
        public List<CurrencyIngestionModel> Currencies { get; set; }
    }
    public class CurrencyIngestionModel 
    {
        public int CurrencyID { get; set; }
        public string Currency { get; set; }
        public string CurrencyCode { get; set; }
    }
    public class CompanyExcelModel
    {
        public string CompanyName { get; set; }
        public string CurrencyCode { get; set; }
        public string Units { get; set; }
    }

        
    public class CompanyDetailModel
    {
        public int FundID { get; set; }
        public int DealID { get; set; }
        public string DealCustomID { get; set; }
        public string EncryptedDealID { get; set; }
        public string EncryptedFundID { get; set; }
        public string FundName { get; set; }
        public int CurrencyID { get; set; }
        public string Currency { get; set; }
        public string CurrencyCode { get; set; }
    }
    public class MappingPCGeograpyLocModel
    {
        public int? RegionId { get; set; }
        public string Region { get; set; }
        public string City { get; set; }
        public int? CityId { get; set; }
        public int? StateId { get; set; }
        public string State { get; set; }
        public string Country { get; set; }
        public int? CountryId { get; set; }
        public bool? IsHeadquarter { get; set; }
        public int? PCGeographicLocationId { get; set; }
        public int? CreatedBy { get; set; }
        public DateTime? CreatedOn { get; set; }
    }
    public class MappingPCEmployeeModel
    {
        public int? EmployeeId { get; set; }
        public int? PCEmployeeID { get; set; }
        public string EmployeeName { get; set; }
        public string Email { get; set; }
        public string Designation { get; set; }
        public int? DesignationId { get; set; }
        public string ContactNo { get; set; }
        public string Education { get; set; }
        public string PastExperience { get; set; }
        public DateTime? CreatedOn { get; set; }
        public int? CreatedBy { get; set; }
    }
    public class MappingPCSectorCurrencyModel
    {
        public string Sector { get; set; }
        public int? SectorID { get; set; }
        public int? SubSectorID { get; set; }
        public string SubSector { get; set; }
        public int? ReportingCurrencyID { get; set; }
        public string CurrencyCode { get; set; }
        public string Currency { get; set; }
    }
    public class CompanyQueryModel
    {
        public int PortfolioCompanyId { get; set; }
    }
    public class SubFeatureQueryModel
    {
        public int SubFeatureId { get; set; }
    }
    public class SectorQueryModel
    {
        public int? SectorID { get; set; }
        public string Sector { get; set; }
    }
    public class MasterPortfolioCompanyDetails
    {
        public int MasterCompanyId { get; set; }
        public string EncryptedPortfolioCompanyId { get; set; }
        public string MasterCompanyName { get; set; }
        public string CompanyName { get; set; }
    }
    public class MasterCompanyDataModel
    {
        public int? FundId { get; set; }
        public int? PortfolioCompanyID { get; set; }
        public int? DealID { get; set; }
        public string MasterCompanyName { get; set; }
    }
    public class MasterCompanyList
    {
        public string MasterCompanyName { get; set; }
    }
    public class DealDataModel
    {
        public int DealId { get; set; }
        public DateTime? InvestmentDate { get; set; }
    }
}
