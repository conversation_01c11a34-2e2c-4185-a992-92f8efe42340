﻿using API.Helpers;
using Contract.Configuration;
using Contract.DataIngestion;
using Contract.Funds;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using PortfolioCompany.Interfaces;
using System.Collections.Generic;
using System.Threading.Tasks;
using Utility.Resource;

namespace API.Controllers.Data_Ingestion
{
    [Route("api/data-ingestion")]
    [ApiController]
    [Authorize(Policy = AppSettings.DefaultAuthPolicy)]
    public class DataIngestionController(IDataIngestionService dataIngestionService, ILogger<DataIngestionController> logger, IHelperService helperService) : ControllerBase
    {
        private readonly ILogger<DataIngestionController> _logger = logger;
        private readonly IDataIngestionService _dataIngestionService = dataIngestionService;
        private readonly IHelperService _helperService = helperService;

        /// <summary>
        /// Publishes KPI data to the system
        /// </summary>
        /// <param name="publishModels">List of KPI data items to publish</param>
        /// <returns>Result of the operation</returns>
        [HttpPost("publish/{processId}")]
        public async Task<IActionResult> Publish([FromRoute] string processId, [FromBody] List<PublishModel> publishModels)
        {
            _logger.LogInformation("Publishing KPI data for Process ID: {ProcessId}", processId);

            if (publishModels == null)
            {
                return BadRequest("No data provided");
            }
            var connectionString = AwsSecretsManagerHelper.GetConnectionString();
            var userId = _helperService.GetCurrentUserId(User);
            var result = await _dataIngestionService.PublishData(publishModels, connectionString, processId , userId);

            if (result)
            {
                return Ok(new { Success = true, Message = "Data published successfully" });
            }

            return BadRequest("Failed to publish data");
        }

        /// <summary>
        /// Gets KPI modules with page configuration details
        /// </summary>
        /// <returns>List of KPI module information</returns>
        [HttpGet("kpi-config")]
        public async Task<IActionResult> GetKpiModulesPageConfigDetails()
        {
            _logger.LogInformation("Retrieving KPI modules configuration details");
            var result = await _dataIngestionService.GetKpiModulesPageConfigDetails();
            
            if (result != null && result.Count > 0)
            {
                return Ok(result);
            }
            
            return NoContent();
        }

        [HttpGet("page-config")]
        public async Task<IActionResult> GetPageConfigDetails()
        {
            _logger.LogInformation("Retrieving KPI modules configuration details");
            var result = await _dataIngestionService.GetPageConfigDetails();

            if (result != null && result.Count > 0)
            {
                return Ok(result);
            }

            return NoContent();
        }

        /// <summary>
        /// Gets KPI mappings for a portfolio company and module
        /// </summary>
        /// <param name="portfolioCompanyId">ID of the portfolio company</param>
        /// <returns>List of KPI mapping information</returns>
        [HttpGet("kpi-mapping")]
        public async Task<IActionResult> GetKPIMapping(int portfolioCompanyId)
        {
            _logger.LogInformation("Retrieving KPI mapping for portfolio company ID: {PortfolioCompanyId}", portfolioCompanyId);
            
            if (portfolioCompanyId <= 0)
            {
                return BadRequest("Invalid portfolio company ID");
            }
            
            var result = await _dataIngestionService.GetKPIMapping(portfolioCompanyId);
            
            if (result != null && result.Count > 0)
            {
                return Ok(result);
            }
            
            return NoContent();
        }
        /// <summary>
        /// GetUserFunds
        /// </summary>
        /// <returns></returns>
        [HttpGet("fetch-funds")]
        public async Task<IActionResult> GetUserFunds()
        {
            var result = await _dataIngestionService.GetUserFunds(_helperService.GetCurrentUserId(User));
            if (result?.Count > 0)
            {
                return Ok(result);
            }
            return NoContent();
        }
        /// <summary>
        /// GeFundKpis
        /// </summary>
        /// <returns></returns>
        [HttpGet("fetch-fund-kpis")]
        public async Task<IActionResult> GetFundKpis()
        {
            var result = await _dataIngestionService.GetFundKpis();
            if (result?.Count > 0)
            {
                return Ok(result);
            }
            return NoContent();
        }


        [HttpGet("fund-details/{fundId}")]
        public async Task<IActionResult> GetFundDetails(int fundId)
        {
            _logger.LogInformation("Retrieving KPI modules configuration details");
            var result = await _dataIngestionService.GetFundDetails(fundId);

            if (result != null)
            {
                return Ok(result);
            }

            return NoContent();
        }

        [HttpGet("companies-and-kpis/{fundId}")]
        public async Task<IActionResult> GetCompaniesAndKpi(int fundId)
        {
            _logger.LogInformation("Retrieving KPI modules configuration details");
            var result = await _dataIngestionService.GetCompaniesAndKpiByFundId(fundId);

            if (result != null)
            {
                return Ok(result);
            }

            return NoContent();
        }
        /// <summary>
        /// GetFundIngestionList
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        [HttpPost("fetch-fundIngestionList")]
        public async Task<IActionResult> GetFundIngestionList(FundIngestionFilter filter)
        {
            if (ModelState.IsValid)
            {
                var result = await _dataIngestionService.GetFundIngestionList(filter);
                string headerText = await _dataIngestionService.GetsubpagedetailsText((int)PageConfigurationSubFeature.FundIngestion);
                List<PageFieldValueModelTrackRecord> dynamicCoulmns = await _dataIngestionService.GetFundIngestionDynamicColumns();
                return Ok(new {  dynamicCoulmns, headerText = headerText, result });
            }
            else
            {
                return Ok(Messages.SomethingWentWrong);
            }
        }

        /// <summary>
        /// SpecificKpisPublish
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost("specific-kpis-publish")]
        public async Task<IActionResult> SpecificKpisPublish([FromBody] SpecificKpiPublishModel data)
        {
            _logger.LogInformation("Publishing SpecificKpis data");
            if (data == null)
            {
                return BadRequest("No data provided");
            }
            var connectionString = AwsSecretsManagerHelper.GetConnectionString();
            data.UserId = _helperService.GetCurrentUserId(User);
            var result = await _dataIngestionService.SpecificKpisPublish(data, connectionString);

            if (result)
            {
                return Ok(new { Success = true, Message = "SpecificKpis published successfully" });
            }
            return BadRequest("Failed to publish SpecificKpis");
        }
    }
}
