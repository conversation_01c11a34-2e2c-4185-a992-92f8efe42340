using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;

namespace Contract.KPI
{
    [ExcludeFromCodeCoverage]
    public class KpiMappingModel
    {
        private bool _newIsMapped;
        private int? _newDisplayOrder;
        public int Id { get; set; }
        public string Name { get; set; }
        public string Formula { get; set; }
        public string FormulaKPIId { get; set; }
        public int? ParentKPIID { get; set; }

        public string KpiInfo { get; set; }
        public int? DisplayOrder
        {
            get { return _newDisplayOrder; }
            set
            {
                _newDisplayOrder = value;

                OldDisplayOrder = _newDisplayOrder;
            }
        }
        public int MappingKPIId { get; set; }
        public bool IsMapped
        {
            get { return _newIsMapped; }
            set
            {
                _newIsMapped = value;

                OldIsMapped = _newIsMapped;
            }
        }
        public bool OldIsMapped { get; set; }
        public int? OldDisplayOrder { get; set; }
        public bool IsHeader { get; set; }
        public bool IsBoldKPI { get; set; }
        public int KpiTypeId { get; set; } = 0;
        public int? MethodologyID { get; set; }
        public string Synonym { get; set; }
        public string Definition { get; set; }
        public bool IsExtraction { get; set; }

        public List<KpiMappingModel> Children { get; set; }
    }
    public class KpiMappingQueryModel
    {
        public List<KpiMappingModel> KPIMappings { get; set; }
        public string CompanyIds { get; set; }
    }
    public class KpiMappingBasedOnModuleId
    {
        public int ModuleId { get; set; }
        public List<KpiMappingModel> KPIMappings { get; set; }
    }
    public class CopyToKpiQueryModel
    {
        public int CompanyId { get; set; }
        public int UserId { get; set; }
        public string KpiType { get; set; }
        public string CompanyIds { get; set; }
        public int SubPageId { get; set; }
        public int ModuleId { get; set; } = 0;
    }
}