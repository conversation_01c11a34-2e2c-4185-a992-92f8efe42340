﻿using System.Linq;
using System.Threading.Tasks;
using API.Filters;
using API.Helpers;
using DocumentCollection.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using DocumentCollection.Models;

namespace API.Controllers.DocumentCollection
{
    [Authorize(Policy = AppSettings.DefaultAuthPolicy)]
    [Route("api")]
    public class RepositoryConfigurationController(IRepositoryConfigurationService _docColConfig, ILogger<RepositoryConfigurationController> _logger, IHelperService _helperService) : ControllerBase
    {
        /// <summary>
        /// Fetches the available document collections.
        /// </summary>
        /// <remarks>
        /// This endpoint retrieves a list of document collections available in the system.
        /// </remarks>
        /// <returns>A list of <see cref="DocumentCollectionDto"/> representing the document collections.</returns>
        [HttpGet("get-document-config/{companyid}/{featureId}")]
        public async Task<IActionResult> GetRepositoryFrequencyConfig(int entityId, int featureId)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            if (entityId == 0)
            {
                return BadRequest();
            }
            _logger.LogInformation("Fetching repository configuration for company");
            return Ok(await _docColConfig.GetRepositoryConfigurationByCompany(entityId, featureId));
        }
        /// <summary>
        /// Adds or updates a repository frequency.
        /// </summary>
        /// <param name="configModel"></param>
        /// <returns></returns>
        [HttpPost("update-document-config")]
        [AuthorizeUserPermission(Contract.Account.Features.RepositoryConfiguration,Contract.Account.Actions.canAdd)]
        public async Task<IActionResult> AddorUpdateRepositoryFreqencyConfig([FromBody] DocumentConfigurationModel configModel)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            if (configModel == null || !configModel.Ids.Any())
            {
                return BadRequest();
            }
            _logger.LogInformation("Updating repository configurations");
            return Ok(await _docColConfig.UpdateRepositoryConfigurations(configModel, _helperService.GetCurrentUserId(User)));
        }
        /// <summary>
        /// Fetches the configured frequencies.
        /// </summary>
        /// <param name="companyid"></param>
        /// <returns></returns>
        [HttpPost("get-combined-document-config/{featureId}")]
        [AuthorizeUserPermission(Contract.Account.Features.RepositoryConfiguration, Contract.Account.Actions.canView)]
        public async Task<IActionResult> GetRepositoryFreqencyConfigByCompanies(int featureId, [FromBody] int[] entityIds)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            if (entityIds == null || !entityIds.Any())
            {
                return BadRequest();
            }
            return Ok(await _docColConfig.GetRepositoryConfigurationByCompanies(entityIds.ToList(), featureId));
        }

        [HttpPost("get-portfolio-companies")]
        [AuthorizeUserPermission(Contract.Account.Features.RepositoryConfiguration, Contract.Account.Actions.canView)]
        public async Task<IActionResult> GetPortfolioCompanyList()
        {
            _logger.LogInformation("Retrieving portfolio company list");
            return Ok(await _docColConfig.GetPortfolioCompaniesList(_helperService.GetCurrentUserId(User)));
        }

        /// <summary>
        /// Retrieves a list of funds associated with the current user's portfolio.
        /// </summary>
        /// <remarks>
        /// This endpoint fetches the fund list based on the user's permissions and the associated portfolio.
        /// </remarks>
        /// <returns>A list of funds available to the user.</returns>
        [HttpPost("get-fund-list")]
        [AuthorizeUserPermission(Contract.Account.Features.RepositoryConfiguration, Contract.Account.Actions.canView)]
        public async Task<IActionResult> GetFundList()
        {
            _logger.LogInformation("Retrieving fund list");
            return Ok(await _docColConfig.GetFundList(_helperService.GetCurrentUserId(User)));
        }
    }
}
