
using System.Collections.Generic;

namespace DataAccessLayer.DBModel
{
    public partial class M_CompanyKpi : BaseModel
    {
        public int CompanywiseKPIID { get; set; }
        public string Kpi { get; set; }
        public string KpiInfo { get; set; }
        public string SegmentType { get; set; }
        public int? PortfolioCompanyId { get; set; }
        public int? ParentKpiId { get; set; }
        public int MethodologyId { get; set; }
        public string Description { get; set; }
        public bool IsDeleted { get; set; }
        public string EncryptedCompanyKpiId { get; set; }
        public string AnnualCalculationMethod { get; set; }
        public string Formula { get; set; }
        public string FormulaKPIId { get; set; }
        public bool IsBoldKPI { get; set; }
        public bool IsHeader { get; set; }
        public string Synonym { get; set; }
    }
}
