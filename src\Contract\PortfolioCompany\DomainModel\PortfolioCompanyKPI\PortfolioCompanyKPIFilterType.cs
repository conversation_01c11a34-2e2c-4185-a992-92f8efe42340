﻿using Contract.Configuration;
using Contract.Utility;
using System.Collections.Generic;
namespace Contract.PortfolioCompany.DomainModel.PortfolioCompanyKPI
{
    public class PcKPIsFilterType: YtdLtmFilter
    {
        public int CompanyId { get; set; }
        public string ValueType { get; set; }
        public int PeriodType { get; set; }
        public bool IsPageLoad { get; set; }
        public bool IsMonthly { get; set; }
        public bool IsQuarterly { get; set; }
        public bool IsAnnually { get; set; }
        public string? CurrencyCode { get; set; }
        public string? ReportingCurrencyCode { get; set; }
        public string? SegmentType { get; set; }

        public int DecimalPlace { get; set; }
        public string? kpis { get; set; }
        public string? CurrencyRateSource { get; set; }
        public int ModuleId { get; set; }

        public int SubPageModuleId { get; set; }
        public int ExportType { get; set; } = 0;
        public int Unit { get; set; }
        public PaginationFilter? PaginationFilter { get; set; }
        public SearchFilter? SearchFilter { get; set; }
        public KpiFilterModel KPIFilter { get; set; }
        public List<MSubFields> KpiConfigurationData { get; set; }
        public string ChartType { get; set; }
        public int KpiId { get; set; }
        public bool HasActualValues { get; set; }
        public bool IsSpotRate { get; set; } = false;
        public string SpotRateDate { get; set; } = null;
        public decimal? SpotRate { get; set; }
        public int FundId { get; set; }
        public bool IsTrend { get; set; } = false;
    }
    public class YtdLtmFilter
    {
        public bool IsLtm { get; set; } = false;
        public bool IsYtd { get; set; } = false;
        public bool IsYtdPageLoad { get; set; } = false;
        public bool IsLtmPageLoad { get; set; } = false;
    }
    public class ChartValueModel
    {
        public int FieldID { get; set; }
        public string Name { get; set; }
        public string ChartValue { get; set; }
        public int ModuleId { get; set; }
    }
}
