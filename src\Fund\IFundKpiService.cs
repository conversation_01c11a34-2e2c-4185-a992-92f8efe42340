using Contract.Configuration;
using Contract.Fund.Models;
using Contract.KPI;
using Contract.PortfolioCompany.DomainModel.PortfolioCompanyKPI;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Fund;

/// <summary>
/// Interface for Fund KPI service operations 
/// </summary>
public interface IFundKpiService
{
    /// <summary>
    /// Add or Update Fund KPI
    /// </summary>
    /// <param name="kpiModel">Fund KPI Model</param>
    /// <param name="userId">User ID</param>
    /// <returns>Result status code</returns>
    Task<int> AddOrUpdateFundKPI(FundKpiModel kpiModel, int userId);

    /// <summary>
    /// Get Fund KPI List by Module ID
    /// </summary>
    /// <param name="moduleId">Module ID</param>
    /// <returns>List of Fund KPIs</returns>
    Task<List<FundKpiModel>> GetFundKPIList(int moduleId);

    /// <summary>
    /// Get Unmapped Fund KPIs
    /// </summary>
    /// <param name="fundId">Fund ID</param>
    /// <param name="moduleId">Module ID</param>
    /// <returns>List of unmapped KPI mapping models</returns>
    Task<List<Contract.KPI.KpiMappingModel>> GetUnMappedFundKpi(int fundId, int moduleId);

    /// <summary>
    /// Get Fund KPI Mapping
    /// </summary>
    /// <param name="fundId">Fund ID</param>
    /// <param name="moduleId">Module ID</param>
    /// <returns>List of KPI mapping models</returns>
    Task<List<Contract.KPI.KpiMappingModel>> FundKPIMapping(int fundId, int moduleId);

    /// <summary>
    /// Create duplicate Fund KPI
    /// </summary>
    /// <param name="duplicateKPI">Duplicate KPI model</param>
    /// <returns>Result status code</returns>
    Task<int> CreateFundKpi(DuplicateKpiModel duplicateKPI);

    /// <summary>
    /// Update Fund KPI Mapping
    /// </summary>
    /// <param name="fundId">Fund ID</param>
    /// <param name="kPIMappingModels">KPI Mapping models</param>
    /// <param name="userId">User ID</param>
    /// <param name="moduleId">Module ID</param>
    /// <returns>Success status</returns>
    Task<bool> UpdateFundKPIMapping(int fundId, List<Contract.KPI.KpiMappingModel> kPIMappingModels, int userId, int moduleId);

    /// <summary>
    /// Delete Fund KPI
    /// </summary>
    /// <param name="kPIModel">KPI Delete model</param>
    /// <returns>Result status code</returns>
    Task<int> DeleteFundKPI(DeleteKpiModel kPIModel);
    Task<int> CopyFundKpiToCompanies(CopyToKpiQueryModel copyToKPIQueryModel);
    Task<List<KpiConfig>> GetPageConfigSubSectionFields();
    Task<PcKPIsResponse> GetFundKpiValues(PcKPIsFilterType filter);
    Task<List<KpiListModel>> GetTabList();
}
