﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Threading.Tasks;
using OfficeOpenXml;
using System.Linq;
using Contract.BulkUpload;
using Shared;
using DataAccessLayer.UnitOfWork;
using DataAccessLayer.DBModel;
using Contract.KPI;
using System.Globalization;
using DapperRepository.Constants;
using Contract.DataIngestion;

namespace Imports.Helpers;
public class ImportHelper
{
    private const string HEADER_KPI = "KPI";
    private const string HEADER_ID = "Id";
    private const string HEADER_HEADER = "Header";
    private const string HEADER_QUARTER = "Quarter";
    private const string HEADER_MONTH = "Month";
    private const string HEADER_YEAR = "Year";
    private const string HEADER_HALF = "Half";
    private const string HEADER_KPI_VALUE = "KPIValue";
    private const string HEADER_CREATED_BY = "CreatedBy";
    private const string HEADER_CREATED_ON = "CreatedOn";
    private const string HEADER_IS_YTD = "IsYTD";
    private const string HEADER_IS_NUMERIC = "IsNumeric";
    private const string HEADER_MODULE_ID = "ModuleID";
    
    private const string SQL_PARAM_USER_ID = "@UserId";
    private const string SQL_PARAM_TABLE_NAME = "@TableName";
    private const string SQL_PARAM_COMPANY_ID = "@CompanyId";
    private const string SQL_PARAM_FUND_ID = "@FundId";
    private const string SQL_PARAM_DOCUMENT_ID = "@DocumentId";
    
    private const string TEXT_LITERAL = "Text";
    private const string YEAR_LITERAL = "Y";
    private const string HALF_LITERAL = "Half";
    private const string NA_LITERAL = "N.A";
    private const string COLUMN_PREFIX = "Column";
    private const string ROW_FORMAT_PERCENT = "%";

    public static async Task ImportCreateTableQueryAtRuntime(string TableName, string query, string connString)
    {
        using var connection = new SqlConnection(connString);
        using SqlCommand cmd = new SqlCommand("spImportCreateTableQueryAtRuntime", connection)
        {
            CommandTimeout = 120000,
            CommandType = CommandType.StoredProcedure
        };
        cmd.Parameters.AddWithValue("@TableName", TableName);
        cmd.Parameters.AddWithValue("@Query", query);
        if (connection.State == ConnectionState.Closed) connection.Open();
        await cmd.ExecuteNonQueryAsync();
    }
    public static async Task ImportDropTableQueryAtRuntime(string TableName, string connString)
    {
        using var connection = new SqlConnection(connString);
        using SqlCommand cmd = new SqlCommand(SqlConstants.QueryByDropStagingTable, connection)
        {
            CommandTimeout = 120000,
            CommandType = CommandType.StoredProcedure
        };
        cmd.Parameters.AddWithValue("@TableName", TableName);
        if (connection.State == ConnectionState.Closed) connection.Open();
        await cmd.ExecuteNonQueryAsync();
    }
    public static async Task ImportCreateTableAtRuntime(string TableName, string ColumnsName, string connString)
    {
        using var connection = new SqlConnection(connString);
        using SqlCommand cmd = new SqlCommand("spImportCreateTableAtRuntime", connection)
        {
            CommandTimeout = 120000,
            CommandType = CommandType.StoredProcedure
        };
        cmd.Parameters.AddWithValue("@TableName", TableName);
        cmd.Parameters.AddWithValue("@ColumnsName", ColumnsName);
        if (connection.State == ConnectionState.Closed) connection.Open();
        await cmd.ExecuteNonQueryAsync();
    }
    public static async Task ImportStagingTblToActualTblQuery(string procName, string tableName, int userId, string connectionString)
    {
        using var connection = new SqlConnection(connectionString);
        using SqlCommand cmd = new SqlCommand(procName, connection)
        {
            CommandTimeout = 120000,
            CommandType = CommandType.StoredProcedure
        };
        cmd.Parameters.AddWithValue(SQL_PARAM_USER_ID, userId);
        cmd.Parameters.AddWithValue(SQL_PARAM_TABLE_NAME, tableName);
        if (connection.State == ConnectionState.Closed) connection.Open();
        await cmd.ExecuteNonQueryAsync();
    }
    public static async Task ImportStagingTblToActualTblQueryWithDocuments(BulkUploadDataModel bulkUploadDataModel, string procName, string tableName, bool isCapTable = false)
    {
        using var connection = new SqlConnection(bulkUploadDataModel.Connection);
        using SqlCommand cmd = new SqlCommand(procName, connection)
        {
            CommandTimeout = 120000,
            CommandType = CommandType.StoredProcedure
        };
        cmd.Parameters.AddWithValue(SQL_PARAM_USER_ID, bulkUploadDataModel.UserID);
        cmd.Parameters.AddWithValue(SQL_PARAM_TABLE_NAME, tableName);
        cmd.Parameters.AddWithValue(SQL_PARAM_DOCUMENT_ID, bulkUploadDataModel.DocumentId > 0 ? bulkUploadDataModel.DocumentId : null);
        cmd.Parameters.AddWithValue("@SupportingDocumentsId", bulkUploadDataModel.SupportingDocuments);
        cmd.Parameters.AddWithValue("@CommentId", bulkUploadDataModel.CommentId > 0 ? bulkUploadDataModel.CommentId : null);
        cmd.Parameters.AddWithValue(SQL_PARAM_COMPANY_ID, bulkUploadDataModel.PortfolioCompanyID);

        if (isCapTable)
        {
            cmd.Parameters.AddWithValue("@PeriodTableName", bulkUploadDataModel.CapTableStagingName);
        }
        else if (bulkUploadDataModel.IsFinancial)
        {
            cmd.Parameters.AddWithValue("@ModuleID", bulkUploadDataModel.ModuleID);
        }
        if (connection.State == ConnectionState.Closed) connection.Open();
        await cmd.ExecuteNonQueryAsync();
    }
    /// <summary>
    /// ImportStagingTblToActualTblQueryWithDocuments
    /// </summary>
    /// <param name="data"></param>
    /// <param name="procName"></param>
    /// <param name="tableName"></param>
    /// <param name="isCapTable"></param>
    /// <returns></returns>
    public static async Task ImportStagingTblToActualTblQueryWithDocuments(PublishModel data, string procName, string tableName, bool isCapTable = false, bool isFundKpi = false)
    {
        using var connection = new SqlConnection(data.ConnectionString);
        using SqlCommand cmd = new SqlCommand(procName, connection)
        {
            CommandTimeout = 120000,
            CommandType = CommandType.StoredProcedure
        };
        cmd.Parameters.AddWithValue(SQL_PARAM_USER_ID, data.UserId);
        cmd.Parameters.AddWithValue(SQL_PARAM_TABLE_NAME, tableName);
        cmd.Parameters.AddWithValue(SQL_PARAM_DOCUMENT_ID, null);
        cmd.Parameters.AddWithValue("@SupportingDocumentsId", null);
        cmd.Parameters.AddWithValue("@CommentId", null);
        if (!isFundKpi)
        {
           
            cmd.Parameters.AddWithValue(SQL_PARAM_COMPANY_ID, data.CompanyId);
        }
        else
        {
            cmd.Parameters.AddWithValue("@IsIngestion", true);
            cmd.Parameters.AddWithValue("@ProcessId", data.ProcessId);
            cmd.Parameters.AddWithValue(SQL_PARAM_FUND_ID, data.FundId);
        }
            
        if (data.IsFinancial)
        {
            cmd.Parameters.AddWithValue("@ModuleID", data.ModuleId);
            cmd.Parameters.AddWithValue("@DataIngestion", true);
        }
        if (connection.State == ConnectionState.Closed) connection.Open();
        await cmd.ExecuteNonQueryAsync();
    }
    /// <summary>
    /// ImportStagingTblToActualTblQueryWithoutDocuments
    /// </summary>
    /// <param name="bulkUploadDataModel"></param>
    /// <param name="procName"></param>
    /// <param name="tableName"></param>
    /// <returns></returns>
    public static async Task ImportStagingTblToActualTblQueryWithoutDocuments(BulkUploadDataModel bulkUploadDataModel, string procName, string tableName)
    {
        using var connection = new SqlConnection(bulkUploadDataModel.Connection);
        using SqlCommand cmd = new SqlCommand(procName, connection)
        {
            CommandTimeout = 120000,
            CommandType = CommandType.StoredProcedure
        };
        cmd.Parameters.AddWithValue(SQL_PARAM_USER_ID, bulkUploadDataModel.UserID);
        cmd.Parameters.AddWithValue(SQL_PARAM_TABLE_NAME, tableName);
        cmd.Parameters.AddWithValue(SQL_PARAM_COMPANY_ID, bulkUploadDataModel.PortfolioCompanyID);
        cmd.Parameters.AddWithValue("@Period", bulkUploadDataModel.MonthlyReportPeriod);
        cmd.Parameters.AddWithValue("@Color", bulkUploadDataModel.Color);

        if (connection.State == ConnectionState.Closed) connection.Open();
        await cmd.ExecuteNonQueryAsync();
    }
    public static async Task ImportStagingTblToActualTbl(BulkUploadDataModel bulkUploadDataModel)
    {
        using var connection = new SqlConnection(bulkUploadDataModel.Connection);
        using SqlCommand cmd = new SqlCommand("spImportStagingTblToActualTbl", connection)
        {
            CommandTimeout = 120000,
            CommandType = CommandType.StoredProcedure
        };
        cmd.Parameters.AddWithValue("@CreatedOn", DateTime.Now);
        cmd.Parameters.AddWithValue("@SheetName", bulkUploadDataModel.SheetName);
        cmd.Parameters.AddWithValue(SQL_PARAM_TABLE_NAME, bulkUploadDataModel.TableName);
        cmd.Parameters.AddWithValue("@ColumnsName", bulkUploadDataModel.ColumnsName);
        cmd.Parameters.AddWithValue("@ColumnsUnpivot", bulkUploadDataModel.ColumnsUnpivot);
        cmd.Parameters.AddWithValue(SQL_PARAM_USER_ID, bulkUploadDataModel.UserID);
        cmd.Parameters.AddWithValue(SQL_PARAM_COMPANY_ID, bulkUploadDataModel.PortfolioCompanyID);
        cmd.Parameters.AddWithValue("@WorkflowRequestId", bulkUploadDataModel.WorkflowRequestId);
        cmd.Parameters.AddWithValue(SQL_PARAM_DOCUMENT_ID, bulkUploadDataModel.DocumentId > 0 ? bulkUploadDataModel.DocumentId : null);
        cmd.Parameters.AddWithValue("@SupportingDocumentsId", !string.IsNullOrEmpty(bulkUploadDataModel.SupportingDocuments) ? bulkUploadDataModel.SupportingDocuments : null);
        cmd.Parameters.AddWithValue("@CommentId", bulkUploadDataModel.CommentId > 0 ? bulkUploadDataModel.CommentId : null);
        if (connection.State == ConnectionState.Closed) connection.Open();
        await cmd.ExecuteNonQueryAsync();
    }

    public static async Task ImportEsgStagingTblToActualTblQuery(string procName, string tableName, int portfolioCompanyID, int userId, string connectionString)
    {
        try
        {
            using var connection = new SqlConnection(connectionString);
            using SqlCommand cmd = new SqlCommand(procName, connection)
            {
                CommandTimeout = 120000,
                CommandType = CommandType.StoredProcedure
            };
            cmd.Parameters.AddWithValue(SQL_PARAM_USER_ID, userId);
            cmd.Parameters.AddWithValue(SQL_PARAM_TABLE_NAME, tableName);
            cmd.Parameters.AddWithValue(SQL_PARAM_COMPANY_ID, portfolioCompanyID);
            cmd.Parameters.AddWithValue("@CreatedOn", DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss"));
            if (connection.State == ConnectionState.Closed) connection.Open();
            await cmd.ExecuteNonQueryAsync();
        }
        catch (Exception ex)
        {
            throw;
        }
    }
    public async Task<DataTable> GetDataTableFromExcel(string sheetName, string path, int UserID, string firstColumnName, List<KpiQueryModel> kpiQueries = null, string secondColumnName = null)
    {
        DataTable dt = new DataTable();
        FileInfo excelFile = new FileInfo(path);
        List<string> lstRemoveColName = new List<string>();
        int colCount = 1;

        try
        {
            return await Task.Run(() =>
            {
                using ExcelPackage package = new ExcelPackage(excelFile);
                ExcelWorksheet worksheet = package.Workbook.Worksheets[sheetName];
                if (worksheet.Dimension == null)
                    return dt;
                for (int i = 1; i <= worksheet.Dimension.End.Column; i++)
                {
                    if (i == 1)
                    {
                        dt.Columns.Add(firstColumnName);
                        worksheet.Cells[1, 1].Value = firstColumnName;
                    }
                    else
                    {
                        dynamic columnValue = worksheet.Cells[1, i].Value != null ? worksheet.Cells[1, i].Value.ToString().Trim() : string.Empty;
                        dt.Columns.Add(Convert.ToString(columnValue));
                    }
                }

                for (int i = 2; i <= worksheet.Dimension.End.Row; i++)
                {

                    DataRow row = dt.NewRow();

                    for (int j = worksheet.Dimension.Start.Column; j <= worksheet.Dimension.End.Column; j++)
                    {
                        var id = worksheet.Cells[i, 2].Value;
                        var kpis = kpiQueries?.FirstOrDefault(x => x.KPIId == Convert.ToInt32(id));
                        string excelCell = GetExcelCell(worksheet, i, j, kpis);
                        if (excelCell != null)
                        {
                            if (IsNumeric(excelCell.ToString()))
                            {
                                row[j - 1] = string.IsNullOrEmpty(excelCell) || string.IsNullOrWhiteSpace(excelCell) ? null : decimal.Parse(excelCell, System.Globalization.NumberStyles.Any).ToString();
                            }
                            else
                            {
                                row[j - 1] = string.IsNullOrEmpty(excelCell) || string.IsNullOrWhiteSpace(excelCell) ? null : excelCell.Trim();
                            }
                        }
                    }

                    dt.Rows.Add(row);
                }

                foreach (DataColumn column in dt.Columns)
                {
                    if (colCount == 1)
                    {
                        column.ColumnName = firstColumnName;
                    }
                    else
                    {
                        if (column.ColumnName.Contains(COLUMN_PREFIX))
                        {
                            lstRemoveColName.Add(column.ColumnName);
                        }
                    }
                    colCount++;
                }

                DataColumn dcCreatedBy = new DataColumn(HEADER_CREATED_BY, typeof(System.Int32));
                DataColumn dcCreatedOn = new DataColumn(HEADER_CREATED_ON, typeof(System.DateTime));
                dcCreatedBy.DefaultValue = UserID.ToString();
                dcCreatedOn.DefaultValue = DateTime.Now;
                dt.Columns.Add(dcCreatedBy);
                dt.Columns.Add(dcCreatedOn);

                for (int iIndex = 0; iIndex < lstRemoveColName.Count; iIndex++)
                {
                    dt.Columns.Remove(lstRemoveColName[iIndex]);
                    dt.AcceptChanges();
                }

                for (int i = dt.Rows.Count - 1; i >= 0; i--)
                {
                    if (dt.Rows[i][0] == DBNull.Value)
                        dt.Rows[i].Delete();
                }

                foreach (var column in dt.Columns.Cast<DataColumn>().ToArray())
                {
                    if (dt.AsEnumerable().All(dr => dr.IsNull(column)))
                        dt.Columns.Remove(column);
                }
                dt.AcceptChanges();
                return dt;
            }).ConfigureAwait(false);
        }
        catch
        {
            return null;
        }
        finally
        {
            dt = null;
        }
    }

    private static string GetExcelCell(ExcelWorksheet worksheet, int i, int j, KpiQueryModel kpis)
    {
        if (kpis != null && j != 1)
            return kpis?.KPIInfo == TEXT_LITERAL ? worksheet.Cells[i, j].Text : worksheet.Cells[i, j]?.Value?.ToString()?.Replace("x", string.Empty).Replace(ROW_FORMAT_PERCENT, string.Empty).Replace("$", string.Empty);
        else
            return worksheet.Cells[i, j]?.Value?.ToString();
    }

    public static async Task<DataTable> GetDataTableFromExcelKPIs(string sheetName, string path, int UserID, string firstColumnName, List<KpiQueryModel> kPIQueries)
    {
        DataTable dt = new DataTable();
        FileInfo excelFile = new FileInfo(path);
        List<string> lstRemoveColName = new List<string>();
        int colCount = 1;
        try
        {
            return await Task.Run(() =>
            {
                using (ExcelPackage package = new ExcelPackage(excelFile))
                {
                    ExcelWorksheet worksheet = package.Workbook.Worksheets[sheetName];
                    if (worksheet.Dimension == null)
                    {
                        return dt;
                    }
                    dt.Columns.Add(firstColumnName);
                    dt.Columns.Add(HEADER_ID);
                    dt.Columns.Add(HEADER_HEADER);
                    dt.Columns.Add(HEADER_QUARTER);
                    dt.Columns.Add(HEADER_MONTH);
                    dt.Columns.Add(HEADER_YEAR);
                    dt.Columns.Add(HEADER_KPI_VALUE);

                    for (int i = 2; i <= worksheet.Dimension.End.Row; i++)
                    {
                        for (int j = 3; j <= worksheet.Dimension.End.Column; j++)
                        {
                            var columnHeader = worksheet.Cells[1, j]?.Value?.ToString().Trim();
                            var delimitingChars = new char[2];
                            delimitingChars[0] = '(';
                            delimitingChars[1] = ')';
                            var headers = columnHeader?.Split(delimitingChars).Where(x => x != "").Select(x => x.Trim()).ToArray();
                            int? year = null, month = null;
                            string quarter = null, dtHeader = null;
                            if (headers?.Length == 1)
                            {
                                var isFY = IsNumeric(headers.FirstOrDefault());
                                year = isFY ? Convert.ToInt32(headers.FirstOrDefault()) : null;
                                dtHeader = YEAR_LITERAL;
                            }
                            else
                            {
                                dtHeader = headers?[0];
                                if (headers != null && headers[1].Contains("-"))
                                {
                                    var headerValues = headers?[1].Split('-').Where(x => x != "").Select(x => x.Trim()).ToArray();
                                    var monthNumber = headerValues != null && headerValues?.Length > 0 ? GetMonthNumber(headerValues[0]?.ToLower()) : 0;
                                    month = (monthNumber != 0) ? monthNumber : null;
                                    if (headerValues?.Length > 0)
                                    {
                                        if (IsQuarter(headerValues[0]?.ToUpper()))
                                        {
                                            quarter = headerValues[0]?.ToUpper();
                                        }
                                        else
                                        {
                                            quarter = null;
                                        }
                                    }
                                    else
                                    {
                                        quarter = null;
                                    }

                                    if (headerValues?.Length > 0)
                                    {
                                        if (IsNumeric(headerValues[1]))
                                        {
                                            year = Convert.ToInt32(headerValues[1]);
                                        }
                                        else
                                        {
                                            year = null;
                                        }
                                    }
                                    else
                                    {
                                        year = null;
                                    }
                                }
                                else
                                {
                                    year = IsNumeric(headers?[1]) ? Convert.ToInt32(headers?[1]) : null;
                                }
                            }
                            DataRow row = dt.NewRow();
                            var kpi = worksheet.Cells[i, 1].Value;
                            var id = worksheet.Cells[i, 2].Value;
                            var kpis = kPIQueries.FirstOrDefault(x => x.KPIId == Convert.ToInt32(id));
                            var excelCell = kpis?.KPIInfo == TEXT_LITERAL ? worksheet.Cells[i, j].Text : worksheet.Cells[i, j].Value;
                            row[HEADER_KPI] = kpi;
                            row[HEADER_ID] = id;
                            row[HEADER_HEADER] = dtHeader;
                            row[HEADER_QUARTER] = quarter;
                            row[HEADER_MONTH] = month;
                            row[HEADER_YEAR] = year;
                            if (excelCell != null)
                            {
                                var format = worksheet.Cells[i, j].Style?.Numberformat?.Format;
                                if (format?.Contains(ROW_FORMAT_PERCENT) == true && kpis.KPIInfo != TEXT_LITERAL)
                                {
                                    if (decimal.TryParse(excelCell?.ToString(), out decimal updatedValue))
                                        excelCell = updatedValue.ToString();
                                    else if (excelCell?.ToString().Contains("E") == true && j != 1 && i > 4)
                                        excelCell = (decimal.Parse(excelCell.ToString(), NumberStyles.Float) * 100).ToString();
                                }
                                else if (kpis.KPIInfo == Constants.KpiInfoMultiple)
                                {
                                    excelCell = excelCell.ToString().Replace(Constants.KpiInfoMultiple, string.Empty);
                                }
                                if (IsNumeric(excelCell.ToString()))
                                {
                                    row[HEADER_KPI_VALUE] = string.IsNullOrEmpty(excelCell.ToString()) || string.IsNullOrWhiteSpace(excelCell.ToString()) ? null : decimal.Parse(excelCell.ToString(), System.Globalization.NumberStyles.Any).ToString();
                                }
                                else
                                {
                                    row[HEADER_KPI_VALUE] = string.IsNullOrEmpty(excelCell.ToString()) || string.IsNullOrWhiteSpace(excelCell.ToString()) ? null : excelCell.ToString().Trim();
                                }
                            }

                            dt.Rows.Add(row);
                        }
                    }

                    foreach (DataColumn column in dt.Columns)
                    {
                        if (colCount == 1)
                        {
                            column.ColumnName = firstColumnName;
                        }
                        else
                        {
                            if (column.ColumnName.Contains(COLUMN_PREFIX))
                            {
                                lstRemoveColName.Add(column.ColumnName);
                            }
                        }
                        colCount++;
                    }

                    DataColumn dcCreatedBy = new DataColumn(HEADER_CREATED_BY, typeof(int));
                    DataColumn dcCreatedOn = new DataColumn(HEADER_CREATED_ON, typeof(System.DateTime));
                    dcCreatedBy.DefaultValue = UserID.ToString();
                    dcCreatedOn.DefaultValue = DateTime.Now;
                    dt.Columns.Add(dcCreatedBy);
                    dt.Columns.Add(dcCreatedOn);

                    for (int iIndex = 0; iIndex < lstRemoveColName.Count; iIndex++)
                    {
                        dt.Columns.Remove(lstRemoveColName[iIndex]);
                        dt.AcceptChanges();
                    }

                    for (int i = dt.Rows.Count - 1; i >= 0; i--)
                    {
                        if (dt.Rows[i][0] == DBNull.Value)
                            dt.Rows[i].Delete();
                    }
                    dt.AcceptChanges();
                    return dt;
                }
            }).ConfigureAwait(false);
        }
        catch
        {
            return null;
        }
        finally
        {
            dt = null;
        }
    }
    public static async Task<DataTable> GetDataTableFromExcelMasterKPIs(IUnitOfWork unitOfWork, string sheetName,
        string path, int UserID, string firstColumnName, int? moduleId, List<M_MasterKpis> kpiDetails)
    {
        DataTable dt = new DataTable();
        FileInfo excelFile = new FileInfo(path);
        List<string> lstRemoveColName = new List<string>();
        int colCount = 1;
        var masterKPIs = await unitOfWork.M_MasterKpisRepository.FindAllAsync(x => !x.IsDeleted && x.ModuleID == moduleId);
        try
        {
            return await Task.Run(() =>
            {
                using (ExcelPackage package = new ExcelPackage(excelFile))
                {
                    ExcelWorksheet worksheet = package.Workbook.Worksheets[sheetName];
                    if (worksheet.Dimension == null)
                    {
                        return dt;
                    }
                    dt.Columns.Add(firstColumnName);
                    dt.Columns.Add(HEADER_ID);
                    dt.Columns.Add(HEADER_HEADER); 
                    dt.Columns.Add(HEADER_QUARTER);
                    dt.Columns.Add(HEADER_MONTH);
                    dt.Columns.Add(HEADER_YEAR);
                    dt.Columns.Add(HEADER_HALF);
                    dt.Columns.Add(HEADER_IS_YTD);
                    dt.Columns.Add(HEADER_KPI_VALUE);
                    dt.Columns.Add(HEADER_IS_NUMERIC);
                    dt.Columns.Add(HEADER_MODULE_ID);
                    for (int i = 2; i <= worksheet.Dimension.End.Row; i++)
                    {
                        for (int j = 3; j <= worksheet.Dimension.End.Column; j++)
                        {
                            var columnHeader = worksheet.Cells[1, j]?.Value?.ToString().Trim();
                            var delimitingChars = new char[2];
                            delimitingChars[0] = '(';
                            delimitingChars[1] = ')';
                            var headers = columnHeader?.Split(delimitingChars).Where(x => x != "").Select(x => x.Trim()).ToArray();
                            int? year = null, month = null, half = null;
                            string quarter = null, dtHeader = null;
                            bool isYTD = false, isNumeric = false;
                            if (headers?.Length == 1)
                            {
                                var isFY = IsNumeric(headers.FirstOrDefault());
                                year = isFY ? Convert.ToInt32(headers.FirstOrDefault()) : null;
                                dtHeader = YEAR_LITERAL;
                            }
                            else
                            {
                                dtHeader = headers?[0];
                                if (headers != null && headers[1].Contains("-"))
                                {
                                    var headerValues = headers?[1].Split('-').Where(x => x != "").Select(x => x.Trim()).ToArray();
                                    var monthNumber = headerValues != null && headerValues?.Length > 0 ? GetMonthNumber(headerValues[0]?.ToLower()) : 0;
                                    month = (monthNumber != 0) ? monthNumber : null;
                                    if (headerValues?.Length > 0)
                                    {
                                        if (IsQuarter(headerValues[0]?.ToUpper()))
                                        {
                                            quarter = headerValues[0]?.ToUpper();
                                        }
                                        else
                                        {
                                            quarter = null;
                                        }
                                    }
                                    else
                                    {
                                        quarter = null;
                                    }

                                    if (headers[0].Contains(HALF_LITERAL))
                                    {
                                        if (IsNumeric(headerValues?[0]))
                                        {
                                            half = Convert.ToInt32(headerValues[0]);
                                        }
                                        else
                                        {
                                            half = null;
                                        }
                                    }
                                    else
                                    {
                                        half = null;
                                    }

                                    if (headerValues?.Length > 0)
                                    {
                                        if (IsNumeric(headerValues[1]))
                                        {
                                            year = Convert.ToInt32(headerValues[1]);
                                        }
                                        else
                                        {
                                            year = null;
                                        }
                                    }
                                    else
                                    {
                                        year = null;
                                    }
                                }
                                else
                                {
                                    year = IsNumeric(headers?[1]) ? Convert.ToInt32(headers?[1]) : null;
                                }
                            }
                            DataRow row = dt.NewRow();
                            var kpi = worksheet.Cells[i, 1].Value;
                            var id = worksheet.Cells[i, 2].Value;
                            var kpis = masterKPIs.FirstOrDefault(x => x.MasterKpiID == Convert.ToInt32(id));
                            var excelCell = kpis?.KpiInfo == TEXT_LITERAL ? worksheet.Cells[i, j].Text : worksheet.Cells[i, j].Value;
                            row[HEADER_KPI] = kpi;
                            row[HEADER_ID] = id;
                            row[HEADER_HEADER] = dtHeader;
                            row[HEADER_QUARTER] = quarter;
                            row[HEADER_MONTH] = month;
                            row[HEADER_YEAR] = year;
                            row[HEADER_HALF] = half;
                            row[HEADER_IS_YTD] = isYTD;
                            row[HEADER_MODULE_ID] = moduleId;
                            if (excelCell != null)
                            {
                                var format = worksheet.Cells[i, j].Style?.Numberformat?.Format;
                                if (format?.Contains(ROW_FORMAT_PERCENT) == true && kpis.KpiInfo != TEXT_LITERAL)
                                {
                                    if (decimal.TryParse(excelCell?.ToString(), out decimal updatedValue))
                                        excelCell = (updatedValue * 100).ToString();
                                    else if (excelCell?.ToString().Contains("E") == true && j != 1 && i > 4)
                                        excelCell = (decimal.Parse(excelCell.ToString(), NumberStyles.Float) * 100).ToString();
                                }
                                else if (kpis.KpiInfo == Constants.KpiInfoMultiple)
                                {
                                    excelCell = excelCell.ToString().Replace(Constants.KpiInfoMultiple, string.Empty);
                                }
                                if (IsNumeric(excelCell.ToString()))
                                {
                                    row[HEADER_KPI_VALUE] = string.IsNullOrEmpty(excelCell.ToString()) || string.IsNullOrWhiteSpace(excelCell.ToString()) ? null : decimal.Parse(excelCell.ToString(), System.Globalization.NumberStyles.Any).ToString();
                                    isNumeric = true;
                                }
                                else
                                {
                                    row[HEADER_KPI_VALUE] = string.IsNullOrEmpty(excelCell.ToString()) || string.IsNullOrWhiteSpace(excelCell.ToString()) ? null : excelCell.ToString().Trim();
                                }
                            }

                            row[HEADER_IS_NUMERIC] = isNumeric;
                            dt.Rows.Add(row);
                        }
                    }

                    foreach (DataColumn column in dt.Columns)
                    {
                        if (colCount == 1)
                        {
                            column.ColumnName = firstColumnName;
                        }
                        else
                        {
                            if (column.ColumnName.Contains(COLUMN_PREFIX))
                            {
                                lstRemoveColName.Add(column.ColumnName);
                            }
                        }
                        colCount++;
                    }

                    DataColumn dcCreatedBy = new DataColumn(HEADER_CREATED_BY, typeof(int));
                    DataColumn dcCreatedOn = new DataColumn(HEADER_CREATED_ON, typeof(System.DateTime));
                    dcCreatedBy.DefaultValue = UserID.ToString();
                    dcCreatedOn.DefaultValue = DateTime.Now;
                    dt.Columns.Add(dcCreatedBy);
                    dt.Columns.Add(dcCreatedOn);

                    for (int iIndex = 0; iIndex < lstRemoveColName.Count; iIndex++)
                    {
                        dt.Columns.Remove(lstRemoveColName[iIndex]);
                        dt.AcceptChanges();
                    }

                    for (int i = dt.Rows.Count - 1; i >= 0; i--)
                    {
                        if (dt.Rows[i][0] == DBNull.Value)
                            dt.Rows[i].Delete();
                    }
                    dt.AcceptChanges();
                    return dt;
                }
            }).ConfigureAwait(false);
        }
        catch
        {
            return null;
        }
        finally
        {
            dt = null;
        }
    }
    public static async Task InsertDataUsingSqlBulkCopy(string tableName, List<string> ExcelColHeaders, DataTable dtUpload, string connString)
    {

        using var connection = new SqlConnection(connString);
        if (connection.State == ConnectionState.Closed) connection.Open();
        using var sqlBulk = new SqlBulkCopy(connection)
        {
            DestinationTableName = tableName,
            BatchSize = dtUpload.Rows.Count,
            BulkCopyTimeout = 0
        };
        foreach (var columnName in ExcelColHeaders)
        {
            SqlBulkCopyColumnMapping mapID = new SqlBulkCopyColumnMapping(columnName, columnName);
            sqlBulk.ColumnMappings.Add(mapID);
        }
        await sqlBulk.WriteToServerAsync(dtUpload).ConfigureAwait(false);
    }
    public static async Task InsertDataQueryUsingSqlBulkCopy(string tableName, DataTable dataTable, string connString)
    {
        const int maxConnectionPoolSize = 10;
        int suggestedBatchSize = CalculateBatchSize(dataTable.Rows.Count, maxConnectionPoolSize);
        using var connection = new SqlConnection(connString);
        await connection.OpenAsync();
        var tasks = SplitDataTable(dataTable, suggestedBatchSize)?.Select(chunk => InsertDataChunk(tableName, chunk, connection));
        await Task.WhenAll(tasks);
    }

    private static int CalculateBatchSize(int rowCount, int maxPoolSize)
    {
        // Calculate a reasonable batch size to stay within the connection pool limit
        return Math.Max(1, rowCount / maxPoolSize);
    }
    private static async Task InsertDataChunk(string tableName, DataTable dataTable, SqlConnection connection)
    {
        using var bulkCopy = new SqlBulkCopy(connection);
        foreach (DataColumn column in dataTable.Columns)
        {
            bulkCopy.ColumnMappings.Add(column.ColumnName, column.ColumnName);
        }
        bulkCopy.BulkCopyTimeout = 120000;
        bulkCopy.BatchSize = dataTable.Rows.Count;
        bulkCopy.DestinationTableName = tableName;
        await bulkCopy.WriteToServerAsync(dataTable).ConfigureAwait(false);
    }
    private static List<DataTable> SplitDataTable(DataTable originalTable, int batchSize)
    {
        return originalTable.AsEnumerable()
            .Select((row, index) => new { Row = row, Index = index })
            .GroupBy(x => x.Index / batchSize)
            .Select(group => group.Select(x => x.Row).CopyToDataTable())
            .ToList();
    }

    public static bool IsNumeric(string text)
    {
        return double.TryParse(text, out double test);
    }

    public static string GetExcelColumnName(int columnNumber)
    {
        int dividend = columnNumber;
        string columnName = String.Empty;
        int modulo;

        while (dividend > 0)
        {
            modulo = (dividend - 1) % 26;
            columnName = Convert.ToChar(65 + modulo).ToString() + columnName;
            dividend = (dividend - modulo) / 26;
        }

        return columnName;
    }

    public static bool IsFileLocked(FileInfo file)
    {
        FileStream stream = null;
        try
        {
            stream = file.Open(FileMode.Open, FileAccess.Read, FileShare.None);
        }
        catch (IOException)
        {
            return true;
        }
        finally
        {
            stream?.Close();
        }
        return false;
    }

    public static bool IsQuarter(string excelValue)
    {
        return excelValue
      switch
        {
            "Q1" => true,
            "Q2" => true,
            "Q3" => true,
            "Q4" => true,
            _ => false
        };
    }
    public static int GetMonthNumber(string month)
    {
        return month
        switch
        {
            "jan" => 1,
            "feb" => 2,
            "mar" => 3,
            "apr" => 4,
            "may" => 5,
            "jun" => 6,
            "jul" => 7,
            "aug" => 8,
            "sep" => 9,
            "oct" => 10,
            "nov" => 11,
            "dec" => 12,
            _ => 0,
        };
    }
}

