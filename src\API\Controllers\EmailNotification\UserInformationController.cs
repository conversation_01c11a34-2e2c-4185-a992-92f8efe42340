﻿using API.Filters;
using API.Helpers;
using Contract.Account;
using EmailConfiguration.DTOs;
using EmailConfiguration.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;

namespace API.Controllers.EmailNotification
{
    [Authorize(Policy = AppSettings.DefaultAuthPolicy)]
    [Route("api/user-information")]
    [ApiController]
    public class UserInformationController : ControllerBase
    {
        private readonly IUserInformationService _userInfoService;
        private readonly ILogger<UserInformationController> _logger;
        private readonly IHelperService _helperService;

        public UserInformationController(
            IUserInformationService userInfoService,
            ILogger<UserInformationController> logger,
            IHelperService helperService)
        {
            _userInfoService = userInfoService;
            _logger = logger;
            _helperService = helperService;
        }

        /// <summary>
        /// Creates a new user information record with associated document types
        /// </summary>
        /// <param name="model">The data transfer object containing user information details</param>
        /// <returns>The ID of the newly created user information record</returns>
        [HttpPost("create-user")]
        [AuthorizeUserPermission(Features.RepositoryConfiguration, Actions.canAdd)]
        public async Task<IActionResult> CreateUserInformation([FromBody] CreateUserInformationDto model)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            _logger.LogInformation($"Creating new user information record for email: {model.Email}");

            int userId = _helperService.GetCurrentUserId(User);

            var result = await _userInfoService.CreateUserInformationWithDocumentTypesAsync(model, userId);

            return Ok(new { UserInformationId = result });
        }

        /// <summary>
        /// Gets a list of user information records with their associated document types
        /// </summary>
        /// <param name="companyId">The company ID to filter users by</param>
        /// <returns>List of user information records with document types</returns>
        [HttpGet("user-info-by-company/{companyId}")]
        [AuthorizeUserPermission(Features.RepositoryConfiguration, Actions.canView)]
        public async Task<IActionResult> GetUserInformationByCompany(int companyId)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            _logger.LogInformation($"Fetching user information for company ID: {companyId}");

            var result = await _userInfoService.GetUserInformationWithDocumentTypesAsync(companyId);

            return Ok(result);
        }

        /// <summary>
        /// Gets the list of user categories
        /// </summary>
        /// <returns>List of user categories</returns>
        [HttpGet("categories-and-doctypes/{companyId}")]
        [AuthorizeUserPermission(Features.RepositoryConfiguration, Actions.canView)]
        public async Task<IActionResult> GetCategoriesAndDocumentTypes(int companyId)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            _logger.LogInformation("Fetching user categories and document types");

            var categories = await _userInfoService.GetCategoriesAndDocumentTypes(companyId);

            return Ok(categories);
        }

        /// <summary>
        /// Gets a specific user information record by its ID with associated document types
        /// </summary>
        /// <param name="userInformationId">The ID of the user information to retrieve</param>
        /// <returns>The user information record with document types</returns>
        [HttpGet("user-info/{userInformationId}")]
        [AuthorizeUserPermission(Features.RepositoryConfiguration, Actions.canView)]
        public async Task<IActionResult> GetUserInformationById(int userInformationId)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            _logger.LogInformation($"Fetching user information for ID: {userInformationId}");

            var result = await _userInfoService.GetUserInformationByIdAsync(userInformationId);

            if (result == null)
            {
                return NotFound($"User information with ID {userInformationId} not found");
            }

            return Ok(result);
        }

        /// <summary>
        /// Updates an existing user information record with associated document types
        /// </summary>
        /// <param name="model">The data transfer object containing updated user information</param>
        /// <returns>Status of the update operation</returns>
        [HttpPut("update-user")]
        [AuthorizeUserPermission(Features.RepositoryConfiguration, Actions.canEdit)]
        public async Task<IActionResult> UpdateUserInformation([FromBody] UpdateUserInformationDto model)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            _logger.LogInformation($"Updating user information for ID: {model.UserInformationID}");

            int userId = _helperService.GetCurrentUserId(User);

            var result = await _userInfoService.UpdateUserInformationAsync(model, userId);

            if (!result.IsSuccess)
            {
                return NotFound(new { Success = false, result.Message });
            }
            return Ok(new { Success = true, result.Message });

        }

        /// <summary>
        /// Deletes a user information record and its related document mappings
        /// </summary>
        /// <param name="userInformationId">The ID of the user information to delete</param>
        /// <returns>Status of the delete operation</returns>
        [HttpDelete("delete-user/{userInformationId}")]
        [AuthorizeUserPermission(Features.RepositoryConfiguration, Actions.canEdit)]
        public async Task<IActionResult> DeleteUserInformation(int userInformationId)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            _logger.LogInformation($"Deleting user information for ID: {userInformationId}");

            int userId = _helperService.GetCurrentUserId(User);

            var result = await _userInfoService.DeleteUserInformationAsync(userInformationId, userId);

            if (!result.IsSuccess)
            {
                return NotFound(result);
            }
            return Ok(result);
        }
    }
}
