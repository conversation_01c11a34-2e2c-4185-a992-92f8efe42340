using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Contract.Account;
using DataAccessLayer.UnitOfWork;
using EmailConfiguration.DTOs;
using EmailConfiguration.Interfaces;
using EmailConfiguration.Helpers;
using Microsoft.Extensions.Logging;
using DataAccessLayer.Models.EmailNotifications;

namespace EmailConfiguration.Services
{
    public class EmailRemainderService : IEmailRemainderService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<EmailRemainderService> _logger;
        private const int PortFolioCompanyFeatureId = (int)Features.PortfolioCompany;

        public EmailRemainderService(
            IUnitOfWork unitOfWork,
            ILogger<EmailRemainderService> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Creates a new email reminder
        /// </summary>
        public async Task<Guid> CreateRemainderAsync(CreateEmailRemainderDto dto, int userId)
        {
            try
            {
                _logger.LogInformation("Creating email reminder for feature {FeatureId} by user {UserId}", dto.FeatureID, userId);

                var entityIds = EmailReminderHelper.ConvertIdsToCommaSeparatedString(dto.PortfolioCompanyIds);
                var documentTypeIds = EmailReminderHelper.ConvertIdsToCommaSeparatedString(dto.DocumentTypeIds);

                var emailReminder = EmailReminderAuditHelper.CreateEmailReminderWithAudit(
                    dto.FeatureID, entityIds, documentTypeIds, userId);

                await _unitOfWork.EmailReminderRepository.AddAsyn(emailReminder);
                await _unitOfWork.SaveAsync();

                _logger.LogInformation("Successfully created email reminder with ID {ReminderId}", emailReminder.ReminderId);
                return emailReminder.ReminderId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating email reminder for feature {FeatureId}", dto.FeatureID);
                throw;
            }
        }

        /// <summary>
        /// Gets an email reminder by ID
        /// </summary>
        public async Task<EmailRemainderDefaultDataResponseDto> GetRemainderDefaultsByIdAsync(Guid reminderId)
        {
            try
            {
                var emailReminder = await EmailReminderDataHelper.FindEmailReminderByIdAsync(_unitOfWork, reminderId);
                if (emailReminder == null)
                {
                    _logger.LogWarning("Email reminder with ID {ReminderId} not found", reminderId);
                    return null;
                }

                var companyIds = EmailReminderHelper.ParseCommaSeparatedIds(emailReminder.EntityIDs);
                var documentTypeIds = EmailReminderHelper.ParseCommaSeparatedIds(emailReminder.DocumentTypeIds);

                // Get portfolio companies and document types
                var portfolioCompanies = await EmailReminderDataHelper.GetPortfolioCompaniesByIdsAsync(_unitOfWork, companyIds);
                var documentTypes = await EmailReminderDataHelper.GetDocumentTypesByIdsAsync(_unitOfWork, documentTypeIds);

                // Get user information for companies and document types
                var userInfoList = await EmailReminderDataHelper.GetUserInformationAsync(_unitOfWork, companyIds, documentTypeIds, PortFolioCompanyFeatureId);

                // Get company email groups
                var groupInfoList = EmailReminderDataHelper.GetCompanyEmailGroups(_unitOfWork, companyIds);

                return EmailReminderMappingHelper.MapToDefaultDataResponseDto(emailReminder, userInfoList, groupInfoList, portfolioCompanies, documentTypes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving email reminder with ID {ReminderId}", reminderId);
                throw;
            }
        }

        /// <summary>
        /// Updates an existing email reminder
        /// </summary>
        public async Task<bool> UpdateRemainderAsync(Guid reminderId, UpdateEmailRemainderDto dto, int userId)
        {
            try
            {
                var emailReminder = await EmailReminderDataHelper.FindEmailReminderByIdAsync(_unitOfWork, reminderId);
                if (emailReminder == null)
                {
                    _logger.LogWarning("Email reminder with ID {ReminderId} not found for update", reminderId);
                    return false;
                }

                // Update EmailReminder properties using helper
                EmailReminderMappingHelper.UpdateEmailReminderFromDto(emailReminder, dto, userId);
                _unitOfWork.EmailReminderRepository.Update(emailReminder);

                // Update EmailReminderRecipients
                await UpdateEmailReminderRecipientsAsync(reminderId, dto.ToRecipients, dto.CcReciepients, userId);

                // Update EmailReminderConfig
                await UpdateEmailReminderConfigAsync(reminderId, dto, userId);

                await _unitOfWork.SaveAsync();

                _logger.LogInformation("Successfully updated email reminder with ID {ReminderId}", reminderId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating email reminder with ID {ReminderId}", reminderId);
                throw;
            }
        }

        /// <summary>
        /// Soft deletes an email reminder
        /// </summary>
        public async Task<bool> DeleteRemainderAsync(Guid reminderId, int userId)
        {
            try
            {
                var emailReminder = await EmailReminderDataHelper.FindEmailReminderByIdAsync(_unitOfWork, reminderId);
                if (emailReminder == null)
                {
                    _logger.LogWarning("Email reminder with ID {ReminderId} not found for deletion", reminderId);
                    return false;
                }

                // Soft delete using helper
                EmailReminderAuditHelper.SetSoftDeleteAuditFields(emailReminder, userId);
                _unitOfWork.EmailReminderRepository.Update(emailReminder);
                await _unitOfWork.SaveAsync();

                _logger.LogInformation("Successfully deleted email reminder with ID {ReminderId}", reminderId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting email reminder with ID {ReminderId}", reminderId);
                throw;
            }
        }



        /// <summary>
        /// Updates email reminder recipients
        /// </summary>
        private async Task UpdateEmailReminderRecipientsAsync(Guid reminderId, List<EmailRecipientsDto> toRecipients, List<EmailRecipientsDto> ccRecipients, int userId)
        {
            try
            {
                // Remove existing recipients for this reminder
                await EmailReminderRecipientHelper.RemoveExistingRecipientsAsync(_unitOfWork, reminderId);

                // Add TO recipients
                if (toRecipients?.Count > 0)
                {
                    var toRecipientEntities = EmailReminderRecipientHelper.CreateEmailReminderRecipients(
                        reminderId, toRecipients, RecipientTypes.To, userId);
                    await EmailReminderRecipientHelper.AddRecipientsAsync(_unitOfWork, toRecipientEntities);
                }

                // Add CC recipients
                if (ccRecipients?.Count > 0)
                {
                    var ccRecipientEntities = EmailReminderRecipientHelper.CreateEmailReminderRecipients(
                        reminderId, ccRecipients, RecipientTypes.Cc, userId);
                    await EmailReminderRecipientHelper.AddRecipientsAsync(_unitOfWork, ccRecipientEntities);
                }

                _logger.LogInformation("Updated email reminder recipients for reminder {ReminderId}", reminderId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating email reminder recipients for reminder {ReminderId}", reminderId);
                throw;
            }
        }

        /// <summary>
        /// Updates email reminder configuration
        /// </summary>
        private async Task UpdateEmailReminderConfigAsync(Guid reminderId, UpdateEmailRemainderDto dto, int userId)
        {
            try
            {
                // Find existing config by ReminderId directly
                var existingConfig = await EmailReminderDataHelper.FindEmailReminderConfigByReminderIdAsync(_unitOfWork, reminderId);

                int configId;
                if (existingConfig != null)
                {
                    // Update existing config using helper
                    EmailReminderMappingHelper.UpdateEmailReminderConfigFromDto(existingConfig, dto, userId);
                    _unitOfWork.EmailReminderConfigRepository.Update(existingConfig);
                    configId = existingConfig.Id;
                }
                else
                {
                    // Create new config if not found
                    configId = await CreateEmailReminderConfigAsync(reminderId, dto, userId);
                }

                // Update EmailReminderSchedule based on the config
                await UpdateEmailReminderScheduleAsync(configId, dto, userId);

                _logger.LogInformation("Updated email reminder configuration for reminder {ReminderId}", reminderId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating email reminder configuration for reminder {ReminderId}", reminderId);
                throw;
            }
        }

        /// <summary>
        /// Creates a new email reminder configuration
        /// </summary>
        private async Task<int> CreateEmailReminderConfigAsync(Guid reminderId, UpdateEmailRemainderDto dto, int userId)
        {
            var emailReminderConfig = EmailReminderAuditHelper.CreateEmailReminderConfigWithAudit(
                reminderId, dto.FrequencyType, dto.TotalRemindersPerCycle, dto.Remainder1Date,
                dto.Remainder2, dto.Remainder3, dto.Remainder4, dto.Remainder5, userId);

            await _unitOfWork.EmailReminderConfigRepository.AddAsyn(emailReminderConfig);
            await _unitOfWork.SaveAsync(); // Save to get the generated ID

            return emailReminderConfig.Id;
        }

        /// <summary>
        /// Updates email reminder schedule based on configuration
        /// </summary>
        private async Task UpdateEmailReminderScheduleAsync(int configId, UpdateEmailRemainderDto dto, int userId)
        {
            try
            {
                // Remove existing schedule entries for this config
                var existingSchedules = await EmailReminderDataHelper.GetEmailReminderSchedulesByConfigIdAsync(_unitOfWork, configId);

                foreach (var schedule in existingSchedules)
                {
                    EmailReminderAuditHelper.SetSoftDeleteAuditFields(schedule, userId);
                    _unitOfWork.EmailReminderScheduleRepository.Update(schedule);
                }

                // Create new schedule entries based on TotalRemindersPerCycle
                var reminderDates = EmailReminderHelper.CalculateReminderDates(dto.Remainder1Date, dto.Remainder2,
                    dto.Remainder3, dto.Remainder4, dto.Remainder5, dto.TotalRemindersPerCycle);

                for (int i = 0; i < dto.TotalRemindersPerCycle && i < reminderDates.Count; i++)
                {
                    var scheduleEntry = EmailReminderAuditHelper.CreateEmailReminderScheduleWithAudit(
                        configId, reminderDates[i], i + 1, userId);

                    await _unitOfWork.EmailReminderScheduleRepository.AddAsyn(scheduleEntry);
                }

                _logger.LogInformation("Updated email reminder schedule for config {ConfigId} with {Count} entries",
                    configId, dto.TotalRemindersPerCycle);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating email reminder schedule for config {ConfigId}", configId);
                throw;
            }
        }

        /// <summary>
        /// Gets pending email reminders for the specified date
        /// </summary>
        public async Task<List<EmailReminderNotificationDto>> GetPendingRemindersAsync(DateTime targetDate)
        {
            try
            {
                _logger.LogInformation("Getting pending email reminders for date {Year}-{Month}-{Day}",
                    targetDate.Year, targetDate.Month, targetDate.Day);

                var pendingSchedules = await EmailReminderDataHelper.GetPendingSchedulesAsync(_unitOfWork, targetDate);
                var reminderNotifications = new List<EmailReminderNotificationDto>();

                foreach (var schedule in pendingSchedules)
                {
                    // Get reminder config to get ReminderId
                    var config = await EmailReminderDataHelper.FindEmailReminderConfigByIdAsync(_unitOfWork, schedule.ReminderConfigId);
                    if (config == null) continue;

                    // Get email reminder details
                    var emailReminder = await EmailReminderDataHelper.FindEmailReminderByIdAsync(_unitOfWork, config.ReminderId);
                    if (emailReminder == null || !emailReminder.IsActive) continue;

                    // Get recipients
                    var recipients = await EmailReminderDataHelper.GetEmailReminderRecipientsAsync(_unitOfWork, config.ReminderId);

                    var notification = EmailReminderMappingHelper.MapToNotificationDto(schedule, config, emailReminder, recipients);
                    reminderNotifications.Add(notification);
                }

                _logger.LogInformation("Found {Count} pending email reminders for date {TargetDate}",
                    reminderNotifications.Count, targetDate.Date);

                return reminderNotifications;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting pending email reminders for date {TargetDate}", targetDate.Date);
                throw;
            }
        }

        /// <summary>
        /// Updates email reminder schedule status
        /// </summary>
        public async Task<bool> UpdateScheduleStatusAsync(int scheduleId, ReminderStatus status, string error = null)
        {
            try
            {
                var schedule = await EmailReminderDataHelper.FindEmailReminderScheduleByIdAsync(_unitOfWork, scheduleId);
                if (schedule == null)
                {
                    _logger.LogWarning("Email reminder schedule with ID {ScheduleId} not found", scheduleId);
                    return false;
                }

                schedule.Status = status;
                schedule.Error = error;
                schedule.ModifiedOn = DateTime.UtcNow;

                if (status == ReminderStatus.Sent) // Sent
                {
                    schedule.SentDate = DateTime.UtcNow;

                    // Check if this is the last schedule in the cycle and create next cycle if needed
                    var isLastInCycle = await EmailReminderDataHelper.IsLastScheduleInCycleAsync(_unitOfWork, scheduleId);
                    if (isLastInCycle)
                    {
                        await CreateNextCycleSchedulesAsync(schedule.ReminderConfigId, 1); // Using system user ID for auto-generated cycles
                    }
                }

                _unitOfWork.EmailReminderScheduleRepository.Update(schedule);
                await _unitOfWork.SaveAsync();

                _logger.LogInformation("Updated email reminder schedule {ScheduleId} status to {Status}",
                    scheduleId, status);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating email reminder schedule {ScheduleId} status", scheduleId);
                throw;
            }
        }

        /// <summary>
        /// Skips pending reminders in current cycle and creates next cycle schedules
        /// </summary>
        public async Task<bool> SkipReminderCycleAsync(Guid reminderId, int userId)
        {
            try
            {
                _logger.LogInformation("Skipping reminder cycle for reminder {ReminderId} by user {UserId}", reminderId, userId);

                // Get pending schedules for this reminder
                var pendingSchedules = await EmailReminderDataHelper.GetPendingSchedulesByReminderIdAsync(_unitOfWork, reminderId);

                if (pendingSchedules.Count == 0)
                {
                    _logger.LogWarning("No pending schedules found for reminder {ReminderId}", reminderId);
                    return false;
                }

                // Update all pending schedules to skipped status (3)
                foreach (var schedule in pendingSchedules)
                {
                    schedule.Status = ReminderStatus.Skipped; // Skipped
                    EmailReminderAuditHelper.SetUpdateAuditFields(schedule, userId);
                    _unitOfWork.EmailReminderScheduleRepository.Update(schedule);
                }

                // Get the reminder config to create next cycle
                var config = await EmailReminderDataHelper.FindEmailReminderConfigByReminderIdAsync(_unitOfWork, reminderId);
                if (config == null)
                {
                    _logger.LogError("Email reminder config not found for reminder {ReminderId}", reminderId);
                    return false;
                }

                // Create next cycle schedules
                await CreateNextCycleSchedulesAsync(config.Id, userId);

                await _unitOfWork.SaveAsync();

                _logger.LogInformation("Successfully skipped reminder cycle for reminder {ReminderId} and created next cycle", reminderId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error skipping reminder cycle for reminder {ReminderId}", reminderId);
                throw;
            }
        }

        /// <summary>
        /// Creates next cycle schedules for a reminder config
        /// </summary>
        private async Task CreateNextCycleSchedulesAsync(int configId, int userId)
        {
            try
            {
                var config = await EmailReminderDataHelper.FindEmailReminderConfigByIdAsync(_unitOfWork, configId);
                if (config == null)
                {
                    _logger.LogError("Email reminder config with ID {ConfigId} not found", configId);
                    return;
                }

                // Update the config's Remainder1Date to the next cycle start date
                var nextCycleStartDate = EmailReminderHelper.CalculateNextCycleStartDate(config.Remainder1Date, config.FrequencyType);
                config.Remainder1Date = nextCycleStartDate;
                EmailReminderAuditHelper.SetUpdateAuditFields(config, userId);
                _unitOfWork.EmailReminderConfigRepository.Update(config);

                // Create next cycle schedule entries
                var nextCycleSchedules = EmailReminderHelper.CreateNextCycleSchedule(config, userId);

                var allschedules = await _unitOfWork.EmailReminderScheduleRepository.GetManyAsync(i => i.ReminderConfigId == config.Id && i.IsActive);
                foreach(var schedule in allschedules)
                {
                    schedule.IsActive = false;
                    EmailReminderAuditHelper.SetUpdateAuditFields(schedule, userId);
                }
                _unitOfWork.EmailReminderScheduleRepository.UpdateBulk(allschedules.ToList());
                foreach (var schedule in nextCycleSchedules)
                {
                    await _unitOfWork.EmailReminderScheduleRepository.AddAsyn(schedule);
                }

                _logger.LogInformation("Created {Count} schedule entries for next cycle of config {ConfigId}",
                    nextCycleSchedules.Count, configId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating next cycle schedules for config {ConfigId}", configId);
                throw;
            }
        }

        /// <summary>
        /// Gets a list of all email reminders with associated company names and document types
        /// </summary>
        /// <returns>List of email reminder details</returns>
        public async Task<List<EmailReminderListResponseDto>> GetEmailReminderListAsync()
        {
            try
            {
                _logger.LogInformation("Retrieving all valid email reminders with non-empty Subject and EmailBody");

                // Get all non-deleted email reminders sorted by modified date
                var emailReminders = await EmailReminderDataHelper.GetAllValidEmailRemindersAsync(_unitOfWork);

                if (emailReminders.Count == 0)
                {
                    _logger.LogInformation("No email reminders found");
                    return new List<EmailReminderListResponseDto>();
                }

                // Extract all unique company and document type IDs
                var allCompanyIds = EmailReminderHelper.ExtractUniqueIdsFromReminders(emailReminders, r => r.EntityIDs);
                var allDocTypeIds = EmailReminderHelper.ExtractUniqueIdsFromReminders(emailReminders, r => r.DocumentTypeIds);

                // Get company and document type details
                var companies = await EmailReminderDataHelper.GetPortfolioCompaniesByIdsAsync(_unitOfWork, allCompanyIds);
                var documentTypes = await EmailReminderDataHelper.GetDocumentTypesByIdsAsync(_unitOfWork, allDocTypeIds);

                // Map data to response DTOs using helper
                var result = emailReminders.Select(reminder =>
                    EmailReminderMappingHelper.MapToListResponseDto(reminder, companies, documentTypes))
                    .ToList();

                _logger.LogInformation("Retrieved {Count} email reminders", result.Count);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving email reminder list");
                throw;
            }
        }

        /// <summary>
        /// Gets email reminder details including TO, CC recipients, Subject and MessageBody
        /// </summary>
        public async Task<EmailReminderDetailsDto> GetEmailReminderDetailsAsync(Guid reminderId)
        {
            try
            {
                _logger.LogInformation("Retrieving email reminder details for ID {ReminderId}", reminderId);

                // Get the email reminder
                var emailReminder = await EmailReminderDataHelper.FindEmailReminderByIdAsync(_unitOfWork, reminderId);
                if (emailReminder == null)
                {
                    _logger.LogWarning("Email reminder with ID {ReminderId} not found", reminderId);
                    return null;
                }               
                // Get all recipients for this reminder
                var recipients = await EmailReminderDataHelper.GetEmailReminderRecipientsAsync(_unitOfWork, reminderId);

                // Get group details for group recipients
                var groupIds = EmailReminderRecipientHelper.GetGroupIdsFromRecipients(recipients);
                var groups = await EmailReminderDataHelper.GetEmailNotificationGroupsAsync(_unitOfWork, groupIds);

                // Get reminder config and schedule statistics
                var config = await EmailReminderDataHelper.FindEmailReminderConfigByReminderIdAsync(_unitOfWork, reminderId);
                int totalSent = 0;
                int errorandSkipedCount = 0;
                DateTime? lastSentDate = null;
                DateTime? nextScheduledDate = null;

                if (config != null)
                {
                    var (sent, errorandskipcount, lastSent, nextScheduled) = await EmailReminderDataHelper.GetScheduleStatisticsAsync(_unitOfWork, config.Id);
                    totalSent = sent;
                    lastSentDate = lastSent;
                    nextScheduledDate = nextScheduled;
                    errorandSkipedCount = errorandskipcount;
                }

                // Map to details DTO using helper
                var result = EmailReminderMappingHelper.MapToDetailsDto(emailReminder, recipients, groups, config, totalSent, lastSentDate, nextScheduledDate, errorandSkipedCount);

                _logger.LogInformation("Successfully retrieved email reminder details for ID {ReminderId}", reminderId);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving email reminder details for ID {ReminderId}", reminderId);
                throw;
            }
        }

        public async Task<UpdateEmailRemainderDto> GetEmailRemainderDetailsForEditAsync(Guid reminderId)
        {
            try
            {
                _logger.LogInformation("Retrieving email reminder details for ID {ReminderId}", reminderId);

                // Get the email reminder
                var emailReminder = await EmailReminderDataHelper.FindEmailReminderByIdAsync(_unitOfWork, reminderId);
                if (emailReminder == null)
                {
                    _logger.LogWarning("Email reminder with ID {ReminderId} not found", reminderId);
                    return null;
                }
                // Extract all unique company and document type IDs
                var allCompanyIds = EmailReminderHelper.ExtractUniqueIdsFromReminders(new List<EmailReminder>() { emailReminder }, r => r.EntityIDs);
                var allDocTypeIds = EmailReminderHelper.ExtractUniqueIdsFromReminders(new List<EmailReminder>() { emailReminder }, r => r.DocumentTypeIds);

                // Get company and document type details
                var companies = await EmailReminderDataHelper.GetPortfolioCompaniesByIdsAsync(_unitOfWork, allCompanyIds);
                var documentTypes = await EmailReminderDataHelper.GetDocumentTypesByIdsAsync(_unitOfWork, allDocTypeIds);
                // Get all recipients for this reminder
                var recipients = await EmailReminderDataHelper.GetEmailReminderRecipientsAsync(_unitOfWork, reminderId);

                // Get group details for group recipients
                var groupIds = EmailReminderRecipientHelper.GetGroupIdsFromRecipients(recipients);
                var groups = await EmailReminderDataHelper.GetEmailNotificationGroupsAsync(_unitOfWork, groupIds);

                // Get reminder config and schedule statistics
                var config = await EmailReminderDataHelper.FindEmailReminderConfigByReminderIdAsync(_unitOfWork, reminderId);
               
                // Map to details DTO using helper
                var result = EmailReminderMappingHelper.MaptoUpdateEmailRemainderDto(emailReminder, companies , documentTypes ,recipients, groups, config);

                _logger.LogInformation("Successfully retrieved email reminder details for ID {ReminderId}", reminderId);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving email reminder details for ID {ReminderId}", reminderId);
                throw;
            }

        }
    }
}



