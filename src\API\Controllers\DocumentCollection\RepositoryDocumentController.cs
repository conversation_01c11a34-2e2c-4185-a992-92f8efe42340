﻿using API.Filters.CustomAuthorization;
using API.Helpers;
using Contract.Account;
using Contract.Utility;
using DocumentCollection.DTOs;
using DocumentCollection.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;

namespace API.Controllers.DocumentCollection
{
    [Authorize(Policy = AppSettings.DefaultAuthPolicy)]
    [Route("api")]
    public class RepositoryDocumentController : ControllerBase
    {
        private readonly IDocumentRepositoryService repository;
        private readonly IRepositoryConfigurationService repoconfig;
        private readonly IInjectedParameters InjectedParameters;
        private readonly ILogger<RepositoryDocumentController> logger;
        private readonly IHelperService helperService;

        public RepositoryDocumentController(IDocumentRepositoryService _repository, IRepositoryConfigurationService _repoconfig, IInjectedParameters _injectedParameters, IHelperService helperService, ILogger<RepositoryDocumentController> _logger)
        {
            this.repository = _repository;
            this.repoconfig = _repoconfig;
            this.InjectedParameters = _injectedParameters;
            this.helperService = helperService;
            logger = _logger;
        }

        /// <summary>
        /// Fetches the available document collections.
        /// </summary>
        /// <remarks>
        /// This endpoint retrieves a list of document collections available in the system.
        /// </remarks>
        /// <returns>A list of <see cref="DocumentCollectionDto"/> representing the document collections.</returns>
        [UserFeatureAuthorize((int)Features.PortfolioCompany)]
        [HttpGet("get-repository-structure/{encryptedCompanyId}/{featureId}")]
        public async Task<IActionResult> GetFolderConfig(string encryptedCompanyId, int featureId)
        {
            var decryptedPortfolioCompanyId = InjectedParameters.Encryption.Decrypt(encryptedCompanyId);

            if (string.IsNullOrEmpty(decryptedPortfolioCompanyId) || !int.TryParse(decryptedPortfolioCompanyId, out int companyId))
            {
                return BadRequest();
            }
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            logger.LogInformation("Fetching repository configuration for company");
            return Ok(await repository.GetRepositoryData(companyId, featureId));
        }

        /// <summary>
        /// Uploads multiple documents to the repository
        /// </summary>
        /// <param name="encryptedCompanyId">Encrypted company ID</param>
        /// <param name="files">Files to upload</param>
        /// <param name="uploadRequest">Document metadata and folder mappings</param>
        /// <returns>Result of document uploads</returns>
        [UserFeatureAuthorize((int)Features.PortfolioCompany)]
        [HttpPost("upload-documents/{encryptedCompanyId}")]     
        public async Task<IActionResult> UploadDocuments(
            string encryptedCompanyId,
            DocumentUploadRequestDto uploadRequest)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var decryptedPortfolioCompanyId = InjectedParameters.Encryption.Decrypt(encryptedCompanyId);

            if (string.IsNullOrEmpty(decryptedPortfolioCompanyId) || !int.TryParse(decryptedPortfolioCompanyId, out int companyId))
            {
                return BadRequest("Invalid company ID");
            }

            if (uploadRequest == null || uploadRequest.Files == null || uploadRequest.Files.Count == 0)
            {
                return BadRequest("No files were uploaded");
            }
            uploadRequest.CreatedBy = helperService.GetCurrentUserId(User);
            logger.LogInformation($"Uploading {uploadRequest.Files.Count} documents for company {companyId}");
            var validationMessage = repository.ValidateUploadedFiles(uploadRequest);
            if (!string.IsNullOrEmpty(validationMessage))
            {
                var response = new ResponseDto<string> { IsSuccess = false, Message = validationMessage };
                return Ok(response);
            }
            var result = await repository.UploadDocuments(companyId, uploadRequest);
            return Ok(result);
        }
        /// <summary>
        /// get files for the selected folder path for the specific portfolio company.
        /// </summary>
        /// <param name="encryptedCompanyId"></param>
        /// <param name="retrieveRequest"></param>
        /// <returns></returns>
        [UserFeatureAuthorize((int)Features.PortfolioCompany)]
        [HttpPost("get-documents/{encryptedCompanyId}")]
        public async Task<IActionResult> GetFilesDataForFolder(string encryptedCompanyId, DocumentRetrieveRequest retrieveRequest)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var decryptedPortfolioCompanyId = InjectedParameters.Encryption.Decrypt(encryptedCompanyId);

            if (string.IsNullOrEmpty(decryptedPortfolioCompanyId) || !int.TryParse(decryptedPortfolioCompanyId, out int companyId))
            {
                return BadRequest("Invalid company ID");
            }

            logger.LogInformation($"Retrieving documents for company {companyId} and {retrieveRequest.Path}");

            var result = await repository.GetDocuments(companyId, retrieveRequest.Path, retrieveRequest.FeatureID);
            return Ok(result);
        }

        /// <summary>
        /// Deletes specified documents from the repository for a portfolio company
        /// </summary>
        /// <param name="encryptedCompanyId">Encrypted portfolio company ID</param>
        /// <param name="deleteRequest">List of document IDs to delete</param>
        /// <returns>Result of document deletion operation</returns>
        [UserFeatureAuthorize((int)Features.PortfolioCompany)]
        [HttpPost("delete-documents/{encryptedCompanyId}")]
        public async Task<IActionResult> DeleteDocuments(string encryptedCompanyId, DocumentDeleteRequestDto deleteRequest)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var decryptedPortfolioCompanyId = InjectedParameters.Encryption.Decrypt(encryptedCompanyId);

            if (string.IsNullOrEmpty(decryptedPortfolioCompanyId) || !int.TryParse(decryptedPortfolioCompanyId, out int companyId))
            {
                return BadRequest("Invalid company ID");
            }

            if (deleteRequest == null || deleteRequest.DocumentIds == null || deleteRequest.DocumentIds.Length == 0)
            {
                return BadRequest("No documents specified for deletion");
            }

            deleteRequest.ModifiedBy = helperService.GetCurrentUserId(User);
            logger.LogInformation($"Deleting {deleteRequest.DocumentIds.Length} documents for company {companyId}");

            var result = await repository.DeleteDocuments(companyId, deleteRequest);
            return Ok(result);
        }
        [UserFeatureAuthorize((int)Features.PortfolioCompany)]
        [HttpGet("download-document/{encryptedCompanyId}/{documentId}")]
        public async Task<IActionResult> DownloadDocument(string encryptedCompanyId, string documentId, [FromQuery] string folderPath)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            var decryptedPortfolioCompanyId = InjectedParameters.Encryption.Decrypt(encryptedCompanyId);

            if (string.IsNullOrEmpty(decryptedPortfolioCompanyId) || !int.TryParse(decryptedPortfolioCompanyId, out int companyId))
                return BadRequest("Invalid company ID");

            if (string.IsNullOrEmpty(documentId))
                return BadRequest("Invalid document ID");

            var result = await repository.DownloadDocument(companyId, documentId, folderPath);
            if (!result.IsSuccess || result.FileStream == null)
                return NotFound("File not found or could not be downloaded.");

            // Set content type and file name
            return File(result.FileStream, result.ContentType ?? "application/octet-stream", result.FileName ?? "document");
        }
    }
}
