using EmailConfiguration.Services;
using EmailConfiguration.DTOs;
using EmailConfiguration.Helpers;
using DataAccessLayer.UnitOfWork;
using DataAccessLayer.GenericRepository;
using DataAccessLayer.Models.EmailNotifications;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using Contract.Account;

namespace EmailConfiguration.UnitTest.Services
{
    /// <summary>
    /// Basic unit tests for EmailRemainderService that compile and run successfully
    /// </summary>
    public class EmailRemainderServiceBasicTests
    {
        private readonly Mock<IUnitOfWork> _unitOfWorkMock;
        private readonly Mock<ILogger<EmailRemainderService>> _loggerMock;
        private readonly EmailRemainderService _emailRemainderService;

        public EmailRemainderServiceBasicTests()
        {
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _loggerMock = new Mock<ILogger<EmailRemainderService>>();

            // Setup basic repository mocks
            SetupRepositoryMocks();

            _emailRemainderService = new EmailRemainderService(_unitOfWorkMock.Object, _loggerMock.Object);
        }

        private void SetupRepositoryMocks()
        {
            _unitOfWorkMock.Setup(u => u.EmailReminderRepository).Returns(new Mock<IGenericRepository<EmailReminder>>().Object);
            _unitOfWorkMock.Setup(u => u.EmailReminderConfigRepository).Returns(new Mock<IGenericRepository<EmailReminderConfig>>().Object);
            _unitOfWorkMock.Setup(u => u.EmailReminderScheduleRepository).Returns(new Mock<IGenericRepository<EmailReminderSchedule>>().Object);
            _unitOfWorkMock.Setup(u => u.EmailReminderRecipientsRepository).Returns(new Mock<IGenericRepository<EmailReminderRecipients>>().Object);
            _unitOfWorkMock.Setup(u => u.PortfolioCompanyDetailRepository).Returns(new Mock<IGenericRepository<DataAccessLayer.DBModel.PortfolioCompanyDetails>>().Object);
            _unitOfWorkMock.Setup(u => u.DataExtractionTypesRepository).Returns(new Mock<IGenericRepository<DataAccessLayer.Models.DataExtraction.DataExtractionTypes>>().Object);
            _unitOfWorkMock.Setup(u => u.UserInfoRepository).Returns(new Mock<IGenericRepository<UserInformation>>().Object);
            _unitOfWorkMock.Setup(u => u.UserDocumentsRepository).Returns(new Mock<IGenericRepository<User_Documents>>().Object);
            _unitOfWorkMock.Setup(u => u.EmailNotificationGroupRepository).Returns(new Mock<IGenericRepository<EmailNotificationGroup>>().Object);
            _unitOfWorkMock.Setup(u => u.CompanyEmailGroupRepository).Returns(new Mock<IGenericRepository<CompanyEmailGroup>>().Object);
        }

        [Fact]
        public async Task CreateRemainderAsync_ValidDto_CallsRepositoryAndSave()
        {
            // Arrange
            var dto = new CreateEmailRemainderDto
            {
                FeatureID = (int)Features.PortfolioCompany,
                PortfolioCompanyIds = new List<int> { 1, 2, 3 },
                DocumentTypeIds = new List<int> { 10, 20, 30 }
            };
            var userId = 123;

            var emailReminderRepoMock = new Mock<IGenericRepository<EmailReminder>>();
            var capturedEmailReminder = new EmailReminder();

            emailReminderRepoMock.Setup(r => r.AddAsyn(It.IsAny<EmailReminder>()))
                .Callback<EmailReminder>(er => capturedEmailReminder = er)
                .ReturnsAsync((EmailReminder er) => er);

            _unitOfWorkMock.Setup(u => u.EmailReminderRepository).Returns(emailReminderRepoMock.Object);
            _unitOfWorkMock.Setup(u => u.SaveAsync()).ReturnsAsync(1);

            // Act
            var result = await _emailRemainderService.CreateRemainderAsync(dto, userId);

            // Assert
            Assert.Equal(capturedEmailReminder.ReminderId, result);
            Assert.NotEqual(Guid.Empty, result);
            emailReminderRepoMock.Verify(r => r.AddAsyn(It.IsAny<EmailReminder>()), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Fact]
        public async Task CreateRemainderAsync_ExceptionThrown_LogsError()
        {
            // Arrange
            var dto = new CreateEmailRemainderDto
            {
                FeatureID = (int)Features.PortfolioCompany,
                PortfolioCompanyIds = new List<int> { 1, 2, 3 },
                DocumentTypeIds = new List<int> { 10, 20, 30 }
            };
            var userId = 123;

            var emailReminderRepoMock = new Mock<IGenericRepository<EmailReminder>>();
            emailReminderRepoMock.Setup(r => r.AddAsyn(It.IsAny<EmailReminder>()))
                .ThrowsAsync(new Exception("Database error"));

            _unitOfWorkMock.Setup(u => u.EmailReminderRepository).Returns(emailReminderRepoMock.Object);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<Exception>(() =>
                _emailRemainderService.CreateRemainderAsync(dto, userId));

            Assert.Equal("Database error", exception.Message);
        }

        [Fact]
        public async Task GetRemainderDefaultsByIdAsync_ReminderNotFound_ReturnsNull()
        {
            // Arrange
            var reminderId = Guid.NewGuid();

            var emailReminderRepoMock = new Mock<IGenericRepository<EmailReminder>>();
            emailReminderRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<System.Linq.Expressions.Expression<Func<EmailReminder, bool>>>()))
                .ReturnsAsync((EmailReminder?)null);

            _unitOfWorkMock.Setup(u => u.EmailReminderRepository).Returns(emailReminderRepoMock.Object);

            // Act
            var result = await _emailRemainderService.GetRemainderDefaultsByIdAsync(reminderId);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task DeleteRemainderAsync_ReminderNotFound_ReturnsFalse()
        {
            // Arrange
            var reminderId = Guid.NewGuid();
            var userId = 123;

            var emailReminderRepoMock = new Mock<IGenericRepository<EmailReminder>>();
            emailReminderRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<System.Linq.Expressions.Expression<Func<EmailReminder, bool>>>()))
                .ReturnsAsync((EmailReminder?)null);

            _unitOfWorkMock.Setup(u => u.EmailReminderRepository).Returns(emailReminderRepoMock.Object);

            // Act
            var result = await _emailRemainderService.DeleteRemainderAsync(reminderId, userId);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task DeleteRemainderAsync_ValidId_ReturnsTrue()
        {
            // Arrange
            var reminderId = Guid.NewGuid();
            var userId = 123;

            var emailReminder = new EmailReminder { ReminderId = reminderId, IsDeleted = false };

            var emailReminderRepoMock = new Mock<IGenericRepository<EmailReminder>>();
            emailReminderRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<System.Linq.Expressions.Expression<Func<EmailReminder, bool>>>()))
                .ReturnsAsync(emailReminder);

            _unitOfWorkMock.Setup(u => u.EmailReminderRepository).Returns(emailReminderRepoMock.Object);
            _unitOfWorkMock.Setup(u => u.SaveAsync()).ReturnsAsync(1);

            // Act
            var result = await _emailRemainderService.DeleteRemainderAsync(reminderId, userId);

            // Assert
            Assert.True(result);
            Assert.True(emailReminder.IsDeleted);
            Assert.Equal(userId, emailReminder.ModifiedBy);
            emailReminderRepoMock.Verify(r => r.Update(It.IsAny<EmailReminder>()), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Fact]
        public async Task UpdateScheduleStatusAsync_ScheduleNotFound_ReturnsFalse()
        {
            // Arrange
            var scheduleId = 1;
            var status = 1;

            var emailReminderScheduleRepoMock = new Mock<IGenericRepository<EmailReminderSchedule>>();
            emailReminderScheduleRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<System.Linq.Expressions.Expression<Func<EmailReminderSchedule, bool>>>()))
                .ReturnsAsync((EmailReminderSchedule?)null);

            _unitOfWorkMock.Setup(u => u.EmailReminderScheduleRepository).Returns(emailReminderScheduleRepoMock.Object);

            // Act
            var result = await _emailRemainderService.UpdateScheduleStatusAsync(scheduleId, ReminderStatus.Sent);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task UpdateScheduleStatusAsync_ValidScheduleId_ReturnsTrue()
        {
            // Arrange
            var scheduleId = 1;
            var error = "No error";

            var schedule = new EmailReminderSchedule { Id = scheduleId };

            var emailReminderScheduleRepoMock = new Mock<IGenericRepository<EmailReminderSchedule>>();
            emailReminderScheduleRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<System.Linq.Expressions.Expression<Func<EmailReminderSchedule, bool>>>()))
                .ReturnsAsync(schedule);

            _unitOfWorkMock.Setup(u => u.EmailReminderScheduleRepository).Returns(emailReminderScheduleRepoMock.Object);
            _unitOfWorkMock.Setup(u => u.SaveAsync()).ReturnsAsync(1);

            // Act
            var result = await _emailRemainderService.UpdateScheduleStatusAsync(scheduleId, ReminderStatus.Sent, error);

            // Assert
            Assert.True(result);
            Assert.Equal(ReminderStatus.Sent, schedule.Status);
            Assert.Equal(error, schedule.Error);
            Assert.NotNull(schedule.SentDate);
            emailReminderScheduleRepoMock.Verify(r => r.Update(It.IsAny<EmailReminderSchedule>()), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Fact]
        public async Task GetPendingRemindersAsync_NoSchedules_ReturnsEmptyList()
        {
            // Arrange
            var targetDate = new DateTime(2024, 1, 15);

            var emailReminderScheduleRepoMock = new Mock<IGenericRepository<EmailReminderSchedule>>();
            emailReminderScheduleRepoMock.Setup(r => r.GetManyAsync(It.IsAny<Func<EmailReminderSchedule, bool>>()))
                .ReturnsAsync(new List<EmailReminderSchedule>());

            _unitOfWorkMock.Setup(u => u.EmailReminderScheduleRepository).Returns(emailReminderScheduleRepoMock.Object);

            // Act
            var result = await _emailRemainderService.GetPendingRemindersAsync(targetDate);

            // Assert
            Assert.Empty(result);
        }

        [Fact]
        public async Task GetEmailReminderListAsync_NoReminders_ReturnsEmptyList()
        {
            // Arrange
            var emailReminderRepoMock = new Mock<IGenericRepository<EmailReminder>>();
            emailReminderRepoMock.Setup(r => r.FindAllAsync(It.IsAny<System.Linq.Expressions.Expression<Func<EmailReminder, bool>>>()))
                .ReturnsAsync(new List<EmailReminder>());

            _unitOfWorkMock.Setup(u => u.EmailReminderRepository).Returns(emailReminderRepoMock.Object);

            // Act
            var result = await _emailRemainderService.GetEmailReminderListAsync();

            // Assert
            Assert.Empty(result);
        }

        [Fact]
        public async Task GetEmailReminderDetailsAsync_ReminderNotFound_ReturnsNull()
        {
            // Arrange
            var reminderId = Guid.NewGuid();

            var emailReminderRepoMock = new Mock<IGenericRepository<EmailReminder>>();
            emailReminderRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<System.Linq.Expressions.Expression<Func<EmailReminder, bool>>>()))
                .ReturnsAsync((EmailReminder?)null);

            _unitOfWorkMock.Setup(u => u.EmailReminderRepository).Returns(emailReminderRepoMock.Object);

            // Act
            var result = await _emailRemainderService.GetEmailReminderDetailsAsync(reminderId);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task GetEmailReminderDetailsAsync_ValidReminder_WithoutConfig_ReturnsDetailsWithDefaultValues()
        {
            // Arrange
            var reminderId = Guid.NewGuid();
            var emailReminder = new EmailReminder
            {
                ReminderId = reminderId,
                Subject = "Test Subject",
                EmailBody = "Test Body",
                IsActive = true
            };

            var recipients = new List<EmailReminderRecipients>
            {
                new EmailReminderRecipients { ReminderId = reminderId, RecipientType = RecipientTypes.To, RecipientId = 1 },
                new EmailReminderRecipients { ReminderId = reminderId, RecipientType = RecipientTypes.Cc, RecipientId = 2 }
            };

            var groups = new List<EmailNotificationGroup>();

            var emailReminderRepoMock = new Mock<IGenericRepository<EmailReminder>>();
            emailReminderRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<System.Linq.Expressions.Expression<Func<EmailReminder, bool>>>()))
                .ReturnsAsync(emailReminder);

            var emailReminderRecipientsRepoMock = new Mock<IGenericRepository<EmailReminderRecipients>>();
            emailReminderRecipientsRepoMock.Setup(r => r.GetManyAsync(It.IsAny<Func<EmailReminderRecipients, bool>>()))
                .ReturnsAsync(recipients);

            var emailNotificationGroupRepoMock = new Mock<IGenericRepository<EmailNotificationGroup>>();
            emailNotificationGroupRepoMock.Setup(r => r.GetManyAsync(It.IsAny<Func<EmailNotificationGroup, bool>>()))
                .ReturnsAsync(groups);

            var emailReminderConfigRepoMock = new Mock<IGenericRepository<EmailReminderConfig?>>();
            emailReminderConfigRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<System.Linq.Expressions.Expression<Func<EmailReminderConfig, bool>>>()))
                .ReturnsAsync((EmailReminderConfig?)null);

            _unitOfWorkMock.Setup(u => u.EmailReminderRepository).Returns(emailReminderRepoMock.Object);
            _unitOfWorkMock.Setup(u => u.EmailReminderRecipientsRepository).Returns(emailReminderRecipientsRepoMock.Object);
            _unitOfWorkMock.Setup(u => u.EmailNotificationGroupRepository).Returns(emailNotificationGroupRepoMock.Object);
            _unitOfWorkMock.Setup(u => u.EmailReminderConfigRepository).Returns(emailReminderConfigRepoMock.Object);

            // Act
            var result = await _emailRemainderService.GetEmailReminderDetailsAsync(reminderId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(reminderId, result.ReminderId);
            Assert.Equal("Test Subject", result.Subject);
            Assert.Equal("Test Body", result.MessageBody);
            Assert.Equal(0, result.TotalNumberOfReminders);
            Assert.Equal(0, result.TotalRemindersSent);
            Assert.Null(result.LastReminderSentDate);
            Assert.Null(result.NextReminderScheduledDate);
        }

        [Fact]
        public async Task GetEmailReminderDetailsAsync_ValidReminder_WithConfig_ReturnsDetailsWithScheduleStatistics()
        {
            // Arrange
            var reminderId = Guid.NewGuid();
            var configId = 1;
            var emailReminder = new EmailReminder
            {
                ReminderId = reminderId,
                Subject = "Test Subject",
                EmailBody = "Test Body",
                IsActive = true
            };

            var config = new EmailReminderConfig
            {
                Id = configId,
                ReminderId = reminderId,
                TotalRemindersPerCycle = 5
            };

            var schedules = new List<EmailReminderSchedule>
            {
                new EmailReminderSchedule { Id = 1, ReminderConfigId = configId, Status = ReminderStatus.Sent, SentDate = new DateTime(2024, 1, 1), IsActive = true },
                new EmailReminderSchedule { Id = 2, ReminderConfigId = configId, Status = ReminderStatus.Sent, SentDate = new DateTime(2024, 1, 5), IsActive = true },
                new EmailReminderSchedule { Id = 3, ReminderConfigId = configId, Status = ReminderStatus.Pending, ReminderDate = new DateTime(2024, 1, 10), IsActive = true },
                new EmailReminderSchedule { Id = 4, ReminderConfigId = configId, Status = ReminderStatus.Pending, ReminderDate = new DateTime(2024, 1, 15), IsActive = true }
            };

            var recipients = new List<EmailReminderRecipients>();
            var groups = new List<EmailNotificationGroup>();

            // Setup mocks
            var emailReminderRepoMock = new Mock<IGenericRepository<EmailReminder>>();
            emailReminderRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<System.Linq.Expressions.Expression<Func<EmailReminder, bool>>>()))
                .ReturnsAsync(emailReminder);

            var emailReminderRecipientsRepoMock = new Mock<IGenericRepository<EmailReminderRecipients>>();
            emailReminderRecipientsRepoMock.Setup(r => r.GetManyAsync(It.IsAny<Func<EmailReminderRecipients, bool>>()))
                .ReturnsAsync(recipients);

            var emailNotificationGroupRepoMock = new Mock<IGenericRepository<EmailNotificationGroup>>();
            emailNotificationGroupRepoMock.Setup(r => r.GetManyAsync(It.IsAny<Func<EmailNotificationGroup, bool>>()))
                .ReturnsAsync(groups);

            var emailReminderConfigRepoMock = new Mock<IGenericRepository<EmailReminderConfig?>>();
            emailReminderConfigRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<System.Linq.Expressions.Expression<Func<EmailReminderConfig, bool>>>()))
                .ReturnsAsync(config);

            var emailReminderScheduleRepoMock = new Mock<IGenericRepository<EmailReminderSchedule>>();
            emailReminderScheduleRepoMock.Setup(r => r.GetManyAsync(It.IsAny<Func<EmailReminderSchedule, bool>>()))
                .ReturnsAsync(schedules);

            _unitOfWorkMock.Setup(u => u.EmailReminderRepository).Returns(emailReminderRepoMock.Object);
            _unitOfWorkMock.Setup(u => u.EmailReminderRecipientsRepository).Returns(emailReminderRecipientsRepoMock.Object);
            _unitOfWorkMock.Setup(u => u.EmailNotificationGroupRepository).Returns(emailNotificationGroupRepoMock.Object);
            _unitOfWorkMock.Setup(u => u.EmailReminderConfigRepository).Returns(emailReminderConfigRepoMock.Object);
            _unitOfWorkMock.Setup(u => u.EmailReminderScheduleRepository).Returns(emailReminderScheduleRepoMock.Object);

            // Act
            var result = await _emailRemainderService.GetEmailReminderDetailsAsync(reminderId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(reminderId, result.ReminderId);
            Assert.Equal("Test Subject", result.Subject);
            Assert.Equal("Test Body", result.MessageBody);
            Assert.Equal(5, result.TotalNumberOfReminders);
            Assert.Equal(2, result.TotalRemindersSent);
            Assert.Equal(new DateTime(2024, 1, 5), result.LastReminderSentDate);
            Assert.Equal(new DateTime(2024, 1, 10), result.NextReminderScheduledDate);
        }

        [Fact]
        public async Task GetEmailReminderDetailsAsync_AllSchedulesSent_NextScheduledDateIsNull()
        {
            // Arrange
            var reminderId = Guid.NewGuid();
            var configId = 1;
            var emailReminder = new EmailReminder
            {
                ReminderId = reminderId,
                Subject = "Test Subject",
                EmailBody = "Test Body",
                IsActive = true
            };

            var config = new EmailReminderConfig
            {
                Id = configId,
                ReminderId = reminderId,
                TotalRemindersPerCycle = 3
            };

            var schedules = new List<EmailReminderSchedule>
            {
                new EmailReminderSchedule { Id = 1, ReminderConfigId = configId, Status = ReminderStatus.Sent, SentDate = new DateTime(2024, 1, 1), IsActive = true },
                new EmailReminderSchedule { Id = 2, ReminderConfigId = configId, Status = ReminderStatus.Sent, SentDate = new DateTime(2024, 1, 5), IsActive = true },
                new EmailReminderSchedule { Id = 3, ReminderConfigId = configId, Status = ReminderStatus.Sent, SentDate = new DateTime(2024, 1, 10), IsActive = true }
            };

            var recipients = new List<EmailReminderRecipients>();
            var groups = new List<EmailNotificationGroup>();

            // Setup mocks
            var emailReminderRepoMock = new Mock<IGenericRepository<EmailReminder>>();
            emailReminderRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<System.Linq.Expressions.Expression<Func<EmailReminder, bool>>>()))
                .ReturnsAsync(emailReminder);

            var emailReminderRecipientsRepoMock = new Mock<IGenericRepository<EmailReminderRecipients>>();
            emailReminderRecipientsRepoMock.Setup(r => r.GetManyAsync(It.IsAny<Func<EmailReminderRecipients, bool>>()))
                .ReturnsAsync(recipients);

            var emailNotificationGroupRepoMock = new Mock<IGenericRepository<EmailNotificationGroup>>();
            emailNotificationGroupRepoMock.Setup(r => r.GetManyAsync(It.IsAny<Func<EmailNotificationGroup, bool>>()))
                .ReturnsAsync(groups);

            var emailReminderConfigRepoMock = new Mock<IGenericRepository<EmailReminderConfig?>>();
            emailReminderConfigRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<System.Linq.Expressions.Expression<Func<EmailReminderConfig, bool>>>()))
                .ReturnsAsync(config);

            var emailReminderScheduleRepoMock = new Mock<IGenericRepository<EmailReminderSchedule>>();
            emailReminderScheduleRepoMock.Setup(r => r.GetManyAsync(It.IsAny<Func<EmailReminderSchedule, bool>>()))
                .ReturnsAsync(schedules);

            _unitOfWorkMock.Setup(u => u.EmailReminderRepository).Returns(emailReminderRepoMock.Object);
            _unitOfWorkMock.Setup(u => u.EmailReminderRecipientsRepository).Returns(emailReminderRecipientsRepoMock.Object);
            _unitOfWorkMock.Setup(u => u.EmailNotificationGroupRepository).Returns(emailNotificationGroupRepoMock.Object);
            _unitOfWorkMock.Setup(u => u.EmailReminderConfigRepository).Returns(emailReminderConfigRepoMock.Object);
            _unitOfWorkMock.Setup(u => u.EmailReminderScheduleRepository).Returns(emailReminderScheduleRepoMock.Object);

            // Act
            var result = await _emailRemainderService.GetEmailReminderDetailsAsync(reminderId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(3, result.TotalNumberOfReminders);
            Assert.Equal(3, result.TotalRemindersSent);
            Assert.Equal(new DateTime(2024, 1, 10), result.LastReminderSentDate);
            Assert.Null(result.NextReminderScheduledDate); // All sent, no pending
        }

        [Fact]
        public async Task GetEmailReminderDetailsAsync_NoSchedulesSent_LastSentDateIsNull()
        {
            // Arrange
            var reminderId = Guid.NewGuid();
            var configId = 1;
            var emailReminder = new EmailReminder
            {
                ReminderId = reminderId,
                Subject = "Test Subject",
                EmailBody = "Test Body",
                IsActive = true
            };

            var config = new EmailReminderConfig
            {
                Id = configId,
                ReminderId = reminderId,
                TotalRemindersPerCycle = 2
            };

            var schedules = new List<EmailReminderSchedule>
            {
                new EmailReminderSchedule { Id = 1, ReminderConfigId = configId, Status = 0, ReminderDate = new DateTime(2024, 1, 15), IsActive = true },
                new EmailReminderSchedule { Id = 2, ReminderConfigId = configId, Status = 0, ReminderDate = new DateTime(2024, 1, 20), IsActive = true }
            };

            var recipients = new List<EmailReminderRecipients>();
            var groups = new List<EmailNotificationGroup>();

            // Setup mocks
            var emailReminderRepoMock = new Mock<IGenericRepository<EmailReminder>>();
            emailReminderRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<System.Linq.Expressions.Expression<Func<EmailReminder, bool>>>()))
                .ReturnsAsync(emailReminder);

            var emailReminderRecipientsRepoMock = new Mock<IGenericRepository<EmailReminderRecipients>>();
            emailReminderRecipientsRepoMock.Setup(r => r.GetManyAsync(It.IsAny<Func<EmailReminderRecipients, bool>>()))
                .ReturnsAsync(recipients);

            var emailNotificationGroupRepoMock = new Mock<IGenericRepository<EmailNotificationGroup>>();
            emailNotificationGroupRepoMock.Setup(r => r.GetManyAsync(It.IsAny<Func<EmailNotificationGroup, bool>>()))
                .ReturnsAsync(groups);

            var emailReminderConfigRepoMock = new Mock<IGenericRepository<EmailReminderConfig?>>();
            emailReminderConfigRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<System.Linq.Expressions.Expression<Func<EmailReminderConfig, bool>>>()))
                .ReturnsAsync(config);

            var emailReminderScheduleRepoMock = new Mock<IGenericRepository<EmailReminderSchedule>>();
            emailReminderScheduleRepoMock.Setup(r => r.GetManyAsync(It.IsAny<Func<EmailReminderSchedule, bool>>()))
                .ReturnsAsync(schedules);

            _unitOfWorkMock.Setup(u => u.EmailReminderRepository).Returns(emailReminderRepoMock.Object);
            _unitOfWorkMock.Setup(u => u.EmailReminderRecipientsRepository).Returns(emailReminderRecipientsRepoMock.Object);
            _unitOfWorkMock.Setup(u => u.EmailNotificationGroupRepository).Returns(emailNotificationGroupRepoMock.Object);
            _unitOfWorkMock.Setup(u => u.EmailReminderConfigRepository).Returns(emailReminderConfigRepoMock.Object);
            _unitOfWorkMock.Setup(u => u.EmailReminderScheduleRepository).Returns(emailReminderScheduleRepoMock.Object);

            // Act
            var result = await _emailRemainderService.GetEmailReminderDetailsAsync(reminderId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.TotalNumberOfReminders);
            Assert.Equal(0, result.TotalRemindersSent);
            Assert.Null(result.LastReminderSentDate); // No sent schedules
            Assert.Equal(new DateTime(2024, 1, 15), result.NextReminderScheduledDate); // Earliest pending
        }

        [Fact]
        public void EmailRemainderService_Constructor_InitializesCorrectly()
        {
            // Arrange & Act
            var service = new EmailRemainderService(_unitOfWorkMock.Object, _loggerMock.Object);

            // Assert
            Assert.NotNull(service);
        }

        [Fact]
        public async Task UpdateRemainderAsync_ReminderNotFound_ReturnsFalse()
        {
            // Arrange
            var reminderId = Guid.NewGuid();
            var dto = new UpdateEmailRemainderDto();
            var userId = 123;

            var emailReminderRepoMock = new Mock<IGenericRepository<EmailReminder>>();
            emailReminderRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<System.Linq.Expressions.Expression<Func<EmailReminder, bool>>>()))
                .ReturnsAsync((EmailReminder?)null);

            _unitOfWorkMock.Setup(u => u.EmailReminderRepository).Returns(emailReminderRepoMock.Object);

            // Act
            var result = await _emailRemainderService.UpdateRemainderAsync(reminderId, dto, userId);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task SkipReminderCycleAsync_NoPendingSchedules_ReturnsFalse()
        {
            // Arrange
            var reminderId = Guid.NewGuid();
            var userId = 123;

            var config = new EmailReminderConfig
            {
                Id = 1,
                ReminderId = reminderId,
                FrequencyType = 1,
                TotalRemindersPerCycle = 3,
                Remainder1Date = new DateTime(2024, 1, 1)
            };

            var emailReminderConfigRepoMock = new Mock<IGenericRepository<EmailReminderConfig>>();
            emailReminderConfigRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<System.Linq.Expressions.Expression<Func<EmailReminderConfig, bool>>>()))
                .ReturnsAsync(config);

            var emailReminderScheduleRepoMock = new Mock<IGenericRepository<EmailReminderSchedule>>();
            emailReminderScheduleRepoMock.Setup(r => r.GetManyAsync(It.IsAny<Func<EmailReminderSchedule, bool>>()))
                .ReturnsAsync(new List<EmailReminderSchedule>()); // No pending schedules

            _unitOfWorkMock.Setup(u => u.EmailReminderConfigRepository).Returns(emailReminderConfigRepoMock.Object);
            _unitOfWorkMock.Setup(u => u.EmailReminderScheduleRepository).Returns(emailReminderScheduleRepoMock.Object);

            // Act
            var result = await _emailRemainderService.SkipReminderCycleAsync(reminderId, userId);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task SkipReminderCycleAsync_ValidRequest_ReturnsTrue()
        {
            // Arrange
            var reminderId = Guid.NewGuid();
            var userId = 123;

            var config = new EmailReminderConfig
            {
                Id = 1,
                ReminderId = reminderId,
                FrequencyType = 1, // Monthly
                TotalRemindersPerCycle = 3,
                Remainder1Date = new DateTime(2024, 1, 1),
                Remainder2 = "5",
                Remainder3 = "10"
            };

            var pendingSchedules = new List<EmailReminderSchedule>
            {
                new EmailReminderSchedule { Id = 1, ReminderConfigId = 1, Status = 0, IsActive = true },
                new EmailReminderSchedule { Id = 2, ReminderConfigId = 1, Status = 0, IsActive = true }
            };

            var emailReminderConfigRepoMock = new Mock<IGenericRepository<EmailReminderConfig>>();
            emailReminderConfigRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<System.Linq.Expressions.Expression<Func<EmailReminderConfig, bool>>>()))
                .ReturnsAsync(config);

            var emailReminderScheduleRepoMock = new Mock<IGenericRepository<EmailReminderSchedule>>();
            emailReminderScheduleRepoMock.Setup(r => r.GetManyAsync(It.IsAny<Func<EmailReminderSchedule, bool>>()))
                .ReturnsAsync(pendingSchedules);

            _unitOfWorkMock.Setup(u => u.EmailReminderConfigRepository).Returns(emailReminderConfigRepoMock.Object);
            _unitOfWorkMock.Setup(u => u.EmailReminderScheduleRepository).Returns(emailReminderScheduleRepoMock.Object);
            _unitOfWorkMock.Setup(u => u.SaveAsync()).ReturnsAsync(1);

            // Act
            var result = await _emailRemainderService.SkipReminderCycleAsync(reminderId, userId);

            // Assert
            Assert.True(result);
            Assert.All(pendingSchedules, schedule => Assert.Equal(ReminderStatus.Skipped, schedule.Status)); // Skipped status
            emailReminderScheduleRepoMock.Verify(r => r.Update(It.IsAny<EmailReminderSchedule>()), Times.Exactly(2));
            emailReminderScheduleRepoMock.Verify(r => r.AddAsyn(It.IsAny<EmailReminderSchedule>()), Times.Exactly(3)); // Next cycle schedules
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Fact]
        public async Task SkipReminderCycleAsync_ConfigNotFound_ReturnsFalse()
        {
            // Arrange
            var reminderId = Guid.NewGuid();
            var userId = 123;

            var pendingSchedules = new List<EmailReminderSchedule>
            {
                new EmailReminderSchedule { Id = 1, ReminderConfigId = 1, Status = 0, IsActive = true }
            };

            var emailReminderConfigRepoMock = new Mock<IGenericRepository<EmailReminderConfig>>();
            emailReminderConfigRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<System.Linq.Expressions.Expression<Func<EmailReminderConfig, bool>>>()))
                .ReturnsAsync((EmailReminderConfig?)null);

            var emailReminderScheduleRepoMock = new Mock<IGenericRepository<EmailReminderSchedule>>();
            emailReminderScheduleRepoMock.Setup(r => r.GetManyAsync(It.IsAny<Func<EmailReminderSchedule, bool>>()))
                .ReturnsAsync(pendingSchedules);

            _unitOfWorkMock.Setup(u => u.EmailReminderConfigRepository).Returns(emailReminderConfigRepoMock.Object);
            _unitOfWorkMock.Setup(u => u.EmailReminderScheduleRepository).Returns(emailReminderScheduleRepoMock.Object);

            // Act
            var result = await _emailRemainderService.SkipReminderCycleAsync(reminderId, userId);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task GetEmailRemainderDetailsForEditAsync_ReminderNotFound_ReturnsNull()
        {
            // Arrange
            var reminderId = Guid.NewGuid();
            var emailReminderRepoMock = new Mock<IGenericRepository<EmailReminder>>();
            emailReminderRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<System.Linq.Expressions.Expression<Func<EmailReminder, bool>>>()))
                .ReturnsAsync((EmailReminder?)null);
            _unitOfWorkMock.Setup(u => u.EmailReminderRepository).Returns(emailReminderRepoMock.Object);

            // Act
            var result = await _emailRemainderService.GetEmailRemainderDetailsForEditAsync(reminderId);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task GetEmailRemainderDetailsForEditAsync_ReminderFound_ReturnsDtoWithExpectedValues()
        {
            // Arrange
            var reminderId = Guid.NewGuid();
            var emailReminder = new EmailReminder { ReminderId = reminderId, Subject = "Test Subject", EmailBody = "Test Body", IsActive = true };
            var recipients = new List<EmailReminderRecipients> {
                new EmailReminderRecipients { ReminderId = reminderId, RecipientType = RecipientTypes.To, RecipientId = 1 },
                new EmailReminderRecipients { ReminderId = reminderId, RecipientType = RecipientTypes.Cc, RecipientId = 2 }
            };
            var groups = new List<EmailNotificationGroup> {
                new EmailNotificationGroup { GroupId = 1, GroupName = "Group1" }
            };
            var config = new EmailReminderConfig { Id = 1, ReminderId = reminderId, FrequencyType = 1, TotalRemindersPerCycle = 2, Remainder1Date = DateTime.UtcNow };

            var emailReminderRepoMock = new Mock<IGenericRepository<EmailReminder>>();
            emailReminderRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<System.Linq.Expressions.Expression<Func<EmailReminder, bool>>>()))
                .ReturnsAsync(emailReminder);
            _unitOfWorkMock.Setup(u => u.EmailReminderRepository).Returns(emailReminderRepoMock.Object);

            var emailReminderRecipientsRepoMock = new Mock<IGenericRepository<EmailReminderRecipients>>();
            emailReminderRecipientsRepoMock.Setup(r => r.GetManyAsync(It.IsAny<Func<EmailReminderRecipients, bool>>()))
                .ReturnsAsync(recipients);
            _unitOfWorkMock.Setup(u => u.EmailReminderRecipientsRepository).Returns(emailReminderRecipientsRepoMock.Object);

            var emailNotificationGroupRepoMock = new Mock<IGenericRepository<EmailNotificationGroup>>();
            emailNotificationGroupRepoMock.Setup(r => r.GetManyAsync(It.IsAny<Func<EmailNotificationGroup, bool>>()))
                .ReturnsAsync(groups);
            _unitOfWorkMock.Setup(u => u.EmailNotificationGroupRepository).Returns(emailNotificationGroupRepoMock.Object);

            var emailReminderConfigRepoMock = new Mock<IGenericRepository<EmailReminderConfig?>>();
            emailReminderConfigRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<System.Linq.Expressions.Expression<Func<EmailReminderConfig, bool>>>()))
                .ReturnsAsync(config);
            _unitOfWorkMock.Setup(u => u.EmailReminderConfigRepository).Returns(emailReminderConfigRepoMock.Object);

            // Act
            var result = await _emailRemainderService.GetEmailRemainderDetailsForEditAsync(reminderId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(emailReminder.Subject, result.Subject);
            Assert.Equal(emailReminder.EmailBody, result.EmailBody);
            Assert.Equal(config.FrequencyType, result.FrequencyType);
            Assert.Equal(config.TotalRemindersPerCycle, result.TotalRemindersPerCycle);
            Assert.Equal(config.Remainder1Date, result.Remainder1Date);
            Assert.NotNull(result.ToRecipients);
            Assert.NotNull(result.CcReciepients);
            Assert.True(result.ToRecipients.Count > 0);
            Assert.True(result.CcReciepients.Count > 0);
        }

        [Fact]
        public async Task GetEmailRemainderDetailsForEditAsync_WhenExceptionThrown_LogsErrorAndThrows()
        {
            // Arrange
            var reminderId = Guid.NewGuid();
            var emailReminderRepoMock = new Mock<IGenericRepository<EmailReminder>>();
            // Use correct nullability for Task<EmailReminder>
            emailReminderRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<System.Linq.Expressions.Expression<Func<EmailReminder, bool>>>()))
                .ThrowsAsync(new Exception("Test exception"));
            _unitOfWorkMock.Setup(u => u.EmailReminderRepository).Returns(emailReminderRepoMock.Object);

            // Act & Assert
            var ex = await Assert.ThrowsAsync<Exception>(async () =>
                await _emailRemainderService.GetEmailRemainderDetailsForEditAsync(reminderId));
            Assert.Equal("Test exception", ex.Message);
            // Optionally, verify logger was called with error
            _loggerMock.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v != null && v.ToString()!.Contains("Error retrieving email reminder details for ID")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }
    }
}
