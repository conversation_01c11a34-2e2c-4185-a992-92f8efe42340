﻿using Contract.Repository;
using System.Threading.Tasks;

namespace PortfolioCompany.Helper
{
    public static class PortfolioCompanyHelper
    {        
        public  static async Task<string> GetCompanyLogo(IFileService fileService, int companyId, string imageName)
        {
            if (string.IsNullOrEmpty(imageName))
            {
                return null;
            }
            string path = $"logos/{companyId}/";

            var files = await fileService.GetFiles(path, "1");

            if (files != null && files?.Count > 0)
            {
                var result = files.Find(x => x.Key.ToLower().Trim() == imageName.ToLower().Trim());
                return result.Key != null ? result.Value : null;
            }
            return null;
        }
    }
}
