﻿using Contract.Configuration;
using Contract.DataIngestion;
using Contract.Funds;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PortfolioCompany.Interfaces
{
    public interface IDataIngestionService
    {
        Task<bool> PublishData(List<PublishModel> publishModels, string connectionString, string processId, int userId);
        Task<List<KpiPageConfigInfo>> GetKpiModulesPageConfigDetails();
        Task<List<KpiModelList>> GetKPIMapping(int portfolioCompanyId);
        Task<List<FundNameModel>> GetUserFunds(int userId);
        Task<List<SubPageFieldsModel>> GetFundKpis();
        Task<FundDetailsModel> GetFundDetails(int fundId);
        Task<List<CompaniesAndKpiResponseModel>> GetCompaniesAndKpiByFundId(int fundId);
        Task<List<FundIngestionModel>> GetFundIngestionList(FundIngestionFilter filter);
        Task<bool> SpecificKpisPublish(SpecificKpiPublishModel data, string connectionString);
        Task<string> GetsubpagedetailsText(int fundIngestion);
        Task<List<PageFieldValueModelTrackRecord>> GetFundIngestionDynamicColumns();
        Task<List<KpiPageConfigInfo>> GetPageConfigDetails();
    }
}
