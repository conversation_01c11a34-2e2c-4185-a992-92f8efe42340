using API.Controllers.Configuration;
using API.Helpers;
using AutoMapper;
using Contract.Configuration;
using Contract.Utility;
using DataAccessLayer.DBModel;
using DataAccessLayer.GenericRepository;
using DataAccessLayer.Models.PageSettings;
using DataAccessLayer.Models.SDG;
using DataAccessLayer.UnitOfWork;
using Master.DtoProfiles;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Xunit;

namespace Master.UnitTests
{
    public class PageConfigurationFixtures
    {
        public readonly PageDetailsConfigurationService PageDetailsConfigurationService;
        public readonly Mock<IUnitOfWork> MockunitOfWork;
        public readonly Mock<IMapper> Mockmapper;
        public readonly Mock<ILogger<IPageDetailsConfigurationService>> Mocklogger;
        public readonly Mock<IGlobalConfigurations> MockglobalConfig;
        public readonly Mock<IMemoryCacher> MockmemoryCacher;
        public readonly PageConfigurationController controller;
        public Mock<IHelperService> helper;
        public Mock<IInjectedParameters> injectParams;
        public PageConfigurationFixtures()
        {
            MockunitOfWork = new Mock<IUnitOfWork>();
            Mockmapper = new Mock<IMapper>();
            MockmemoryCacher = new Mock<IMemoryCacher>();
            MockglobalConfig = new Mock<IGlobalConfigurations>();
            Mocklogger = new Mock<ILogger<IPageDetailsConfigurationService>>();
            helper = new Mock<IHelperService>();
            injectParams = new Mock<IInjectedParameters>();
            var config = new MapperConfiguration(cfg => {
                cfg.AddProfile(new MasterMappingProfile());
                cfg.AddProfile(new PageSettingsProfiles());
            });
            var mapper = config.CreateMapper();
            PageDetailsConfigurationService = new(MockunitOfWork.Object, mapper, Mocklogger.Object, MockglobalConfig.Object, MockmemoryCacher.Object);
            controller = new PageConfigurationController(PageDetailsConfigurationService, injectParams.Object, helper.Object);
        }
    }
    public class PageDetailsConfigurationServiceUnitTest : IClassFixture<PageConfigurationFixtures>
    {
        private readonly PageConfigurationFixtures _fixture;

        public PageDetailsConfigurationServiceUnitTest(PageConfigurationFixtures fixture)
        {
            _fixture = fixture;
            List<M_PageDetails> m_PageDetails = new() { new M_PageDetails { PageID = 1, Name = "Page", AliasName = "Page Page", IsActive = true, IsDeleted = false } };
            List<M_SubPageDetails> m_SubPageDetails = new() { new M_SubPageDetails { PageID = 1, Name = "SubPage", AliasName = "SubPage Page", IsActive = true, IsDeleted = false, SubPageID = 1, SequenceNo = 1, IsCustom = false, IsDataType = false, IsDragDrop = false, IsFootNote = true } };
            List<M_SubPageFields> m_SubPageFields = new() { new M_SubPageFields { FieldID = 1, IsCustom = false, IsActive = true, IsDeleted = false, IsListData = false, ShowOnList = false, SequenceNo = 1, AliasName = "Filed Name", Name = "Name", IsMandatory = false, DataTypeId = 0, SubPageID = 1, PagePath = "", EncryptedSubPageID = "s", CreatedBy = 1, CreatedOn = DateTime.Now, ModifiedBy = 1, ModifiedOn = DateTime.Now }, new M_SubPageFields { FieldID = 2, IsCustom = false, IsActive = false, IsDeleted = false, IsListData = false, ShowOnList = false, SequenceNo = 2, AliasName = "2Filed Name", Name = "Customfield", IsMandatory = false, DataTypeId = 0, SubPageID = 1, PagePath = "", EncryptedSubPageID = "s", CreatedBy = 1, CreatedOn = DateTime.Now, ModifiedBy = 1, ModifiedOn = DateTime.Now }, new M_SubPageFields { FieldID = 2, IsCustom = false, IsActive = true, IsDeleted = false, IsListData = false, ShowOnList = false, SequenceNo = 2, AliasName = "2Filed Name", Name = "Customfield", IsMandatory = false, DataTypeId = 0, SubPageID = 9, PagePath = "", EncryptedSubPageID = "s", CreatedBy = 1, CreatedOn = DateTime.Now, ModifiedBy = 1, ModifiedOn = DateTime.Now } };
            _fixture.MockunitOfWork.Setup(x => x.PageDetailsRepository.GetManyAsync(It.IsAny<Func<M_PageDetails, bool>>())).Returns(Task.FromResult(m_PageDetails.AsEnumerable()));
            _fixture.MockunitOfWork.Setup(x => x.SubPageDetailsRepository.GetManyAsync(It.IsAny<Func<M_SubPageDetails, bool>>())).Returns(Task.FromResult(m_SubPageDetails.AsEnumerable()));
            _fixture.MockunitOfWork.Setup(x => x.SubPageFieldsRepository.GetManyAsync(It.IsAny<Func<M_SubPageFields, bool>>())).Returns(Task.FromResult(m_SubPageFields.AsEnumerable()));
            _fixture.MockunitOfWork.Setup(x => x.MSubSectionFieldsRepository.GetManyAsync(It.IsAny<Func<MSubSectionFields, bool>>())).Returns(Task.FromResult(GetSubSectionFieldsData().AsEnumerable()));

            _fixture.MockunitOfWork.Setup(x => x.SubPageDetailsRepository.FindAllAsync(It.IsAny<Expression<Func<M_SubPageDetails, bool>>>())).Returns(Task.FromResult(m_SubPageDetails));
            _fixture.MockunitOfWork.Setup(x => x.SubPageFieldsRepository.FindAllAsync(It.IsAny<Expression<Func<M_SubPageFields, bool>>>())).Returns(Task.FromResult(m_SubPageFields));

            List<PageConfigurationFieldValue> pageConfigurationFieldValues = new() { new PageConfigurationFieldValue { PageConfigValueId = 1, PageFeatureId = 1, FieldID = 1, FieldValue = "Test", IsActive = true, IsDeleted = false, PageID = 1, SubPageID = 1, CreatedOn = DateTime.Now, CreatedBy = 1, ModifiedBy = 1, ModifiedOn = DateTime.Now } };
            List<PageConfigurationTrackRecordFieldValue> pageConfigurationTrackRecordFieldValues = new() { new PageConfigurationTrackRecordFieldValue { PageID = 1, PageFeatureId = 1, PageTrackRecordId = 1, FieldID = 1, FieldValue = "Test", SubPageID = 1, FundId = 1, IsActive = true, IsDeleted = false, Year = DateTime.Now.Year, Quarter = "Q1" } };
            _fixture.MockunitOfWork.Setup(x => x.PageConfigurationFieldValueRepository.FindAllAsync(It.IsAny<Expression<Func<PageConfigurationFieldValue, bool>>>())).Returns(Task.FromResult(pageConfigurationFieldValues));
            _fixture.MockunitOfWork.Setup(x => x.PageConfigurationTrackRecordFieldValueRepository.FindAllAsync(It.IsAny<Expression<Func<PageConfigurationTrackRecordFieldValue, bool>>>())).Returns(Task.FromResult(pageConfigurationTrackRecordFieldValues));
            List<M_TrackRecordDataTypes> m_TrackRecordDataTypes = new() { new M_TrackRecordDataTypes { TrackRecordId = 1, IsActive = true, DataType = "A" } };
            _fixture.MockunitOfWork.Setup(x => x.M_TrackRecordDataTypesRepository.FindAllAsync(It.IsAny<Expression<Func<M_TrackRecordDataTypes, bool>>>())).Returns(Task.FromResult(m_TrackRecordDataTypes));
            _fixture.MockunitOfWork.Setup(x => x.SubPageDetailsRepository.GetFirstOrDefaultAsync(It.IsAny<Expression<Func<M_SubPageDetails, bool>>>())).Returns(Task.FromResult(new M_SubPageDetails
            {
                PageID = 1,
                IsActive = true,
                IsDeleted = false,
                IsDragDrop = false,
                IsCustom = false,
                SubPageID = 1,
                Name = "GeoLocation Info",
                AliasName = "GeoLocation Info",
                SequenceNo = 14,
                IsDataType = false,
                IsDynamicFieldSupported = false
            }));          
        }
        public static List<MSubSectionFields> GetSubSectionFieldsData()
        {
            return new List<MSubSectionFields>
    {
        new MSubSectionFields
        {
            SectionID = 1,
            FieldID = 1,
            Name = "My Subsection Field",
            AliasName = "My Subsection Field Alias Name",
            SubPageID = 1,
            IsActive = true,
            IsDeleted = false,
            SequenceNo = 1,
            Options = "My Subsection Field Options",
            ChartValue = "My Subsection Field Chart Value",
        },
        new MSubSectionFields
        {
            SectionID = 2,
            FieldID = 2,
            Name = "My Other Subsection Field",
            AliasName = "My Other Subsection Field Alias Name",
            SubPageID = 1,
            IsActive = true,
            IsDeleted = false,
            SequenceNo = 2,
            Options = "My Other Subsection Field Options",
            ChartValue = "My Other Subsection Field Chart Value",
        },
    };
        }
        [Fact]
        public void LoadInserorUpdatet()
        {
            List<PageConfigurationFieldValue> pageConfigurationFieldValues = new() { new PageConfigurationFieldValue { PageConfigValueId = 1, PageFeatureId = 1, FieldID = 1, FieldValue = "Test", IsActive = true, IsDeleted = false, PageID = 1, SubPageID = 1, CreatedOn = DateTime.Now, CreatedBy = 1, ModifiedBy = 1, ModifiedOn = DateTime.Now } };
            List<M_SubPageFields> m_SubPageFields = new() { new M_SubPageFields { FieldID = 1, IsCustom = false, IsActive = true, IsDeleted = false, IsListData = false, ShowOnList = false, SequenceNo = 1, AliasName = "Filed Name", Name = "Name", IsMandatory = false, DataTypeId = 0, SubPageID = 1, PagePath = "", EncryptedSubPageID = "s", CreatedBy = 1, CreatedOn = DateTime.Now, ModifiedBy = 1, ModifiedOn = DateTime.Now }, new M_SubPageFields { FieldID = 2, IsCustom = false, IsActive = false, IsDeleted = false, IsListData = false, ShowOnList = false, SequenceNo = 2, AliasName = "2Filed Name", Name = "Customfield", IsMandatory = false, DataTypeId = 0, SubPageID = 1, PagePath = "", EncryptedSubPageID = "s", CreatedBy = 1, CreatedOn = DateTime.Now, ModifiedBy = 1, ModifiedOn = DateTime.Now }, new M_SubPageFields { FieldID = 2, IsCustom = false, IsActive = true, IsDeleted = false, IsListData = false, ShowOnList = false, SequenceNo = 2, AliasName = "2Filed Name", Name = "Customfield", IsMandatory = false, DataTypeId = 0, SubPageID = 9, PagePath = "", EncryptedSubPageID = "s", CreatedBy = 1, CreatedOn = DateTime.Now, ModifiedBy = 1, ModifiedOn = DateTime.Now } };
            Mock<IGenericRepository<M_SubPageFields>> subPageFieldsConfig = new();
            subPageFieldsConfig.Setup(S => S.AddBulkAsyn(m_SubPageFields));
            _fixture.MockunitOfWork.Setup(s => s.SubPageFieldsRepository).Returns(subPageFieldsConfig.Object);
            subPageFieldsConfig.Setup(S => S.UpdateBulk(m_SubPageFields));
            _fixture.MockunitOfWork.Setup(s => s.SubPageFieldsRepository).Returns(subPageFieldsConfig.Object);
            Mock<IGenericRepository<PageConfigurationFieldValue>> pageConfigurationFieldValue = new();
            pageConfigurationFieldValue.Setup(S => S.AddBulkAsyn(pageConfigurationFieldValues));
            _fixture.MockunitOfWork.Setup(s => s.PageConfigurationFieldValueRepository).Returns(pageConfigurationFieldValue.Object);
            pageConfigurationFieldValue.Setup(S => S.UpdateBulk(pageConfigurationFieldValues));
            _fixture.MockunitOfWork.Setup(s => s.PageConfigurationFieldValueRepository).Returns(pageConfigurationFieldValue.Object);
            Assert.True(true);
        }
        [Fact]
        public async Task WhenuserInvokes_GetConfiguration()
        {
            var actual = await _fixture.controller.GetConfiguration();
            var okResult = actual as ObjectResult;
            Assert.IsType<List<PageDetailModel>>(okResult?.Value);
        }
        [Fact]
        public async Task WhenuserInvokes_GetPageDetailsByID()
        {
            var actual = await _fixture.PageDetailsConfigurationService.GetPageDetailsByID(1);
            Assert.IsType<PageDetailModel>(actual);
        }
        [Fact]
        public async Task WhenuserInvokes_GetAllActiveFieldsBySubPageID()
        {
            var actual = await _fixture.PageDetailsConfigurationService.GetAllActiveFieldsBySubPageID(1, 1);
            Assert.IsType<List<SubPageFieldModel>>(actual);
        }
        [Fact]
        public async Task WhenuserInvokes_GetActiveSubPageSectionByPageId()
        {
            var actual = await _fixture.PageDetailsConfigurationService.GetActiveSubPageSectionByPageId(1);
            Assert.IsType<List<SubPageDetailModel>>(actual);
        }
        [Fact]
        public async Task GetActiveSubPageSectionByPageId_ShouldReturnSubPageDetails_WhenPageIdIsValid()
        {
            // Arrange
            int pageId = 1;
            var subPageDetails = new List<M_SubPageDetails> { new M_SubPageDetails { SubPageID = 1, PageID = pageId, IsActive = true, IsDeleted = false } };
            var subPageDetailModels = new List<SubPageDetailModel> { new SubPageDetailModel { Id = 1 } };

            _fixture.MockunitOfWork.Setup(u => u.SubPageDetailsRepository.FindAllAsync(It.IsAny<Expression<Func<M_SubPageDetails, bool>>>()))
                .ReturnsAsync(subPageDetails);
            _fixture.Mockmapper.Setup(m => m.Map<SubPageDetailModel>(It.IsAny<M_SubPageDetails>()))
                .Returns((M_SubPageDetails source) => new SubPageDetailModel { Id = source.SubPageID });

            // Act
            var result = await _fixture.PageDetailsConfigurationService.GetActiveSubPageSectionByPageId(pageId);

            // Assert
            Assert.NotNull(result);
            Assert.Single(result);
            Assert.Equal(subPageDetailModels.First().Id, result.First().Id);
        }

        [Fact]
        public async Task GetActiveSubPageSectionByPageId_ShouldReturnEmptyList_WhenNoActiveSubPageDetails()
        {
            // Arrange
            int pageId = 1;
            var subPageDetails = new List<M_SubPageDetails>();

            _fixture.MockunitOfWork.Setup(u => u.SubPageDetailsRepository.FindAllAsync(It.IsAny<Expression<Func<M_SubPageDetails, bool>>>()))
                .ReturnsAsync(subPageDetails);

            // Act
            var result = await _fixture.PageDetailsConfigurationService.GetActiveSubPageSectionByPageId(pageId);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public async Task PopulateSubPageDetails_ShouldPopulateFields_WhenSubPageDetailNameMatches()
        {
            // Arrange
            var subPageDetailList = new List<SubPageDetailModel>
            {
                new SubPageDetailModel { Name = Shared.Constants.SustainableDevelopmentGoalsImages }
            };
            var subPageFields = new List<M_SubPageFields> { new M_SubPageFields { SubPageID = 1, IsActive = true, IsDeleted = false } };
            _fixture.MockunitOfWork.Setup(u => u.SubPageFieldsRepository.FindAllAsync(It.IsAny<Expression<Func<M_SubPageFields, bool>>>()))
                .ReturnsAsync(subPageFields);

            // Act
            await _fixture.PageDetailsConfigurationService.PopulateSubPageDetails(subPageDetailList);

            // Assert
            Assert.NotNull(subPageDetailList.First().SubPageFieldList);
        }

        [Fact]
        public async Task PopulateSubPageFields_ShouldPopulateMSubFields_WhenSubPageFieldsExist()
        {
            // Arrange
            var subPageDetail = new SubPageDetailModel { Id = 1 };
            var subPageFields = new List<M_SubPageFields> { new M_SubPageFields { SubPageID = 1, IsActive = true, IsDeleted = false } };

            _fixture.MockunitOfWork.Setup(u => u.SubPageFieldsRepository.FindAllAsync(It.IsAny<Expression<Func<M_SubPageFields, bool>>>()))
                .ReturnsAsync(subPageFields);
            _fixture.Mockmapper.Setup(m => m.Map<SubPageFieldModel>(It.IsAny<M_SubPageFields>()))
                .Returns((M_SubPageFields source) => new SubPageFieldModel { SubPageID = source.SubPageID });

            // Act
            await _fixture.PageDetailsConfigurationService.PopulateSubPageFields(subPageDetail);

            // Assert
            Assert.NotNull(subPageDetail.SubPageFieldList);
            Assert.Single(subPageDetail.SubPageFieldList);
        }

        [Fact]
        public async Task PopulateMSubFields_ShouldPopulateMSubFields_WhenSubSectionsExist()
        {
            // Arrange
            var subPageDetail = new SubPageDetailModel
            {
                Id = 1,
                SubPageFieldList = new List<SubPageFieldModel> { new SubPageFieldModel { SubPageID = 1 } }
            };
            var subSections = new List<MSubSectionFields> { new MSubSectionFields { 
                SubPageID = 1, 
                IsActive = true, 
                IsDeleted = false,
                SectionID = 1,
                FieldID = 1,
                Name = "test",
                AliasName = "test",
                SequenceNo = 0,
                ChartValue = "test,test1",
                Options = "test,test1"
            } };

            _fixture.MockunitOfWork.Setup(u => u.MSubSectionFieldsRepository.FindAllAsync(It.IsAny<Expression<Func<MSubSectionFields, bool>>>()))
                .ReturnsAsync(subSections);

            // Act
            await _fixture.PageDetailsConfigurationService.PopulateMSubFields(subPageDetail);

            // Assert
            Assert.NotNull(subPageDetail.SubPageFieldList.First().MSubFields);
            Assert.Single(subPageDetail.SubPageFieldList.First().MSubFields);
        }
        [Fact]
        public async Task WhenuserInvokes_GeAllSubPageSectionByPageId()
        {
            var actual = await _fixture.PageDetailsConfigurationService.GeAllSubPageSectionByPageId(1);
            Assert.IsType<List<SubPageDetailModel>>(actual);
        }
        [Fact]
        public async Task WhenuserInvokes_GetAllSubPageFieldsByPageId()
        {
            var actual = await _fixture.PageDetailsConfigurationService.GetAllSubPageFieldsByPageId(1);
            Assert.IsType<List<SubPageFieldModel>>(actual);
        }
        [Fact]
        public async Task WhenuserInvokes_GetActiveFieldsByPageId()
        {
            var actual = await _fixture.PageDetailsConfigurationService.GetActiveFieldsByPageId(1);
            Assert.IsType<List<SubPageFieldModel>>(actual);
        }
        [Fact]
        public async Task WhenuserInvokes_GetActiveFieldsBySubPageIdWithSequenceNo()
        {
            var actual = await _fixture.PageDetailsConfigurationService.GetActiveFieldsBySubPageIdWithSequenceNo(1);
            Assert.IsType<List<SubPageFieldModel>>(actual);
        }
        [Fact]
        public async Task WhenuserInvokes_GetActiveFieldsBySubPageIdWithMultpleSubpageId()
        {
            var actual = await _fixture.PageDetailsConfigurationService.GetActiveFieldsBySubPageIdWithMultpleSubpageId(new List<int> { 1 });
            Assert.IsType<List<SubPageFieldModel>>(actual);
        }
        [Fact]
        public async Task WhenuserInvokes_GetActiveFieldsBySubPageIdWithSequenceNoActiveInactive()
        {
            var actual = await _fixture.PageDetailsConfigurationService.GetActiveFieldsBySubPageIdWithSequenceNoActiveInactive(1);
            Assert.IsType<List<SubPageFieldModel>>(actual);
        }
        [Fact]
        public async Task WhenuserInvokes_GetActiveFieldsBySubPageId()
        {
            var actual = await _fixture.PageDetailsConfigurationService.GetActiveFieldsBySubPageId(1);
            Assert.IsType<List<SubPageFieldModel>>(actual);
        }
        [Fact]
        public async Task WhenuserInvokes_GetAllFieldsBySubPageId()
        {
            var actual = await _fixture.PageDetailsConfigurationService.GetAllFieldsBySubPageId(1);
            Assert.IsType<List<SubPageFieldModel>>(actual);
        }

        [Fact]
        public async Task WhenuserInvokes_GetPageConfigFieldValueByFeatureId()
        {
            var actual = await _fixture.PageDetailsConfigurationService.GetPageConfigFieldValueByFeatureId(1, 1);
            Assert.IsType<List<PageFieldValueModel>>(actual);
        }
        [Fact]
        public async Task WhenuserInvokes_GetPageConfigFieldTrackRecordValueByFeatureId()
        {
            var actual = await _fixture.PageDetailsConfigurationService.GetPageConfigFieldTrackRecordValueByFeatureId(1, 1);
            Assert.IsType<List<PageFieldValueModelTrackRecord>>(actual);
        }
        [Fact]
        public async Task WhenuserInvokes_GetPageConfigFieldMultipleTrackRecordValueByFeatureId()
        {
            var actual = await _fixture.PageDetailsConfigurationService.GetPageConfigFieldMultipleTrackRecordValueByFeatureId(1, new List<int> { 1 });
            Assert.IsType<List<PageFieldValueModelTrackRecord>>(actual);
        }
        [Fact]
        public async Task WhenuserInvokes_TrackrecordDataTypes()
        {
            var actual = await _fixture.PageDetailsConfigurationService.TrackrecordDataTypes();
            Assert.IsType<List<M_TrackRecordDataTypes>>(actual);
        }
        [Fact]
        public async Task WhenuserInvokes_CheckDataExitsOnCustomFields()
        {
            _fixture.MockunitOfWork.Setup(x => x.PortfolioCustomListRepository.FindAllAsync(It.IsAny<Expression<Func<PortfolioCompanyCustomListDetails, bool>>>())).Returns(Task.FromResult(new List<PortfolioCompanyCustomListDetails> { }));
            var actual = await _fixture.controller.getTrackrecordDataTypes();
            var expected = actual as ObjectResult;
            Assert.NotNull(expected?.Value);
        }
        [Fact]
        public async Task WhenuserInvokes_GetPageSettingsByID()
        {
            var actual = await _fixture.controller.GetPageSettingsByID(new StringValueModel { Id = "1" });
            var expected = actual as ObjectResult;
            Assert.NotNull(expected?.Value);
        }
        [Fact]
        public async Task WhenuserInvokes_GetsubpagedetailsText()
        {
            var actual = await _fixture.PageDetailsConfigurationService.GetsubpagedetailsText(1);
            Assert.IsType<string>(actual);
        }
        [Fact]
        public void WhenuserInvokesNumber_ConvertCustomValues()
        {
            var actual = _fixture.PageDetailsConfigurationService.ConvertCustomValues(new PageFieldValueModelTrackRecord { DataType = 2, Value = "12" });
            Assert.IsType<string>(actual);
            var actualNumber = _fixture.PageDetailsConfigurationService.ConvertCustomValues(new PageFieldValueModelTrackRecord { DataType = 3, Value = "12" });
            Assert.IsType<string>(actualNumber);
            var actualPercentage = _fixture.PageDetailsConfigurationService.ConvertCustomValues(new PageFieldValueModelTrackRecord { DataType = 4, Value = "12" });
            Assert.IsType<string>(actualPercentage);
            var actualCurrency = _fixture.PageDetailsConfigurationService.ConvertCustomValues(new PageFieldValueModelTrackRecord { DataType = 5, Value = "12" });
            Assert.IsType<string>(actualCurrency);
            var actualDefault = _fixture.PageDetailsConfigurationService.ConvertCustomValues(new PageFieldValueModelTrackRecord { DataType = 1, Value = "12" });
            Assert.IsType<string>(actualDefault);
            var actualDate = _fixture.PageDetailsConfigurationService.ConvertCustomValues(new PageFieldValueModelTrackRecord { DataType = 6, Value = "2022-03-31T18:30:00.000Z" });
            Assert.IsType<string>(actualDate);
        }
        [Fact]
        public async Task TrackRecordSavePageConfigurationFieldValue_Add()
        {
            _fixture.MockunitOfWork.Setup(x => x.PageConfigurationTrackRecordFieldValueRepository.FindAllAsync(It.IsAny<Expression<Func<PageConfigurationTrackRecordFieldValue, bool>>>())).Returns(Task.FromResult(new List<PageConfigurationTrackRecordFieldValue> { }));
            List<PageFieldValueModelTrackRecord> configurationData = new() { new PageFieldValueModelTrackRecord { FieldID=1,SubPageID=1,PageFeatureId=1,PageID=1 } };
            var actual = await _fixture.PageDetailsConfigurationService.TrackRecordSavePageConfigurationFieldValue(configurationData, 1,1,1);
            Assert.IsType<int>(actual);
        }
        [Fact]
        public async Task TrackRecordSavePageConfigurationFieldValue_Update()
        {
            _fixture.MockunitOfWork.Setup(x => x.PageConfigurationTrackRecordFieldValueRepository.GetFirstOrDefaultAsync(It.IsAny<Expression<Func<PageConfigurationTrackRecordFieldValue, bool>>>())).Returns(Task.FromResult(new PageConfigurationTrackRecordFieldValue { PageID = 1, PageFeatureId = 1, PageTrackRecordId = 1, FieldID = 1, FieldValue = "Test", SubPageID = 1, FundId = 1, IsActive = true, IsDeleted = false, Year = DateTime.Now.Year, Quarter = "Q1" }));
            List<PageFieldValueModelTrackRecord> configurationData = new() { new PageFieldValueModelTrackRecord { FieldID = 1, SubPageID = 1, PageFeatureId = 1, PageID = 1, Year = DateTime.Now.Year, Quarter = "Q1" } };
            var actual = await _fixture.PageDetailsConfigurationService.TrackRecordSavePageConfigurationFieldValue(configurationData, 1, 1, 1);
            Assert.IsType<int>(actual);
        }
        [Fact]
        public async Task SavePageConfigurationFieldValue_EmptyFilters()
        {
            List<PageFieldValueModel> configurationData = new() { };
            var actual = await _fixture.PageDetailsConfigurationService.SavePageConfigurationFieldValue(configurationData, 20, 20, 20);
            Assert.IsType<int>(actual);
        }
        [Fact]
        public async Task SavePageConfigurationFieldValue_Add()
        {
            _fixture.MockunitOfWork.Setup(x => x.PageConfigurationFieldValueRepository.FindAllAsync(It.IsAny<Expression<Func<PageConfigurationFieldValue, bool>>>())).Returns(Task.FromResult(new List<PageConfigurationFieldValue>()));
            List<PageFieldValueModel> configurationData = new() { new PageFieldValueModel { FieldID = 0, SubPageID = 0, PageFeatureId = 2, PageID = 2 } };
            var actual = await _fixture.PageDetailsConfigurationService.SavePageConfigurationFieldValue(configurationData, 20, 20, 20);
            Assert.IsType<int>(actual);
        }
        [Fact]
        public async Task SavePageConfigurationFieldValue_Update()
        {
            List<PageFieldValueModel> configurationData = new() { new PageFieldValueModel { FieldID = 1, SubPageID = 1, PageFeatureId = 1, PageID = 1 } };
            var actual = await _fixture.PageDetailsConfigurationService.SavePageConfigurationFieldValue(configurationData, 1, 1, 1);
            Assert.IsType<int>(actual);
        }
        [Fact]
        public async Task Should_Save_Page_Configuration_Fields()
        {
            // Arrange
            var pageDetail = new List<PageDetailModel>
    {
        new PageDetailModel
        {
            Id = 1,
            DisplayName = "Test Page",
            Name = "Test",
            Description = "This is a test page.",
            IsActive = true,
            IsDeleted = false,
            EncryptedID = "1234567890",
            SequenceNo = 1,
            PagePath = "/",
            IsCustom = false,
            SubPageDetailList = new List<SubPageDetailModel>(),
            SubPageFieldList = new List<SubPageFieldModel>{ new SubPageFieldModel { Id=1,IsActive=true,IsDeleted=false,Name="test",SubPageID=1,SequenceNo=1,DisplayName="gg",DataTypeId=0,MSubFields=new List<MSubFields>() { new MSubFields { AliasName="Test",ChartValue=new List<string> { "Q","M","A" },FieldID=1,SubPageID=1,SectionID=1,Options= new List<string> { "Q", "M", "A" } } } }, new SubPageFieldModel { Id = 2, IsActive = true, IsDeleted = false, Name = "test", SubPageID = 2, SequenceNo = 1, DisplayName = "gg", DataTypeId = 0, MSubFields = new List<MSubFields>() { new MSubFields { FieldID=2,SubPageID=2,SectionID=2,AliasName="Test123",ChartValue= new List<string> { "Q", "M", "A" },Options= new List<string> { "Q", "M", "A" } } } } }
        }
    };

            var subpageDetailsList = new List<M_SubPageDetails>
    {
        new M_SubPageDetails
        {

            SubPageID = 1,
            AliasName = "Test Page",
            IsDeleted = false,
            IsActive = true,
            PageID = null,
            IsCustom = false,
            ModifiedBy = 1,
            ModifiedOn = DateTime.Now,
            IsFootNote = true
        }
    };

            var subpageFieldsList = new List<M_SubPageFields>
    {
        new M_SubPageFields
        {

            FieldID = 1,
            SubPageID = 1,
            AliasName = "Test Field",
            IsActive = true,
            IsDeleted = false,
            IsCustom = false,
            DataTypeId = 1,
            ModifiedBy = 1,
            ModifiedOn = DateTime.Now,
            IsMandatory = true,
            IsListData = true,
            ShowOnList = true
        }
    };

            _fixture.MockunitOfWork.Setup(unitOfWork => unitOfWork.SubPageDetailsRepository.FindAllAsync(x => !x.IsDeleted))
                .Returns(Task.FromResult(subpageDetailsList));

            _fixture.MockunitOfWork.Setup(unitOfWork => unitOfWork.SubPageFieldsRepository.FindAllAsync(x => !x.IsDeleted))
                .Returns(Task.FromResult(subpageFieldsList));

            _fixture.MockunitOfWork.Setup(unitOfWork => unitOfWork.SubPageFieldsRepository.UpdateBulk(null));

            _fixture.MockunitOfWork.Setup(unitOfWork => unitOfWork.SubPageFieldsRepository.AddBulkAsyn(null));

            _fixture.MockunitOfWork.Setup(unitOfWork => unitOfWork.SaveAsync());
            // Act
            var result = await _fixture.controller.SavePageConfiguration(pageDetail) as OkObjectResult;
            // Assert
            Assert.IsType<Status>(result?.Value);
        }
        [Fact]
        public async Task Should_Throw_Exception_When_SubPageFieldsRepository_UpdateBulk_Throws_Exception()
        {

            // Arrange
            _fixture.MockunitOfWork.Setup(unitOfWork => unitOfWork.SubPageFieldsRepository.FindAllAsync(x => !x.IsDeleted))
                .Returns(Task.FromResult(new List<M_SubPageFields>()));
            _fixture.MockunitOfWork.Setup(unitOfWork => unitOfWork.SubPageFieldsRepository.UpdateBulk(null));
            await Assert.ThrowsAsync<NullReferenceException>(async () => await _fixture.controller.SavePageConfiguration(null));
        }
        [Fact]
        public async Task Should_Get_Field_Value()
        {
            // Arrange
            var subPageId = 1;
            var name = "Test Field";

            var configuration = new M_SubPageFields
            {
                SubPageID = subPageId,
                Name = name,
                AliasName = "Test Field",
                IsActive = true,
                IsDeleted = false,
                IsCustom = false,
                DataTypeId = 1,
                ModifiedBy = 1,
                ModifiedOn = DateTime.Now,
                IsMandatory = true,
                IsListData = true,
                ShowOnList = true
            };

            _fixture.MockunitOfWork.Setup(unitOfWork => unitOfWork.SubPageFieldsRepository.FindFirstAsync(x => !x.IsDeleted && x.IsActive && x.SubPageID == subPageId && x.Name == name))
                .Returns(Task.FromResult(configuration));
            var result = await _fixture.PageDetailsConfigurationService.GetFieldValue(subPageId, name);
            Assert.IsType<string>(result);
        }

        [Fact]
        public async Task Should_Return_Null_When_No_Configuration_Is_Found()
        {
            var subPageId = 1;
            var name = "Test Field";

            _fixture.MockunitOfWork.Setup(unitOfWork => unitOfWork.SubPageFieldsRepository.FindFirstAsync(x => !x.IsDeleted && x.IsActive && x.SubPageID == subPageId && x.Name == name))
                .Returns(Task.FromResult(new M_SubPageFields()));
            var result = await _fixture.PageDetailsConfigurationService.GetFieldValue(subPageId, name);
            Assert.Null(result);
        }
        [Fact]
        public async Task Should_Get_Field_Value_MoreThan25()
        {
            // Arrange
            var subPageId = 1;
            var name = "Test Field";

            var configuration = new M_SubPageFields
            {
                SubPageID = subPageId,
                Name = name,
                AliasName = "_fixture.PageDetailsConfigurationService.GetFieldValue",
                IsActive = true,
                IsDeleted = false,
                IsCustom = false,
                DataTypeId = 1,
                ModifiedBy = 1,
                ModifiedOn = DateTime.Now,
                IsMandatory = true,
                IsListData = true,
                ShowOnList = true
            };

            _fixture.MockunitOfWork.Setup(unitOfWork => unitOfWork.SubPageFieldsRepository.FindFirstAsync(x => !x.IsDeleted && x.IsActive && x.SubPageID == subPageId && x.Name == name))
                .Returns(Task.FromResult(configuration));
            var result = await _fixture.PageDetailsConfigurationService.GetCommonSheetNameformPageConfig(subPageId, name);
            Assert.IsType<string>(result);
        }
        [Fact]
        public void ShouldAddSubPageFieldIfNotExists()
        {
            var subPageField = new SubPageFieldModel() { Id = 11, DisplayName = "Test", IsActive = true, IsDeleted = false, IsCustom = true, IsChart = true, SequenceNo = 1, ShowOnList = true, IsListData = true, SubPageID = 1, DataTypeId = 1, Name = "Test" };
            var item = new PageDetailModel() { Id = 6 };
            var subPageFieldsInsert = new List<M_SubPageFields>();
            var m_SubPageFields = new List<M_SubPageFields>() { new M_SubPageFields { FieldID = 1, IsCustom = false, IsActive = true, IsDeleted = false, IsListData = false, ShowOnList = false, SequenceNo = 1, AliasName = "Filed Name", Name = "Name", IsMandatory = false, DataTypeId = 0, SubPageID = 1, PagePath = "", EncryptedSubPageID = "s", CreatedBy = 1, CreatedOn = DateTime.Now, ModifiedBy = 1, ModifiedOn = DateTime.Now }, new M_SubPageFields { FieldID = 2, IsCustom = false, IsActive = false, IsDeleted = false, IsListData = false, ShowOnList = false, SequenceNo = 2, AliasName = "2Filed Name", Name = "Customfield", IsMandatory = false, DataTypeId = 0, SubPageID = 1, PagePath = "", EncryptedSubPageID = "s", CreatedBy = 1, CreatedOn = DateTime.Now, ModifiedBy = 1, ModifiedOn = DateTime.Now }, new M_SubPageFields { FieldID = 2, IsCustom = false, IsActive = true, IsDeleted = false, IsListData = false, ShowOnList = false, SequenceNo = 2, AliasName = "2Filed Name", Name = "Customfield", IsMandatory = false, DataTypeId = 0, SubPageID = 9, PagePath = "", EncryptedSubPageID = "s", CreatedBy = 1, CreatedOn = DateTime.Now, ModifiedBy = 1, ModifiedOn = DateTime.Now } };
            _fixture.PageDetailsConfigurationService.PageConfigNotExistsSubPageFields(1, m_SubPageFields, subPageFieldsInsert, item, 1, subPageField);
            Assert.IsType<List<M_SubPageFields>>(subPageFieldsInsert);
        }
        [Fact]
        public void ShouldUpdateSubPageFieldIfExists()
        {
            var subPageField = new SubPageFieldModel() { Id = 1, DisplayName = "Test", IsActive = true, IsDeleted = false, IsCustom = true, IsChart = true, SequenceNo = 1, ShowOnList = true, IsListData = true, SubPageID = 1, DataTypeId = 1, Name = "Test" };
            var item = new PageDetailModel() { Id = 6 };
            var subPageFieldsupdate = new List<M_SubPageFields>();
            var m_SubPageFields = new List<M_SubPageFields>() { new M_SubPageFields { FieldID = 1, IsCustom = false, IsActive = true, IsDeleted = false, IsListData = false, ShowOnList = false, SequenceNo = 1, AliasName = "Filed Name", Name = "Name", IsMandatory = false, DataTypeId = 0, SubPageID = 1, PagePath = "", EncryptedSubPageID = "s", CreatedBy = 1, CreatedOn = DateTime.Now, ModifiedBy = 1, ModifiedOn = DateTime.Now }, new M_SubPageFields { FieldID = 2, IsCustom = false, IsActive = false, IsDeleted = false, IsListData = false, ShowOnList = false, SequenceNo = 2, AliasName = "2Filed Name", Name = "Customfield", IsMandatory = false, DataTypeId = 0, SubPageID = 1, PagePath = "", EncryptedSubPageID = "s", CreatedBy = 1, CreatedOn = DateTime.Now, ModifiedBy = 1, ModifiedOn = DateTime.Now }, new M_SubPageFields { FieldID = 2, IsCustom = false, IsActive = true, IsDeleted = false, IsListData = false, ShowOnList = false, SequenceNo = 2, AliasName = "2Filed Name", Name = "Customfield", IsMandatory = false, DataTypeId = 0, SubPageID = 9, PagePath = "", EncryptedSubPageID = "s", CreatedBy = 1, CreatedOn = DateTime.Now, ModifiedBy = 1, ModifiedOn = DateTime.Now } };
            _fixture.PageDetailsConfigurationService.PageConfigExistsSubPagePageFields(1, m_SubPageFields, subPageFieldsupdate, item, 1, subPageField);
            Assert.IsType<List<M_SubPageFields>>(subPageFieldsupdate);
        }
        [Fact]
        public void ShouldUpdateChartValueForSubSectionFields()
        {
            var mSubsectionValuesList = GetSubSectionFieldsData();
            var mSubSectionFieldsUpdateList = new List<MSubSectionFields>();
            SubPageFieldModel values = new () { Id=1,SubPageID=2,MSubFields= new List<MSubFields>() { new MSubFields { AliasName = "Test", ChartValue = new List<string> { "Q", "M", "A" }, FieldID = 1, SubPageID = 1, SectionID = 1, Options = new List<string> { "Q", "M", "A" } } } };
            PageDetailsConfigurationService.KeyPerformanceAndFinancialDataTypes(1, mSubsectionValuesList, mSubSectionFieldsUpdateList, values);
            Assert.IsType<int>(mSubSectionFieldsUpdateList.Count);
        }
        [Fact]
        public void PageConfigPageDetails_WhenNotEmpty_ThenReturnResults()
        {
           const int userId = 1;
            List<M_SubPageDetails> subpageDetailsList = new();
            M_SubPageDetails subpageDetails = new() { SubPageID = 1 };
            subpageDetailsList.Add(subpageDetails);
            List<M_SubPageDetails> updateSubPageDetails = new();
            PageDetailModel item = new() { Id = 1 };
            _fixture.PageDetailsConfigurationService.PageConfigPageDetails(userId, subpageDetailsList, updateSubPageDetails, item,0);
            Assert.Single(updateSubPageDetails);
        }
        [Fact]
        public void PageConfigPageDetails_WhenEmpty_ThenReturnResults()
        {
            const int userId = 1;
            List<M_SubPageDetails> subpageDetailsList = new();
            List<M_SubPageDetails> updateSubPageDetails = new();
            PageDetailModel item = new() { Id = 1 };
            _fixture.PageDetailsConfigurationService.PageConfigPageDetails(userId, subpageDetailsList, updateSubPageDetails, item,0);
            Assert.Empty(updateSubPageDetails);
        }
        [Fact]
        public void ShouldAddSubPageField_WhenListEmpty_ThenReturnResults()
        {
            var subPageField = new SubPageFieldModel();
            var item = new PageDetailModel();
            var subPageFieldsInsert = new List<M_SubPageFields>();
            var m_SubPageFields = new List<M_SubPageFields>();
            _fixture.PageDetailsConfigurationService.PageConfigNotExistsSubPageFields(1, m_SubPageFields, subPageFieldsInsert, item, 1, subPageField);
            Assert.Null(subPageFieldsInsert[0].AliasName);
            Assert.Equal(0,subPageFieldsInsert[0].FieldID);
            Assert.IsType<List<M_SubPageFields>>(subPageFieldsInsert);
        }
        [Fact]
        public async Task WhenuserInvokes_GetPageConfigSubSectionFields()
        {
            // Arrange
            List<string> kpiTypes = new List<string>() { "CompanyKPIs" };
            //Act
            var actual = await _fixture.PageDetailsConfigurationService.GetPageConfigSubSectionFields(kpiTypes);
            // Assert
            Assert.IsType<List<KpiConfig>>(actual);
        }

        [Fact]
        public async Task UpdateWorkFlowPCDetails_ShouldReturnTrue_WhenWorkFlowIsUpdated()
        {
            // Arrange
            var pageDetail = new M_PageDetails { Name = "Portfolio Company", IsWorkFlow = false };
            _fixture.MockunitOfWork.Setup(u => u.PageDetailsRepository.FindFirstAsync(x=>x.Name== "Portfolio Company"))
                .ReturnsAsync(pageDetail);
            // Act
            var result = await _fixture.PageDetailsConfigurationService.UpdateWorkFlowPCDetails(true);
            // Assert
            Assert.True(result);           
        }
        [Fact]
        public async Task UpdateWorkFlowPCDetails_ShouldReturnFalse_WhenWorkFlowIsNotFound()
        {
            _fixture.MockunitOfWork.Setup(u => u.PageDetailsRepository.FindFirstAsync(x => x.Name == "Funds"))
                .ReturnsAsync((M_PageDetails)null);
            var result = await _fixture.PageDetailsConfigurationService.UpdateWorkFlowPCDetails(true);

            // Assert
            Assert.False(result);
        }
    }
}