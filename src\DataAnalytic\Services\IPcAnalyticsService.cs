﻿using Contract.Configuration;
using Contract.ConsolidatedReport;
using Contract.Employee;
using Contract.FxRates;
using Contract.PortfolioCompany;
using DataAccessLayer.DBModel;
using DataAnalytic.Models;
using System.Dynamic;
namespace DataAnalytic.Services
{
    public interface IPcAnalyticsService
    {
        Task<List<ExpandoObject>> GetPortfolioCompanyDetailsBySection(int userId, DataAnalyticsPcFilter filter);
        Task<List<ExpandoObject>> GetPortfolioCompanyDetailsForAnalytics(DataAnalyticsBiFilter filter);
        Task<List<SubPageFieldModel>> GetPageConfigActiveFieldsBySubPageId(List<int> subPageId);
        List<PageFieldValueModel> GetPageConfigStaticFieldValues(int pageID, int entityID);
        List<MappingEmployeeModel> PortfolioInvestmentProfessionals();
        Task<StaticSubPageFieldModel> GetStaticFields();
        List<DataAnalyticsPortfolioCompanyDetails> PortfolioStaticDetails();
        List<ConsolidatedFundDetails> GetFundDetials(int fundId);
        List<DataAnalyticsGeographicLocation> PortfolioGeographicLocation();
        Task<StaticSubPageFieldModel> GetFixedStaticFields();
        Task<List<Dictionary<string, object>>> GetPcAnalyticsDetails(DataAnalyticsBiFilter filter);
        Task<Dictionary<string, List<KpiMaster>>> GetMasterKpiList(DataAnalyticsPcFilter pcFilter);
        Task<List<KpiResponse>> GetMappingKpiList(DataAnalyticsPcFilter pcFilter);
        Task<List<CompanyModel>> GetCompaniesByAccess(int userId);
        Task<List<FxRateModel>> GetFxRates(FxRateFilterModel filter);
    }
}