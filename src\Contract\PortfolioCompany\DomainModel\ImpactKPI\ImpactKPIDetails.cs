using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Contract.PortfolioCompany
{
    public class ImpactKpiDetails : BaseModel
    {
        public int ImpactKPIId { get; set; }
        [MaxLength(200)]
        public string KPI { get; set; }
        [MaxLength(20)]
        public string KpiInfo { get; set; }
        [MaxLength(500)]
        public string Description { get; set; }
        public string MethodologyName { get; set; }
        public bool IsDeleted { get; set; }
        [MaxLength(500)]
        public string EncryptedImpactKPIId { get; set; }
        public int? ParentId { get; set; }
        public bool IsParent { get; set; }
        public int KPIOrder { get; set; }
        public bool IsHeader { get; set; }
        public bool? IsBoldKPI { get; set; }
        public string KpiInfoType { get; set; }
        public string Formula { get; set; }
        public string FormulaKPIId { get; set; }
        public int MethodologyID { get; set; }
        public string Synonym { get; set; }
    }
}
