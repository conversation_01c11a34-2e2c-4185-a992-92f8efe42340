﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DataAccessLayer.DBModel;

namespace DataAccessLayer.Models.DocumentCollection
{
    [Table("DocumentCollectionStore")]
    public class DocumentCollectionStore : BaseCommonModel
    {
        /// <summary>
        /// Primary key identifier
        /// </summary>
        [Key]   
        public Guid ID { get; set; }

        /// <summary>
        /// Type of source (1 = local, other values for external resources)
        /// </summary>
        public int SourceTypeId { get; set; }

        /// <summary>
        /// S3 path where the document is stored
        /// </summary>
        public string S3Path { get; set; }

        /// <summary>
        /// Original filename of the document
        /// </summary>
        [MaxLength(255)]
        public string FileName { get; set; }

        /// <summary>
        /// File type/extension
        /// </summary>
        [MaxLength(100)]
        public string Type { get; set; }

        /// <summary>
        /// ID reference to RepositoryDocumentMappingDetails
        /// </summary>
        public Guid FolderMappingId { get; set; }

        /// <summary>
        /// Indicates the document upload source; 0 represents legacy entries, 1 for manual uploads, and 2 for data ingestion.
        /// </summary>
        public int UploadType { get; set; }


    }
}
