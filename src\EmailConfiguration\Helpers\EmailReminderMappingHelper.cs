using System;
using System.Collections.Generic;
using System.Linq;
using DataAccessLayer.Models;
using DataAccessLayer.Models.DataExtraction;
using DataAccessLayer.Models.EmailNotifications;
using DataAccessLayer.DBModel;
using EmailConfiguration.DTOs;

namespace EmailConfiguration.Helpers
{
    /// <summary>
    /// Helper class for mapping email reminder entities to DTOs
    /// </summary>
    public static class EmailReminderMappingHelper
    {
        /// <summary>
        /// Maps EmailReminder entity to EmailRemainderDefaultDataResponseDto
        /// </summary>
        /// <param name="emailReminder">Email reminder entity</param>
        /// <param name="userInformations">User information list</param>
        /// <param name="groupInfoList">Company email groups</param>
        /// <param name="portfolioCompanies">Portfolio companies</param>
        /// <param name="extractionTypes">Document types</param>
        /// <returns>EmailRemainderDefaultDataResponseDto</returns>
        public static EmailRemainderDefaultDataResponseDto MapToDefaultDataResponseDto(
            EmailReminder emailReminder,
            List<UserInformation> userInformations,
            List<CompanyEmailGroup> groupInfoList,
            List<PortfolioCompanyDetails> portfolioCompanies,
            List<DataExtractionTypes> extractionTypes)
        {
            var responseDto = new EmailRemainderDefaultDataResponseDto
            {
                ReminderId = emailReminder.ReminderId.ToString(),
                PortfolioCompanys = EmailReminderHelper.MapCompaniesToEntityDetails(portfolioCompanies),
                DocumentTypes = EmailReminderHelper.MapDocumentTypesToEntityDetails(extractionTypes),
                ToRecipients = MapUserInformationToEmailRecipients(userInformations, "to"),
                CcReciepients = MapUserInformationToEmailRecipients(userInformations, "cc")
            };

            // Add group recipients to CC
            if (groupInfoList.Count > 0)
            {
                var groupRecipients = groupInfoList.Select(x => new EmailRecipientsDto
                {
                    InternalID = x.GroupId,
                    Name = x.Group.GroupName,
                    Email = string.Empty,
                });
                responseDto.CcReciepients.AddRange(groupRecipients);
            }

            return responseDto;
        }

        /// <summary>
        /// Maps user information to email recipients based on recipient type
        /// </summary>
        /// <param name="userInformations">User information list</param>
        /// <param name="recipientType">Recipient type (to/cc)</param>
        /// <returns>List of EmailRecipientsDto</returns>
        public static List<EmailRecipientsDto> MapUserInformationToEmailRecipients(
            List<UserInformation> userInformations, string recipientType)
        {
            return userInformations
                .Where(x => EmailReminderHelper.IsStringMatch(x.Recipient, recipientType))
                .Select(x => new EmailRecipientsDto
                {
                    InternalID = x.UserInformationID,
                    Name = x.Name,
                    Email = x.Email,
                })
                .ToList();
        }

        /// <summary>
        /// Maps EmailReminder to EmailReminderListResponseDto
        /// </summary>
        /// <param name="reminder">Email reminder entity</param>
        /// <param name="companies">All available companies</param>
        /// <param name="documentTypes">All available document types</param>
        /// <returns>EmailReminderListResponseDto</returns>
        public static EmailReminderListResponseDto MapToListResponseDto(
            EmailReminder reminder,
            IEnumerable<PortfolioCompanyDetails> companies,
            IEnumerable<DataExtractionTypes> documentTypes)
        {
            return new EmailReminderListResponseDto
            {
                ReminderId = reminder.ReminderId,
                IsActive = reminder.IsActive,
                CreatedOn = reminder.CreatedOn,
                Companies = EmailReminderHelper.GetCompanyDetailsForReminder(reminder.EntityIDs, companies),
                DocumentTypes = EmailReminderHelper.GetDocumentTypeDetailsForReminder(reminder.DocumentTypeIds, documentTypes)
            };
        }

        /// <summary>
        /// Maps EmailReminder and recipients to EmailReminderDetailsDto
        /// </summary>
        /// <param name="emailReminder">Email reminder entity</param>
        /// <param name="recipients">Email reminder recipients</param>
        /// <param name="groups">Email notification groups</param>
        /// <param name="config">Email reminder config (optional)</param>
        /// <param name="totalSent">Total reminders sent</param>
        /// <param name="lastSentDate">Last reminder sent date</param>
        /// <param name="nextScheduledDate">Next reminder scheduled date</param>
        /// <returns>EmailReminderDetailsDto</returns>
        public static EmailReminderDetailsDto MapToDetailsDto(
            EmailReminder emailReminder,
            List<EmailReminderRecipients> recipients,
            List<EmailNotificationGroup> groups,
            EmailReminderConfig config = null,
            int totalSent = 0,
            DateTime? lastSentDate = null,
            DateTime? nextScheduledDate = null,
            int errorandSkipedCount = 0)
        {
            var toRecipients = recipients
                .Where(r => r.RecipientType == RecipientTypes.To)
                .Select(r => EmailReminderRecipientHelper.MapToEmailRecipientDetailsDto(r, groups))
                .ToList();

            var ccRecipients = recipients
                .Where(r => r.RecipientType == RecipientTypes.Cc)
                .Select(r => EmailReminderRecipientHelper.MapToEmailRecipientDetailsDto(r, groups))
                .ToList();

            return new EmailReminderDetailsDto
            {
                ReminderId = emailReminder.ReminderId,
                Subject = emailReminder.Subject,
                MessageBody = emailReminder.EmailBody,
                ToRecipients = toRecipients,
                CcRecipients = ccRecipients,
                TotalNumberOfReminders = config?.TotalRemindersPerCycle ?? 0,
                TotalRemindersSent = totalSent,
                LastReminderSentDate = lastSentDate,
                NextReminderScheduledDate = nextScheduledDate,
                TotalErrorAndSkippedReminders = errorandSkipedCount
            };
        }

        /// <summary>
        /// Maps schedule and reminder data to EmailReminderNotificationDto
        /// </summary>
        /// <param name="schedule">Email reminder schedule</param>
        /// <param name="config">Email reminder config</param>
        /// <param name="emailReminder">Email reminder</param>
        /// <param name="recipients">Email reminder recipients</param>
        /// <returns>EmailReminderNotificationDto</returns>
        public static EmailReminderNotificationDto MapToNotificationDto(
            EmailReminderSchedule schedule,
            EmailReminderConfig config,
            EmailReminder emailReminder,
            List<EmailReminderRecipients> recipients)
        {
            var toRecipients = EmailReminderRecipientHelper.MapToEmailRecipientDto(
                recipients, RecipientTypes.To);

            var ccRecipients = EmailReminderRecipientHelper.MapToEmailRecipientDto(
                recipients, RecipientTypes.Cc);

            return new EmailReminderNotificationDto
            {
                ScheduleId = schedule.Id,
                ReminderId = config.ReminderId,
                Subject = emailReminder.Subject,
                EmailBody = emailReminder.EmailBody,
                ReminderDate = schedule.ReminderDate,
                ScheduleOccurrence = schedule.ScheduleOccurrence,
                ToRecipients = toRecipients,
                CcRecipients = ccRecipients
            };
        }

        /// <summary>
        /// Updates EmailReminder entity from UpdateEmailRemainderDto
        /// </summary>
        /// <param name="emailReminder">Email reminder entity to update</param>
        /// <param name="dto">Update DTO</param>
        /// <param name="userId">User ID for audit</param>
        public static void UpdateEmailReminderFromDto(EmailReminder emailReminder, UpdateEmailRemainderDto dto, int userId)
        {
            emailReminder.FeatureID = dto.FeatureID;
            emailReminder.EntityIDs = EmailReminderHelper.ConvertIdsToCommaSeparatedString(dto.PortfolioCompanys.Select(x => x.Id));
            emailReminder.DocumentTypeIds = EmailReminderHelper.ConvertIdsToCommaSeparatedString(dto.DocumentTypes.Select(x => x.Id));
            emailReminder.Subject = dto.Subject;
            emailReminder.EmailBody = dto.EmailBody;

            EmailReminderAuditHelper.SetUpdateAuditFields(emailReminder, userId);
        }

        /// <summary>
        /// Updates EmailReminderConfig entity from UpdateEmailRemainderDto
        /// </summary>
        /// <param name="config">Email reminder config to update</param>
        /// <param name="dto">Update DTO</param>
        /// <param name="userId">User ID for audit</param>
        public static void UpdateEmailReminderConfigFromDto(EmailReminderConfig config, UpdateEmailRemainderDto dto, int userId)
        {
            config.FrequencyType = dto.FrequencyType;
            config.TotalRemindersPerCycle = dto.TotalRemindersPerCycle;
            config.Remainder1Date = dto.Remainder1Date;
            config.Remainder2 = dto.Remainder2;
            config.Remainder3 = dto.Remainder3;
            config.Remainder4 = dto.Remainder4;
            config.Remainder5 = dto.Remainder5;

            EmailReminderAuditHelper.SetUpdateAuditFields(config, userId);
        }

        public static UpdateEmailRemainderDto MaptoUpdateEmailRemainderDto(EmailReminder emailReminder, 
            List<PortfolioCompanyDetails> companies,
            List<DataExtractionTypes>  documentTypes,
            List<EmailReminderRecipients> recipients,
            List<EmailNotificationGroup> groups,
            EmailReminderConfig config)
        {
            var toRecipients = recipients
                .Where(r => r.RecipientType == RecipientTypes.To)
                .Select(r => EmailReminderRecipientHelper.MapToEmailRecipientDetailsDto(r, groups))
                .ToList();

            var ccRecipients = recipients
                .Where(r => r.RecipientType == RecipientTypes.Cc)
                .Select(r => EmailReminderRecipientHelper.MapToEmailRecipientDetailsDto(r, groups))
                .ToList();
            return new UpdateEmailRemainderDto()
            {
                FeatureID = emailReminder.FeatureID,
                PortfolioCompanys = companies.Select(i => new EntityDetails() { Id = i.PortfolioCompanyId, Name = i.CompanyName }).ToList(),
                DocumentTypes = documentTypes.Select(i => new EntityDetails() { Id = i.Id, Name = i.DocumentName }).ToList(),
                ToRecipients = toRecipients.Select(i => new EmailRecipientsDto() { InternalID = i.RecipientId.HasValue ? (int)i.RecipientId : 0, Name = i.Name, Email = i.EmailAddress }).ToList(),
                CcReciepients = ccRecipients.Select(i => new EmailRecipientsDto() { InternalID = i.IsGroupMember ? (int)i.GroupID : i.RecipientId.HasValue ? (int)i.RecipientId : 0, Name = i.Name, Email = i.EmailAddress }).ToList(),
                Subject = emailReminder.Subject,
                EmailBody = emailReminder.EmailBody,
                TotalRemindersPerCycle = config.TotalRemindersPerCycle,
                FrequencyType = config.FrequencyType,
                Remainder1Date =config.Remainder1Date,
                Remainder2 = config.Remainder2,
                Remainder3 = config.Remainder3,
                Remainder4 = config.Remainder4,
                Remainder5 = config.Remainder5,
            };
        }       
    }
}
