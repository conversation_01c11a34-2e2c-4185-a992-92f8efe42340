using DataAccessLayer.DBModel;
namespace DataAccessLayer.Models
{
    public partial class Mapping_ImpactKPI_Order : BaseModel
    {
        public int ImpactKPIMappingID { get; set; }
        public int? PortfolioCompanyID { get; set; }
        public int? ImpactKPIID { get; set; }
        public int? KPIOrder { get; set; }
        public int? ParentKPIID { get; set; }
        public bool IsDeleted { get; set; }
        public string EncryptedImpactKPIOrderID { get; set; }
        public string Formula { get; set; }
        public string FormulaKPIId { get; set; }
        public bool IsExtraction { get; set; }
        public string Synonym { get; set; }
        public string Definition { get; set; }

        public virtual MImpactKpi MImpactKpi { get; set; }
        public virtual PortfolioCompanyDetails PortfolioCompanyDetails { get; set; }
    }
}
