﻿using API.Controllers;
using API.Helpers;
using Contract.Deals;
using Contract.Funds;
using Contract.KPI;
using Contract.Utility;
using DapperRepository;
using DataAccessLayer.DBModel;
using DataAccessLayer.GenericRepository;
using DataAccessLayer.Models;
using DataAccessLayer.UnitOfWork;
using ESG;
using Master;
using Microsoft.Extensions.Logging;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PortfolioCompany.UnitTest.Tests {
    public class PortfolioCompanyFixtures {
        public Mock<IKpiService> KPIService;
        public Mock<IEsgKpiService> _esgKpiService;
        public KpiController _kpiControllerMockService;
        public Mock<IInjectedParameters> InjectedParameters;
        public readonly KpiService _kpiService;
        public readonly PortfolioCompanyService _portfolioCompanyService;
        public readonly Mock<IUnitOfWork> UnitOfWork;
        public readonly Mock<IGenericRepository<MImpactKpi>> _impactKpiRepository;
        public readonly Mock<IGenericRepository<M_CompanyKpi>> _companyKpiRepository;
        public readonly Mock<IGenericRepository<M_InvestmentKpi>> _investmentKpiRepository;
        public readonly Mock<IGenericRepository<M_BalanceSheet_LineItems>> _balanceSheetKpiRepository;
        public readonly Mock<IGenericRepository<M_CashFlow_LineItems>> _cashflowKpiRepository;
        public readonly Mock<IGenericRepository<M_ProfitAndLoss_LineItems>> _profitLossKpiRepository;
        public readonly Mock<IGenericRepository<M_SectorwiseKPI>> _operationalKpiRepository;
        public readonly Mock<IGenericRepository<ProfitAndLossValues>> _profitAndLossValuesRepository;
        public readonly Mock<IGenericRepository<M_ProfitAndLoss_LineItems>> _profitAndLoss_LineItemsRepository;
        public readonly Mock<IGenericRepository<Mapping_CompanyProfitAndLossLineItems>> _mapping_CompanyProfitAndLossLineItemsRepository;
        public readonly Mock<IGenericRepository<BalanceSheetValues>> _balanceSheetValuesRepository;
        public readonly Mock<IGenericRepository<M_BalanceSheet_LineItems>> _balanceSheet_LineItemsRepository;
        public readonly Mock<IGenericRepository<Mapping_CompanyBalanceSheetLineItems>> _mapping_CompanyBalanceSheetLineItemsRepository;
        public readonly Mock<IGenericRepository<CashFlowValues>> _cashFlowValuesRepository;
        public readonly Mock<IGenericRepository<M_CashFlow_LineItems>> _cashFlow_LineItemsRepository;
        public readonly Mock<IGenericRepository<Mapping_CompanyCashFlowLineItems>> _mapping_CompanyCashFlowLineItemsRepository;
        private readonly Mock<IDealService> dealService;
        public Mock<IEncryption> Encryption;
        public Mock<ILogger> Logger;
        readonly Mock<IPageDetailsConfigurationService> MockPageDetailsConfigurationService = new();
        readonly Mock<IFundService> MockIFundService = new();
        public readonly Mock<IGenericRepository<MappingPortfolioCompanyKpi>> _portfolioCompanyKpiMappingRepository;
        readonly Mock<IMemoryCacher> _memoryCacher = new();
        readonly Mock<IGlobalConfigurations> _globalConfig = new();
        public readonly Mock<IPageDetailsConfigurationService> IPageDetailsConfigurationService = new();
        public PortfolioCompanyFixtures () {
            UnitOfWork = new Mock<IUnitOfWork> ();
            dealService = new Mock<IDealService> ();
            _impactKpiRepository = new Mock<IGenericRepository<MImpactKpi>> ();
            UnitOfWork.Setup (s => s.M_ImpactKPIRepository).Returns (_impactKpiRepository.Object);

            _companyKpiRepository = new Mock<IGenericRepository<M_CompanyKpi>> ();
            UnitOfWork.Setup (s => s.M_CompanyKPIRepository).Returns (_companyKpiRepository.Object);

            _investmentKpiRepository = new Mock<IGenericRepository<M_InvestmentKpi>> ();
            UnitOfWork.Setup (s => s.M_InvestmentKPIRepository).Returns (_investmentKpiRepository.Object);

            _balanceSheetKpiRepository = new Mock<IGenericRepository<M_BalanceSheet_LineItems>> ();
            UnitOfWork.Setup (s => s.M_BalanceSheet_LineItemsRepository).Returns (_balanceSheetKpiRepository.Object);

            _cashflowKpiRepository = new Mock<IGenericRepository<M_CashFlow_LineItems>> ();
            UnitOfWork.Setup (s => s.M_CashFlow_LineItemsRepository).Returns (_cashflowKpiRepository.Object);

            _companyKpiRepository = new Mock<IGenericRepository<M_CompanyKpi>> ();
            UnitOfWork.Setup (s => s.M_CompanyKPIRepository).Returns (_companyKpiRepository.Object);

            _profitLossKpiRepository = new Mock<IGenericRepository<M_ProfitAndLoss_LineItems>> ();
            UnitOfWork.Setup (s => s.M_ProfitAndLoss_LineItemsRepository).Returns (_profitLossKpiRepository.Object);

            _operationalKpiRepository = new Mock<IGenericRepository<M_SectorwiseKPI>> ();
            UnitOfWork.Setup (s => s.M_SectorwiseKPIRepository).Returns (_operationalKpiRepository.Object);

            _portfolioCompanyKpiMappingRepository = new Mock<IGenericRepository<MappingPortfolioCompanyKpi>> ();
            UnitOfWork.Setup (s => s.PortfolioCompanyKpiMappingRepository).Returns (_portfolioCompanyKpiMappingRepository.Object);

            Mock<IGenericRepository<ProfitAndLossValues>> ProfitAndLossValuesRepository = new();
            ProfitAndLossValuesRepository.Setup(S => S.GetManyQueryable(It.IsAny<Func<ProfitAndLossValues, bool>>())).Returns(new List<ProfitAndLossValues>() {
                new ProfitAndLossValues () {
                    ActualValue="5",
                    BudgetValue="NA",
                    IsActive=true,
                    IsDeleted=false,
                    Month=1,
                    Year=2020,
                    ValueInfo="$",
                    //Mapping_CompanyProfitAndLossLineItemsDetail = new Mapping_CompanyProfitAndLossLineItems()
                    //{
                    //    PortfolioCompanyID = 1,
                    //    M_ProfitAndLoss_LineItemsDetail = new M_ProfitAndLoss_LineItems()
                    //    {
                    //        DisplayOrder = 1
                    //    }
                    //}
                }
            }.AsQueryable());

            _profitAndLossValuesRepository = new Mock<IGenericRepository<ProfitAndLossValues>>();
            UnitOfWork.Setup(s => s.ProfitAndLossValuesRepository).Returns(ProfitAndLossValuesRepository.Object);

            _profitAndLoss_LineItemsRepository = new Mock<IGenericRepository<M_ProfitAndLoss_LineItems>>();
            UnitOfWork.Setup(s => s.M_ProfitAndLoss_LineItemsRepository).Returns(_profitAndLoss_LineItemsRepository.Object);

            _mapping_CompanyProfitAndLossLineItemsRepository = new Mock<IGenericRepository<Mapping_CompanyProfitAndLossLineItems>>();
            UnitOfWork.Setup(s => s.Mapping_CompanyProfitAndLossLineItemsRepository).Returns(_mapping_CompanyProfitAndLossLineItemsRepository.Object);


            Mock<IGenericRepository<BalanceSheetValues>> BalanceSheetValuesRepository = new();
            BalanceSheetValuesRepository.Setup(S => S.GetManyQueryable(It.IsAny<Func<BalanceSheetValues, bool>>())).Returns(new List<BalanceSheetValues>() {
                new BalanceSheetValues () {
                    ActualValue="5",
                    BudgetValue="NA",
                    IsActive=true,
                    IsDeleted=false,
                    Month=1,
                    Year=2020,
                    ValueInfo="$"
                }
            }.AsQueryable());

            _balanceSheetValuesRepository = new Mock<IGenericRepository<BalanceSheetValues>>();
            UnitOfWork.Setup(s => s.BalanceSheetValuesRepository).Returns(BalanceSheetValuesRepository.Object);

            _balanceSheet_LineItemsRepository = new Mock<IGenericRepository<M_BalanceSheet_LineItems>>();
            UnitOfWork.Setup(s => s.M_BalanceSheet_LineItemsRepository).Returns(_balanceSheet_LineItemsRepository.Object);

            _mapping_CompanyBalanceSheetLineItemsRepository = new Mock<IGenericRepository<Mapping_CompanyBalanceSheetLineItems>>();
            UnitOfWork.Setup(s => s.Mapping_CompanyBalanceSheetLineItemsRepository).Returns(_mapping_CompanyBalanceSheetLineItemsRepository.Object);


            Mock<IGenericRepository<CashFlowValues>> CashFlowValuesRepository = new();
            CashFlowValuesRepository.Setup(S => S.GetManyQueryable(It.IsAny<Func<CashFlowValues, bool>>())).Returns(new List<CashFlowValues>() {
                new CashFlowValues () {
                    ActualValue="5",
                    BudgetValue="NA",
                    IsActive=true,
                    IsDeleted=false,
                    Month=1,
                    Year=2020
                }
            }.AsQueryable());

            _cashFlowValuesRepository = new Mock<IGenericRepository<CashFlowValues>>();
            UnitOfWork.Setup(s => s.CashFlowValuesRepository).Returns(CashFlowValuesRepository.Object);

            _cashFlow_LineItemsRepository = new Mock<IGenericRepository<M_CashFlow_LineItems>>();
            UnitOfWork.Setup(s => s.M_CashFlow_LineItemsRepository).Returns(_cashFlow_LineItemsRepository.Object);

            _mapping_CompanyCashFlowLineItemsRepository= new Mock<IGenericRepository<Mapping_CompanyCashFlowLineItems>>();
            UnitOfWork.Setup(s => s.Mapping_CompanyCashFlowLineItemsRepository).Returns(_mapping_CompanyCashFlowLineItemsRepository.Object);


            KPIService = new Mock<IKpiService> ();
            _esgKpiService=new Mock<IEsgKpiService> ();
            InjectedParameters = new Mock<IInjectedParameters> ();
            Encryption = new Mock<IEncryption>();
            Logger = new Mock<ILogger>();
            Mock<IDapperGenericRepository> dapperGenericRepository = new();
            InjectedParameters.Setup(S => S.UnitOfWork).Returns(UnitOfWork.Object);
            InjectedParameters.Setup(S => S.Logger).Returns(Logger.Object);
            Encryption.Setup(S => S.Encrypt(It.IsAny<string>())).Returns("66F63D87E53A6F638CBDBE6B3B83AEA3");
            Mock<IHelperService> helperService = new Mock<IHelperService>();
            List<Mapping_Kpis> mappedKpis = new()
            {
                new Mapping_Kpis
                {
                    KpiID = 1,
                    IsDeleted = false,
                    Mapping_KpisID = 1,
                    PortfolioCompanyID = 1,
                    ModuleID = 17,
                    DisplayOrder = 1,
                }
            };
            List<M_MasterKpis> kpis = new()
            {
                new M_MasterKpis { KPI = "Custom Table Kpi", KpiInfo = "$", MasterKpiID = 1, IsDeleted = false, ModuleID = 17 }
            };
            _kpiControllerMockService = new KpiController (KPIService.Object, InjectedParameters.Object,helperService.Object, IPageDetailsConfigurationService.Object, _esgKpiService.Object);
            dapperGenericRepository.Setup(x => x.Query<Mapping_Kpis>(DapperRepository.Constants.SqlConstants.QueryByMappingKPIs, It.IsAny<object>())).Returns(Task.FromResult(mappedKpis));
            dapperGenericRepository.Setup(x => x.Query<M_MasterKpis>(DapperRepository.Constants.SqlConstants.QueryByMasterKpis, It.IsAny<object>())).Returns(Task.FromResult(kpis));
            _kpiService = new KpiService(dapperGenericRepository.Object,UnitOfWork.Object,Encryption.Object,_memoryCacher.Object,_globalConfig.Object);
            Mock<IDapperGenericRepository> dappermock = new Mock<IDapperGenericRepository>();
            _portfolioCompanyService = new PortfolioCompanyService(dappermock.Object, dealService.Object);
            _portfolioCompanyService.InjectedParameters = InjectedParameters.Object;
        }
    }
}