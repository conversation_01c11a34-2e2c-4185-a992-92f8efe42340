﻿
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace DataAccessLayer.DBModel
{
    public partial class M_CashFlow_LineItems : BaseModel
    {
        public int CashFlowLineItemID { get; set; }
        public string CashFlowLineItem { get; set; }
        public int? ParentId { get; set; }
        public int DisplayOrder { get; set; }
        public int? MethodologyID { get; set; }
        public bool isHeader { get; set; }
        public string Description { get; set; }
        public bool IsDeleted { get; set; }
        public bool IsActive { get; set; }
        public string EncryptedCashFlowLineItemID { get; set; }
        public bool IsCalculatedValue { get; set; }
        public string AnnualCalculationMethod { get; set; }
        public string KPIInfo { get; set; }
        public bool IsBoldKPI { get; set; }
        public string Formula { get; set; }
        public string FormulaKPIId { get; set; }
        [NotMapped]
        public string KpiInfoType { get; set; }
        [NotMapped]
        public string MethodologyName { get; set; }
        public string Synonym { get; set; }
    }
}
