using System.Collections.Generic;
using System;

namespace Contract.Fund.Models
{
    public class FundKpiModel
    {
        public int FundSectionKpiId { get; set; }
        public int? ModuleId { get; set; }
        public string Kpi { get; set; }
        public string KpiInfo { get; set; }
        public string Description { get; set; }
        public int? MethodologyId { get; set; }
        public string MethodologyName { get; set; }
        public bool IsBoldKpi { get; set; }
        public bool IsHeader { get; set; }
        public string Formula { get; set; }
        public string FormulaKpiId { get; set; }
        public string KpiInfoType { get; set; }
        public string EncryptedFundSectionKpiId { get; set; }
        public int CreatedBy { get; set; }
        public DateTime CreatedOn { get; set; }
        public DateTime? ModifiedOn { get; set; }
        public int? ModifiedBy { get; set; }
        public bool IsActive { get; set; }
        public bool IsExtraction { get; set; }
        public string Synonym { get; set; }
        public string Definition { get; set; }
    }
}
