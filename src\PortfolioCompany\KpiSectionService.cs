using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Contract.Account;
using Contract.KPI;
using DataAccessLayer.Models;
using DataAccessLayer.Models.MonthlyReport;
using DataAccessLayer.UnitOfWork;
using PortfolioCompany.Interfaces;
using Shared;

namespace PortfolioCompany
{
    public class KpiSectionService(IUnitOfWork unitOfWork) : IKpiSection
    {
        private readonly IUnitOfWork _unitOfWork = unitOfWork;
        /// <summary>
        /// Retrieves a list of CapTableModel objects based on the specified module ID.
        /// </summary>
        /// <param name="moduleId">The ID of the module.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains a list of CapTableModel objects.</returns>
        public Task<List<CapTableModel>> GetKpiListByModuleId(int moduleId)
        {
            return Task.FromResult((from kpiLineItem in _unitOfWork.MCapTableRepository.GetQueryable()
                                    join methodology in _unitOfWork.M_MethodologyRepository.GetQueryable() on kpiLineItem.MethodologyID equals methodology.MethodologyID into methodologyGroup
                                    from methodology in methodologyGroup.DefaultIfEmpty()
                                    join kpiType in _unitOfWork.MKpiTypeRepository.GetQueryable() on kpiLineItem.KpiTypeId equals kpiType.KpiTypeId into kpiTypeGroup
                                    from kpiType in kpiTypeGroup.DefaultIfEmpty()
                                    where kpiLineItem != null && (methodology != null || kpiLineItem.MethodologyID == 0) && kpiLineItem.ModuleId == moduleId && !kpiLineItem.IsDeleted && (methodology == null || !methodology.IsDeleted)
                                    select new CapTableModel()
                                    {
                                        KpiId = kpiLineItem.KpiId,
                                        Kpi = kpiLineItem.Kpi,
                                        KpiInfo = kpiLineItem.KpiInfo,
                                        ModuleId = moduleId,
                                        MethodologyID = kpiLineItem.MethodologyID == 0 ? 0 : (int)kpiLineItem.MethodologyID,
                                        MethodologyName = methodology == null ? null : methodology.MethodologyName,
                                        KpiInfoType = GetKpiInfoType(kpiLineItem.KpiInfo),
                                        IsBoldKpi = kpiLineItem.IsBoldKpi,
                                        IsHeader = kpiLineItem.IsHeader,
                                        Formula = kpiLineItem.Formula,
                                        FormulaKPIId = kpiLineItem.FormulaKPIId,
                                        KpiType = kpiType.KpiType,
                                        Description = kpiLineItem.Description,
                                        IsOverrule = kpiLineItem.IsOverrule,
                                        Synonym =  kpiLineItem.Synonym
                                    }).ToList());
        }
        /// <summary>
        /// Retrieves a list of MonthlyReportModel objects representing the monthly report KPIs.
        /// </summary>
        /// <returns>A task that represents the asynchronous operation. The task result contains the list of MonthlyReportModel objects.</returns>
        public Task<List<MonthlyReportModel>> GetMonthlyReportKpiList()
        {
            return Task.FromResult((from kpiLineItem in _unitOfWork.MMonthlyReportRepository.GetQueryable() where kpiLineItem != null && !kpiLineItem.IsDeleted
                                    select new MonthlyReportModel()
                                    {
                                        KpiId = kpiLineItem.KpiId,
                                        Kpi = kpiLineItem.Kpi,
                                        KpiInfo = kpiLineItem.KpiInfo,
                                        IsBoldKpi = kpiLineItem.IsBoldKpi,
                                        KpiInfoType = GetKpiInfoType(kpiLineItem.KpiInfo),
                                        IsHeader = kpiLineItem.IsHeader,
                                        Formula = kpiLineItem.Formula,
                                        FormulaKpiId = kpiLineItem.FormulaKpiId,
                                        Description = kpiLineItem.Description,
                                    }).ToList());
        }
        /// <summary>
        /// Retrieves a list of KpiTypeModel objects.
        /// </summary>
        /// <returns>A task that represents the asynchronous operation. The task result contains the list of KpiTypeModel objects.</returns>
        public async Task<List<Contract.Account.KpiTypeModel>> GetKpiTypeList()
        {
            var kpiTypes = await _unitOfWork.MKpiTypeRepository.FindAllAsync(x => !x.IsDeleted);
            return kpiTypes?.Select(x => new Contract.Account.KpiTypeModel()
            {
                KpiTypeId = x.KpiTypeId,
                KpiType = x.KpiType
            }).ToList();
        }
        /// <summary>
        /// Adds or updates a CapTable entry in the database.
        /// </summary>
        /// <param name="capTableModel">The CapTableModel object containing the data to be added or updated.</param>
        /// <param name="userId">The ID of the user performing the operation.</param>
        /// <returns>An integer value indicating the result of the operation. 
        /// Returns 1 if the CapTable entry was added or updated successfully, 
        /// -1 if there is already an existing CapTable entry with the same Kpi and ModuleId, 
        /// and 0 if the operation failed for any other reason.</returns>
        public async Task<int> AddUpdateCapTable(CapTableModel capTableModel, int userId)
        {
            if (capTableModel.KpiId > 0)
            {
                if (await _unitOfWork.MCapTableRepository.CountAsync(x => x.Kpi.ToLower() == capTableModel.Kpi.ToLower() && x.ModuleId == capTableModel.ModuleId && !x.IsDeleted) > 1)
                    return -1;
                var kpiLineItem = await _unitOfWork.MCapTableRepository.FindFirstAsync(x => x.KpiId == capTableModel.KpiId);
                if (kpiLineItem != null)
                {
                    kpiLineItem.Kpi = capTableModel.Kpi;
                    kpiLineItem.KpiInfo = capTableModel.KpiInfo;
                    kpiLineItem.Description = capTableModel.Description;
                    kpiLineItem.MethodologyID = (capTableModel.KpiInfo != Constants.KpiInfoText) ? capTableModel.MethodologyID : 0;
                    kpiLineItem.KpiTypeId = capTableModel.KpiTypeId;
                    kpiLineItem.IsBoldKpi = capTableModel.IsBoldKpi;
                    kpiLineItem.IsHeader = capTableModel.IsHeader;
                    kpiLineItem.KpiTypeId = capTableModel.KpiTypeId;
                    kpiLineItem.IsOverrule = capTableModel.IsOverrule;
                    kpiLineItem.CreatedBy = userId;
                    kpiLineItem.CreatedOn = DateTime.UtcNow;
                    kpiLineItem.Synonym = capTableModel.Synonym;
                    _unitOfWork.MCapTableRepository.Update(kpiLineItem);
                    await _unitOfWork.SaveAsync();
                    return 1;
                }
            }
            else
            {
                return await UpdateCapTable(capTableModel, userId);
            }
            return 0;
        }
        /// <summary>
        /// Adds or updates a monthly report table for a given monthly report model and user ID.
        /// </summary>
        /// <param name="monthlyReportModel">The monthly report model containing the data to be added or updated.</param>
        /// <param name="userId">The ID of the user performing the operation.</param>
        /// <returns>An integer representing the result of the operation. 
        /// Returns 1 if the monthly report table was successfully added or updated.
        /// Returns -1 if there are multiple existing records with the same KPI and not marked as deleted.
        /// Returns 0 if the operation was not performed.</returns>
        public async Task<int> AddUpdateMonthlyReportTable(MonthlyReportModel monthlyReportModel, int userId)
        {
            if (monthlyReportModel.KpiId > 0)
            {
                if (await _unitOfWork.MMonthlyReportRepository.CountAsync(x => x.Kpi.ToLower() == monthlyReportModel.Kpi.ToLower() && !x.IsDeleted) > 1)
                    return -1;
                var kpiLineItem = await _unitOfWork.MMonthlyReportRepository.FindFirstAsync(x => x.KpiId == monthlyReportModel.KpiId);
                if (kpiLineItem != null)
                {
                    kpiLineItem.Kpi = monthlyReportModel.Kpi;
                    kpiLineItem.KpiInfo = monthlyReportModel.KpiInfo;
                    kpiLineItem.Description = monthlyReportModel.Description;
                    kpiLineItem.IsBoldKpi = monthlyReportModel.IsBoldKpi;
                    kpiLineItem.IsHeader = monthlyReportModel.IsHeader;
                    kpiLineItem.ModifiedBy = userId;
                    kpiLineItem.ModifiedOn = DateTime.UtcNow;
                    _unitOfWork.MMonthlyReportRepository.Update(kpiLineItem);
                    await _unitOfWork.SaveAsync();
                    return 1;
                }
            }
            else
            {
                return await AddMonthlyReportTable(monthlyReportModel, userId);
            }
            return 0;
        }

        private async Task<int> UpdateCapTable(CapTableModel capTableModel, int userId)
        {
            if (await _unitOfWork.MCapTableRepository.ExistsAsyncAny(x => x.Kpi.ToLower() == capTableModel.Kpi.ToLower() && x.ModuleId == capTableModel.ModuleId && !x.IsDeleted))
                return -1;
            else
            {
                var kpiLineItem = new MCapTable()
                {
                    Kpi = capTableModel.Kpi,
                    KpiInfo = capTableModel.KpiInfo,
                    Description = capTableModel.Description,
                    MethodologyID = (capTableModel.KpiInfo != Constants.KpiInfoText) ? capTableModel.MethodologyID : 0,
                    KpiTypeId = capTableModel.KpiTypeId,
                    IsBoldKpi = capTableModel.IsBoldKpi,
                    IsHeader = capTableModel.IsHeader,
                    ModuleId = capTableModel.ModuleId,
                    IsOverrule = capTableModel.IsOverrule,
                    CreatedBy = userId,
                    CreatedOn = DateTime.UtcNow,
                    Synonym = capTableModel.Synonym
                };
                await _unitOfWork.MCapTableRepository.AddAsyn(kpiLineItem);
                await _unitOfWork.SaveAsync();
                return 1;
            }
        }
        /// <summary>
        /// Adds a monthly report table to the database.
        /// </summary>
        /// <param name="monthlyReport">The monthly report model containing the data for the table.</param>
        /// <param name="userId">The ID of the user adding the table.</param>
        /// <returns>
        /// Returns -1 if a table with the same KPI already exists and is not deleted.
        /// Returns 1 if the table is successfully added to the database.
        /// </returns>
        private async Task<int> AddMonthlyReportTable(MonthlyReportModel monthlyReport, int userId)
        {
            if (await _unitOfWork.MMonthlyReportRepository.ExistsAsyncAny(x => x.Kpi.ToLower() == monthlyReport.Kpi.ToLower() && !x.IsDeleted))
                return -1;
            else
            {
                var kpiLineItem = new MMonthlyReport()
                {
                    Kpi = monthlyReport.Kpi,
                    KpiInfo = monthlyReport.KpiInfo,
                    Description = monthlyReport.Description,
                    IsBoldKpi = monthlyReport.IsBoldKpi,
                    IsHeader = monthlyReport.IsHeader,
                    CreatedBy = userId
                };
                await _unitOfWork.MMonthlyReportRepository.AddAsyn(kpiLineItem);
                await _unitOfWork.SaveAsync();
                return 1;
            }
        }
        

        /// <summary>
        /// Gets the KPI information type based on the provided KPI information.
        /// </summary>
        /// <param name="kpiInfo">The KPI information.</param>
        /// <returns>The KPI information type.</returns>
        private static string GetKpiInfoType(string kpiInfo)
        {
            return kpiInfo switch
            {
                Constants.KpiInfoCurrency => Constants.Currency,
                Constants.KpiInfoNumber => Constants.DataTypeNumber,
                _ => kpiInfo ?? string.Empty
            };
        }
    }
}