﻿using Xunit;
using Moq;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using API.Controllers.EmailNotification;
using EmailConfiguration.Interfaces;
using API.Helpers;
using System;
using EmailConfiguration.DTOs;

namespace DataCollection.UnitTest.Controller
{
    public class EmailGroupControllerTests
    {
        private readonly Mock<IEmailGroupService> _emailGroupServiceMock;
        private readonly Mock<IHelperService> _helperServiceMock;
        private readonly EmailGroupController _controller;

        public EmailGroupControllerTests()
        {
            _emailGroupServiceMock = new Mock<IEmailGroupService>();
            _helperServiceMock = new Mock<IHelperService>();
            _controller = new EmailGroupController(_emailGroupServiceMock.Object, _helperServiceMock.Object);
        }

        [Fact]
        public async Task CreateEmailGroup_ReturnsOk_WhenGroupCreated()
        {
            // Arrange
            var dto = new EmailGroupCreateDto { GroupName = "Test", EmailList = new List<EmailMemberDto>(), CompanyIds = new List<int>() };
            _helperServiceMock.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(1);
            _emailGroupServiceMock.Setup(s => s.CreateEmailGroupAsync(dto, 1)).ReturnsAsync(123);

            // Act
            var result = await _controller.CreateEmailGroup(dto);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.Equal(200, okResult.StatusCode);
        }

       

        [Fact]
        public async Task GetEmailGroups_ReturnsOk_WhenGroupsExist()
        {
            // Arrange
            var groups = new List<EmailGroupDto> { new EmailGroupDto { GroupId = 1, GroupName = "Test" } };
            _emailGroupServiceMock.Setup(s => s.GetEmailGroupsWithCountAsync()).ReturnsAsync(groups);

            // Act
            var result = await _controller.GetEmailGroups();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.Equal(200, okResult.StatusCode);
            Assert.NotNull(okResult.Value);
        }

        [Fact]
        public async Task GetEmailGroups_ReturnsNoContent_WhenNoGroups()
        {
            // Arrange
            _emailGroupServiceMock.Setup(s => s.GetEmailGroupsWithCountAsync()).ReturnsAsync(new List<EmailGroupDto>());

            // Act
            var result = await _controller.GetEmailGroups();

            // Assert
            Assert.IsType<NoContentResult>(result);
        }

        [Fact]
        public async Task GetEmailListByGroupId_ReturnsOk_WhenMembersExist()
        {
            // Arrange
            var members = new List<EmailMemberDto> { new EmailMemberDto { Name = "Test", Email = "<EMAIL>", IsActive = true } };
            _emailGroupServiceMock.Setup(s => s.GetEmailMembersByGroupIdAsync(1)).ReturnsAsync(members);

            // Act
            var result = await _controller.GetEmailListByGroupId(1);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.Equal(200, okResult.StatusCode);
            Assert.NotNull(okResult.Value);
        }

        [Fact]
        public async Task GetEmailListByGroupId_ReturnsNoContent_WhenNoMembers()
        {
            // Arrange
            _emailGroupServiceMock.Setup(s => s.GetEmailMembersByGroupIdAsync(1)).ReturnsAsync(new List<EmailMemberDto>());

            // Act
            var result = await _controller.GetEmailListByGroupId(1);

            // Assert
            Assert.IsType<NoContentResult>(result);
        }

        [Fact]
        public async Task DeleteEmailGroup_ReturnsOk_WhenGroupDeletedSuccessfully()
        {
            // Arrange
            var groupId = 1;
            var userId = 123;
            _helperServiceMock.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _emailGroupServiceMock.Setup(s => s.DeleteEmailGroupAsync(groupId, userId)).ReturnsAsync(true);

            // Act
            var result = await _controller.DeleteEmailGroup(groupId);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.Equal(200, okResult.StatusCode);
            var response = okResult.Value;
            Assert.NotNull(response);
        }

        [Fact]
        public async Task DeleteEmailGroup_ReturnsNotFound_WhenGroupNotFound()
        {
            // Arrange
            var groupId = 999;
            var userId = 123;
            _helperServiceMock.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _emailGroupServiceMock.Setup(s => s.DeleteEmailGroupAsync(groupId, userId)).ReturnsAsync(false);

            // Act
            var result = await _controller.DeleteEmailGroup(groupId);

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result);
            Assert.Equal(404, notFoundResult.StatusCode);
        }

        [Fact]
        public async Task DeleteEmailGroup_ReturnsBadRequest_WhenInvalidGroupId()
        {
            // Arrange
            var invalidGroupId = 0;

            // Act
            var result = await _controller.DeleteEmailGroup(invalidGroupId);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            Assert.Equal(400, badRequestResult.StatusCode);
            Assert.Equal("Invalid group ID", badRequestResult.Value);
        }

        [Fact]
        public async Task DeleteEmailGroup_ReturnsBadRequest_WhenGroupReferencedInReminders()
        {
            // Arrange
            var groupId = 1;
            var userId = 123;
            _helperServiceMock.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _emailGroupServiceMock.Setup(s => s.DeleteEmailGroupAsync(groupId, userId))
                .ThrowsAsync(new InvalidOperationException("Cannot delete email group as it is referenced in 2 active email reminders. Please remove the group from email reminders first."));

            // Act
            var result = await _controller.DeleteEmailGroup(groupId);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            Assert.Equal(400, badRequestResult.StatusCode);
        }

        [Fact]
        public async Task DeleteEmailGroup_ReturnsInternalServerError_WhenUnexpectedExceptionOccurs()
        {
            // Arrange
            var groupId = 1;
            var userId = 123;
            _helperServiceMock.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _emailGroupServiceMock.Setup(s => s.DeleteEmailGroupAsync(groupId, userId))
                .ThrowsAsync(new Exception("Database connection failed"));

            // Act
            var result = await _controller.DeleteEmailGroup(groupId);

            // Assert
            var statusCodeResult = Assert.IsType<ObjectResult>(result);
            Assert.Equal(500, statusCodeResult.StatusCode);
        }

        #region DeleteEmailMember Tests

        [Fact]
        public async Task DeleteEmailMember_ReturnsOk_WhenMemberDeletedSuccessfully()
        {
            // Arrange
            var groupId = 1;
            var memberId = 1;
            var userId = 123;
            _helperServiceMock.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _emailGroupServiceMock.Setup(s => s.DeleteEmailMemberAsync(groupId, memberId, userId)).ReturnsAsync(true);

            // Act
            var result = await _controller.DeleteEmailMember(groupId, memberId);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.Equal(200, okResult.StatusCode);
            var response = okResult.Value;
            Assert.NotNull(response);
        }

        [Fact]
        public async Task DeleteEmailMember_ReturnsNotFound_WhenMemberNotFound()
        {
            // Arrange
            var groupId = 1;
            var memberId = 999;
            var userId = 123;
            _helperServiceMock.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _emailGroupServiceMock.Setup(s => s.DeleteEmailMemberAsync(groupId, memberId, userId)).ReturnsAsync(false);

            // Act
            var result = await _controller.DeleteEmailMember(groupId, memberId);

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result);
            Assert.Equal(404, notFoundResult.StatusCode);
            Assert.Contains($"Email member with ID {memberId} not found in group {groupId}", notFoundResult.Value.ToString());
        }

        [Fact]
        public async Task DeleteEmailMember_ReturnsBadRequest_WhenInvalidGroupId()
        {
            // Arrange
            var invalidGroupId = 0;
            var memberId = 1;

            // Act
            var result = await _controller.DeleteEmailMember(invalidGroupId, memberId);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            Assert.Equal(400, badRequestResult.StatusCode);
            Assert.Equal("Invalid group ID", badRequestResult.Value);
        }

        [Fact]
        public async Task DeleteEmailMember_ReturnsBadRequest_WhenInvalidMemberId()
        {
            // Arrange
            var groupId = 1;
            var invalidMemberId = 0;

            // Act
            var result = await _controller.DeleteEmailMember(groupId, invalidMemberId);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            Assert.Equal(400, badRequestResult.StatusCode);
            Assert.Equal("Invalid member ID", badRequestResult.Value);
        }

        [Fact]
        public async Task DeleteEmailMember_ReturnsInternalServerError_WhenUnexpectedExceptionOccurs()
        {
            // Arrange
            var groupId = 1;
            var memberId = 1;
            var userId = 123;
            _helperServiceMock.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _emailGroupServiceMock.Setup(s => s.DeleteEmailMemberAsync(groupId, memberId, userId))
                .ThrowsAsync(new Exception("Database connection failed"));

            // Act
            var result = await _controller.DeleteEmailMember(groupId, memberId);

            // Assert
            var statusCodeResult = Assert.IsType<ObjectResult>(result);
            Assert.Equal(500, statusCodeResult.StatusCode);
        }

        [Fact]
        public async Task DeleteEmailMember_CallsServiceWithCorrectParameters()
        {
            // Arrange
            var groupId = 1;
            var memberId = 1;
            var userId = 123;
            _helperServiceMock.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _emailGroupServiceMock.Setup(s => s.DeleteEmailMemberAsync(groupId, memberId, userId)).ReturnsAsync(true);

            // Act
            await _controller.DeleteEmailMember(groupId, memberId);

            // Assert
            _emailGroupServiceMock.Verify(s => s.DeleteEmailMemberAsync(groupId, memberId, userId), Times.Once);
            _helperServiceMock.Verify(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>()), Times.Once);
        }

        #endregion

        #region DeleteEmailMembers Tests

        [Fact]
        public async Task DeleteEmailMembers_ReturnsOk_WhenAllMembersDeletedSuccessfully()
        {
            // Arrange
            var groupId = 1;
            var dto = new DeleteEmailMembersDto { MemberIds = new List<int> { 1, 2, 3 } };
            var userId = 123;
            var serviceResult = (Success: true, DeletedMemberIds: new List<int> { 1, 2, 3 }, NotFoundMemberIds: new List<int>());

            _helperServiceMock.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _emailGroupServiceMock.Setup(s => s.DeleteEmailMembersAsync(groupId, dto.MemberIds, userId)).ReturnsAsync(serviceResult);

            // Act
            var result = await _controller.DeleteEmailMembers(groupId, dto);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.Equal(200, okResult.StatusCode);
            var response = okResult.Value;
            Assert.NotNull(response);
        }

        [Fact]
        public async Task DeleteEmailMembers_ReturnsMultiStatus_WhenSomeMembersNotFound()
        {
            // Arrange
            var groupId = 1;
            var dto = new DeleteEmailMembersDto { MemberIds = new List<int> { 1, 2, 3 } };
            var userId = 123;
            var serviceResult = (Success: true, DeletedMemberIds: new List<int> { 1, 2 }, NotFoundMemberIds: new List<int> { 3 });

            _helperServiceMock.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _emailGroupServiceMock.Setup(s => s.DeleteEmailMembersAsync(groupId, dto.MemberIds, userId)).ReturnsAsync(serviceResult);

            // Act
            var result = await _controller.DeleteEmailMembers(groupId, dto);

            // Assert
            var statusCodeResult = Assert.IsType<ObjectResult>(result);
            Assert.Equal(207, statusCodeResult.StatusCode); // Multi-Status
            var response = statusCodeResult.Value;
            Assert.NotNull(response);
        }

        [Fact]
        public async Task DeleteEmailMembers_ReturnsNotFound_WhenGroupNotFound()
        {
            // Arrange
            var groupId = 999;
            var dto = new DeleteEmailMembersDto { MemberIds = new List<int> { 1, 2, 3 } };
            var userId = 123;
            var serviceResult = (Success: false, DeletedMemberIds: new List<int>(), NotFoundMemberIds: new List<int> { 1, 2, 3 });

            _helperServiceMock.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _emailGroupServiceMock.Setup(s => s.DeleteEmailMembersAsync(groupId, dto.MemberIds, userId)).ReturnsAsync(serviceResult);

            // Act
            var result = await _controller.DeleteEmailMembers(groupId, dto);

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result);
            Assert.Equal(404, notFoundResult.StatusCode);
            Assert.Contains($"Email group with ID {groupId} not found", notFoundResult.Value.ToString());
        }

        [Fact]
        public async Task DeleteEmailMembers_ReturnsBadRequest_WhenInvalidGroupId()
        {
            // Arrange
            var invalidGroupId = 0;
            var dto = new DeleteEmailMembersDto { MemberIds = new List<int> { 1, 2, 3 } };

            // Act
            var result = await _controller.DeleteEmailMembers(invalidGroupId, dto);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            Assert.Equal(400, badRequestResult.StatusCode);
            Assert.Equal("Invalid group ID", badRequestResult.Value);
        }

        [Fact]
        public async Task DeleteEmailMembers_ReturnsBadRequest_WhenDtoIsNull()
        {
            // Arrange
            var groupId = 1;
            DeleteEmailMembersDto dto = null;

            // Act
            var result = await _controller.DeleteEmailMembers(groupId, dto);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            Assert.Equal(400, badRequestResult.StatusCode);
            Assert.Equal("No member IDs provided for deletion", badRequestResult.Value);
        }

        [Fact]
        public async Task DeleteEmailMembers_ReturnsBadRequest_WhenMemberIdsIsEmpty()
        {
            // Arrange
            var groupId = 1;
            var dto = new DeleteEmailMembersDto { MemberIds = new List<int>() };

            // Act
            var result = await _controller.DeleteEmailMembers(groupId, dto);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            Assert.Equal(400, badRequestResult.StatusCode);
            Assert.Equal("No member IDs provided for deletion", badRequestResult.Value);
        }

        [Fact]
        public async Task DeleteEmailMembers_ReturnsInternalServerError_WhenUnexpectedExceptionOccurs()
        {
            // Arrange
            var groupId = 1;
            var dto = new DeleteEmailMembersDto { MemberIds = new List<int> { 1, 2, 3 } };
            var userId = 123;

            _helperServiceMock.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _emailGroupServiceMock.Setup(s => s.DeleteEmailMembersAsync(groupId, dto.MemberIds, userId))
                .ThrowsAsync(new Exception("Database connection failed"));

            // Act
            var result = await _controller.DeleteEmailMembers(groupId, dto);

            // Assert
            var statusCodeResult = Assert.IsType<ObjectResult>(result);
            Assert.Equal(500, statusCodeResult.StatusCode);
        }

        [Fact]
        public async Task DeleteEmailMembers_CallsServiceWithCorrectParameters()
        {
            // Arrange
            var groupId = 1;
            var dto = new DeleteEmailMembersDto { MemberIds = new List<int> { 1, 2, 3 } };
            var userId = 123;
            var serviceResult = (Success: true, DeletedMemberIds: new List<int> { 1, 2, 3 }, NotFoundMemberIds: new List<int>());

            _helperServiceMock.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _emailGroupServiceMock.Setup(s => s.DeleteEmailMembersAsync(groupId, dto.MemberIds, userId)).ReturnsAsync(serviceResult);

            // Act
            await _controller.DeleteEmailMembers(groupId, dto);

            // Assert
            _emailGroupServiceMock.Verify(s => s.DeleteEmailMembersAsync(groupId, dto.MemberIds, userId), Times.Once);
            _helperServiceMock.Verify(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>()), Times.Once);
        }

        #endregion

        #region GetEmailGroupDetail Tests

        [Fact]
        public async Task GetEmailGroupDetail_ReturnsOk_WhenGroupExists()
        {
            // Arrange
            var groupId = 1;
            var groupDetail = new EmailGroupDetailDto
            {
                GroupId = groupId,
                GroupName = "Test Group",
                CreatedBy = "123",
                CreatedOn = DateTime.UtcNow,
                UploadedBy = "John Doe",
                EmailMembers = new List<EmailMemberDto>
                {
                    new EmailMemberDto { MemberId = 1, Name = "User 1", Email = "<EMAIL>", IsActive = true }
                },
                CompanyAssociations = new List<CompanyEmailGroupDto>
                {
                    new CompanyEmailGroupDto { CompanyEmailGroupId = 1, GroupId = groupId, CompanyId = "1", IsSelected = true }
                }
            };

            _emailGroupServiceMock.Setup(s => s.GetEmailGroupDetailAsync(groupId)).ReturnsAsync(groupDetail);

            // Act
            var result = await _controller.GetEmailGroupDetail(groupId);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.Equal(200, okResult.StatusCode);
            var response = Assert.IsType<EmailGroupDetailDto>(okResult.Value);
            Assert.Equal(groupId, response.GroupId);
            Assert.Equal("Test Group", response.GroupName);
        }

        [Fact]
        public async Task GetEmailGroupDetail_ReturnsNotFound_WhenGroupDoesNotExist()
        {
            // Arrange
            var groupId = 999;
            _emailGroupServiceMock.Setup(s => s.GetEmailGroupDetailAsync(groupId)).ReturnsAsync((EmailGroupDetailDto)null);

            // Act
            var result = await _controller.GetEmailGroupDetail(groupId);

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result);
            Assert.Equal(404, notFoundResult.StatusCode);
            Assert.Contains($"Email group with ID {groupId} not found", notFoundResult.Value.ToString());
        }

        [Fact]
        public async Task GetEmailGroupDetail_ReturnsBadRequest_WhenInvalidGroupId()
        {
            // Arrange
            var invalidGroupId = 0;

            // Act
            var result = await _controller.GetEmailGroupDetail(invalidGroupId);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            Assert.Equal(400, badRequestResult.StatusCode);
            Assert.Equal("Invalid group ID", badRequestResult.Value);
        }

        [Fact]
        public async Task GetEmailGroupDetail_ReturnsInternalServerError_WhenUnexpectedExceptionOccurs()
        {
            // Arrange
            var groupId = 1;
            _emailGroupServiceMock.Setup(s => s.GetEmailGroupDetailAsync(groupId))
                .ThrowsAsync(new Exception("Database connection failed"));

            // Act
            var result = await _controller.GetEmailGroupDetail(groupId);

            // Assert
            var statusCodeResult = Assert.IsType<ObjectResult>(result);
            Assert.Equal(500, statusCodeResult.StatusCode);
        }

        #endregion

        #region UpdateEmailGroup Tests

        [Fact]
        public async Task UpdateEmailGroup_ReturnsOk_WhenUpdateSuccessful()
        {
            // Arrange
            var groupId = 1;
            var userId = 123;
            var dto = new EmailGroupUpdateDto
            {
                GroupName = "Updated Group Name",
                EmailMembers = new List<EmailMemberDto>
                {
                    new EmailMemberDto { Name = "New User", Email = "<EMAIL>", IsActive = true }
                },
                CompanyIds = new List<int> { 1, 2 }
            };

            _helperServiceMock.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _emailGroupServiceMock.Setup(s => s.UpdateEmailGroupAsync(groupId, dto, userId)).ReturnsAsync(true);

            // Act
            var result = await _controller.UpdateEmailGroup(groupId, dto);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.Equal(200, okResult.StatusCode);
            var response = okResult.Value;
            Assert.NotNull(response);
        }

        [Fact]
        public async Task UpdateEmailGroup_ReturnsNotFound_WhenGroupNotFound()
        {
            // Arrange
            var groupId = 999;
            var userId = 123;
            var dto = new EmailGroupUpdateDto
            {
                GroupName = "Updated Group Name",
                EmailMembers = new List<EmailMemberDto>(),
                CompanyIds = new List<int>()
            };

            _helperServiceMock.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _emailGroupServiceMock.Setup(s => s.UpdateEmailGroupAsync(groupId, dto, userId)).ReturnsAsync(false);

            // Act
            var result = await _controller.UpdateEmailGroup(groupId, dto);

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result);
            Assert.Equal(404, notFoundResult.StatusCode);
            Assert.Contains($"Email group with ID {groupId} not found", notFoundResult.Value.ToString());
        }

        [Fact]
        public async Task UpdateEmailGroup_ReturnsBadRequest_WhenInvalidGroupId()
        {
            // Arrange
            var invalidGroupId = 0;
            var dto = new EmailGroupUpdateDto { GroupName = "Test Group" };

            // Act
            var result = await _controller.UpdateEmailGroup(invalidGroupId, dto);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            Assert.Equal(400, badRequestResult.StatusCode);
            Assert.Equal("Invalid group ID", badRequestResult.Value);
        }

        [Fact]
        public async Task UpdateEmailGroup_ReturnsBadRequest_WhenDtoIsNull()
        {
            // Arrange
            var groupId = 1;
            EmailGroupUpdateDto dto = null;

            // Act
            var result = await _controller.UpdateEmailGroup(groupId, dto);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            Assert.Equal(400, badRequestResult.StatusCode);
            Assert.Equal("Update data is required", badRequestResult.Value);
        }

        [Fact]
        public async Task UpdateEmailGroup_ReturnsBadRequest_WhenGroupNameIsEmpty()
        {
            // Arrange
            var groupId = 1;
            var dto = new EmailGroupUpdateDto
            {
                GroupName = "",
                EmailMembers = new List<EmailMemberDto>(),
                CompanyIds = new List<int>()
            };

            // Act
            var result = await _controller.UpdateEmailGroup(groupId, dto);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            Assert.Equal(400, badRequestResult.StatusCode);
            Assert.Equal("Group name is required", badRequestResult.Value);
        }

        [Fact]
        public async Task UpdateEmailGroup_ReturnsInternalServerError_WhenUnexpectedExceptionOccurs()
        {
            // Arrange
            var groupId = 1;
            var userId = 123;
            var dto = new EmailGroupUpdateDto
            {
                GroupName = "Updated Group Name",
                EmailMembers = new List<EmailMemberDto>(),
                CompanyIds = new List<int>()
            };

            _helperServiceMock.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _emailGroupServiceMock.Setup(s => s.UpdateEmailGroupAsync(groupId, dto, userId))
                .ThrowsAsync(new Exception("Database connection failed"));

            // Act
            var result = await _controller.UpdateEmailGroup(groupId, dto);

            // Assert
            var statusCodeResult = Assert.IsType<ObjectResult>(result);
            Assert.Equal(500, statusCodeResult.StatusCode);
        }

        [Fact]
        public async Task UpdateEmailGroup_CallsServiceWithCorrectParameters()
        {
            // Arrange
            var groupId = 1;
            var userId = 123;
            var dto = new EmailGroupUpdateDto
            {
                GroupName = "Updated Group Name",
                EmailMembers = new List<EmailMemberDto>(),
                CompanyIds = new List<int>()
            };

            _helperServiceMock.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).Returns(userId);
            _emailGroupServiceMock.Setup(s => s.UpdateEmailGroupAsync(groupId, dto, userId)).ReturnsAsync(true);

            // Act
            await _controller.UpdateEmailGroup(groupId, dto);

            // Assert
            _emailGroupServiceMock.Verify(s => s.UpdateEmailGroupAsync(groupId, dto, userId), Times.Once);
            _helperServiceMock.Verify(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>()), Times.Once);
        }

        #endregion

        [Fact]
        public async Task CheckDuplicateGroupName_ReturnsOk_WhenNotDuplicate()
        {
            // Arrange
            var dto = new CheckGroupNameDto { GroupName = "Test", GroupId = 0 };
            _helperServiceMock.Setup(h => h.GetCurrentUserId(It.IsAny<System.Security.Claims.ClaimsPrincipal>())).Returns(1);
            _emailGroupServiceMock.Setup(s => s.CheckDuplicateName(dto.GroupName, dto.GroupId)).ReturnsAsync(false);

            // Act
            var result = await _controller.CheckDuplicateGroupName(dto);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.Equal(200, okResult.StatusCode);
            Assert.False((bool)okResult.Value.GetType().GetProperty("isDuplicateName").GetValue(okResult.Value));
        }

        [Fact]
        public async Task CheckDuplicateGroupName_ReturnsOk_WhenDuplicate()
        {
            // Arrange
            var dto = new CheckGroupNameDto { GroupName = "Test", GroupId = 0 };
            _helperServiceMock.Setup(h => h.GetCurrentUserId(It.IsAny<System.Security.Claims.ClaimsPrincipal>())).Returns(1);
            _emailGroupServiceMock.Setup(s => s.CheckDuplicateName(dto.GroupName, dto.GroupId)).ReturnsAsync(true);

            // Act
            var result = await _controller.CheckDuplicateGroupName(dto);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.Equal(200, okResult.StatusCode);
            Assert.True((bool)okResult.Value.GetType().GetProperty("isDuplicateName").GetValue(okResult.Value));
        }

        [Fact]
        public async Task CheckDuplicateGroupName_ReturnsBadRequest_WhenModelStateInvalid()
        {
            // Arrange
            var dto = new CheckGroupNameDto { GroupName = null, GroupId = 0 };
            _controller.ModelState.AddModelError("GroupName", "Required");

            // Act
            var result = await _controller.CheckDuplicateGroupName(dto);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            Assert.Equal(400, badRequestResult.StatusCode);
        }
    }
}