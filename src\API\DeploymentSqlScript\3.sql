-----------------------BFB-9859------------------------
IF OBJECT_ID('[dbo].[SPGetFundListByUserID]', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE [dbo].[SPGetFundListByUserID]
END
GO
CREATE PROCEDURE [dbo].[SPGetFundListByUserID]  
(@UserID INT)  
AS  
BEGIN  
SELECT FundID, FundName FROM [dbo].[view_FundList]
WHERE FundID IN 
	(SELECT DISTINCT FundId 
	 FROM(
		SELECT DISTINCT         
		CASE             
		WHEN CanView = 1 
		THEN 1            
		ELSE 0       
		END AS HasPermission,
		mugc.CompanyID FundId  
		FROM Mapping_UserSubFeature AS musf   
		INNER JOIN M_SubFeature AS sf ON musf.SubFeatureID = sf.SubFeatureID   
		INNER JOIN Mapping_UserGroup mug ON musf.GroupId = mug.GroupID 
		INNER JOIN Mapping_UserGroupCompany mugc  ON musf.FeatureMappingId=mugc.CompanyID AND mug.GroupID=mugc.GroupID  
		INNER JOIN M_Features mf on mf.FeatureID= mugc.FeatureId 
		WHERE mug.UserID = @UserID AND mf.Feature='Fund' AND mug.IsDeleted = 0 AND musf.IsDeleted = 0 AND sf.IsDeleted = 0  
		AND sf.IsActive = 1 AND mugc.IsActive = 1 AND mugc.IsDeleted = 0
      )
ActiveInvestors WHERE HasPermission<>0) ORDER BY FundName ASC
END 
GO

-----------------------BFB-9916------------------------

IF NOT EXISTS (
    SELECT *
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'DataExtractionTypes'
    AND COLUMN_NAME = 'FeatureId'
) and EXISTS (SELECT * FROM sysobjects WHERE name='DataExtractionTypes')
BEGIN
	ALTER TABLE [dbo].[DataExtractionTypes]
	ADD FeatureId INT NOT NULL DEFAULT 14;
END
GO

IF NOT EXISTS (SELECT * FROM [dbo].[DataExtractionTypes] WHERE [DocumentName] = 'Fund Financials' AND FeatureId = 13)
BEGIN
    INSERT INTO [dbo].[DataExtractionTypes] (DocumentName, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted, FeatureId) VALUES (N'Fund Financials', GETDATE(), 0, NULL, NULL, 0, 13);
END
GO

IF NOT EXISTS (SELECT * FROM [dbo].[DataExtractionTypes] WHERE [DocumentName] = 'Quarterly Report' AND FeatureId = 13)
BEGIN
    INSERT INTO DataExtractionTypes (DocumentName, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted, FeatureId) VALUES (N'Quarterly Report', GETDATE(), 0, NULL, NULL, 0, 13);
END
GO

IF NOT EXISTS (SELECT * FROM [dbo].[DataExtractionTypes] WHERE [DocumentName] = 'Capital Account Statement' AND FeatureId = 13)
BEGIN
    INSERT INTO DataExtractionTypes (DocumentName, CreatedOn, CreatedBy, ModifiedOn, ModifiedBy, IsDeleted, FeatureId) VALUES (N'Capital Account Statement', GETDATE(), 0, NULL, NULL, 0, 13);
END
GO

IF NOT EXISTS (
    SELECT *
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'DocumentCollectionStore'
    AND COLUMN_NAME = 'UploadType'
) and EXISTS (SELECT * FROM sysobjects WHERE name='DocumentCollectionStore')
BEGIN
	ALTER TABLE [dbo].[DocumentCollectionStore]
	ADD UploadType INT NOT NULL DEFAULT 0;
END

--- Dashboard tracker tables --
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='DashboardTrackerConfig' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[DashboardTrackerConfig] (
        [ID] INT IDENTITY(1,1) PRIMARY KEY,
        [FieldType] INT NOT NULL,           -- 1=Data, 2=TimeSeries
        [DataType] INT NOT NULL,            -- 1=Text, 2=Number, 3=Date, 4=DropDown
        [Name] NVARCHAR(200) NOT NULL,      -- Column display name
        [FrequencyType] INT NULL,           -- 1=Monthly, 2=Quarterly, 3=Annual
        [StartPeriod] NVARCHAR(50) NULL,    -- e.g., 2024-Q1, 2024-01
        [EndPeriod] NVARCHAR(50) NULL,      -- e.g., 2024-Q4, 2024-12
        [MapTo] INT NULL,         -- Maps to another field/table if needed
        [IsActive] BIT NOT NULL DEFAULT 1,
        [IsDeleted] BIT NOT NULL DEFAULT 0,
        [CreatedOn] DATETIME NOT NULL DEFAULT GETUTCDATE(),
        [CreatedBy] INT NOT NULL,
        [ModifiedOn] DATETIME NULL,
        [ModifiedBy] INT NULL
    )
END
