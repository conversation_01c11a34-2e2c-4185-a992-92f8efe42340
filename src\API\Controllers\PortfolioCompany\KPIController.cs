using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using API.Helpers;
using Contract.KPI;
using Contract.PortfolioCompany;
using Contract.Utility;
using ESG;
using Master;
using Microsoft.AspNetCore.Mvc;
using Utility.Resource;
using Contract.Account;
using API.Filters.CustomAuthorization;
using DocumentFormat.OpenXml.Office2010.Excel;
using System.Reflection;

namespace API.Controllers;

public class KpiController : BaseController
{
    private readonly IKpiService _kpiService;
    private readonly IPageDetailsConfigurationService _pageDetailsConfigurationService;
    private readonly IEsgKpiService _esgKpiService;
    public KpiController(IKpiService kPIService, IInjectedParameters InjectedParameters, IHelperService helperService, IPageDetailsConfigurationService pageDetailsConfigurationService, IEsgKpiService esgKpiService) : base(InjectedParameters, helperService)
    {
        _kpiService = kPIService;
        _pageDetailsConfigurationService = pageDetailsConfigurationService;
        _esgKpiService = esgKpiService;

    }
    [HttpGet("company-kpis/get")]
    [UserFeatureAuthorize((int)Features.KPIsMapping)]
    public IActionResult GetCompanywiseKPIListForMapping()
    {
        var result = _kpiService.GetCompanyKpi();
        return result != null ? Ok(result) : NoContent();
    }
    [Route("kpi/delete")]
    [UserFeatureAuthorize((int)Features.KPIsMapping)]
    [HttpPost]
    public async Task<IActionResult> DeleteKPI(DeleteKpiModel kPIModel)
    {
        if (!ModelState.IsValid)
            return BadRequest();
        else
        {
            var result = kPIModel.ModuleId > 0 ? await _kpiService.DeleteKPI(kPIModel) : await _esgKpiService.DeleteESGKPI(kPIModel);
            return Ok(result);

        }
    }
    [Route("KPI/{Id}/{Type}/{moduleID}/{subPageId}")]
    [UserFeatureAuthorize((int)Features.KPIsMapping)]
    [HttpGet]
    public async Task<IActionResult> GetKPIMapping(string Id, string Type, int moduleID, int subPageId)
    {
        if (string.IsNullOrWhiteSpace(Id) || string.IsNullOrWhiteSpace(Type)) return BadRequest();
        string encryptedPortfolioCompanyID = Id;
        int decryptedPortfolioCompanyID = int.Parse(Decrypt(encryptedPortfolioCompanyID));

        var result = moduleID > 0 ? await _kpiService.GetKPIMapping(decryptedPortfolioCompanyID, Type, moduleID) : await _esgKpiService.EsgKPIMapping(decryptedPortfolioCompanyID, subPageId);
        return result == null ? JsonResponse.Create(HttpStatusCode.OK, Messages.NoRecordFound) : JsonResponse.Create(HttpStatusCode.OK, result);
    }
    [HttpGet("un-mapped-kpi/{id}/{type}/{moduleID}/{subPageId}")]
    [UserFeatureAuthorize((int)Features.KPIsMapping)]
    public async Task<IActionResult> GetUnMappedKpi(string id, string type, int moduleID, int subPageId)
    {
        if (string.IsNullOrWhiteSpace(id) || string.IsNullOrWhiteSpace(type)) return BadRequest();
        _ = int.TryParse(Decrypt(id), out int decryptedPortfolioCompanyID);

        var result = moduleID > 0 ? await _kpiService.GetUnMappedKpi(decryptedPortfolioCompanyID, type, moduleID) : await _esgKpiService.GetUnMappedEsgKpi(decryptedPortfolioCompanyID, subPageId);
        return Ok(result);
    }

    [Route("kpi/create/duplicate")]
    [UserFeatureAuthorize((int)Features.KPIsMapping)]
    [HttpPost]
    public async Task<IActionResult> CreateKPI(DuplicateKpiModel duplicateKPI)
    {
        int kpiId;
        if (duplicateKPI is not null)
            duplicateKPI.UserId = GetCurrentUserId();
        kpiId = duplicateKPI?.ModuleId > 0 ? await _kpiService.CreateKPI(duplicateKPI) : await _esgKpiService.CreateEsgKpi(duplicateKPI);
        return kpiId == 0 ? BadRequest() : Ok(kpiId);
    }

    [Route("KPI/{Id}/{Type}/{moduleID}/{subPageId}")]
    [HttpPost]
    [UserFeatureAuthorize((int)Features.KPIsMapping)]
    public async Task<IActionResult> UpdateKPIMapping(string Id, string Type, KpiMappingQueryModel KpiMappingQueryModel, int moduleID, int subPageId)
    {
        if (string.IsNullOrWhiteSpace(Id) || string.IsNullOrWhiteSpace(Type)) return BadRequest();
        string encryptedPortfolioCompanyID = Id;
        int decryptedPortfolioCompanyID = int.Parse(_injectedParameters.Encryption.Decrypt(encryptedPortfolioCompanyID.ToString()));

        if (moduleID > 0)
            await UpdatePortfolioKPIMapping(decryptedPortfolioCompanyID, Type, KpiMappingQueryModel, moduleID);
        else
            await UpdateEsgKPIMapping(decryptedPortfolioCompanyID, Type, KpiMappingQueryModel, subPageId);

        return JsonResponse.Create(HttpStatusCode.OK, "Done");
    }
    [NonAction]
    public async Task UpdatePortfolioKPIMapping(int Id, string Type, KpiMappingQueryModel KpiMappingQueryModel, int moduleID)
    {
        await _kpiService.UpdateKPIMapping(Id, Type, KpiMappingQueryModel?.KPIMappings, GetCurrentUserId(), moduleID);
        if (KpiMappingQueryModel?.CompanyIds?.Any() == true)
        {
            await _kpiService.CopyKPIToCompanies(new CopyToKpiQueryModel()
            {
                CompanyId = Id,
                KpiType = Type == "Financial KPIs" ? "Trading Records" : Type,
                CompanyIds = KpiMappingQueryModel.CompanyIds,
                UserId = GetCurrentUserId(),
                ModuleId = moduleID
            });
        }
    }

    [NonAction]
    public async Task UpdateEsgKPIMapping(int Id, string Type, KpiMappingQueryModel KpiMappingQueryModel, int moduleID)
    {
        await _esgKpiService.UpdateEsgKPIMapping(Id, KpiMappingQueryModel?.KPIMappings, _helperService.GetCurrentUserId(User), moduleID);
        if (KpiMappingQueryModel?.CompanyIds?.Any() == true)
        {
            await _esgKpiService.CopyEsgKPIToCompanies(new CopyToKpiQueryModel()
            {
                CompanyId = Id,
                CompanyIds = KpiMappingQueryModel.CompanyIds,
                UserId = _helperService.GetCurrentUserId(User),
                SubPageId = moduleID
            });
        }
    }
    [Route("investment-kpi/get")]
    [HttpGet]
    public IActionResult GetInvestmentKPI()
    {
        return GetInvestmentKPI(new PaginationFilter());
    }
    [Route("investment-kpis/get")]
    [UserFeatureAuthorize((int)Features.KPIsMapping)]
    [HttpPost]
    public IActionResult GetInvestmentKPI(PaginationFilter filter)
    {
        InvestmentKpiListModel investmentKPI = _kpiService.GetInvestmentKPIDetails();
        if (investmentKPI == null)
            return JsonResponse.Create(HttpStatusCode.OK, Messages.NoRecordFound);
        else
            return JsonResponse.Create(HttpStatusCode.OK, investmentKPI);
    }
    [Route("impact-kpis/get")]
    [UserFeatureAuthorize((int)Features.KPIsMapping)]
    [HttpPost]
    public IActionResult GetImpactKPI(PaginationFilter filter)
    {
        ImpactKpiListModel impactKPI = _kpiService.GetImpactList();
        if (impactKPI == null)
            return JsonResponse.Create(HttpStatusCode.OK, Messages.NoRecordFound);
        else
            return JsonResponse.Create(HttpStatusCode.OK, impactKPI);
    }
    [Route("financial-kpis/get")]
    [UserFeatureAuthorize((int)Features.KPIsMapping)]
    [HttpPost]
    public IActionResult GetFinancialKPI(PaginationFilter filter)
    {
        FinancialKpiListModel financial = _kpiService.GetFinancialKPIList(null);
        if (financial == null)
            return JsonResponse.Create(HttpStatusCode.OK, Messages.NoRecordFound);
        else
            return JsonResponse.Create(HttpStatusCode.OK, financial);
    }
    [HttpGet("operational-kpis/get")]
    [UserFeatureAuthorize((int)Features.KPIsMapping)]
    public IActionResult GetOperationalKPIs()
    {
        var result = _kpiService.GetOperationalKpi();
        return result != null ? Ok(result) : NoContent();
    }
    [Route("AddOrUpdateKPI")]
    [UserFeatureAuthorize((int)Features.KPIsMapping)]
    [HttpPost]
    public IActionResult AddOrUpdateKPI(KpiModel kpiModel)
    {
        var userId = GetCurrentUserId();
        return Ok(_kpiService.AddOrUpdateKPI(kpiModel, userId));
    }
    [Route("balanceSheet-kpis/get")]
    [UserFeatureAuthorize((int)Features.KPIsMapping)]
    [HttpPost]
    public IActionResult GetBalanceSheetKPI()
    {
        var result = _kpiService.GetBalancesheetKPIList();
        return JsonResponse.Create(HttpStatusCode.OK, result);
    }
    [Route("profitLoss-kpis/get")]
    [UserFeatureAuthorize((int)Features.KPIsMapping)]
    [HttpGet]
    public IActionResult GetProfitLossKPIs()
    {
        return GetProfitLossKPI();
    }
    [Route("profitLoss-kpis/get")]
    [UserFeatureAuthorize((int)Features.KPIsMapping)]
    [HttpPost]
    public IActionResult GetProfitLossKPI()
    {
        var result = _kpiService.GetProfitLossKPIList();
        return JsonResponse.Create(HttpStatusCode.OK, result);
    }
    [Route("cashflow-kpis/get")]
    [UserFeatureAuthorize((int)Features.KPIsMapping)]
    [HttpPost]
    public IActionResult GetCashflowKPI()
    {
        var result = _kpiService.GetCashflowKPIList();
        return JsonResponse.Create(HttpStatusCode.OK, result);
    }
    /// <summary>
    /// Retrieves the KPIs based on the specified type, company ID, and module ID.
    /// </summary>
    /// <param name="type">The type of KPIs to retrieve.</param>
    /// <param name="companyId">The ID of the company.</param>
    /// <param name="moduleId">The ID of the module.</param>
    /// <returns>An <see cref="IActionResult"/> representing the response of the API call.</returns>
    [HttpGet("kpi/get/{type}/{companyId}/{moduleId}")]
    [UserFeatureAuthorize((int)Features.KPIsMapping)]
    public async Task<IActionResult> GetKPIs(string type, string companyId, int moduleId)
    {
        int portfolioCompId = !string.IsNullOrEmpty(companyId) && companyId != "0" ? int.Parse(_injectedParameters.Encryption.Decrypt(companyId)) : 0;
        List<KpiFormulaModel> kpiFormulaModels = await _kpiService.GetKpisByType(type, portfolioCompId, moduleId);
        if (kpiFormulaModels.Any())
            return Ok(kpiFormulaModels);
        else
            return NoContent();
    }
    /// <summary>
    /// Updates the formula for a KPI by its ID.
    /// </summary>
    /// <param name="kpiFormula">The KpiFormula object containing the updated formula.</param>
    /// <returns>An IActionResult representing the result of the update operation.</returns>
    [HttpPut("kpi/formula/update")]
    [UserFeatureAuthorize((int)Features.KPIsMapping)]
    public async Task<IActionResult> UpdateFormulaByKPIId(KpiFormula kpiFormula)
    {
        if (kpiFormula is not null)
        {
            kpiFormula.ModifiedBy = GetCurrentUserId();
            return Ok(await _kpiService.UpdateFormulaByKPIId(kpiFormula));
        }
        else
        {
            return BadRequest();
        }
    }
    /// <summary>
    /// Decrypts the encrypted portfolio company ID.
    /// </summary>
    /// <param name="encryptedPortfolioCompanyID">The encrypted portfolio company ID to decrypt.</param>
    /// <returns>The decrypted portfolio company ID.</returns>
    private string Decrypt(string encryptedPortfolioCompanyID)
    {
        return _injectedParameters.Encryption.Decrypt(encryptedPortfolioCompanyID.ToString());
    }
    /// <summary>
    /// Retrieves the KPI formula based on the provided KpiFormula object.
    /// </summary>
    /// <param name="formula">The KpiFormula object containing the necessary information.</param>
    /// <returns>An IActionResult containing the KPI formula value.</returns>
    [HttpPost("kpi-formula")]
    [UserFeatureAuthorize((int)Features.KPIsMapping)]
    public async Task<IActionResult> GetKpiFormula([FromBody] KpiFormula formula)
    {
        var result = _kpiService.GetKpiFormula(formula);
        return Ok(new { value = result });
    }
    [HttpPost("update-mapped-kpis-synonyms")]
    [UserFeatureAuthorize((int)Features.KPIsMapping)]
    public async Task<IActionResult> UpdateCompLevelSynonyms([FromBody] MappedKpisSynonymsModel MappedKpisSynonymData)
    {
        MappedKpisSynonymData.ModifiedBy = GetCurrentUserId();
        var result = await _kpiService.UpdateCompLevelSynonyms(MappedKpisSynonymData);
        return Ok(new { value = result });
    }

    /// <summary>
    /// Retrieves all mapped KPIs for the specified company IDs.
    /// </summary>
    /// <param name="companyIds">A comma-separated string of company IDs to retrieve mapped KPIs for.</param>
    /// <returns>An <see cref="IActionResult"/> containing the mapped KPIs or a not found message.</returns>
    [HttpGet("get-all-mapped-kpi/{companyIds}")]
    [UserFeatureAuthorize((int)Features.KPIsMapping)]
    public async Task<IActionResult> GetAllMappedKPIs(string companyIds)
    {
        if (string.IsNullOrWhiteSpace(companyIds)) return BadRequest();

        var result = await _kpiService.GetAllMappedKPIs(companyIds);
        return result == null ? JsonResponse.Create(HttpStatusCode.OK, Messages.NoRecordFound) : JsonResponse.Create(HttpStatusCode.OK, result);
    }
}

