﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace API.Middlewares;
/// <summary>
/// Middleware for input validation in the API.
/// </summary>
public class InputValidationMiddleware
{
    private readonly RequestDelegate _next;
    private readonly List<string> dontAllowList = new() { "<script>", "</script>", "AND 1=1", "AND '1'='1'", "OR 1=1", "OR '1'='1'", "response.write(", "XXXXXXX", "alert(", "prompt(", "onload=", "onerror=", "onmouseover=",
        "location.href=", "document.cookie(", "crowdshield", "<script" };
    private readonly string[] xss_Swf = new string[] {
                            "#getURL,javascript:alert(1)","#goto,javascript:alert(1)", "?javascript:alert(1)","?alert(1)","?getURL(javascript:alert(1))",
                            "?asfunction:getURL,javascript:alert(1)//","?getURL,javascript:alert(1)","?goto,javascript:alert(1)","?clickTAG=javascript:alert(1)",
                            "?url=javascript:alert(1)","?clickTAG=javascript:alert(1)&TargetAS=","?TargetAS=javascript:alert(1)","?skinName=asfunction:getURL,javascript:alert(1)//",
                            "?baseurl=asfunction:getURL,javascript:alert(1)//","?base=javascript:alert(0)","?onend=javascript:alert(1)//",
                            "?userDefined=');function someFunction(a){}alert(1)//","?URI=javascript:alert(1)","?callback=javascript:alert(1)",
                            "?getURLValue=javascript:alert(1)","?goto=javascript:alert(1)","?pg=javascript:alert(1)","?page=javascript:alert(1)","?playerready=alert(document.cookie)"
                            };
    /// <summary>
    /// Initializes a new instance of the <see cref="InputValidationMiddleware"/> class.
    /// </summary>
    /// <param name="next">The next middleware in the pipeline.</param>
    public InputValidationMiddleware(RequestDelegate next)
    {
        _next = next;
    }

    /// <summary>
    /// Invokes the middleware to validate the input in the request body.
    /// </summary>
    /// <param name="context">The HTTP context.</param>
    /// <returns>A task that represents the asynchronous operation.</returns>
    public async Task InvokeAsync(HttpContext context, IConfiguration configuration)
    {
        var request = context.Request;
        List<string> excludeEndPointList = configuration.GetSection("ExcludeEndPointList").Get<List<string>>();
        if ((excludeEndPointList == null || !excludeEndPointList.Any(s => s.Equals(request.Path.Value, StringComparison.OrdinalIgnoreCase))) && (request.Method == HttpMethods.Post || request.Method == HttpMethods.Put) && request.ContentLength > 0)
        {
            string requestBody = await request.GetStringAsync();
            Regex blockScript = new Regex("<script[\\s\\S]*?>[\\s\\S]*?<\\/script>", RegexOptions.IgnoreCase, TimeSpan.FromSeconds(30));
            if (blockScript.IsMatch(requestBody) || dontAllowList.Exists(x => requestBody.Contains(x, System.StringComparison.OrdinalIgnoreCase)))
            {
                context.Response.StatusCode = StatusCodes.Status400BadRequest;
                return;
            }
            if (!request.HasFormContentType)
            {
                ValidateRegex(requestBody, RegexConstants.SqlInjection1, ErrorMessages.SqlInjection);
                ValidateRegex(requestBody, RegexConstants.SqlInjection2, ErrorMessages.SqlInjection);
                ValidateRegex(requestBody, RegexConstants.SqlInjection3, ErrorMessages.SqlInjection);
                ValidateRegex(requestBody, RegexConstants.SqlInjection4, ErrorMessages.SqlInjection);
                ValidateRegex(requestBody, RegexConstants.XPathAbbreviatedSyntaxInjection, ErrorMessages.XPathAbbreviatedSyntaxInjection);
                ValidateRegex(requestBody, RegexConstants.JavaExceptionInjection, ErrorMessages.JavaExceptionInjection);
            }
            ValidateRegex(requestBody, RegexConstants.ServerSideIncludeInjection1, ErrorMessages.ServerSideIncludeInjection);
            ValidateRegex(requestBody, RegexConstants.ServerSideIncludeInjection2, ErrorMessages.ServerSideIncludeInjection);
            ValidateRegex(requestBody, RegexConstants.XPathExpandedSyntaxInjection, ErrorMessages.XPathExpandedSyntaxInjection);
            ValidateRegex(requestBody, RegexConstants.JavaScriptInjection1, ErrorMessages.JavaScriptInjection);
            ValidateRegex(requestBody, RegexConstants.JavaScriptInjection2, ErrorMessages.JavaScriptInjection);
            ValidateRegex(requestBody, RegexConstants.XSSInjection, ErrorMessages.XSSInjection);
            ValidateRegex(requestBody, RegexConstants.TraversalInjection, ErrorMessages.TraversalInjection);
            if (!request.ContentType.StartsWith("multipart/form-data"))
                ValidateRegex(requestBody, RegexConstants.TraversalShortInjection, ErrorMessages.TraversalShortInjection);
            ValidateRegex(requestBody, string.Join("|", xss_Swf.Select(p => Regex.Escape(p))), ErrorMessages.HarmfulPattern);
            ValidateRegex(requestBody, RegexConstants.HarmfulPattern, ErrorMessages.HarmfulPattern);
            ValidateRegex(requestBody, RegexConstants.HarmfulTag, ErrorMessages.HarmfulTag);
            ValidateRegex(requestBody, RegexConstants.AttributeVector, ErrorMessages.AttributeVector);
            ValidateRegex(requestBody, RegexConstants.MaliciousAttributeInjectionAndMHTMLAttacks, ErrorMessages.MaliciousAttributeInjectionAndMHTMLAttacks);
            ValidateRegex(requestBody, RegexConstants.BlindSqlInjectionTests, ErrorMessages.BlindSqlInjectionTests);
            ValidateRegex(requestBody, RegexConstants.FileLocationAttempts, ErrorMessages.FileLocationAttempts);
            ValidateRegex(requestBody, RegexConstants.sqlInjectionPattern, ErrorMessages.SqlInjection);

        }
        await _next(context);
    }

    /// <summary>
    /// Validates a regular expression pattern against a given input string and returns a <see cref="Regex"/> object.
    /// </summary>
    /// <param name="requestBody">The input string to be validated.</param>
    /// <param name="regex">The regular expression pattern to be validated against.</param>
    /// <param name="errorMessage">The error message to be thrown if the input string does not match the pattern.</param>
    /// <returns>A <see cref="Regex"/> object if the input string matches the pattern; otherwise, throws a <see cref="BadHttpRequestException"/>.</returns>
    private static Regex ValidateRegex(string requestBody, string regex, string errorMessage)
    {
        Regex blockScript = new(regex, RegexOptions.IgnoreCase, TimeSpan.FromSeconds(30));
        return !string.IsNullOrEmpty(requestBody) && blockScript.IsMatch(requestBody)
            ? throw new BadHttpRequestException(errorMessage)
            : blockScript;
    }
}

public static class RequestExtensions
{
    /// <summary>
    /// Reads the content of the HTTP request body as a string asynchronously.
    /// </summary>
    /// <param name="request">The HTTP request.</param>
    /// <returns>A task representing the asynchronous operation. The task result contains the content of the request body as a string.</returns>
    public static async Task<string> GetStringAsync(this HttpRequest request)
    {
        request.EnableBuffering();
        var buffer = new byte[Convert.ToInt32(request.ContentLength)];
        await request.Body.ReadAsync(buffer, 0, buffer.Length);
        var requestContent = Encoding.UTF8.GetString(buffer);
        request.Body.Position = 0;
        return requestContent;
    }
}

public static class InputValidationMiddlewareExtensions
{
    /// <summary>
    /// Represents a class that provides the mechanisms to configure an application's request pipeline.
    /// </summary>
    /// <remarks>
    /// The IApplicationBuilder interface is used to configure the request pipeline for an application. It provides methods for adding middleware components to the pipeline and building the request processing pipeline.
    /// </remarks>
    public static IApplicationBuilder UseInputValidation(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<InputValidationMiddleware>();
    }
}
