﻿using Contract.Configuration;
using Contract.Funds;
using Contract.PortfolioCompany;
using Contract.PortfolioCompany.DomainModel.PortfolioCompanyKPI;
using DapperRepository;
using DapperRepository.Constants;
using DataAccessLayer.DBModel;
using DataAccessLayer.Models.PageSettings;
using DataAccessLayer.UnitOfWork;
using Financials;
using Microsoft.Extensions.Logging;
using PortfolioCompany.API.Helpers;
using PortfolioCompany.Interfaces;
using Shared;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;
using Utility.Helpers;

namespace PortfolioCompany.Services
{
    public class KpiChartService : IkpiChartService
    {
        private readonly IDapperGenericRepository _dapperGenericRepository;
        private readonly ILogger<KpiChartService> _logger;
        private readonly IUnitOfWork _unitOfWork;
        private readonly int[] ModuleIds = new[] { (int)KpiModuleType.Company, (int)KpiModuleType.TradingRecords, (int)KpiModuleType.Operational, (int)KpiModuleType.Investment,(int)KpiModuleType.Impact, (int)KpiModuleType.CreditKPI,
         (int)KpiModuleType.CustomTable1, (int)KpiModuleType.CustomTable2, (int)KpiModuleType.CustomTable3, (int)KpiModuleType.CustomTable4, (int)KpiModuleType.OtherKPI1,
         (int)KpiModuleType.OtherKPI2, (int)KpiModuleType.OtherKPI3 , (int)KpiModuleType.OtherKPI4 , (int)KpiModuleType.OtherKPI5 , (int)KpiModuleType.OtherKPI6 , (int)KpiModuleType.OtherKPI7 , (int)KpiModuleType.OtherKPI8 , (int)KpiModuleType.OtherKPI9 , (int)KpiModuleType.OtherKPI10};
        public KpiChartService(IDapperGenericRepository dapperGenericRepository, ILogger<KpiChartService> logger, IUnitOfWork unitOfWork)
        {
            _dapperGenericRepository = dapperGenericRepository;
            _logger = logger;
            _unitOfWork = unitOfWork;
        }
        /// <summary>
        /// GetKpiValuesByModule
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        public async Task<List<PortfolioChartModel>> GetKpiValuesByModule(PcKPIsFilterType filter)
        {
            return filter.ModuleId switch
            {
                (int)KpiModuleType.TradingRecords or (int)KpiModuleType.Operational or (int)KpiModuleType.Company or (int)KpiModuleType.Investment or (int)KpiModuleType.Impact or (int)KpiModuleType.CreditKPI
                or (int)KpiModuleType.CustomTable1 or (int)KpiModuleType.CustomTable2 or (int)KpiModuleType.CustomTable3 or (int)KpiModuleType.CustomTable4
                or (int)KpiModuleType.OtherKPI1 or (int)KpiModuleType.OtherKPI2 or (int)KpiModuleType.OtherKPI3 or (int)KpiModuleType.OtherKPI4 or (int)KpiModuleType.OtherKPI5
                or (int)KpiModuleType.OtherKPI6 or (int)KpiModuleType.OtherKPI7 or (int)KpiModuleType.OtherKPI8 or (int)KpiModuleType.OtherKPI9 or (int)KpiModuleType.OtherKPI10
                => await FetchPortfolioKpiValuesByModuleId(filter),
                (int)KpiModuleType.ESG => await FetchEsgKpiValues(filter),
                _ => new List<PortfolioChartModel>()
            };
        }
        /// <summary>
        /// RelevantModuleIds
        /// </summary>
        private static readonly HashSet<int> RelevantModuleIds =
        [
            (int)KpiModuleType.TradingRecords,
            (int)KpiModuleType.CreditKPI,
            (int)KpiModuleType.Operational,
            (int)KpiModuleType.Investment,
            (int)KpiModuleType.Company,
            (int)KpiModuleType.Impact,
            (int)KpiModuleType.ProfitAndLoss,
            (int)KpiModuleType.BalanceSheet,
            (int)KpiModuleType.CashFlow,
            (int)KpiModuleType.CustomTable1,
            (int)KpiModuleType.CustomTable2,
            (int)KpiModuleType.CustomTable3,
            (int)KpiModuleType.CustomTable4,
            (int)KpiModuleType.OtherKPI1,
            (int)KpiModuleType.OtherKPI2,
            (int)KpiModuleType.OtherKPI3,
            (int)KpiModuleType.OtherKPI4,
            (int)KpiModuleType.OtherKPI5,
            (int)KpiModuleType.OtherKPI6,
            (int)KpiModuleType.OtherKPI7,
            (int)KpiModuleType.OtherKPI8,
            (int)KpiModuleType.OtherKPI9,
            (int)KpiModuleType.OtherKPI10
        ];
        public async Task<KpiChartModel> CreateChartResponse(PcKPIsFilterType filter)
        {
            List<PortfolioChartModel> kpiDataList = await GetKpiValuesByModule(filter);
            kpiDataList = CommonCalculateValues(kpiDataList, filter.ChartType);
            List<string> pageConfigFields = new();
            if (RelevantModuleIds.Contains(filter.ModuleId))
            {
                pageConfigFields = GetPageConfigKpiWiseChartFields(filter);
                pageConfigFields = pageConfigFields.Select(item => item.Replace(" LTM", "").Replace(" YTD", "")).ToList();
            }
            else
            {
                pageConfigFields = new() { Constants.ActualString };
                kpiDataList = kpiDataList.Any(k => k.ActualValue == "") ? new() : kpiDataList;
            }

            List<string> yLineFields = filter.IsTrend ? []: pageConfigFields.Count == 0 ? new() : pageConfigFields.ConvertAll(x => PcChartHelper.ChartYLineFieldsCopy(x, kpiDataList));
            List<string> yBarFields = pageConfigFields.ConvertAll(x => PcChartHelper.ChartYBarFieldsCopy(x, kpiDataList));
            List<string> YShades = pageConfigFields.ConvertAll(x => PcChartHelper.GetBarColor(x, kpiDataList));

            return new KpiChartModel()
            {
                YShades = YShades.Where(s => s != null).ToList(),
                XField = PcChartHelper.GetXField(filter),
                YBarFields = yBarFields.Where(s => s != null).ToList(),
                YLineFields = yLineFields.Where(s => s != null).ToList(),
                Data = pageConfigFields.Count == 0 ? new() : FormatChartResponse(kpiDataList, filter)
            };
        }
        public List<string> GetPageConfigKpiWiseChartFields(PcKPIsFilterType filter)
        {
            string pageConfigName = _unitOfWork.M_KpiModulesRepository.GetFirstOrDefault(x => !x.IsDeleted && x.IsActive && x.ModuleID == filter.ModuleId)?.PageConfigFieldName;
            int? fieldId = _unitOfWork.SubPageFieldsRepository.GetFirstOrDefault(x => !x.IsDeleted && x.IsActive && (x.SubPageID == (int)PageConfigurationSubFeature.KeyPerformanceIndicator || x.SubPageID == (int)PageConfigurationSubFeature.OtherKPIs) && x.Name == pageConfigName)?.FieldID;
            filter.IsTrend = _unitOfWork.SubPageFieldsRepository
                    .GetFirstOrDefault(y => !y.IsDeleted && y.FieldID == fieldId).IsTrend;
            var subSectionFields = _unitOfWork.MSubSectionFieldsRepository.GetManyQueryable(y => !y.IsDeleted && y.FieldID == fieldId).ToList();
            subSectionFields = FilterSubSectionFields(subSectionFields, filter);
            var data = subSectionFields.OrderBy(x => x.SequenceNo).ToList().ConvertAll(x => new MSubFields { AliasName = x.Name, ChartValue = string.IsNullOrEmpty(x.ChartValue) ? new() : x.ChartValue?.Split(',').ToList<string>(), FieldID = x.FieldID });
            List<SubPageDetailsUploadModel> configValues = new();
            data.ForEach(item => item.ChartValue.ForEach(value => configValues.Add(new SubPageDetailsUploadModel { AliasName = item.AliasName, Name = value })));
            return configValues.Where(x => x.Name == filter.ChartType).Select(x => x.AliasName).ToList();
        }

        public List<ExpandoObject> FormatChartResponse(List<PortfolioChartModel> kpiDataList, PcKPIsFilterType filter)
        {
            List<ExpandoObject> chartResponse = new();
            kpiDataList.ForEach(kpiData =>
            {
                ExpandoObject expandObject = new();
                var expandDictionary = expandObject as IDictionary<string, object>;
                PcChartHelper.CreateChartExpandoObject(filter, kpiData, expandDictionary);
                chartResponse.Add(expandObject);
            });
            return chartResponse;
        }
        public async Task<List<PortfolioChartModel>> FetchEsgKpiValues(PcKPIsFilterType pcKPIsFilter)
        {
            var valueList = await GetValueTypes();
            var esgkpiValues = await FetchESGKpiValuesByFilter(pcKPIsFilter);
            var filteredKpiValues = ApplyChartTypeFilter(pcKPIsFilter, esgkpiValues);
            int? valueTypeId = GetValueTypeId(filteredKpiValues, valueList);
            return PcChartHelper.FilterAndProcessKpiValues(pcKPIsFilter, filteredKpiValues, valueTypeId);
        }
        public List<PortfolioChartModel> EsgApplyChartTypeFilter(PcKPIsFilterType filter, List<PortfolioChartModel> kpiValues)
        {
            return filter.ChartType switch
            {
                Constants.Quarterly =>  kpiValues.Where(x => !string.IsNullOrEmpty(x.Quarter) && x.Month == 0 && x.Year > 0).ToList(),
                Constants.Annual =>kpiValues.Where(x => string.IsNullOrEmpty(x.Quarter) && x.Month == 0 && x.Year > 0).ToList(),
                _ => new List<PortfolioChartModel>()
            };
        }
        private async Task<List<M_ValueTypes>> GetValueTypes()
        {
            return await _unitOfWork.M_ValueTypesRepository.FindAllAsync(x => !x.IsDeleted);
        }
        public int? GetValueTypeId(List<PortfolioChartModel> filteredKpiValues, List<M_ValueTypes> valueList)
        {
            List<M_ValueTypes> dataValueTypeList = valueList.Where(x => filteredKpiValues.Select(x => x.ValueTypeId).Distinct().ToList().Contains(x.ValueTypeID)).OrderBy(x => x.HeaderValue).ToList();
            return dataValueTypeList.FirstOrDefault()?.ValueTypeID;
        }
        public async Task<List<PortfolioChartModel>> FetchESGKpiValuesByFilter(PcKPIsFilterType pcKPIsFilter)
        {
            return await _dapperGenericRepository.Query<PortfolioChartModel>(SqlConstants.QueryGetEsgKpiValues, new { @kpiId = pcKPIsFilter.KpiId, @moduleId = pcKPIsFilter.SubPageModuleId, @portfolioCompanyId = pcKPIsFilter.CompanyId });  
        }
        public List<PortfolioChartModel> ApplyChartTypeFilter(PcKPIsFilterType filter, List<PortfolioChartModel> kpiValues, List<PortfolioChartModel> actualValues=null)
        {
            return filter.ChartType switch
            {
                Constants.Monthly => PcChartHelper.GetFilteredMonthlyData(filter, kpiValues.Where(x => string.IsNullOrEmpty(x.Quarter) && x.Month != 0 && x.Year > 0).ToList(), actualValues),
                Constants.Quarterly => PcChartHelper.GetQuarterlyData(filter, kpiValues.Where(x => !string.IsNullOrEmpty(x.Quarter) && x.Month == 0 && x.Year > 0).ToList(), actualValues),
                Constants.Annual => PcChartHelper.GetFilteredAnnualData(filter, kpiValues.Where(x => string.IsNullOrEmpty(x.Quarter) && x.Month == 0 && x.Year > 0).ToList(), actualValues),
                _ => new List<PortfolioChartModel>()
            };
        }
        public List<PortfolioChartModel> CommonCalculateValues(List<PortfolioChartModel> kpiDataList, string chartType)
        {
            try
            {
                kpiDataList = PcChartHelper.SortKPIDataList(kpiDataList, chartType);
                decimal? previousActualValue = null;
                decimal? previousBudgetValue = null;
                decimal? previousForecastValue = null;
                decimal? previousICValue = null;
                kpiDataList.ForEach(kpiData =>
                {
                    previousActualValue = PcChartHelper.CalculateChangeInValue(kpiData, previousActualValue, Constants.ActualString);
                    previousBudgetValue = PcChartHelper.CalculateChangeInValue(kpiData, previousBudgetValue, Constants.BudgetString);
                    previousForecastValue = PcChartHelper.CalculateChangeInValue(kpiData, previousForecastValue, Constants.ForecastString);
                    previousICValue = PcChartHelper.CalculateChangeInValue(kpiData, previousICValue, Constants.ICString);
                });
                return kpiDataList;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Method:CommonCalculateValues: {ex.Message}");
                return new List<PortfolioChartModel>();
            }
        }
        public async Task<List<PortfolioChartModel>> FetchPortfolioKpiValuesByModuleId(PcKPIsFilterType pcKPIsFilter)
        {
            var valueList = await GetValueTypes();
            var kpiValues = ModuleIds.Contains(pcKPIsFilter.ModuleId) ?
                            await DefaultViewFetchCompanyKpiValuesByFilter(pcKPIsFilter) :
                            new List<PortfolioChartModel>();
            if (ModuleIds.Contains(pcKPIsFilter.ModuleId) && pcKPIsFilter?.SearchFilter != null && pcKPIsFilter?.SearchFilter?.PeriodType == Common.GetDescription(PeriodTypeFeature.Last1Year))
            {
                return PcChartHelper.FilterAndProcessKpiValues(pcKPIsFilter, kpiValues, GetValueTypeId(kpiValues, valueList));
            }
            var filteredKpiValues = ApplyChartTypeFilter(pcKPIsFilter, kpiValues);
            int? valueTypeId = GetValueTypeId(filteredKpiValues, valueList);
            return PcChartHelper.FilterAndProcessKpiValues(pcKPIsFilter, filteredKpiValues, valueTypeId);
        }
        /// <summary>
        /// A service class that provides a mapping between KpiModuleType and corresponding SQL queries.
        /// </summary>
        private readonly Dictionary<int, string> _queryByModuleId = new()
        {
            [(int)KpiModuleType.TradingRecords] = SqlConstants.QueryGetPortfolioMasterKpiChartValues,
            [(int)KpiModuleType.Investment] = SqlConstants.QueryGetInvestmentKpiChartValues,
            [(int)KpiModuleType.Operational] = SqlConstants.QueryGetPortfolioOperationalChartValues,
            [(int)KpiModuleType.Company] = SqlConstants.QueryGetPortfolioCompanyChartValues,
            [(int)KpiModuleType.Impact] = SqlConstants.QueryGetImpactKpiChartValues,
            [(int)KpiModuleType.CreditKPI] = SqlConstants.QueryGetPortfolioMasterKpiChartValues,
            [(int)KpiModuleType.CustomTable1] = SqlConstants.QueryGetPortfolioMasterKpiChartValues,
            [(int)KpiModuleType.CustomTable2] = SqlConstants.QueryGetPortfolioMasterKpiChartValues,
            [(int)KpiModuleType.CustomTable3] = SqlConstants.QueryGetPortfolioMasterKpiChartValues,
            [(int)KpiModuleType.CustomTable4] = SqlConstants.QueryGetPortfolioMasterKpiChartValues,
            [(int)KpiModuleType.OtherKPI1] = SqlConstants.QueryGetPortfolioMasterKpiChartValues,
            [(int)KpiModuleType.OtherKPI2] = SqlConstants.QueryGetPortfolioMasterKpiChartValues,
            [(int)KpiModuleType.OtherKPI3] = SqlConstants.QueryGetPortfolioMasterKpiChartValues,
            [(int)KpiModuleType.OtherKPI4] = SqlConstants.QueryGetPortfolioMasterKpiChartValues,
            [(int)KpiModuleType.OtherKPI5] = SqlConstants.QueryGetPortfolioMasterKpiChartValues,
            [(int)KpiModuleType.OtherKPI6] = SqlConstants.QueryGetPortfolioMasterKpiChartValues,
            [(int)KpiModuleType.OtherKPI7] = SqlConstants.QueryGetPortfolioMasterKpiChartValues,
            [(int)KpiModuleType.OtherKPI8] = SqlConstants.QueryGetPortfolioMasterKpiChartValues,
            [(int)KpiModuleType.OtherKPI9] = SqlConstants.QueryGetPortfolioMasterKpiChartValues,
            [(int)KpiModuleType.OtherKPI10] = SqlConstants.QueryGetPortfolioMasterKpiChartValues,
        };
        /// <summary>
        /// Retrieves the query associated with the specified module ID.
        /// </summary>
        /// <param name="moduleId">The ID of the module.</param>
        /// <returns>The query associated with the module ID.</returns>
        public string GetQueryByModuleId(int moduleId)
        {
            return _queryByModuleId[moduleId];
        }
        /// <summary>
        /// Fetches the KPI values for a company based on the provided filter.
        /// </summary>
        /// <param name="pcKPIsFilter">The filter containing the KPI ID, company ID, module ID, and other search parameters.</param>
        /// <returns>A list of PortfolioChartModel objects representing the KPI values for the company.</returns>
        /// <remarks>
        /// This method first retrieves the chart fields for the specified module and chart type from the page configuration.
        /// It then executes a query to fetch the KPI values from the database.
        /// If the period type in the search filter is set to 'Last1Year', the method applies additional filtering based on the chart type.
        /// The method returns the filtered list of KPI values.
        /// </remarks>
        public async Task<List<PortfolioChartModel>> DefaultViewFetchCompanyKpiValuesByFilter(PcKPIsFilterType pcKPIsFilter)
        {
            List<PortfolioChartModel> portfolioCharts = new();
            List<string> pageConfigMappedFields = GetPageConfigKpiWiseChartFields(pcKPIsFilter);
            string query = GetQueryByModuleId(pcKPIsFilter.ModuleId);
            List<PortfolioChartModel> chartModels = await _dapperGenericRepository.Query<PortfolioChartModel>(query, new { @kpiId = pcKPIsFilter.KpiId, @portfolioCompanyId = pcKPIsFilter.CompanyId, @moduleId = pcKPIsFilter.ModuleId });
            if (pcKPIsFilter?.SearchFilter != null && pcKPIsFilter?.SearchFilter?.PeriodType == Common.GetDescription(PeriodTypeFeature.Last1Year))
            {
                var defaultHeadersActualValues = pageConfigMappedFields.Any(x => x == Constants.ActualString) ?ApplyChartTypeFilter(pcKPIsFilter, chartModels.Where(x => x.ValueType == Constants.ActualString&&!string.IsNullOrEmpty(x.ActualValue)).ToList()):new List<PortfolioChartModel>();
                pageConfigMappedFields = pageConfigMappedFields.Where(x => x != Constants.ActualString).ToList();
                pageConfigMappedFields.ForEach(item => portfolioCharts.AddRange(ApplyChartTypeFilter(pcKPIsFilter, chartModels.Where(x => x.ValueType == item).ToList(), defaultHeadersActualValues)));
                if (defaultHeadersActualValues.Count == 0 && (pcKPIsFilter.IsYtd || pcKPIsFilter.IsLtm))
                    return await JoinPortfolioChartsByLtmYtdCheckedChartType(pcKPIsFilter, chartModels, pageConfigMappedFields);
                if (!defaultHeadersActualValues.Any())
                    return portfolioCharts;
                return await JoinPortfolioChartsByChartType(pcKPIsFilter, portfolioCharts, defaultHeadersActualValues);
            }
            return chartModels.Where(x => pageConfigMappedFields.Contains(x.ValueType)).ToList();
        }      
        public async Task<List<PortfolioChartModel>> JoinPortfolioChartsByChartType(PcKPIsFilterType pcKPIsFilter, List<PortfolioChartModel> portfolioCharts, List<PortfolioChartModel> defaultHeadersActualValues)
        {
            var defaultHeadersActual = await GetDistinctHeaders(pcKPIsFilter);
            defaultHeadersActualValues = JoinActualValuesByChartType(pcKPIsFilter, defaultHeadersActual, defaultHeadersActualValues);
            if (!defaultHeadersActualValues.Any())
                return new();
            List<PortfolioChartModel> kpiFilteredValues = JoinActualValuesByChartType(pcKPIsFilter, defaultHeadersActual, portfolioCharts);
            kpiFilteredValues.AddRange(defaultHeadersActualValues);
            return kpiFilteredValues;
        }
        public async Task<List<PeriodHeaders>> GetDistinctHeaders(PcKPIsFilterType pcKPIsFilter)
        {
            List<PortfolioChartModel> actualFilteredValues = await GetChartActualValues(pcKPIsFilter);
            List<PeriodHeaders> distinctHeaders = actualFilteredValues.ConvertAll(x => new PeriodHeaders { Month = x.Month, Year = x.Year, Quarter = x.Quarter });
            var chartTypeKeySelectors = new Dictionary<string, Func<PeriodHeaders, object>>
            {
            { Constants.Monthly, h => new { h.Year, h.Month } },
            { Constants.Quarterly, h => new { h.Year, h.Quarter } },
            { Constants.Annual, h => h.Year }
            };
            if (chartTypeKeySelectors.TryGetValue(pcKPIsFilter.ChartType, out var keySelector))
            {
                distinctHeaders = distinctHeaders.GroupBy(keySelector)
                    .Select(g => g.First())
                    .ToList();
            }
            return distinctHeaders;
        }

        private async Task<List<PortfolioChartModel>> GetChartActualValues(PcKPIsFilterType pcKPIsFilter)
        {
            string query = GetActualDataQueryByModuleId(pcKPIsFilter.ModuleId);
            List<PortfolioChartModel> ActualValues = await _dapperGenericRepository.Query<PortfolioChartModel>(query, new { @portfolioCompanyId = pcKPIsFilter.CompanyId, @moduleId = pcKPIsFilter.ModuleId });
            var actualFilteredValues = ApplyChartTypeFilter(pcKPIsFilter, ActualValues);
            return actualFilteredValues;
        }

        public List<PortfolioChartModel> JoinActualValuesByChartType(PcKPIsFilterType pcKPIsFilter, List<PeriodHeaders> defaultHeadersActual, List<PortfolioChartModel> defaultHeadersActualValues)
        {
            List<PortfolioChartModel> kpiFilteredValues = new();
            switch (pcKPIsFilter.ChartType)
            {
                case Constants.Monthly:
                    kpiFilteredValues = defaultHeadersActualValues.Join(defaultHeadersActual,
                        e1 => (e1.Year, e1.Month),
                        e2 => (e2.Year, e2.Month),
                        (e1, e2) => e1).ToList();
                    break;
                case Constants.Quarterly:
                    kpiFilteredValues = defaultHeadersActualValues.Join(defaultHeadersActual,
                        e1 => (e1.Year, e1.Quarter),
                        e2 => (e2.Year, e2.Quarter),
                        (e1, e2) => e1).ToList();
                    break;
                case Constants.Annual:
                    kpiFilteredValues = defaultHeadersActualValues.Join(defaultHeadersActual,
                        e1 => (e1.Year),
                        e2 => (e2.Year),
                        (e1, e2) => e1).ToList();
                    break;
            }
            return kpiFilteredValues;
        }
        /// <summary>
        /// Dictionary that maps module IDs to SQL queries for retrieving actual values for KPI charts.
        /// </summary>
        private readonly Dictionary<int, string> _queryGetActualValuesByModuleId = new()
        {
            [(int)KpiModuleType.Operational] = SqlConstants.QueryGetPortfolioOperationalChartActualValues,
            [(int)KpiModuleType.TradingRecords] = SqlConstants.QueryGetPortfolioMasterKpiChartActualValues,
            [(int)KpiModuleType.Investment] = SqlConstants.QueryGetPortfolioInvestmentKpiChartActualValues,
            [(int)KpiModuleType.Company] = SqlConstants.QueryGetPortfolioCompanyChartActualValues,
            [(int)KpiModuleType.Impact] = SqlConstants.QueryGetPortfolioImpactKpiChartActualValues,
            [(int)KpiModuleType.CreditKPI] = SqlConstants.QueryGetPortfolioMasterKpiChartActualValues,
            [(int)KpiModuleType.CustomTable1] = SqlConstants.QueryGetPortfolioMasterKpiChartActualValues,
            [(int)KpiModuleType.CustomTable2] = SqlConstants.QueryGetPortfolioMasterKpiChartActualValues,
            [(int)KpiModuleType.CustomTable3] = SqlConstants.QueryGetPortfolioMasterKpiChartActualValues,
            [(int)KpiModuleType.CustomTable4] = SqlConstants.QueryGetPortfolioMasterKpiChartActualValues,
            [(int)KpiModuleType.OtherKPI1] = SqlConstants.QueryGetPortfolioMasterKpiChartActualValues,
            [(int)KpiModuleType.OtherKPI2] = SqlConstants.QueryGetPortfolioMasterKpiChartActualValues,
            [(int)KpiModuleType.OtherKPI3] = SqlConstants.QueryGetPortfolioMasterKpiChartActualValues,
            [(int)KpiModuleType.OtherKPI4] = SqlConstants.QueryGetPortfolioMasterKpiChartActualValues,
            [(int)KpiModuleType.OtherKPI5] = SqlConstants.QueryGetPortfolioMasterKpiChartActualValues,
            [(int)KpiModuleType.OtherKPI6] = SqlConstants.QueryGetPortfolioMasterKpiChartActualValues,
            [(int)KpiModuleType.OtherKPI7] = SqlConstants.QueryGetPortfolioMasterKpiChartActualValues,
            [(int)KpiModuleType.OtherKPI8] = SqlConstants.QueryGetPortfolioMasterKpiChartActualValues,
            [(int)KpiModuleType.OtherKPI9] = SqlConstants.QueryGetPortfolioMasterKpiChartActualValues,
            [(int)KpiModuleType.OtherKPI10] = SqlConstants.QueryGetPortfolioMasterKpiChartActualValues,

        };
        /// <summary>
        /// Retrieves the actual data query by module ID.
        /// </summary>
        /// <param name="moduleId">The ID of the module.</param>
        /// <returns>The actual data query.</returns>
        public string GetActualDataQueryByModuleId(int moduleId)
        {
            return _queryGetActualValuesByModuleId[moduleId];
        }
        public async Task<List<PortfolioChartModel>> JoinPortfolioChartsByLtmYtdCheckedChartType(PcKPIsFilterType pcKPIsFilter, List<PortfolioChartModel> chartModels, List<string> pageConfigMappedFields)
        {
            // Initialize a new list of PortfolioChartModel
            List<PortfolioChartModel> portfolioCharts = new();
            var defaultHeadersActual = await GetDistinctHeaders(pcKPIsFilter);
            string valueType = GetValueType(pcKPIsFilter);
            var filteredActualLtmYtdValues = chartModels.Where(x => x.ValueType == valueType).ToList();
            var actualValues = await GetChartActualValues(pcKPIsFilter);
            var defaultFilteredActualLtmYtdValues = ApplyChartTypeFilter(pcKPIsFilter, filteredActualLtmYtdValues, actualValues);
            if (defaultHeadersActual.Count > 0)
                defaultFilteredActualLtmYtdValues = JoinActualValuesByChartType(pcKPIsFilter, defaultHeadersActual, defaultFilteredActualLtmYtdValues);

            var defaultHeadersActualValues = pageConfigMappedFields.Any(x => x == valueType && defaultHeadersActual.Count > 0) ?
                defaultFilteredActualLtmYtdValues :
                new List<PortfolioChartModel>();

            pageConfigMappedFields = pageConfigMappedFields.Where(x => x != valueType).ToList();
            pageConfigMappedFields.ForEach(item =>
            {
                var filteredChartModels = chartModels.Where(x => x.ValueType == item).ToList();
                if (defaultHeadersActual.Count > 0)//HotFix:BPF-14081:If "Actual" headers are present, they will be compared to other types 
                    filteredChartModels = JoinActualValuesByChartType(pcKPIsFilter, defaultHeadersActual, filteredChartModels);
                var chartTypeFilteredData = ApplyChartTypeFilter(pcKPIsFilter, filteredChartModels, defaultHeadersActualValues);
                portfolioCharts.AddRange(chartTypeFilteredData);
            });
            defaultHeadersActualValues = JoinActualValuesByChartType(pcKPIsFilter, defaultHeadersActual, defaultHeadersActualValues);
            if (!defaultHeadersActual.Any())
                return GetWhenActualNullLtmYtd(portfolioCharts, defaultFilteredActualLtmYtdValues);
            List<PortfolioChartModel> kpiFilteredValues = JoinActualValuesByChartType(pcKPIsFilter, defaultHeadersActual, portfolioCharts);
            kpiFilteredValues.AddRange(defaultHeadersActualValues);
            return kpiFilteredValues;
        }
        public List<MSubSectionFields> FilterSubSectionFields(List<MSubSectionFields> subSectionFields, PcKPIsFilterType filter)
        {
            if (!filter.IsLtm && !filter.IsYtd)
            {
                return subSectionFields.Where(x => Constants.ChartLTM.All(ltm => ltm != x.Name) && Constants.ChartYTD.All(ytd => ytd != x.Name)).ToList();
            }
            else if (filter.IsLtm)
            {
                return subSectionFields.Where(x => Constants.ChartLTM.Any(ltm => ltm == x.Name)).ToList();
            }
            else if (filter.IsYtd)
            {
                return subSectionFields.Where(x => Constants.ChartYTD.Any(ytd => ytd == x.Name)).ToList();
            }
            return subSectionFields;
        }
        private List<PortfolioChartModel> GetWhenActualNullLtmYtd(List<PortfolioChartModel> portfolioCharts, List<PortfolioChartModel> copyDefaultHeadersActualValues)
        {
            copyDefaultHeadersActualValues.AddRange(portfolioCharts);
            return copyDefaultHeadersActualValues;
        }
        private string GetValueType(PcKPIsFilterType pcKPIsFilter)
        {
            return pcKPIsFilter.IsLtm ? PcChartHelper.ActualLTM :
            pcKPIsFilter.IsYtd ? PcChartHelper.ActualYTD :
            Constants.ActualString;
        }
    }
}