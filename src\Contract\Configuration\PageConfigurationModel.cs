﻿using Contract.Utility;
using DataAccessLayer.DBModel;
using OfficeOpenXml;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;

namespace Contract.Configuration
{
    [ExcludeFromCodeCoverage]
    public class PageSettingBaseModel : BaseModel
    {
    }
    [ExcludeFromCodeCoverage]
    public class PageDetailModel : PageSettingBaseModel
    {

        public int Id { get; set; }
        [Required]
        public string DisplayName { get; set; }
        public int? ParentId { get; set; }
        [Required]
        public string Name { get; set; }

        public string Description { get; set; }
        public bool IsActive { get; set; }
        public bool IsDeleted { get; set; }
        public string EncryptedID { get; set; }
        public int? SequenceNo { get; set; }
        public string PagePath { get; set; }
        public bool? IsCustom { get; set; }
        public bool IsFootNote { get; set; }
        public bool IsDragDrop { get; set; } = false;
        public bool IsTrend { get; set; }

        public List<SubPageDetailModel> SubPageDetailList { get; set; }
        public List<SubPageFieldModel>  SubPageFieldList { get; set; }
    }

    public class SubPageDetailModel : PageSettingBaseModel
    {

        public int Id { get; set; }
        public string DisplayName { get; set; }
        public int? ParentId { get; set; }
        public string Name { get; set; }

        public string Description { get; set; }
        public bool IsActive { get; set; }
        public bool IsInValid { get; set; } = false;
        public bool IsDeleted { get; set; }
        public string EncryptedID { get; set; }
        public int? SequenceNo { get; set; }

        public string PagePath { get; set; }
        public bool? IsCustom { get; set; }
        public bool IsExpanded { get; set; }
        public bool IsDynamicFieldSupported { get; set; }
        public List<SubPageFieldModel> SubPageFieldList { get; set; }
        public bool IsMandatory { get; set; }
        public bool IsDataType { get; set; }
        public bool IsDragDrop { get; set; }
        public bool IsListData { get; set; }
        public bool ShowOnList { get; set; }
        public bool IsFootNote { get; set; }
    }

    public class SubPageFieldModel : PageSettingBaseModel
    {

        public int Id { get; set; }
        public string DisplayName { get; set; }
        public int SubPageID { get; set; }

        public string Name { get; set; }

        public string Description { get; set; }
        public bool IsActive { get; set; }
        public bool IsDeleted { get; set; }
        public string EncryptedID { get; set; }
        public int SequenceNo { get; set; }

        public string PagePath { get; set; }
        public bool IsCustom { get; set; }
        public bool IsMandatory { get; set; }
        public int DataTypeId { get; set; }
        public bool IsListData { get; set; }
        public bool ShowOnList { get; set; }
        public bool IsChart { get; set; }
        public bool IsHighLight { get; set; } = false;
        public bool IsPcLink{ get; set; } = false;
        public bool IsInValid { get; set; } = false;
        public bool IsTrend { get; set; }
        public List<MSubFields> MSubFields { get; set; }
    }

    public class PageDetailUploadModel
    {
        public int PageID { get; set; }
        public string Name { get; set; }
        public string AliasName { get; set; }
        public bool IsCustom { get; set; }
    }
    public class SubPageFieldsUploadModel
    {
        public int FieldID { get; set; }
        public int SubPageID { get; set; }
        public string Name { get; set; }
        public string AliasName { get; set; }
        public bool IsCustom { get; set; }
        public int SequenceNo { get; set; }
    }
    public class CompanyFieldModel
    {
        public int PortfolioCompanyId { get; set; }
        public List<PageFieldValueModel> PageFieldValues { get; set; }
        public List<PageFieldValueModelTrackRecord> PageFieldTrackRecordValues { get; set; }
    }
    public class TrackRecordFieldModel
    {
        public int FeatureId { get; set; }
        public List<PageFieldTrackRecordValueModel> PageFieldTrackRecordValues { get; set; }
    }
    public class FundFieldModel
    {
        public int? FundId { get; set; }
        public List<PageFieldValueModel> PageFieldValues { get; set; }
    }
    public class SubPageDetailsUploadModel
    {
        public int SubPageID { get; set; }
        public string Name { get; set; }
        public string AliasName { get; set; }
        public bool IsCustom { get; set; }
    }

    public class PageFieldValueModel
    {
        public int PageID { get; set; }
        public int SubPageID { get; set; }
        public int FieldID { get; set; }
        public string Name { get; set; }
        public string DisplayName { get; set; }
        public string Value { get; set; }
        public int Sequence { get; set; }
        public bool HasLink { get; set; } = false;
        public string Link { get; set; }
        public string LinkEncryptedId { get; set; } 
        public int? PageFeatureId { get; set; } 
        public bool IsActive { get; set; }
        public bool IsCustom { get; set; }
        public bool IsMandatory { get; set; }
        public bool IsListData { get; set; }
        public bool IsHighLight { get; set; } = false;
        public bool ShowOnList { get; set; }
        public int DataTypeId { get; set; }
    }
    public class PageFieldTrackRecordValueModel
    {
        public int PageID { get; set; }
        public int SubPageID { get; set; }
        public int FieldID { get; set; }
        public string Name { get; set; }
        public string DisplayName { get; set; }
        public string Value { get; set; }
        public int Sequence { get; set; }
        public bool HasLink { get; set; } = false;
        public string Link { get; set; }
        public string LinkEncryptedId { get; set; }
        public int? PageFeatureId { get; set; }
        public bool IsActive { get; set; }
        public string Quarter { get; set; }
        public int? Year { get; set; }
    }
    public class PageFieldValueModelTrackRecord 
    {
        public int Year { get; set; }
        public string Quarter { get; set; }
        public bool IsCustom { get; set; }
        public int DataType { get; set; }
        public int PageID { get; set; }
        public int SubPageID { get; set; }
        public int FieldID { get; set; }
        public string Name { get; set; }
        public string DisplayName { get; set; }
        public dynamic Value { get; set; }
        public int Sequence { get; set; }
        public bool HasLink { get; set; } = false;
        public string Link { get; set; }
        public string LinkEncryptedId { get; set; }
        public int? PageFeatureId { get; set; }
        public bool IsActive { get; set; }
        public int? FundId { get; set; }
    }

    public class PageFieldCommentaryCustomFieldValueModel : PageSettingBaseModel
    {
        public int Id { get; set; }      
        public int FieldID { get; set; }
        public string Name { get; set; }
        public string DisplayName { get; set; }
        public string Value { get; set; }         
        public int PageFeatureEntityId { get; set; }
        public bool IsActive { get; set; }
        public bool IsDeleted { get; set; }
        public string Quarter { get; set; }
        public int Year { get; set; }
        public string Period { get; set; }
        public int Month { get; set; }
    }

    public class InvestorFundsFieldValidationModel 
    {
        public int InvestorId { get; set; }
        public string Cell { get; set; }
        public FundInvestors FundInvestors { get; set; }
        public int NewRow { get; set; }
        public M_SubPageFields PageField { get; set; }
    }
    public class MSubFields
    {
        public string Name { get; set; }
        public int SectionID { get; set; }
        public int FieldID { get; set; }
        public string AliasName { get; set; }
        public int SubPageID { get; set; }
        public List<string> Options { get; set; }
        public List<string> ChartValue { get; set; }
        public string SubFieldAliasName { get; set; }
    }

    public class KpiConfig
    {
        public List<MSubFields> KpiConfigurationData { get; set; }
        public bool HasChart { get; set; }
        public string KpiType { get; set; }
        public string AliasName { get; set; }
        public string SectionName { get; set; }
    }
    public class SubPageFieldAnalyticsModel
    {
        public int FieldID { get; set; }
        public int SubPageID { get; set; }
        public string Name { get; set; }
        public string AliasName { get; set; }
    }
    public class StaticSubPageFieldModel
    {
        public List<SubPageFieldAnalyticsModel> FundStaticFields { get; set; }
        public List<SubPageFieldAnalyticsModel> DealStaticFields { get; set; }
        public List<SubPageFieldAnalyticsModel> InvestorStaticFields { get; set; }
        public List<SubPageFieldAnalyticsModel> FixedStaticFields { get; set; }
    }
    public class SubPageFieldsModel
    {
        public int FieldId { get; set; }
        public int SubPageId { get; set; }
        public string Name { get; set; }
        public string AliasName { get; set; }
        public bool IsCustom { get; set; }
        public int DataTypeId { get; set; }
    }
}
