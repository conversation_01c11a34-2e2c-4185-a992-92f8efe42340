﻿using DataAccessLayer.Models;
using System.Collections.Generic;

namespace DataAccessLayer.DBModel
{
    public partial class MImpactKpi : BaseModel
    {
        public int ImpactKpiId { get; set; }
        public string Kpi { get; set; }
        public string KpiInfo { get; set; }
        public string Description { get; set; }
        public bool IsDeleted { get; set; }
        public int? ParentId { get; set; }
        public int MethodologyId { get; set; }
        public bool IsParent { get; set; }
        public bool IsHeader { get; set; }
        public string AnnualCalculationMethod { get; set; }
        public string EncryptedImpactKpiId { get; set; }
        public string Formula { get; set; }
        public string FormulaKPIId { get; set; }
        public string Synonym { get; set; }
        public virtual ICollection<PcImpactKpiQuarterlyValue> PcImpactKpiQuarterlyValues { get; set; }
        public virtual ICollection<Mapping_ImpactKPI_Order> Mapping_ImpactKPI_Order { get; set; }
        public bool IsBoldKPI { get; set; }
    }
}
