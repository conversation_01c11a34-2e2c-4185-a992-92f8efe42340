using DataAccessLayer.DBModel;
using DataAccessLayer.GenericRepository;
using DataAccessLayer.Models.EmailNotifications;
using DataAccessLayer.UnitOfWork;
using EmailNotification.Services;
using Microsoft.Extensions.Logging;
using Moq;
using System.Linq.Expressions;

namespace EmailConfiguration.UnitTest.Services
{
    public class EmailGroupServiceTests
    {
        private readonly Mock<IUnitOfWork> _unitOfWorkMock;
        private readonly Mock<ILogger<EmailGroupService>> _loggerMock;
        private readonly Mock<IGenericRepository<EmailNotificationGroup>> _emailGroupRepoMock;
        private readonly Mock<IGenericRepository<EmailListMember>> _emailMemberRepoMock;
        private readonly Mock<IGenericRepository<CompanyEmailGroup>> _companyGroupRepoMock;
        private readonly Mock<IGenericRepository<CompanyEmailGroup>> _companyEmailGroupRepoMock;
        private readonly Mock<IGenericRepository<EmailReminderRecipients>> _reminderRecipientsRepoMock;
        private readonly Mock<IGenericRepository<UserDetails>> _userRepoMock;
        private readonly EmailGroupService _emailGroupService;

        public EmailGroupServiceTests()
        {
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            _loggerMock = new Mock<ILogger<EmailGroupService>>();
            _emailGroupRepoMock = new Mock<IGenericRepository<EmailNotificationGroup>>();
            _emailMemberRepoMock = new Mock<IGenericRepository<EmailListMember>>();
            _companyGroupRepoMock = new Mock<IGenericRepository<CompanyEmailGroup>>();
            _companyEmailGroupRepoMock = new Mock<IGenericRepository<CompanyEmailGroup>>();
            _reminderRecipientsRepoMock = new Mock<IGenericRepository<EmailReminderRecipients>>();
            _userRepoMock = new Mock<IGenericRepository<UserDetails>>();

            SetupRepositoryMocks();
            _emailGroupService = new EmailGroupService(_unitOfWorkMock.Object, _loggerMock.Object);
        }

        private void SetupRepositoryMocks()
        {
            _unitOfWorkMock.Setup(u => u.EmailNotificationGroupRepository).Returns(_emailGroupRepoMock.Object);
            _unitOfWorkMock.Setup(u => u.EmailListMemberRepository).Returns(_emailMemberRepoMock.Object);
            _unitOfWorkMock.Setup(u => u.CompanyEmailGroupRepository).Returns(_companyEmailGroupRepoMock.Object);
            _unitOfWorkMock.Setup(u => u.EmailReminderRecipientsRepository).Returns(_reminderRecipientsRepoMock.Object);
            _unitOfWorkMock.Setup(u => u.UserRepository).Returns(_userRepoMock.Object);
        }

        [Fact]
        public async Task DeleteEmailGroupAsync_ValidGroupId_ReturnsTrue()
        {
            // Arrange
            var groupId = 1;
            var userId = 123;
            var group = new EmailNotificationGroup
            {
                GroupId = groupId,
                GroupName = "Test Group",
                IsDeleted = false,
                CreatedBy = userId,
                CreatedOn = DateTime.UtcNow
            };

            var members = new List<EmailListMember>
            {
                new EmailListMember { MemberId = 1, GroupId = groupId, Name = "Test User", Email = "<EMAIL>", IsDeleted = false }
            };

            var companyAssociations = new List<CompanyEmailGroup>
            {
                new CompanyEmailGroup { CompanyEmailGroupId = 1, GroupId = groupId, CompanyId = "1", IsDeleted = false }
            };

            _emailGroupRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<Expression<Func<EmailNotificationGroup, bool>>>()))
                .ReturnsAsync(group);
            _reminderRecipientsRepoMock.Setup(r => r.FindAllAsync(It.IsAny<Expression<Func<EmailReminderRecipients, bool>>>()))
                .ReturnsAsync(new List<EmailReminderRecipients>());
            _emailMemberRepoMock.Setup(r => r.FindAllAsync(It.IsAny<Expression<Func<EmailListMember, bool>>>()))
                .ReturnsAsync(members);
            _companyEmailGroupRepoMock.Setup(r => r.FindAllAsync(It.IsAny<Expression<Func<CompanyEmailGroup, bool>>>()))
                .ReturnsAsync(companyAssociations);
            _unitOfWorkMock.Setup(u => u.SaveAsync()).ReturnsAsync(1);

            // Act
            var result = await _emailGroupService.DeleteEmailGroupAsync(groupId, userId);

            // Assert
            Assert.True(result);
            _emailGroupRepoMock.Verify(r => r.Update(It.Is<EmailNotificationGroup>(g => g.IsDeleted == true)), Times.Once);
            _emailMemberRepoMock.Verify(r => r.Update(It.Is<EmailListMember>(m => m.IsDeleted == true)), Times.Once);
            _companyEmailGroupRepoMock.Verify(r => r.Update(It.Is<CompanyEmailGroup>(c => c.IsDeleted == true)), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Fact]
        public async Task DeleteEmailGroupAsync_GroupNotFound_ReturnsFalse()
        {
            // Arrange
            var groupId = 999;
            var userId = 123;

            _emailGroupRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<Expression<Func<EmailNotificationGroup, bool>>>()))
                .ReturnsAsync((EmailNotificationGroup)null);

            // Act
            var result = await _emailGroupService.DeleteEmailGroupAsync(groupId, userId);

            // Assert
            Assert.False(result);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Never);
        }

        [Fact]
        public async Task DeleteEmailGroupAsync_GroupReferencedInReminders_ThrowsInvalidOperationException()
        {
            // Arrange
            var groupId = 1;
            var userId = 123;
            var group = new EmailNotificationGroup
            {
                GroupId = groupId,
                GroupName = "Test Group",
                IsDeleted = false
            };

            var reminderReferences = new List<EmailReminderRecipients>
            {
                new EmailReminderRecipients { Id = 1, GroupID = groupId, IsGroupMember = true, IsDeleted = false },
                new EmailReminderRecipients { Id = 2, GroupID = groupId, IsGroupMember = true, IsDeleted = false }
            };

            _emailGroupRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<Expression<Func<EmailNotificationGroup, bool>>>()))
                .ReturnsAsync(group);
            _reminderRecipientsRepoMock.Setup(r => r.FindAllAsync(It.IsAny<Expression<Func<EmailReminderRecipients, bool>>>()))
                .ReturnsAsync(reminderReferences);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
                _emailGroupService.DeleteEmailGroupAsync(groupId, userId));

            Assert.Contains("Cannot delete email group as it is referenced in 2 active email reminders", exception.Message);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Never);
        }

        [Fact]
        public async Task DeleteEmailGroupAsync_ExceptionThrown_LogsErrorAndRethrows()
        {
            // Arrange
            var groupId = 1;
            var userId = 123;

            _emailGroupRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<Expression<Func<EmailNotificationGroup, bool>>>()))
                .ThrowsAsync(new Exception("Database connection failed"));

            // Act & Assert
            var exception = await Assert.ThrowsAsync<Exception>(() =>
                _emailGroupService.DeleteEmailGroupAsync(groupId, userId));

            Assert.Equal("Database connection failed", exception.Message);
        }

        [Fact]
        public async Task DeleteEmailGroupAsync_SetsCorrectAuditFields()
        {
            // Arrange
            var groupId = 1;
            var userId = 123;
            var group = new EmailNotificationGroup
            {
                GroupId = groupId,
                GroupName = "Test Group",
                IsDeleted = false
            };

            var member = new EmailListMember
            {
                MemberId = 1,
                GroupId = groupId,
                Name = "Test User",
                Email = "<EMAIL>",
                IsDeleted = false
            };

            var companyAssociation = new CompanyEmailGroup
            {
                CompanyEmailGroupId = 1,
                GroupId = groupId,
                CompanyId = "1",
                IsDeleted = false
            };

            _emailGroupRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<Expression<Func<EmailNotificationGroup, bool>>>()))
                .ReturnsAsync(group);
            _reminderRecipientsRepoMock.Setup(r => r.FindAllAsync(It.IsAny<Expression<Func<EmailReminderRecipients, bool>>>()))
                .ReturnsAsync(new List<EmailReminderRecipients>());
            _emailMemberRepoMock.Setup(r => r.FindAllAsync(It.IsAny<Expression<Func<EmailListMember, bool>>>()))
                .ReturnsAsync(new List<EmailListMember> { member });
            _companyEmailGroupRepoMock.Setup(r => r.FindAllAsync(It.IsAny<Expression<Func<CompanyEmailGroup, bool>>>()))
                .ReturnsAsync(new List<CompanyEmailGroup> { companyAssociation });
            _unitOfWorkMock.Setup(u => u.SaveAsync()).ReturnsAsync(1);

            // Act
            var result = await _emailGroupService.DeleteEmailGroupAsync(groupId, userId);

            // Assert
            Assert.True(result);
            
            // Verify audit fields are set correctly
            _emailGroupRepoMock.Verify(r => r.Update(It.Is<EmailNotificationGroup>(g => 
                g.IsDeleted == true && 
                g.ModifiedBy == userId && 
                g.ModifiedOn.HasValue)), Times.Once);
                
            _emailMemberRepoMock.Verify(r => r.Update(It.Is<EmailListMember>(m => 
                m.IsDeleted == true && 
                m.ModifiedBy == userId && 
                m.ModifiedOn.HasValue)), Times.Once);
                
            _companyEmailGroupRepoMock.Verify(r => r.Update(It.Is<CompanyEmailGroup>(c =>
                c.IsDeleted == true &&
                c.ModifiedBy == userId &&
                c.ModifiedOn.HasValue)), Times.Once);
        }

        [Fact]
        public async Task DeleteEmailGroupAsync_NoMembersOrCompanyAssociations_StillSucceeds()
        {
            // Arrange
            var groupId = 1;
            var userId = 123;
            var group = new EmailNotificationGroup
            {
                GroupId = groupId,
                GroupName = "Test Group",
                IsDeleted = false
            };

            _emailGroupRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<Expression<Func<EmailNotificationGroup, bool>>>()))
                .ReturnsAsync(group);
            _reminderRecipientsRepoMock.Setup(r => r.FindAllAsync(It.IsAny<Expression<Func<EmailReminderRecipients, bool>>>()))
                .ReturnsAsync(new List<EmailReminderRecipients>());
            _emailMemberRepoMock.Setup(r => r.FindAllAsync(It.IsAny<Expression<Func<EmailListMember, bool>>>()))
                .ReturnsAsync(new List<EmailListMember>());
            _companyEmailGroupRepoMock.Setup(r => r.FindAllAsync(It.IsAny<Expression<Func<CompanyEmailGroup, bool>>>()))
                .ReturnsAsync(new List<CompanyEmailGroup>());
            _unitOfWorkMock.Setup(u => u.SaveAsync()).ReturnsAsync(1);

            // Act
            var result = await _emailGroupService.DeleteEmailGroupAsync(groupId, userId);

            // Assert
            Assert.True(result);
            _emailGroupRepoMock.Verify(r => r.Update(It.Is<EmailNotificationGroup>(g => g.IsDeleted == true)), Times.Once);
            _emailMemberRepoMock.Verify(r => r.Update(It.IsAny<EmailListMember>()), Times.Never);
            _companyEmailGroupRepoMock.Verify(r => r.Update(It.IsAny<CompanyEmailGroup>()), Times.Never);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        #region DeleteEmailMemberAsync Tests

        [Fact]
        public async Task DeleteEmailMemberAsync_ValidGroupIdAndMemberId_ReturnsTrue()
        {
            // Arrange
            var groupId = 1;
            var memberId = 1;
            var userId = 123;
            var group = new EmailNotificationGroup
            {
                GroupId = groupId,
                GroupName = "Test Group",
                IsDeleted = false
            };

            var member = new EmailListMember
            {
                MemberId = memberId,
                GroupId = groupId,
                Name = "Test User",
                Email = "<EMAIL>",
                IsDeleted = false
            };

            _emailGroupRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<Expression<Func<EmailNotificationGroup, bool>>>()))
                .ReturnsAsync(group);
            _emailMemberRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<Expression<Func<EmailListMember, bool>>>()))
                .ReturnsAsync(member);
            _unitOfWorkMock.Setup(u => u.SaveAsync()).ReturnsAsync(1);

            // Act
            var result = await _emailGroupService.DeleteEmailMemberAsync(groupId, memberId, userId);

            // Assert
            Assert.True(result);
            _emailMemberRepoMock.Verify(r => r.Update(It.Is<EmailListMember>(m =>
                m.IsDeleted == true &&
                m.ModifiedBy == userId &&
                m.ModifiedOn.HasValue)), Times.Once);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Fact]
        public async Task DeleteEmailMemberAsync_GroupNotFound_ReturnsFalse()
        {
            // Arrange
            var groupId = 999;
            var memberId = 1;
            var userId = 123;

            _emailGroupRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<Expression<Func<EmailNotificationGroup, bool>>>()))
                .ReturnsAsync((EmailNotificationGroup)null);

            // Act
            var result = await _emailGroupService.DeleteEmailMemberAsync(groupId, memberId, userId);

            // Assert
            Assert.False(result);
            _emailMemberRepoMock.Verify(r => r.FindFirstAsync(It.IsAny<Expression<Func<EmailListMember, bool>>>()), Times.Never);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Never);
        }

        [Fact]
        public async Task DeleteEmailMemberAsync_MemberNotFound_ReturnsFalse()
        {
            // Arrange
            var groupId = 1;
            var memberId = 999;
            var userId = 123;
            var group = new EmailNotificationGroup
            {
                GroupId = groupId,
                GroupName = "Test Group",
                IsDeleted = false
            };

            _emailGroupRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<Expression<Func<EmailNotificationGroup, bool>>>()))
                .ReturnsAsync(group);
            _emailMemberRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<Expression<Func<EmailListMember, bool>>>()))
                .ReturnsAsync((EmailListMember)null);

            // Act
            var result = await _emailGroupService.DeleteEmailMemberAsync(groupId, memberId, userId);

            // Assert
            Assert.False(result);
            _emailMemberRepoMock.Verify(r => r.Update(It.IsAny<EmailListMember>()), Times.Never);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Never);
        }

        [Fact]
        public async Task DeleteEmailMemberAsync_ExceptionThrown_LogsErrorAndRethrows()
        {
            // Arrange
            var groupId = 1;
            var memberId = 1;
            var userId = 123;

            _emailGroupRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<Expression<Func<EmailNotificationGroup, bool>>>()))
                .ThrowsAsync(new Exception("Database connection failed"));

            // Act & Assert
            var exception = await Assert.ThrowsAsync<Exception>(() =>
                _emailGroupService.DeleteEmailMemberAsync(groupId, memberId, userId));

            Assert.Equal("Database connection failed", exception.Message);
        }

        [Fact]
        public async Task DeleteEmailMemberAsync_SetsCorrectAuditFields()
        {
            // Arrange
            var groupId = 1;
            var memberId = 1;
            var userId = 123;
            var testDateTime = DateTime.UtcNow;

            var group = new EmailNotificationGroup
            {
                GroupId = groupId,
                GroupName = "Test Group",
                IsDeleted = false
            };

            var member = new EmailListMember
            {
                MemberId = memberId,
                GroupId = groupId,
                Name = "Test User",
                Email = "<EMAIL>",
                IsDeleted = false
            };

            _emailGroupRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<Expression<Func<EmailNotificationGroup, bool>>>()))
                .ReturnsAsync(group);
            _emailMemberRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<Expression<Func<EmailListMember, bool>>>()))
                .ReturnsAsync(member);
            _unitOfWorkMock.Setup(u => u.SaveAsync()).ReturnsAsync(1);

            // Act
            var result = await _emailGroupService.DeleteEmailMemberAsync(groupId, memberId, userId);

            // Assert
            Assert.True(result);
            _emailMemberRepoMock.Verify(r => r.Update(It.Is<EmailListMember>(m =>
                m.IsDeleted == true &&
                m.ModifiedBy == userId &&
                m.ModifiedOn.HasValue &&
                m.ModifiedOn.Value >= testDateTime)), Times.Once);
        }

        #endregion

        #region DeleteEmailMembersAsync Tests

        [Fact]
        public async Task DeleteEmailMembersAsync_ValidGroupIdAndMemberIds_ReturnsSuccessWithDeletedIds()
        {
            // Arrange
            var groupId = 1;
            var memberIds = new List<int> { 1, 2, 3 };
            var userId = 123;
            var group = new EmailNotificationGroup
            {
                GroupId = groupId,
                GroupName = "Test Group",
                IsDeleted = false
            };

            var members = new List<EmailListMember>
            {
                new EmailListMember { MemberId = 1, GroupId = groupId, Name = "User 1", Email = "<EMAIL>", IsDeleted = false },
                new EmailListMember { MemberId = 2, GroupId = groupId, Name = "User 2", Email = "<EMAIL>", IsDeleted = false }
            };

            _emailGroupRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<Expression<Func<EmailNotificationGroup, bool>>>()))
                .ReturnsAsync(group);
            _emailMemberRepoMock.Setup(r => r.FindAllAsync(It.IsAny<Expression<Func<EmailListMember, bool>>>()))
                .ReturnsAsync(members);
            _unitOfWorkMock.Setup(u => u.SaveAsync()).ReturnsAsync(1);

            // Act
            var result = await _emailGroupService.DeleteEmailMembersAsync(groupId, memberIds, userId);

            // Assert
            Assert.True(result.Success);
            Assert.Equal(2, result.DeletedMemberIds.Count);
            Assert.Contains(1, result.DeletedMemberIds);
            Assert.Contains(2, result.DeletedMemberIds);
            Assert.Single(result.NotFoundMemberIds);
            Assert.Contains(3, result.NotFoundMemberIds);
            _emailMemberRepoMock.Verify(r => r.Update(It.IsAny<EmailListMember>()), Times.Exactly(2));
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Fact]
        public async Task DeleteEmailMembersAsync_GroupNotFound_ReturnsFailureWithAllMembersAsNotFound()
        {
            // Arrange
            var groupId = 999;
            var memberIds = new List<int> { 1, 2, 3 };
            var userId = 123;

            _emailGroupRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<Expression<Func<EmailNotificationGroup, bool>>>()))
                .ReturnsAsync((EmailNotificationGroup)null);

            // Act
            var result = await _emailGroupService.DeleteEmailMembersAsync(groupId, memberIds, userId);

            // Assert
            Assert.False(result.Success);
            Assert.Empty(result.DeletedMemberIds);
            Assert.Equal(memberIds, result.NotFoundMemberIds);
            _emailMemberRepoMock.Verify(r => r.FindAllAsync(It.IsAny<Expression<Func<EmailListMember, bool>>>()), Times.Never);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Never);
        }

        [Fact]
        public async Task DeleteEmailMembersAsync_EmptyMemberIdsList_ReturnsSuccessWithNoChanges()
        {
            // Arrange
            var groupId = 1;
            var memberIds = new List<int>();
            var userId = 123;
            var group = new EmailNotificationGroup
            {
                GroupId = groupId,
                GroupName = "Test Group",
                IsDeleted = false
            };

            _emailGroupRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<Expression<Func<EmailNotificationGroup, bool>>>()))
                .ReturnsAsync(group);

            // Act
            var result = await _emailGroupService.DeleteEmailMembersAsync(groupId, memberIds, userId);

            // Assert
            Assert.True(result.Success);
            Assert.Empty(result.DeletedMemberIds);
            Assert.Empty(result.NotFoundMemberIds);
            _emailMemberRepoMock.Verify(r => r.FindAllAsync(It.IsAny<Expression<Func<EmailListMember, bool>>>()), Times.Never);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Fact]
        public async Task DeleteEmailMembersAsync_NullMemberIdsList_ReturnsSuccessWithNoChanges()
        {
            // Arrange
            var groupId = 1;
            List<int> memberIds = null;
            var userId = 123;
            var group = new EmailNotificationGroup
            {
                GroupId = groupId,
                GroupName = "Test Group",
                IsDeleted = false
            };

            _emailGroupRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<Expression<Func<EmailNotificationGroup, bool>>>()))
                .ReturnsAsync(group);

            // Act
            var result = await _emailGroupService.DeleteEmailMembersAsync(groupId, memberIds, userId);

            // Assert
            Assert.True(result.Success);
            Assert.Empty(result.DeletedMemberIds);
            Assert.Empty(result.NotFoundMemberIds);
            _emailMemberRepoMock.Verify(r => r.FindAllAsync(It.IsAny<Expression<Func<EmailListMember, bool>>>()), Times.Never);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Fact]
        public async Task DeleteEmailMembersAsync_AllMembersNotFound_ReturnsSuccessWithAllAsNotFound()
        {
            // Arrange
            var groupId = 1;
            var memberIds = new List<int> { 999, 998, 997 };
            var userId = 123;
            var group = new EmailNotificationGroup
            {
                GroupId = groupId,
                GroupName = "Test Group",
                IsDeleted = false
            };

            _emailGroupRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<Expression<Func<EmailNotificationGroup, bool>>>()))
                .ReturnsAsync(group);
            _emailMemberRepoMock.Setup(r => r.FindAllAsync(It.IsAny<Expression<Func<EmailListMember, bool>>>()))
                .ReturnsAsync(new List<EmailListMember>());
            _unitOfWorkMock.Setup(u => u.SaveAsync()).ReturnsAsync(1);

            // Act
            var result = await _emailGroupService.DeleteEmailMembersAsync(groupId, memberIds, userId);

            // Assert
            Assert.True(result.Success);
            Assert.Empty(result.DeletedMemberIds);
            Assert.Equal(memberIds, result.NotFoundMemberIds);
            _emailMemberRepoMock.Verify(r => r.Update(It.IsAny<EmailListMember>()), Times.Never);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Fact]
        public async Task DeleteEmailMembersAsync_WithInvalidMemberIds_FiltersOutInvalidIds()
        {
            // Arrange
            var groupId = 1;
            var memberIds = new List<int> { 1, 0, -1, 2 }; // Contains invalid IDs
            var userId = 123;
            var group = new EmailNotificationGroup
            {
                GroupId = groupId,
                GroupName = "Test Group",
                IsDeleted = false
            };

            var members = new List<EmailListMember>
            {
                new EmailListMember { MemberId = 1, GroupId = groupId, Name = "User 1", Email = "<EMAIL>", IsDeleted = false }
            };

            _emailGroupRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<Expression<Func<EmailNotificationGroup, bool>>>()))
                .ReturnsAsync(group);
            _emailMemberRepoMock.Setup(r => r.FindAllAsync(It.IsAny<Expression<Func<EmailListMember, bool>>>()))
                .ReturnsAsync(members);
            _unitOfWorkMock.Setup(u => u.SaveAsync()).ReturnsAsync(1);

            // Act
            var result = await _emailGroupService.DeleteEmailMembersAsync(groupId, memberIds, userId);

            // Assert
            Assert.True(result.Success);
            Assert.Single(result.DeletedMemberIds);
            Assert.Contains(1, result.DeletedMemberIds);
            Assert.Single(result.NotFoundMemberIds);
            Assert.Contains(2, result.NotFoundMemberIds);
            _emailMemberRepoMock.Verify(r => r.Update(It.IsAny<EmailListMember>()), Times.Once);
        }

        #endregion

        #region GetEmailGroupDetailAsync Tests

        [Fact]
        public async Task GetEmailGroupDetailAsync_ValidGroupId_ReturnsCompleteDetails()
        {
            // Arrange
            var groupId = 1;
            var group = new EmailNotificationGroup
            {
                GroupId = groupId,
                GroupName = "Test Group",
                CreatedBy = 123,
                CreatedOn = DateTime.UtcNow,
                ModifiedBy = 456,
                ModifiedOn = DateTime.UtcNow.AddDays(-1),
                IsDeleted = false
            };

            var members = new List<EmailListMember>
            {
                new EmailListMember { MemberId = 1, GroupId = groupId, Name = "User 1", Email = "<EMAIL>", IsActive = true, IsDeleted = false },
                new EmailListMember { MemberId = 2, GroupId = groupId, Name = "User 2", Email = "<EMAIL>", IsActive = false, IsDeleted = false }
            };

            var companyAssociations = new List<CompanyEmailGroup>
            {
                new CompanyEmailGroup { CompanyEmailGroupId = 1, GroupId = groupId, CompanyId = "1", IsSelected = true, IsDeleted = false },
                new CompanyEmailGroup { CompanyEmailGroupId = 2, GroupId = groupId, CompanyId = "2", IsSelected = true, IsDeleted = false }
            };

            var users = new List<UserDetails>
            {
                new UserDetails { UserId = 123, FirstName = "John", LastName = "Doe" },
                new UserDetails { UserId = 456, FirstName = "Jane", LastName = "Smith" }
            };

            _emailGroupRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<Expression<Func<EmailNotificationGroup, bool>>>()))
                .ReturnsAsync(group);
            _emailMemberRepoMock.Setup(r => r.FindAllAsync(It.IsAny<Expression<Func<EmailListMember, bool>>>()))
                .ReturnsAsync(members);
            _companyEmailGroupRepoMock.Setup(r => r.FindAllAsync(It.IsAny<Expression<Func<CompanyEmailGroup, bool>>>()))
                .ReturnsAsync(companyAssociations);
            _userRepoMock.Setup(r => r.GetAll()).Returns(users.AsQueryable());

            // Act
            var result = await _emailGroupService.GetEmailGroupDetailAsync(groupId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(groupId, result.GroupId);
            Assert.Equal("Test Group", result.GroupName);
            Assert.Equal("123", result.CreatedBy);
            Assert.Equal("456", result.ModifiedBy);
            Assert.Equal("John Doe", result.UploadedBy);
            Assert.Equal(2, result.EmailMembers.Count);
            Assert.Equal(2, result.CompanyAssociations.Count);
        }

        [Fact]
        public async Task GetEmailGroupDetailAsync_GroupNotFound_ReturnsNull()
        {
            // Arrange
            var groupId = 999;

            _emailGroupRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<Expression<Func<EmailNotificationGroup, bool>>>()))
                .ReturnsAsync((EmailNotificationGroup)null);

            // Act
            var result = await _emailGroupService.GetEmailGroupDetailAsync(groupId);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task GetEmailGroupDetailAsync_ExceptionThrown_LogsErrorAndRethrows()
        {
            // Arrange
            var groupId = 1;

            _emailGroupRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<Expression<Func<EmailNotificationGroup, bool>>>()))
                .ThrowsAsync(new Exception("Database connection failed"));

            // Act & Assert
            var exception = await Assert.ThrowsAsync<Exception>(() =>
                _emailGroupService.GetEmailGroupDetailAsync(groupId));

            Assert.Equal("Database connection failed", exception.Message);
        }

        #endregion

        #region UpdateEmailGroupAsync Tests

        [Fact]
        public async Task UpdateEmailGroupAsync_ValidUpdate_ReturnsTrue()
        {
            // Arrange
            var groupId = 1;
            var userId = 123;
            var dto = new EmailGroupUpdateDto
            {
                GroupName = "Updated Group Name",
                EmailMembers = new List<EmailMemberDto>
                {
                    new EmailMemberDto { Name = "New User", Email = "<EMAIL>", IsActive = true }
                },
                CompanyIds = new List<int> { 1, 2 }
            };

            var group = new EmailNotificationGroup
            {
                GroupId = groupId,
                GroupName = "Old Group Name",
                IsDeleted = false
            };

            var existingMembers = new List<EmailListMember>
            {
                new EmailListMember { MemberId = 1, GroupId = groupId, IsDeleted = false }
            };

            var existingCompanyAssociations = new List<CompanyEmailGroup>
            {
                new CompanyEmailGroup { CompanyEmailGroupId = 1, GroupId = groupId, CompanyId = "3", IsDeleted = false }
            };

            _emailGroupRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<Expression<Func<EmailNotificationGroup, bool>>>()))
                .ReturnsAsync(group);
            _emailMemberRepoMock.Setup(r => r.FindAllAsync(It.IsAny<Expression<Func<EmailListMember, bool>>>()))
                .ReturnsAsync(existingMembers);
            _companyEmailGroupRepoMock.Setup(r => r.FindAllAsync(It.IsAny<Expression<Func<CompanyEmailGroup, bool>>>()))
                .ReturnsAsync(existingCompanyAssociations);
            _unitOfWorkMock.Setup(u => u.SaveAsync()).ReturnsAsync(1);

            // Act
            var result = await _emailGroupService.UpdateEmailGroupAsync(groupId, dto, userId);

            // Assert
            Assert.True(result);
            Assert.Equal("Updated Group Name", group.GroupName);
            Assert.Equal(userId, group.ModifiedBy);
            Assert.True(group.ModifiedOn.HasValue);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Fact]
        public async Task UpdateEmailGroupAsync_NullDto_ReturnsFalse()
        {
            // Arrange
            var groupId = 1;
            var userId = 123;
            EmailGroupUpdateDto dto = null;

            // Act
            var result = await _emailGroupService.UpdateEmailGroupAsync(groupId, dto, userId);

            // Assert
            Assert.False(result);
            _emailGroupRepoMock.Verify(r => r.FindFirstAsync(It.IsAny<Expression<Func<EmailNotificationGroup, bool>>>()), Times.Never);
        }

        [Fact]
        public async Task UpdateEmailGroupAsync_EmptyGroupName_ReturnsFalse()
        {
            // Arrange
            var groupId = 1;
            var userId = 123;
            var dto = new EmailGroupUpdateDto
            {
                GroupName = "",
                EmailMembers = new List<EmailMemberDto>(),
                CompanyIds = new List<int>()
            };

            // Act
            var result = await _emailGroupService.UpdateEmailGroupAsync(groupId, dto, userId);

            // Assert
            Assert.False(result);
            _emailGroupRepoMock.Verify(r => r.FindFirstAsync(It.IsAny<Expression<Func<EmailNotificationGroup, bool>>>()), Times.Never);
        }

        [Fact]
        public async Task UpdateEmailGroupAsync_GroupNotFound_ReturnsFalse()
        {
            // Arrange
            var groupId = 999;
            var userId = 123;
            var dto = new EmailGroupUpdateDto
            {
                GroupName = "Updated Group Name",
                EmailMembers = new List<EmailMemberDto>(),
                CompanyIds = new List<int>()
            };

            _emailGroupRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<Expression<Func<EmailNotificationGroup, bool>>>()))
                .ReturnsAsync((EmailNotificationGroup)null);

            // Act
            var result = await _emailGroupService.UpdateEmailGroupAsync(groupId, dto, userId);

            // Assert
            Assert.False(result);
            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Never);
        }

        [Fact]
        public async Task UpdateEmailGroupAsync_SmartUpdate_UpdatesExistingAndAddsNewMembers()
        {
            // Arrange
            var groupId = 1;
            var userId = 123;
            var dto = new EmailGroupUpdateDto
            {
                GroupName = "Updated Group Name",
                EmailMembers = new List<EmailMemberDto>
                {
                    new EmailMemberDto { MemberId = 1, Name = "Updated User", Email = "<EMAIL>", IsActive = true }, // Update existing
                    new EmailMemberDto { MemberId = 0, Name = "New User", Email = "<EMAIL>", IsActive = true } // Add new
                },
                CompanyIds = new List<int> { 1, 4 } // Keep 1, add 4, remove 3
            };

            var group = new EmailNotificationGroup
            {
                GroupId = groupId,
                GroupName = "Old Group Name",
                IsDeleted = false
            };

            var existingMembers = new List<EmailListMember>
            {
                new EmailListMember { MemberId = 1, GroupId = groupId, Name = "Old User", Email = "<EMAIL>", IsDeleted = false },
                new EmailListMember { MemberId = 2, GroupId = groupId, Name = "To Delete", Email = "<EMAIL>", IsDeleted = false }
            };

            var existingCompanyAssociations = new List<CompanyEmailGroup>
            {
                new CompanyEmailGroup { CompanyEmailGroupId = 1, GroupId = groupId, CompanyId = "1", IsDeleted = false },
                new CompanyEmailGroup { CompanyEmailGroupId = 2, GroupId = groupId, CompanyId = "3", IsDeleted = false }
            };

            _emailGroupRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<Expression<Func<EmailNotificationGroup, bool>>>()))
                .ReturnsAsync(group);
            _emailMemberRepoMock.Setup(r => r.FindAllAsync(It.IsAny<Expression<Func<EmailListMember, bool>>>()))
                .ReturnsAsync(existingMembers);
            _companyEmailGroupRepoMock.Setup(r => r.FindAllAsync(It.IsAny<Expression<Func<CompanyEmailGroup, bool>>>()))
                .ReturnsAsync(existingCompanyAssociations);
            _unitOfWorkMock.Setup(u => u.SaveAsync()).ReturnsAsync(1);

            // Act
            var result = await _emailGroupService.UpdateEmailGroupAsync(groupId, dto, userId);

            // Assert
            Assert.True(result);

            // Verify group name was updated
            Assert.Equal("Updated Group Name", group.GroupName);

            // Verify existing member was updated (not deleted and re-created)
            var updatedMember = existingMembers.First(m => m.MemberId == 1);
            Assert.Equal("Updated User", updatedMember.Name);
            Assert.Equal("<EMAIL>", updatedMember.Email);
            Assert.Equal(userId, updatedMember.ModifiedBy);
            Assert.True(updatedMember.ModifiedOn.HasValue);

            // Verify member to be deleted was marked as deleted
            var deletedMember = existingMembers.First(m => m.MemberId == 2);
            Assert.True(deletedMember.IsDeleted);
            Assert.Equal(userId, deletedMember.ModifiedBy);

            // Verify new member was added
            _emailMemberRepoMock.Verify(r => r.AddAsyn(It.Is<EmailListMember>(m =>
                m.Name == "New User" && m.Email == "<EMAIL>")), Times.Once);

            // Verify existing company association was updated
            var updatedAssociation = existingCompanyAssociations.First(c => c.CompanyId == "1");
            Assert.True(updatedAssociation.IsSelected);
            Assert.Equal(userId, updatedAssociation.ModifiedBy);

            // Verify company association to be deleted was marked as deleted
            var deletedAssociation = existingCompanyAssociations.First(c => c.CompanyId == "3");
            Assert.True(deletedAssociation.IsDeleted);
            Assert.Equal(userId, deletedAssociation.ModifiedBy);

            // Verify new company association was added
            _companyEmailGroupRepoMock.Verify(r => r.AddAsyn(It.Is<CompanyEmailGroup>(c =>
                c.CompanyId == "4" && c.IsSelected)), Times.Once);

            _unitOfWorkMock.Verify(u => u.SaveAsync(), Times.Once);
        }

        #endregion

        #region GetEmailGroupsWithCountAsync Tests

        [Fact]
        public async Task GetEmailGroupsWithCountAsync_ReturnsCompanyNamesForEachGroup()
        {
            // Arrange
            var group1 = new EmailNotificationGroup { GroupId = 1, GroupName = "Group 1", CreatedBy = 10, CreatedOn = DateTime.UtcNow, IsDeleted = false };
            var group2 = new EmailNotificationGroup { GroupId = 2, GroupName = "Group 2", CreatedBy = 20, CreatedOn = DateTime.UtcNow, IsDeleted = false };
            var groups = new List<EmailNotificationGroup> { group1, group2 };
            var members = new List<EmailListMember> {
                new EmailListMember { MemberId = 1, GroupId = 1, IsDeleted = false },
                new EmailListMember { MemberId = 2, GroupId = 2, IsDeleted = false }
            };
            var users = new List<UserDetails> {
                new UserDetails { UserId = 10, FirstName = "Alice", LastName = "Smith" },
                new UserDetails { UserId = 20, FirstName = "Bob", LastName = "Jones" }
            };
            var companyAssociations = new List<CompanyEmailGroup> {
                new CompanyEmailGroup { GroupId = 1, CompanyId = "100", IsDeleted = false, IsSelected = true },
                new CompanyEmailGroup { GroupId = 1, CompanyId = "101", IsDeleted = false, IsSelected = true },
                new CompanyEmailGroup { GroupId = 2, CompanyId = "102", IsDeleted = false, IsSelected = true }
            };
            var companies = new List<PortfolioCompanyDetails> {
                new PortfolioCompanyDetails { PortfolioCompanyId = 100, CompanyName = "Company A", IsDeleted = false, IsActive = true },
                new PortfolioCompanyDetails { PortfolioCompanyId = 101, CompanyName = "Company B", IsDeleted = false, IsActive = true },
                new PortfolioCompanyDetails { PortfolioCompanyId = 102, CompanyName = "Company C", IsDeleted = false, IsActive = true }
            };
            _emailGroupRepoMock.Setup(r => r.FindAllAsync(It.IsAny<Expression<Func<EmailNotificationGroup, bool>>>())).ReturnsAsync(groups);
            _emailMemberRepoMock.Setup(r => r.FindAllAsync(It.IsAny<Expression<Func<EmailListMember, bool>>>())).ReturnsAsync(members);
            _userRepoMock.Setup(r => r.GetAll()).Returns(users.AsQueryable());
            _companyEmailGroupRepoMock.Setup(r => r.FindAllAsync(It.IsAny<Expression<Func<CompanyEmailGroup, bool>>>())).ReturnsAsync(companyAssociations);
            _unitOfWorkMock.Setup(u => u.PortfolioCompanyDetailRepository.FindAllAsync(It.IsAny<Expression<Func<PortfolioCompanyDetails, bool>>>()))
                .ReturnsAsync(companies);

            // Act
            var result = await _emailGroupService.GetEmailGroupsWithCountAsync();

            // Assert
            Assert.Equal(2, result.Count);
            var group1Dto = result.First(g => g.GroupId == 1);
            var group2Dto = result.First(g => g.GroupId == 2);
            Assert.Contains("Company A", group1Dto.CompanyNames);
            Assert.Contains("Company B", group1Dto.CompanyNames);
            Assert.Contains("Company C", group2Dto.CompanyNames);
            Assert.Equal("Alice Smith", group1Dto.UploadedBy);
            Assert.Equal("Bob Jones", group2Dto.UploadedBy);
        }

        [Fact]
        public async Task GetEmailGroupsWithCountAsync_HandlesNoCompanyAssociations()
        {
            // Arrange
            var group = new EmailNotificationGroup { GroupId = 1, GroupName = "Group 1", CreatedBy = 10, CreatedOn = DateTime.UtcNow, IsDeleted = false };
            var groups = new List<EmailNotificationGroup> { group };
            var members = new List<EmailListMember> { new EmailListMember { MemberId = 1, GroupId = 1, IsDeleted = false } };
            var users = new List<UserDetails> { new UserDetails { UserId = 10, FirstName = "Alice", LastName = "Smith" } };
            _emailGroupRepoMock.Setup(r => r.FindAllAsync(It.IsAny<Expression<Func<EmailNotificationGroup, bool>>>())).ReturnsAsync(groups);
            _emailMemberRepoMock.Setup(r => r.FindAllAsync(It.IsAny<Expression<Func<EmailListMember, bool>>>())).ReturnsAsync(members);
            _userRepoMock.Setup(r => r.GetAll()).Returns(users.AsQueryable());
            _companyEmailGroupRepoMock.Setup(r => r.FindAllAsync(It.IsAny<Expression<Func<CompanyEmailGroup, bool>>>())).ReturnsAsync(new List<CompanyEmailGroup>());
            _unitOfWorkMock.Setup(u => u.PortfolioCompanyDetailRepository.FindAllAsync(It.IsAny<Expression<Func<PortfolioCompanyDetails, bool>>>()))
                .ReturnsAsync(new List<PortfolioCompanyDetails>());

            // Act
            var result = await _emailGroupService.GetEmailGroupsWithCountAsync();

            // Assert
            Assert.Single(result);
            Assert.Empty(result[0].CompanyNames);
        }

        [Fact]
        public async Task GetEmailGroupsWithCountAsync_SkipsInvalidCompanyIds()
        {
            // Arrange
            var group = new EmailNotificationGroup { GroupId = 1, GroupName = "Group 1", CreatedBy = 10, CreatedOn = DateTime.UtcNow, IsDeleted = false };
            var groups = new List<EmailNotificationGroup> { group };
            var members = new List<EmailListMember> { new EmailListMember { MemberId = 1, GroupId = 1, IsDeleted = false } };
            var users = new List<UserDetails> { new UserDetails { UserId = 10, FirstName = "Alice", LastName = "Smith" } };
            var companyAssociations = new List<CompanyEmailGroup> {
                new CompanyEmailGroup { GroupId = 1, CompanyId = "abc", IsDeleted = false, IsSelected = true }, // invalid
                new CompanyEmailGroup { GroupId = 1, CompanyId = "100", IsDeleted = false, IsSelected = true }
            };
            var companies = new List<PortfolioCompanyDetails> {
                new PortfolioCompanyDetails { PortfolioCompanyId = 100, CompanyName = "Company A", IsDeleted = false, IsActive = true }
            };
            _emailGroupRepoMock.Setup(r => r.FindAllAsync(It.IsAny<Expression<Func<EmailNotificationGroup, bool>>>())).ReturnsAsync(groups);
            _emailMemberRepoMock.Setup(r => r.FindAllAsync(It.IsAny<Expression<Func<EmailListMember, bool>>>())).ReturnsAsync(members);
            _userRepoMock.Setup(r => r.GetAll()).Returns(users.AsQueryable());
            _companyEmailGroupRepoMock.Setup(r => r.FindAllAsync(It.IsAny<Expression<Func<CompanyEmailGroup, bool>>>())).ReturnsAsync(companyAssociations);
            _unitOfWorkMock.Setup(u => u.PortfolioCompanyDetailRepository.FindAllAsync(It.IsAny<Expression<Func<PortfolioCompanyDetails, bool>>>()))
                .ReturnsAsync(companies);

            // Act
            var result = await _emailGroupService.GetEmailGroupsWithCountAsync();

            // Assert
            Assert.Single(result);
            Assert.Single(result[0].CompanyNames);
            Assert.Contains("Company A", result[0].CompanyNames);
        }

        [Fact]
        public async Task GetEmailGroupsWithCountAsync_SkipsCompanyIdsNotInCompanyTable()
        {
            // Arrange
            var group = new EmailNotificationGroup { GroupId = 1, GroupName = "Group 1", CreatedBy = 10, CreatedOn = DateTime.UtcNow, IsDeleted = false };
            var groups = new List<EmailNotificationGroup> { group };
            var members = new List<EmailListMember> { new EmailListMember { MemberId = 1, GroupId = 1, IsDeleted = false } };
            var users = new List<UserDetails> { new UserDetails { UserId = 10, FirstName = "Alice", LastName = "Smith" } };
            var companyAssociations = new List<CompanyEmailGroup> {
                new CompanyEmailGroup { GroupId = 1, CompanyId = "999", IsDeleted = false, IsSelected = true }
            };
            var companies = new List<PortfolioCompanyDetails> {
                new PortfolioCompanyDetails { PortfolioCompanyId = 100, CompanyName = "Company A", IsDeleted = false, IsActive = true }
            };
            _emailGroupRepoMock.Setup(r => r.FindAllAsync(It.IsAny<Expression<Func<EmailNotificationGroup, bool>>>())).ReturnsAsync(groups);
            _emailMemberRepoMock.Setup(r => r.FindAllAsync(It.IsAny<Expression<Func<EmailListMember, bool>>>())).ReturnsAsync(members);
            _userRepoMock.Setup(r => r.GetAll()).Returns(users.AsQueryable());
            _companyEmailGroupRepoMock.Setup(r => r.FindAllAsync(It.IsAny<Expression<Func<CompanyEmailGroup, bool>>>())).ReturnsAsync(companyAssociations);
            _unitOfWorkMock.Setup(u => u.PortfolioCompanyDetailRepository.FindAllAsync(It.IsAny<Expression<Func<PortfolioCompanyDetails, bool>>>()))
                .ReturnsAsync(companies);

            // Act
            var result = await _emailGroupService.GetEmailGroupsWithCountAsync();

            // Assert
            Assert.Single(result);
            Assert.Empty(result[0].CompanyNames);
        }

        #endregion

         [Fact]
        public async Task CheckDuplicateName_ReturnsFalse_WhenNoDuplicate()
        {
            // Arrange
            _emailGroupRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<Expression<System.Func<EmailNotificationGroup, bool>>>()))
                .ReturnsAsync((EmailNotificationGroup)null);

            // Act
            var result = await _emailGroupService.CheckDuplicateName("Test", 0);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task CheckDuplicateName_ReturnsTrue_WhenDuplicateExists()
        {
            // Arrange
            _emailGroupRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<Expression<System.Func<EmailNotificationGroup, bool>>>()))
                .ReturnsAsync(new EmailNotificationGroup { GroupId = 2, GroupName = "Test", CreatedBy = 1 });

            // Act
            var result = await _emailGroupService.CheckDuplicateName("Test", 0);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task CheckDuplicateName_ExcludesCurrentGroupId_OnUpdate()
        {
            // Arrange
            _emailGroupRepoMock.Setup(r => r.FindFirstAsync(It.IsAny<Expression<System.Func<EmailNotificationGroup, bool>>>()))
                .ReturnsAsync((EmailNotificationGroup)null);

            // Act
            var result = await _emailGroupService.CheckDuplicateName("Test", 5);

            // Assert
            Assert.False(result);
        }
    }
}
