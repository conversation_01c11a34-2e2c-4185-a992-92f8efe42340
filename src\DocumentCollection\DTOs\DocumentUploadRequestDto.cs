using Microsoft.AspNetCore.Http;

namespace DocumentCollection.DTOs
{
    public class DocumentUploadRequestDto
    {
        /// <summary>
        /// Get or sets the list of files to be uploaded.
        /// </summary>
        public required List<IFormFile> Files { get; set; }

        /// <summary>
        /// Get or sets the list of folder mappings for the uploaded files. example {DoctypeId}/{Year OR Null}/{Null OR Quarter OR Month}
        /// </summary>
        public required string FolderPath { get; set; }

        /// <summary>
        /// Get or sets the list of document types for the uploaded files.
        /// </summary>
        public int CreatedBy { get; set; }

        public int FeatureID { get; set; }
    }
}