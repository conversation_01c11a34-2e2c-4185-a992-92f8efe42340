using Contract.AccountType;
using Contract.City;
using Contract.Country;
using Contract.Currency;
using Contract.Designation;
using Contract.Firm;
using Contract.Funds;
using Contract.MasterMapping;
using Contract.PortfolioCompany;
using Contract.Region;
using Contract.Reports;
using Contract.ReportTemplateConfig;
using Contract.Sector;
using Contract.State;
using Contract.Strategy;
using Contract.Utility;
using DapperRepository;
using DataAccessLayer.DBModel;
using DataAccessLayer.Models;
using DataAccessLayer.UnitOfWork;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Shared;
using System;
using System.Collections.Generic;
using System.Composition;
using System.Data;
using System.Globalization;
using System.Linq;
using System.Transactions;
using Utility.Helpers;
using Contract.Deals;
using System.Text;
using System.Threading.Tasks;
using Contract.Configuration;
using DapperRepository.Constants;
using AutoMapper;
using Utility.Mapper;
using DataAccessLayer.Models.PageSettings;
using DataAccessLayer.Models.Workflow;
using Shared.Enums;
using Deals;

namespace PortfolioCompany
{
    [Export(typeof(IPortfolioCompanyService))]
    [ExportMetadata("Name", "PortfolioCompanyService")]
    public class PortfolioCompanyService : IPortfolioCompanyService
    {
        private IUnitOfWork _unitOfWork = null;
        private IEncryption _encryption = null;
        private IMemoryCacher _memoryCacher = null;
        private IGlobalConfigurations _globalConfig = null;
        private IConfiguration _configuration;
        private const string _allFundingTypesToken = "allFundingTypesStatus";
        private readonly IDapperGenericRepository _dapperGenericRepository;
        private ILogger _logger = null;
        private IMapper _mapper;
        private readonly IDealService _dealService;
        public IInjectedParameters InjectedParameters
        {
            get
            {
                if (_unitOfWork != null)
                {
                    return InjectedParameters;
                }
                else
                {
                    return null;
                }
            }
            set
            {
                var injectedParameters = value;
                _unitOfWork = injectedParameters.UnitOfWork;
                _encryption = injectedParameters.Encryption;
                _memoryCacher = injectedParameters.MemoryCache;
                _globalConfig = injectedParameters.GlobalConfigurations;
                _logger = injectedParameters.Logger;
                _configuration = injectedParameters.Configuration;
                _mapper = injectedParameters.Mapper;
            }
        }
        public PortfolioCompanyService(IDapperGenericRepository dapperGenericRepository,IDealService dealService)
        {
            _dapperGenericRepository = dapperGenericRepository;
            _dealService = dealService;
        }
        public async Task<List<PageColumnModel>> GetPCConfigurationDataForCompanyList(List<SubPageFieldModel> companyStaticFieldsData, PortfolioCompanyQueryListModel companyData, int sectionId)
        {
            List<PageFieldValueModel> customFields = new();
            companyStaticFieldsData = companyStaticFieldsData.Where(x => x.IsListData && x.ShowOnList).Select(x => x).ToList();
            var NA = "NA";
            List<PageFieldValueModel> _PageFieldModelDTO = companyStaticFieldsData.Select(x => new PageFieldValueModel()
            {
                SubPageID = x.SubPageID,
                FieldID = x.Id,
                Name = x.Name,
                Value = NA,
                DisplayName = x.DisplayName,
                Sequence = x.SequenceNo,
                HasLink = false,
                Link = string.Empty,
                LinkEncryptedId = string.Empty,
                IsActive = x.IsActive
            }).ToList();

            var res = new List<PageColumnModel>();
            var configurations = await _unitOfWork.PageConfigurationFieldValueRepository.FindAllAsync(x => x.PageID == (int)PageConfigurationFeature.PortfolioCompany);
            customFields = configurations.OrderBy(x => x.SubPageID).Select(x => _mapper.Map<PageFieldValueModel>(x)).ToList();
            List<PageFieldValueModel> customFieldsCopy = new List<PageFieldValueModel>(customFields);
            companyData?.PortfolioCompanyList.ForEach(company =>
            {
                var data = new PageColumnModel();
                int i = 1;
                customFields = customFieldsCopy;
                _PageFieldModelDTO.ForEach(async item =>
                {
                    var link = item.Link;
                    customFields = customFields.Where(x => x.PageFeatureId == company.PortfolioCompanyID && x.PageID == (int)PageConfigurationFeature.PortfolioCompany && x.SubPageID == sectionId).ToList();
                    var target = customFields.Find(x => x.FieldID == item.FieldID);
                    if (target != null)
                    {
                        data = MapData(data, i, target.Value, link, "Customfield", "", company.EncryptedPortfolioCompanyId, company.WorkflowRequestId, company.DraftName);
                    }
                    else
                    {
                        data = MapData(data, i, null, link, "Customfield", "", company.EncryptedPortfolioCompanyId, company.WorkflowRequestId, company.DraftName);
                    }
                    if (sectionId == (int)PageConfigurationSubFeature.StaticInformation && company != null)
                    {
                        switch (item.Name)
                        {
                            case Constants.CompanyName:
                                data = MapData(data, i, company.CompanyName, "/portfolio-company-detail", Constants.CompanyName, company.EncryptedPortfolioCompanyId, company.EncryptedPortfolioCompanyId, company.WorkflowRequestId, company.DraftName);
                                break;
                            case Constants.FundId:
                                data = MapData(data, i, company.FundName, "/fund-details", "FundName", company.EncryptedFundID, company.EncryptedPortfolioCompanyId, company.WorkflowRequestId, company.DraftName);
                                break;
                            case Constants.DealId:
                                data = MapData(data, i, company.DealCustomID, "/deal-details", "DealCustomID", company.EncryptedDealID, company.EncryptedPortfolioCompanyId, company.WorkflowRequestId, company.DraftName);
                                break;
                            case Constants.MasterCompanyName:
                                data = MapData(data, i, company.MasterCompanyName, "", "MasterCompanyName", "", company.EncryptedPortfolioCompanyId, company.WorkflowRequestId, company.DraftName);
                                break;
                            case Constants.CompanyGroupId:
                                data = MapData(data, i, company.GroupName, "", "GroupName", "", company.EncryptedPortfolioCompanyId, company.WorkflowRequestId, company.DraftName);
                                break;
                            case Constants.SubSector:
                                data = MapData(data, i, company.SubSectorDetail?.SubSector, "", "SubSector", "", company.EncryptedPortfolioCompanyId, company.WorkflowRequestId, company.DraftName);
                                break;
                            case Constants.Sector:
                                data = MapData(data, i, company.SectorDetail?.Sector, "", "Sector", "", company.EncryptedPortfolioCompanyId, company.WorkflowRequestId, company.DraftName);
                                break;
                            case Constants.Currency:
                                data = MapData(data, i, company.ReportingCurrencyDetail?.Currency, "", "Currency", "", company.EncryptedPortfolioCompanyId, company.WorkflowRequestId, company.DraftName);
                                break;
                            case Constants.Website:
                                data = MapData(data, i, company.Website, company.Website, "Website", "", company.EncryptedPortfolioCompanyId, company.WorkflowRequestId, company.DraftName);
                                break;
                            case Constants.CompanyStatus:
                                data = MapData(data, i, company.Status, "", "Status", "", company.EncryptedPortfolioCompanyId, company.WorkflowRequestId, company.DraftName);
                                break;
                            case Constants.StockExchange_Ticker:
                                data = MapData(data, i, company.StockExchange_Ticker, "", "StockExchange_Ticker", "", company.EncryptedPortfolioCompanyId, company.WorkflowRequestId, company.DraftName);
                                break;
                            case Constants.FinancialYearEnd:
                                data = MapData(data, i, company?.FinancialYearEnd?.Split(" ")?.LastOrDefault(), "", "FinancialYearEnd", "", company.EncryptedPortfolioCompanyId, company.WorkflowRequestId, company.DraftName);
                                break;
                            case Constants.CompanyLegalName:
                                data = MapData(data, i, company.CompanyLegalName, string.Empty, Constants.CompanyLegalName, string.Empty, company.EncryptedPortfolioCompanyId, company.WorkflowRequestId, company.DraftName);
                                break;
                            case Constants.InvestmentDate:
                                data = MapData(data, i, company.InvestmentDate?.ToString("MM/dd/yyyy"), string.Empty, Constants.InvestmentDate, string.Empty, company.EncryptedPortfolioCompanyId, company.WorkflowRequestId, company.DraftName);
                                break;
                        }
                    }
                    i++;
                });
                res.Add(data);
            });
            return res;
        }
        public List<PageColumnHeaderModel> GetListHeaders(List<SubPageFieldModel> headers)
        {
            List<PageColumnHeaderModel> listHeaders = new();
            int i = 1;
            foreach (var header in headers)
            {
                var item = new PageColumnHeaderModel();
                item.Header = header.DisplayName;
                switch (i)
                {
                    case 1:
                        item.SortFieldName = "ValueColumn1.Value";
                        break;
                    case 2:
                        item.SortFieldName = "ValueColumn2.Value";
                        break;
                    case 3:
                        item.SortFieldName = "ValueColumn3.Value";
                        break;
                    case 4:
                        item.SortFieldName = "ValueColumn4.Value";
                        break;
                }
                listHeaders.Add(item);
                i++;
            }
            return listHeaders;
        }
        public List<PageColumnModel> FilterAndSort(List<PageColumnModel> companyStaticConfiguartionData, PortfolioCompanyFilter filter, ref int totalCompanyRows)
        {
            var data = companyStaticConfiguartionData.AsQueryable();
            if (filter != null && filter.PaginationFilter != null)
            {
                int totalRows = 0;
                if (!string.IsNullOrEmpty(filter.PaginationFilter.GlobalFilter))
                {
                    var lowerGlobalFilter = filter.PaginationFilter.GlobalFilter.ToLower();
                    data = data?.Where(u => u.ValueColumn1 != null && u.ValueColumn1.Value.ToLower().Contains(lowerGlobalFilter) ||
                    u.ValueColumn2 != null && u.ValueColumn2.Value.ToLower().Contains(lowerGlobalFilter) ||
                    u.ValueColumn3 != null && u.ValueColumn3.Value.ToLower().Contains(lowerGlobalFilter) ||
                    u.ValueColumn4 != null && u.ValueColumn4.Value.ToLower().Contains(lowerGlobalFilter)
                       );
                }
                if (!filter.PaginationFilter.FilterWithoutPaging)
                    data = data?.PagedResult(filter.PaginationFilter.First, filter.PaginationFilter.Rows, filter.PaginationFilter.MultiSortMeta, out totalRows);
                else
                    data = data?.SortedResult(filter.PaginationFilter.SortField, filter.PaginationFilter.SortOrder == 1);
                totalCompanyRows = totalRows;
            }
            return data.ToList();
        }
        public int AddPortfolioCompany(PortfolioCompanyModel PortfolioCompanyModel)
        {
            if (!IsPortfolioCompanyExist(PortfolioCompanyModel))
            {
                using (var scope = new TransactionScope())
                {
                    PortfolioCompanyDetails portfolioCompanyDetail = InsertIntoPortfolioCompanyDetailRepository(PortfolioCompanyModel);
                    _unitOfWork.Save();

                    portfolioCompanyDetail.EncryptedPortfolioCompanyId = _encryption.Encrypt(portfolioCompanyDetail.PortfolioCompanyId.ToString());
                    _unitOfWork.Save();
                    scope.Complete();
                    return portfolioCompanyDetail.PortfolioCompanyId;
                }

            }
            else
            {
                return -1;
            }
        }

        public async Task<bool> UpdateEncryptCompanyId()
        {
            var companies = await _unitOfWork.PortfolioCompanyDetailRepository.FindAllAsync(x => x.EncryptedPortfolioCompanyId == null);
            List<PortfolioCompanyDetails> portfolioCompanyDetails = new();
            foreach (var item in companies)
            {
                item.EncryptedPortfolioCompanyId = _encryption.Encrypt(item.PortfolioCompanyId.ToString());
                portfolioCompanyDetails.Add(item);
            }
            if (portfolioCompanyDetails.Count > 0)
            {
                _unitOfWork.PortfolioCompanyDetailRepository.UpdateBulk(portfolioCompanyDetails);
                await _unitOfWork.SaveAsync();
            }
            return true;
        }
        public async Task<bool> UpdateEncryptFundId()
        {
            var funds = await _unitOfWork.FundDetailRepository.FindAllAsync(x => x.EncryptedFundId == null);
            List<FundDetails> fundDetails = new();
            foreach (var item in funds)
            {
                item.EncryptedFundId = _encryption.Encrypt(item.FundId.ToString());
                fundDetails.Add(item);
            }
            if (fundDetails.Count > 0)
            {
                _unitOfWork.FundDetailRepository.UpdateBulk(fundDetails);
                await _unitOfWork.SaveAsync();
            }
            return true;
        }
        public async Task<bool> UpdateEncryptDealId()
        {
            var deals = await _unitOfWork.DealDetailRepository.FindAllAsync(x => x.EncryptedDealId == null);
            List<DealDetails> dealDetails = new();
            foreach (var item in deals)
            {
                item.EncryptedDealId = _encryption.Encrypt(item.DealId.ToString());
                dealDetails.Add(item);
            }
            if (dealDetails.Count > 0)
            {
                _unitOfWork.DealDetailRepository.UpdateBulk(dealDetails);
                await _unitOfWork.SaveAsync();
            }
            return true;
        }
        private PageColumnModel MapData(PageColumnModel data, int i, string value, string link, string name, string encryptedId, string pcEncryptedId, int workflowRequestId = 0, string draftName = null)
        {
            var colData = new ColumnDataModel();
            switch (i)
            {
                case 1:
                    colData.Value = string.IsNullOrEmpty(value) ? "-" : value;
                    colData.Link = link;
                    colData.Name = name;
                    colData.EncryptedId = encryptedId;
                    colData.PcEncryptedId = pcEncryptedId;
                    colData.WorkflowRequestId = workflowRequestId;
                    colData.DraftName = draftName;
                    data.ValueColumn1 = colData;
                    break;
                case 2:
                    colData.Value = string.IsNullOrEmpty(value) ? "-" : value;
                    colData.Link = link;
                    colData.Name = name;
                    colData.EncryptedId = encryptedId;
                    colData.PcEncryptedId = pcEncryptedId;
                    colData.WorkflowRequestId = workflowRequestId;
                    colData.DraftName = draftName;
                    data.ValueColumn2 = colData;
                    break;
                case 3:
                    colData.Value = string.IsNullOrEmpty(value) ? "-" : value;
                    colData.Link = link;
                    colData.Name = name;
                    colData.EncryptedId = encryptedId;
                    colData.PcEncryptedId = pcEncryptedId;
                    colData.WorkflowRequestId = workflowRequestId;
                    colData.DraftName = draftName;
                    data.ValueColumn3 = colData;
                    break;
                case 4:
                    colData.Value = string.IsNullOrEmpty(value) ? "-" : value;
                    colData.Link = link;
                    colData.Name = name;
                    colData.EncryptedId = encryptedId;
                    colData.PcEncryptedId = pcEncryptedId;
                    colData.WorkflowRequestId = workflowRequestId;
                    colData.DraftName = draftName;
                    data.ValueColumn4 = colData;
                    break;
            }
            return data;
        }
        private PortfolioCompanyDetails InsertIntoPortfolioCompanyDetailRepository(PortfolioCompanyModel portfolioCompanyModel)
        {
            PortfolioCompanyDetails portfolioCompanyDetail = new PortfolioCompanyDetails
            {
                CompanyName = portfolioCompanyModel.CompanyName?.Trim(),

                Website = portfolioCompanyModel.Website?.Trim(),
                BussinessDescription = portfolioCompanyModel.BusinessDescription?.Trim(),
                SectorId = portfolioCompanyModel.SectorDetail?.SectorID,
                SubSectorId = portfolioCompanyModel.SubSectorDetail?.SubSectorID,
                ReportingCurrencyId = portfolioCompanyModel.ReportingCurrencyDetail.CurrencyID,
                StockExchangeTicker = portfolioCompanyModel.StockExchange_Ticker,
                HeadquarterId = portfolioCompanyModel.HeadquarterID,
                Status = portfolioCompanyModel.Status,
                CreatedBy = portfolioCompanyModel.CreatedBy,
                CreatedOn = portfolioCompanyModel.CreatedOn,
                IsActive = true,
                IsDeleted = false,
                FinancialYearEnd = "FY end " + Common.GetMonthDays(portfolioCompanyModel.FinancialYearEnd) + " " + portfolioCompanyModel.FinancialYearEnd,
                ImagePath = portfolioCompanyModel.ImagePath,
                MasterCompanyName = portfolioCompanyModel.MasterCompanyName.Trim(),
                GroupId = portfolioCompanyModel.CompanyGroupId,
                CompanyLegalName = portfolioCompanyModel.CompanyLegalName,
                PCInvestmentDate = portfolioCompanyModel.InvestmentDate,
                MappingPCGeographicLocation = portfolioCompanyModel.GeographicLocations?.Select(loc => new Mapping_PCGeographicLocation
                {
                    PortfolioCompanyId = portfolioCompanyModel.PortfolioCompanyID,
                    RegionId = loc.Region?.RegionId,
                    CountryId = loc.Country.CountryId,
                    StateId = loc.State == null ? null : loc.State.StateId,
                    CityId = loc.City == null ? null : loc.City.CityId,
                    CreatedBy = portfolioCompanyModel.CreatedBy,
                    CreatedOn = portfolioCompanyModel.CreatedOn,
                    IsActive = true,
                    IsHeadquarter = loc.IsHeadquarter,
                    IsDeleted = false
                }).ToList(),

                MappingPCEmployee = portfolioCompanyModel.PCEmployees?.Select(pe => new Mapping_PCEmployee
                {
                    PortfolioCompanyId = portfolioCompanyModel.PortfolioCompanyID,
                    CreatedBy = portfolioCompanyModel.CreatedBy,
                    CreatedOn = portfolioCompanyModel.CreatedOn,
                    IsActive = true,

                    EmployeeDetail = new EmployeeDetails
                    {
                        EmployeeName = pe.EmployeeName,
                        DesignationId = pe.Designation?.DesignationId,
                        Education = pe.Education,
                        PastExperience = pe.PastExperience,
                        Email = pe.EmailId,
                        ContactNo = pe.ContactNo,
                        IsActive = true,
                        IsDeleted = false,
                        CreatedOn = portfolioCompanyModel.CreatedOn,
                        CreatedBy = portfolioCompanyModel.CreatedBy
                    },
                    PCEmployeeId = pe.PCEmployeeId
                }).ToList(),
                PortfolioCompanyProfitabilityDetails = portfolioCompanyModel.PortfolioCompanyProfitabilityList?.Any() == true ? portfolioCompanyModel.PortfolioCompanyProfitabilityList.Select(portfolioCompanyProfitabilityModel => new PortfolioCompanyProfitabilityDetails
                {
                    PortfolioCompanyId = portfolioCompanyProfitabilityModel.PortfolioCompanyID,
                    Quarter = portfolioCompanyProfitabilityModel.Quarter,
                    Year = portfolioCompanyProfitabilityModel.Year,
                    Ebitda = portfolioCompanyProfitabilityModel.EBITDA,
                    NetDebt = portfolioCompanyProfitabilityModel.NetDebt,
                    Revenue = portfolioCompanyProfitabilityModel.Revenue,
                    EnterpriseValue = portfolioCompanyProfitabilityModel.EnterpriseValue,
                    CreatedOn = portfolioCompanyProfitabilityModel.CreatedOn,
                    CreatedBy = portfolioCompanyProfitabilityModel.CreatedBy,
                    IsDeleted = portfolioCompanyProfitabilityModel.IsDeleted,
                    IsActive = portfolioCompanyProfitabilityModel.IsActive
                }).ToList() : null
            };
            _unitOfWork.PortfolioCompanyDetailRepository.Insert(portfolioCompanyDetail);
            return portfolioCompanyDetail;
        }
        private int InsertIntoPortfolioCompanyDetailDraftRepository(PortfolioCompanyModel portfolioCompanyModel)
        {
            PortfolioCompanyDetailsDraft portfolioCompanyDetail = new()
            {
                PortfolioCompanyID = portfolioCompanyModel.PortfolioCompanyID,
                CompanyName = portfolioCompanyModel.CompanyName?.Trim(),
                Website = portfolioCompanyModel.Website?.Trim(),
                BussinessDescription = portfolioCompanyModel.BusinessDescription?.Trim(),
                SectorID = portfolioCompanyModel.SectorDetail?.SectorID,
                SubSectorID = portfolioCompanyModel.SubSectorDetail?.SubSectorID,
                ReportingCurrencyID = portfolioCompanyModel.ReportingCurrencyDetail.CurrencyID,
                StockExchange_Ticker = portfolioCompanyModel.StockExchange_Ticker,
                HeadquarterID = portfolioCompanyModel.HeadquarterID,
                Status = portfolioCompanyModel.Status,
                CreatedBy = portfolioCompanyModel.CreatedBy,
                CreatedOn = DateTime.UtcNow,
                IsActive = true,
                IsDeleted = false,
                ImagePath = portfolioCompanyModel.ImagePath,
                MasterCompanyName = portfolioCompanyModel.MasterCompanyName.Trim(),
                FinancialYearEnd = "FY end 31 " + portfolioCompanyModel.FinancialYearEnd,
                WorkfloRequestId = (int)portfolioCompanyModel.WorkflowRequestId,
            };
            var mappingPCGeographicLocationDraft = portfolioCompanyModel.GeographicLocations?.Select(loc => new MappingPCGeographicLocationDraft
            {
                PortfolioCompanyID = portfolioCompanyModel.PortfolioCompanyID,
                RegionID = loc.Region?.RegionId,
                CountryID = loc.Country.CountryId,
                StateID = loc.State == null ? (int?)null : loc.State.StateId,
                CityID = loc.City == null ? (int?)null : loc.City.CityId,
                CreatedBy = portfolioCompanyModel.CreatedBy,
                IsActive = true,
                IsHeadquarter = loc.IsHeadquarter,
                IsDeleted = false,
                WorkflowRequestId = (int)portfolioCompanyModel.WorkflowRequestId
            }).ToList();
            var employeeList = portfolioCompanyModel.PCEmployees?.Select(pe => new EmployeeDetailsDraft()
            {
                EmployeeName = pe.EmployeeName,
                DesignationID = pe.Designation?.DesignationId,
                Education = pe.Education,
                PastExperience = pe.PastExperience,
                Email = pe.EmailId,
                ContactNo = pe.ContactNo,
                IsActive = true,
                IsDeleted = false,
                CreatedOn = portfolioCompanyModel.CreatedOn,
                CreatedBy = portfolioCompanyModel.CreatedBy
            }).ToList();
            using var scope = new TransactionScope();
            _unitOfWork.PortfolioCompanyDetailsDraftRepository.Insert(portfolioCompanyDetail);
            portfolioCompanyDetail.EncryptedPortfolioCompanyID = _encryption.Encrypt(portfolioCompanyDetail.PortfolioCompanyID.ToString());
            if (mappingPCGeographicLocationDraft.Count > 0)
            {
                _unitOfWork.MappingPCGeographicLocationDraftRepository.InsertBulk(mappingPCGeographicLocationDraft);
            }

            if (employeeList.Count > 0)
            {
                _unitOfWork.EmployeeDetailsDraftRepository.InsertBulk(employeeList);
            }

            _unitOfWork.Save();
            var mappingPCEmployee = employeeList.Select(pe => new MappingPCEmployeeDraft
            {
                PortfolioCompanyID = portfolioCompanyModel.PortfolioCompanyID,
                CreatedBy = portfolioCompanyModel.CreatedBy,
                IsActive = true,
                IsDeleted = false,
                EncryptedPCEmployeeID = null,
                WorkflowRequestId = (int)portfolioCompanyModel.WorkflowRequestId,
                EmployeeID = pe.EmployeeID
            }).ToList();

            if (mappingPCEmployee.Count > 0)
            {
                _unitOfWork.MappingPCEmployeeDraftRepository.InsertBulk(mappingPCEmployee);
            }
            if (portfolioCompanyModel.WorkflowMappingId.HasValue)
            {
                var workflowRequest = _unitOfWork.WorkflowRequestRepository.GetFirstOrDefault(x => !x.IsDeleted && x.WorkflowRequestId == (int)portfolioCompanyModel.WorkflowRequestId);
                MappingWorkflowRequest workflowRequestObj = _unitOfWork.MappingWorkflowRequestRepository.GetFirstOrDefault(x => !x.IsDeleted && x.WorkflowMappingId == portfolioCompanyModel.WorkflowMappingId);
                workflowRequestObj.StatusId = workflowRequest.StatusId;
                workflowRequestObj.ModifiedOn = DateTime.UtcNow;
                workflowRequestObj.ModifiedBy = portfolioCompanyModel.ModifiedBy;
                workflowRequestObj.IsMarkedForReview = false;
                workflowRequestObj.IsEdited = true;
                _unitOfWork.MappingWorkflowRequestRepository.Update(workflowRequestObj);
            }
            _unitOfWork.Save();
            scope.Complete();
            return 2;
        }
        public int AddPortfolioCompanyList(List<BulkUploadPortFolioCompanyDetails> portfolioCompanyModelList)
        {
            var companyNameList = portfolioCompanyModelList.Select(x => x.CompanyName.Trim().ToLower()).ToList();
            _unitOfWork.AutoDetectChangesEnabled = false;
            using (var scope = new TransactionScope())
            {
                var isPortfolioCompanyExist = _unitOfWork.PortfolioCompanyDetailRepository.ExistsAny(x => !x.IsDeleted && companyNameList.Contains(x.CompanyName.ToLower().Trim()));
                if (!isPortfolioCompanyExist)
                {
                    var dbList = new List<PortfolioCompanyDetails>();
                    foreach (var companyModel in portfolioCompanyModelList)
                    {
                        var portfolioCompanyDetail = InsertIntoPortfolioCompanyDetailRepository(companyModel);
                        dbList.Add(portfolioCompanyDetail);
                    }
                    _unitOfWork.Save();
                    _unitOfWork.Save();
                    scope.Complete();
                    return 1;
                }
                else
                {
                    return -1;
                }
            }

        }


        public int EditPortfolioCompany(PortfolioCompanyModel portfolioCompanyModel)
        {
            if (!IsPortfolioCompanyExist(portfolioCompanyModel) && portfolioCompanyModel.PortfolioCompanyID > 0)
            {
                var IsWorkFlow = _configuration.GetSection(Constants.IsWorkflow).Value;
                if (Convert.ToBoolean(IsWorkFlow))
                    return UpdatePortfolioCompanyDraft(portfolioCompanyModel);
                else
                    return UpdatePortfolioCompany(portfolioCompanyModel);
            }
            else
                return -1;
        }

        private int UpdatePortfolioCompanyDraft(PortfolioCompanyModel portfolioCompanyModel)
        {

            var pcDbModel = _unitOfWork.PortfolioCompanyDetailsDraftRepository.Get(x => x.PortfolioCompanyID == portfolioCompanyModel.PortfolioCompanyID && !x.IsDeleted && x.WorkfloRequestId == portfolioCompanyModel.WorkflowRequestId);
            if (pcDbModel == null) return InsertIntoPortfolioCompanyDetailDraftRepository(portfolioCompanyModel);
            if (pcDbModel?.PortfolioCompanyID > 0)
            {
                pcDbModel.CompanyName = portfolioCompanyModel.CompanyName?.Trim();
                pcDbModel.BussinessDescription = portfolioCompanyModel.BusinessDescription?.Trim();
                pcDbModel.Website = portfolioCompanyModel.Website?.Trim();
                pcDbModel.SectorID = portfolioCompanyModel.SectorDetail?.SectorID;
                pcDbModel.SubSectorID = portfolioCompanyModel.SubSectorDetail?.SubSectorID;
                pcDbModel.ReportingCurrencyID = portfolioCompanyModel.ReportingCurrencyDetail.CurrencyID;
                pcDbModel.StockExchange_Ticker = portfolioCompanyModel.StockExchange_Ticker;
                pcDbModel.Status = portfolioCompanyModel.Status;
                pcDbModel.HeadquarterID = portfolioCompanyModel.HeadquarterID;
                pcDbModel.ModifiedBy = portfolioCompanyModel.ModifiedBy;
                pcDbModel.ModifiedOn = portfolioCompanyModel.ModifiedOn;
                pcDbModel.IsActive = true;
                pcDbModel.IsDeleted = false;
                pcDbModel.ImagePath = portfolioCompanyModel.ImagePath;
                pcDbModel.MasterCompanyName = portfolioCompanyModel.MasterCompanyName?.Trim();
                pcDbModel.WorkfloRequestId = (int)portfolioCompanyModel.WorkflowRequestId;
                pcDbModel.FinancialYearEnd = "FY end 31 " + portfolioCompanyModel.FinancialYearEnd;
                _unitOfWork.PortfolioCompanyDetailsDraftRepository.Update(pcDbModel);
                if (portfolioCompanyModel.WorkflowMappingId.HasValue)
                {
                    var workflowRequest = _unitOfWork.WorkflowRequestRepository.GetFirstOrDefault(x => !x.IsDeleted && x.WorkflowRequestId == (int)portfolioCompanyModel.WorkflowRequestId);
                    MappingWorkflowRequest workflowRequestObj = _unitOfWork.MappingWorkflowRequestRepository.GetFirstOrDefault(x => !x.IsDeleted && x.WorkflowMappingId == portfolioCompanyModel.WorkflowMappingId);
                    workflowRequestObj.StatusId = workflowRequest.StatusId;
                    workflowRequestObj.ModifiedOn = DateTime.UtcNow;
                    workflowRequestObj.ModifiedBy = portfolioCompanyModel.ModifiedBy;
                    workflowRequestObj.IsMarkedForReview = false;
                    workflowRequestObj.IsEdited = true;
                    _unitOfWork.MappingWorkflowRequestRepository.Update(workflowRequestObj);
                }
                _unitOfWork.Save();
            }

            List<MappingPCGeographicLocationDraft> pcLocDbModel = UpdatePCGeographyDraft(portfolioCompanyModel);
            var uniqueLocId = portfolioCompanyModel.GeographicLocations.Select(x => x.LocationID).Distinct();
            var locDetailsPresentInDB = pcLocDbModel.Where(y => !uniqueLocId.Contains(y.PCGeographicLocationID)).ToList();
            if (locDetailsPresentInDB?.Any() == true)
            {
                foreach (var locDetail in locDetailsPresentInDB)
                {
                    locDetail.IsActive = false;
                    locDetail.IsDeleted = true;
                    locDetail.ModifiedBy = portfolioCompanyModel.ModifiedBy;
                    locDetail.ModifiedOn = portfolioCompanyModel.ModifiedOn;
                    locDetail.WorkflowRequestId = (int)portfolioCompanyModel.WorkflowRequestId;
                    _unitOfWork.MappingPCGeographicLocationDraftRepository.Update(locDetail);

                }
                _unitOfWork.Save();
            }

            if (portfolioCompanyModel.PCEmployees != null)
            {
                List<MappingPCEmployeeDraft> pcEmpDbModel = UpdateMappingPCEmployeeDraft(portfolioCompanyModel);
                var uniqueEmpId = portfolioCompanyModel.PCEmployees.Select(x => x.PCEmployeeId).Distinct();
                var empDetailsPresentInDB = pcEmpDbModel.Where(y => !uniqueEmpId.Contains(y.PCEmployeeID)).ToList();
                if (empDetailsPresentInDB?.Any() == true)
                {
                    foreach (var empDetail in empDetailsPresentInDB)
                    {
                        empDetail.IsActive = false;
                        empDetail.IsDeleted = true;
                        empDetail.ModifiedBy = portfolioCompanyModel.ModifiedBy;
                        empDetail.ModifiedOn = portfolioCompanyModel.ModifiedOn;
                        empDetail.WorkflowRequestId = (int)portfolioCompanyModel.WorkflowRequestId;
                        _unitOfWork.MappingPCEmployeeDraftRepository.Update(empDetail);

                    }

                }
                _unitOfWork.Save();
            }


            return 2;

        }
        private int UpdatePortfolioCompany(PortfolioCompanyModel portfolioCompanyModel)
        {

            var pcDbModel = _unitOfWork.PortfolioCompanyDetailRepository.Get(x => x.PortfolioCompanyId == portfolioCompanyModel.PortfolioCompanyID && !x.IsDeleted);
            if (pcDbModel?.PortfolioCompanyId > 0)
            {

                pcDbModel.CompanyName = portfolioCompanyModel.CompanyName?.Trim();
                pcDbModel.BussinessDescription = portfolioCompanyModel.BusinessDescription?.Trim();
                pcDbModel.Website = portfolioCompanyModel.Website?.Trim();
                pcDbModel.SectorId = portfolioCompanyModel.SectorDetail?.SectorID;
                pcDbModel.SubSectorId = portfolioCompanyModel.SubSectorDetail?.SubSectorID;
                pcDbModel.ReportingCurrencyId = portfolioCompanyModel.ReportingCurrencyDetail.CurrencyID;
                pcDbModel.StockExchangeTicker = portfolioCompanyModel.StockExchange_Ticker;
                pcDbModel.Status = portfolioCompanyModel.Status;
                pcDbModel.HeadquarterId = portfolioCompanyModel.HeadquarterID;
                pcDbModel.ModifiedBy = portfolioCompanyModel.ModifiedBy;
                pcDbModel.ModifiedOn = portfolioCompanyModel.ModifiedOn;
                pcDbModel.MasterCompanyName = portfolioCompanyModel.MasterCompanyName?.Trim();
                pcDbModel.IsActive = true;
                pcDbModel.IsDeleted = false;
                pcDbModel.ImagePath = portfolioCompanyModel.ImagePath;
                pcDbModel.GroupId = portfolioCompanyModel.CompanyGroupId;
                pcDbModel.FinancialYearEnd = "FY end " + Common.GetMonthDays(portfolioCompanyModel.FinancialYearEnd) + " " + portfolioCompanyModel.FinancialYearEnd;
                pcDbModel.CompanyLegalName = portfolioCompanyModel.CompanyLegalName?.Trim();
                pcDbModel.PCInvestmentDate = portfolioCompanyModel.InvestmentDate;
                _unitOfWork.PortfolioCompanyDetailRepository.Update(pcDbModel);
                _unitOfWork.Save();
            }
            else
            {
                return -1;
            }


            List<Mapping_PCGeographicLocation> pcLocDbModel = UpdatePCGeography(portfolioCompanyModel);

            var uniqueLocId = portfolioCompanyModel.GeographicLocations.Select(x => x.LocationID).Distinct();
            var locDetailsPresentInDB = pcLocDbModel.Where(y => !uniqueLocId.Contains(y.PCGeographicLocationId)).ToList();
            if (locDetailsPresentInDB?.Any() == true)
            {
                foreach (var locDetail in locDetailsPresentInDB)
                {
                    locDetail.IsActive = false;
                    locDetail.IsDeleted = true;
                    locDetail.ModifiedBy = portfolioCompanyModel.ModifiedBy;
                    locDetail.ModifiedOn = portfolioCompanyModel.ModifiedOn;
                    _unitOfWork.PCGeographyRepository.Update(locDetail);

                }
                _unitOfWork.Save();
            }


            if (portfolioCompanyModel.PCEmployees != null)
            {
                List<Mapping_PCEmployee> pcEmpDbModel = UpdateMappingPCEmployee(portfolioCompanyModel);
                var uniqueEmpId = portfolioCompanyModel.PCEmployees.Select(x => x.PCEmployeeId).Distinct();
                var empDetailsPresentInDB = pcEmpDbModel.Where(y => !uniqueEmpId.Contains(y.PCEmployeeId)).ToList();
                if (empDetailsPresentInDB?.Any() == true)
                {
                    foreach (var empDetail in empDetailsPresentInDB)
                    {
                        empDetail.IsActive = false;
                        empDetail.IsDeleted = true;
                        empDetail.ModifiedBy = portfolioCompanyModel.ModifiedBy;
                        empDetail.ModifiedOn = portfolioCompanyModel.ModifiedOn;
                        _unitOfWork.PCEmployeeDetailRepository.Update(empDetail);

                    }

                }
                _unitOfWork.Save();
            }

            return 2;

        }

        private List<Mapping_PCEmployee> UpdateMappingPCEmployee(PortfolioCompanyModel portfolioCompanyModel)
        {
            var pcEmpDbModel = _unitOfWork.PCEmployeeDetailRepository.GetMany(x => x.PortfolioCompanyId == portfolioCompanyModel.PortfolioCompanyID && !x.IsDeleted).ToList();
            foreach (var pcEmp in portfolioCompanyModel.PCEmployees)
            {

                if (pcEmp.PCEmployeeId == 0)
                {

                    Mapping_PCEmployee newEmpDetails = new Mapping_PCEmployee
                    {
                        PortfolioCompanyId = portfolioCompanyModel.PortfolioCompanyID,
                        EmployeeId = pcEmp.EmployeeId,

                        CreatedBy = (int)portfolioCompanyModel.ModifiedBy,
                        CreatedOn = (DateTime)portfolioCompanyModel.ModifiedOn,
                        IsActive = true,
                        IsDeleted = false,
                        EmployeeDetail = new EmployeeDetails
                        {
                            EmployeeName = pcEmp.EmployeeName,
                            DesignationId = pcEmp.Designation?.DesignationId,
                            Email = pcEmp.EmailId,
                            PastExperience = pcEmp.PastExperience,
                            Education = pcEmp.Education,
                            ContactNo = pcEmp.ContactNo,

                            IsActive = true,
                            IsDeleted = false,
                            CreatedBy = (int)portfolioCompanyModel.ModifiedBy,
                            CreatedOn = (DateTime)portfolioCompanyModel.ModifiedOn,
                        }
                    };
                    _unitOfWork.PCEmployeeDetailRepository.Insert(newEmpDetails);

                }
                else
                {
                    var dbDetails = pcEmpDbModel.FirstOrDefault(x => x.PCEmployeeId == pcEmp.PCEmployeeId);
                    if (dbDetails != null)
                    {
                        dbDetails.ModifiedBy = portfolioCompanyModel.ModifiedBy;
                        dbDetails.ModifiedOn = (DateTime)portfolioCompanyModel.ModifiedOn;
                        dbDetails.EmployeeDetail.EmployeeName = pcEmp.EmployeeName;
                        dbDetails.EmployeeDetail.DesignationId = pcEmp.Designation?.DesignationId;
                        dbDetails.EmployeeDetail.Email = pcEmp.EmailId;
                        dbDetails.EmployeeDetail.ContactNo = pcEmp.ContactNo;
                        dbDetails.EmployeeDetail.PastExperience = pcEmp.PastExperience;
                        dbDetails.EmployeeDetail.Education = pcEmp.Education;
                        dbDetails.EmployeeDetail.ModifiedBy = portfolioCompanyModel.ModifiedBy;
                        dbDetails.EmployeeDetail.ModifiedOn = (DateTime)portfolioCompanyModel.ModifiedOn;
                    }
                }
            }

            return pcEmpDbModel;
        }
        private List<MappingPCEmployeeDraft> UpdateMappingPCEmployeeDraft(PortfolioCompanyModel portfolioCompanyModel)
        {
            var pcEmpDbModel = _unitOfWork.MappingPCEmployeeDraftRepository.GetMany(x => x.PortfolioCompanyID == portfolioCompanyModel.PortfolioCompanyID && !(bool)x.IsDeleted).ToList();
            foreach (var pcEmp in portfolioCompanyModel.PCEmployees)
            {

                if (pcEmp.PCEmployeeId == 0)
                {

                    MappingPCEmployeeDraft employeeDraft = new()
                    {
                        PortfolioCompanyID = portfolioCompanyModel.PortfolioCompanyID,
                        EmployeeID = pcEmp.EmployeeId,
                        CreatedBy = (int)portfolioCompanyModel.ModifiedBy,
                        CreatedOn = (DateTime)portfolioCompanyModel.ModifiedOn,
                        IsActive = true,
                        IsDeleted = false,
                        WorkflowRequestId = (int)portfolioCompanyModel.WorkflowRequestId,
                        EmployeeDetailDraft = new EmployeeDetailsDraft
                        {
                            EmployeeName = pcEmp.EmployeeName,
                            DesignationID = pcEmp.Designation?.DesignationId,
                            Email = pcEmp.EmailId,
                            PastExperience = pcEmp.PastExperience,
                            Education = pcEmp.Education,
                            ContactNo = pcEmp.ContactNo,
                            IsActive = true,
                            IsDeleted = false,
                            CreatedBy = (int)portfolioCompanyModel.ModifiedBy,
                            CreatedOn = (DateTime)portfolioCompanyModel.ModifiedOn,
                        }
                    };
                    _unitOfWork.MappingPCEmployeeDraftRepository.Insert(employeeDraft);

                }
                else
                {
                    var dbDetails = pcEmpDbModel.FirstOrDefault(x => x.PCEmployeeID == pcEmp.PCEmployeeId);
                    if (dbDetails != null)
                    {
                        dbDetails.ModifiedBy = portfolioCompanyModel.ModifiedBy;
                        dbDetails.ModifiedOn = (DateTime)portfolioCompanyModel.ModifiedOn;
                        dbDetails.EmployeeDetailDraft.EmployeeName = pcEmp.EmployeeName;
                        dbDetails.EmployeeDetailDraft.DesignationID = pcEmp.Designation?.DesignationId;
                        dbDetails.EmployeeDetailDraft.Email = pcEmp.EmailId;
                        dbDetails.EmployeeDetailDraft.ContactNo = pcEmp.ContactNo;
                        dbDetails.EmployeeDetailDraft.PastExperience = pcEmp.PastExperience;
                        dbDetails.EmployeeDetailDraft.Education = pcEmp.Education;
                        dbDetails.EmployeeDetailDraft.ModifiedBy = portfolioCompanyModel.ModifiedBy;
                        dbDetails.EmployeeDetailDraft.ModifiedOn = (DateTime)portfolioCompanyModel.ModifiedOn;
                    }
                }
            }

            return pcEmpDbModel;
        }
        private List<Mapping_PCGeographicLocation> UpdatePCGeography(PortfolioCompanyModel portfolioCompanyModel)
        {
            var pcLocDbModel = _unitOfWork.PCGeographyRepository.GetMany(x => x.PortfolioCompanyId == portfolioCompanyModel.PortfolioCompanyID && x.IsDeleted == false).ToList();
            foreach (var loc in portfolioCompanyModel.GeographicLocations)
            {
                if (loc.LocationID == 0)
                {

                    Mapping_PCGeographicLocation newLocDetails = new Mapping_PCGeographicLocation
                    {
                        PortfolioCompanyId = portfolioCompanyModel.PortfolioCompanyID,
                        RegionId = loc.Region?.RegionId,
                        CountryId = loc.Country.CountryId,
                        StateId = loc.State == null ? (int?)null : loc.State.StateId,
                        CityId = loc.City == null ? (int?)null : loc.City.CityId,
                        CreatedBy = (int)portfolioCompanyModel.ModifiedBy,
                        CreatedOn = (DateTime)portfolioCompanyModel.ModifiedOn,
                        IsActive = true,
                        IsHeadquarter = loc.IsHeadquarter,
                        IsDeleted = false
                    };
                    _unitOfWork.PCGeographyRepository.Insert(newLocDetails);
                    _unitOfWork.Save();

                }
            }

            return pcLocDbModel;
        }
        private List<MappingPCGeographicLocationDraft> UpdatePCGeographyDraft(PortfolioCompanyModel portfolioCompanyModel)
        {
            var pcLocDbModel = _unitOfWork.MappingPCGeographicLocationDraftRepository.GetMany(x => x.PortfolioCompanyID == portfolioCompanyModel.PortfolioCompanyID && x.IsDeleted == false && x.WorkflowRequestId == portfolioCompanyModel.WorkflowRequestId).ToList();
            foreach (var loc in portfolioCompanyModel.GeographicLocations)
            {
                if (loc.LocationID == 0)
                {

                    MappingPCGeographicLocationDraft newLocDetails = new()
                    {
                        PortfolioCompanyID = portfolioCompanyModel.PortfolioCompanyID,
                        RegionID = loc.Region?.RegionId,
                        CountryID = loc.Country.CountryId,
                        StateID = loc.State == null ? (int?)null : loc.State.StateId,
                        CityID = loc.City == null ? (int?)null : loc.City.CityId,
                        CreatedBy = (int)portfolioCompanyModel.ModifiedBy,
                        CreatedOn = (DateTime)portfolioCompanyModel.ModifiedOn,
                        IsActive = true,
                        IsHeadquarter = loc.IsHeadquarter,
                        IsDeleted = false,
                        WorkflowRequestId = (int)portfolioCompanyModel.WorkflowRequestId
                    };
                    _unitOfWork.MappingPCGeographicLocationDraftRepository.Insert(newLocDetails);
                    _unitOfWork.Save();

                }
            }

            return pcLocDbModel;
        }
        private bool IsPortfolioCompanyExist(PortfolioCompanyModel portfolioCompanyModel)
        {
            PortfolioCompanyDetails portfolioCompany;
            if (portfolioCompanyModel.PortfolioCompanyID > 0)
            {
                portfolioCompany = _unitOfWork.PortfolioCompanyDetailRepository.Get(x => x.CompanyName.Trim().ToLower() == portfolioCompanyModel.CompanyName.Trim().ToLower() && x.PortfolioCompanyId != portfolioCompanyModel.PortfolioCompanyID && !x.IsDeleted);
            }
            else
            {
                portfolioCompany = _unitOfWork.PortfolioCompanyDetailRepository.Get(x => x.CompanyName.Trim().ToLower() == portfolioCompanyModel.CompanyName.Trim().ToLower() && !x.IsDeleted);
            }

            return portfolioCompany != null;
        }
        public async Task<PortfolioCompanyQueryListModel> GetPortfolioCompanyListQuery(PortfolioCompanyFilter filter, bool isAccessFilterEnable = false)
        {
            PortfolioCompanyQueryListModel portfolioCompanyQueryList = new();
            var query = await _dapperGenericRepository.Query<PortfolioCompanyQueryModel>(isAccessFilterEnable ? SqlConstants.QueryPCMappingByUserID : SqlConstants.QueryByPCMapping, new { userID = filter.CreatedBy });
            var sectors = await _dapperGenericRepository.Query<SectorQueryModel>(SqlConstants.QueryBySector);
            var subSectors = await _dapperGenericRepository.Query<SubSectorModel>(SqlConstants.QueryBySubSector);
            var currencies = await _dapperGenericRepository.Query<DealCurrencyQuery>(SqlConstants.QueryByCurrency);
            var companyGroup = await _dapperGenericRepository.Query<MGroupingList>(SqlConstants.QueryByGetGroupingList);
            var data = query.AsQueryable();
            portfolioCompanyQueryList.PortfolioCompanyList = data.Select(portfolioCompanyDetails => new PortfolioCompanyQueryModel
            {
                PortfolioCompanyID = portfolioCompanyDetails.PortfolioCompanyID,
                CompanyName = portfolioCompanyDetails.CompanyName,
                Website = portfolioCompanyDetails.Website,
                SectorID = portfolioCompanyDetails.SectorID,
                EncryptedPortfolioCompanyId = portfolioCompanyDetails.EncryptedPortfolioCompanyId,
                DealCustomID = portfolioCompanyDetails.DealCustomID,
                EncryptedDealID = portfolioCompanyDetails.EncryptedDealID,
                EncryptedFundID = portfolioCompanyDetails.EncryptedFundID,
                FundName = portfolioCompanyDetails.FundName,
                Status = portfolioCompanyDetails.Status,
                FinancialYearEnd = portfolioCompanyDetails.FinancialYearEnd,
                MasterCompanyName = portfolioCompanyDetails.MasterCompanyName,
                GroupName = GetCompanyGroup(companyGroup, portfolioCompanyDetails.GroupId),
                StockExchange_Ticker = portfolioCompanyDetails.StockExchange_Ticker,
                CompanyLegalName = portfolioCompanyDetails.CompanyLegalName,
                InvestmentDate = portfolioCompanyDetails.InvestmentDate,
                SectorDetail = portfolioCompanyDetails.SectorID == null ? null : new SectorModel
                {
                    Sector = sectors.FirstOrDefault(x => x.SectorID == (int)portfolioCompanyDetails.SectorID).Sector,
                    SectorID = (int)portfolioCompanyDetails.SectorID
                },
                SubSectorDetail = portfolioCompanyDetails.SubSectorDetail == null ? null : new SubSectorModel
                {
                    SubSector = subSectors.FirstOrDefault(x => x.SubSectorID == (int)portfolioCompanyDetails.SubSectorID && x.SectorID == portfolioCompanyDetails.SectorID).SubSector,
                    SubSectorID = (int)portfolioCompanyDetails.SubSectorID
                },
                ReportingCurrencyDetail = portfolioCompanyDetails.ReportingCurrencyID == 0 ? null : new CurrencyModel
                {
                    Currency = currencies.FirstOrDefault(x => x.CurrencyID == portfolioCompanyDetails.ReportingCurrencyID).Currency,
                    CurrencyID = portfolioCompanyDetails.ReportingCurrencyID,
                    CurrencyCode = currencies.FirstOrDefault(x => x.CurrencyID == portfolioCompanyDetails.ReportingCurrencyID).CurrencyCode,
                }
            }).ToList();
            return portfolioCompanyQueryList;

        }
        private static string GetCompanyGroup(List<MGroupingList> groupingList, int? groupId)
        {
            if (groupingList?.Count > 0 && groupId != null)
            {
                return groupingList.FirstOrDefault(x => x.GroupId == groupId)?.GroupName;
            }
            return null;
        }

        public PortfolioCompanyListModel GetPortfolioCompanyList(PortfolioCompanyFilter filter)
        {
            var result = new PortfolioCompanyListModel();
            try
            {
                var data = _unitOfWork.PortfolioCompanyDetailRepository.GetManyQueryable(x => !x.IsDeleted).OrderByDescending(x => x.PortfolioCompanyId).AsQueryable();
                if (data.Any(x => x.EncryptedPortfolioCompanyId == null))
                {
                    _unitOfWork.Save();
                }
                if (filter?.PaginationFilter != null)
                {
                    int totalRows = 0;
                    if (!string.IsNullOrEmpty(filter.PaginationFilter.GlobalFilter))
                    {
                        var lowerGlobalFilter = filter.PaginationFilter.GlobalFilter.ToLower();
                        data = data.Where(u => u.CompanyName.ToLower().Contains(lowerGlobalFilter) ||
                           (u.CompanyName != null && u.CompanyName.ToLower().Contains(lowerGlobalFilter)) ||
                           (u.MappingPCGeographicLocation.Any(x => x.IsHeadquarter && (
                              (x.Country != null && x.Country.Country.ToLower().Contains(lowerGlobalFilter)) ||
                              (x.City != null && x.City.City.ToLower().Contains(lowerGlobalFilter)) ||
                              (x.State != null && x.State.State.ToLower().Contains(lowerGlobalFilter)) ||
                              (x.Region != null && x.Region.Region.ToLower().Contains(lowerGlobalFilter))))) ||
                           (u.Sector != null && u.Sector.Sector.ToLower().Contains(lowerGlobalFilter)) ||
                           (u.Website != null && u.Website.ToLower().Contains(lowerGlobalFilter))
                        );
                    }

                    if (filter.PaginationFilter.MultiSortMeta.Any() && filter.PaginationFilter.MultiSortMeta.Exists(x => x.Field.ToLower() == ("SubSector.SubSector").ToLower()))
                    {

                        int orderType = filter.PaginationFilter.MultiSortMeta[0].Order;
                        if (orderType == 1)
                        {
                            data = data.OrderBy(x => x.SubSector != null && x.SubSector.SubSector != null)
                                .ThenBy(x => x.SubSector != null ? x.SubSector.SubSector : null);
                        }
                        else
                        {
                            data = data.OrderByDescending(x => x.SubSector != null && x.SubSector.SubSector != null)
                                .ThenByDescending(x => x.SubSector != null ? x.SubSector.SubSector : null);
                        }
                        filter.PaginationFilter.MultiSortMeta = null;
                        totalRows = data.Count();
                    }

                    if (!filter.PaginationFilter.FilterWithoutPaging)
                        data = data.PagedResult(filter.PaginationFilter.First, filter.PaginationFilter.Rows, filter.PaginationFilter.MultiSortMeta, out totalRows);
                    else
                        data = data.SortedResult(filter.PaginationFilter.MultiSortMeta);

                    result.TotalRecords = totalRows;
                }
                result.PortfolioCompanyList = data.ToList().ConvertAll(portfolioCompanyDetails => new PortfolioCompanyModel
                {
                    PortfolioCompanyID = portfolioCompanyDetails.PortfolioCompanyId,
                    CompanyName = portfolioCompanyDetails.CompanyName,
                    Website = portfolioCompanyDetails.Website,
                    BusinessDescription = portfolioCompanyDetails.BussinessDescription,
                    SectorID = portfolioCompanyDetails?.SectorId,
                    Status = portfolioCompanyDetails.Status,
                    StockExchange_Ticker = portfolioCompanyDetails.StockExchangeTicker,
                    HeadquarterID = portfolioCompanyDetails?.HeadquarterId,
                    CreatedBy = portfolioCompanyDetails.CreatedBy,
                    CreatedOn = portfolioCompanyDetails.CreatedOn,
                    ModifiedBy = portfolioCompanyDetails.ModifiedBy,
                    ModifiedOn = portfolioCompanyDetails.ModifiedOn,
                    IsActive = portfolioCompanyDetails.IsActive ?? false,
                    MasterCompanyName = portfolioCompanyDetails.MasterCompanyName,
                    EncryptedPortfolioCompanyId = portfolioCompanyDetails.EncryptedPortfolioCompanyId,
                    GeographicLocations = portfolioCompanyDetails.MappingPCGeographicLocation?.Where(x => x.IsHeadquarter && x.IsDeleted == false).Select(x => new LocationModel
                    {
                        LocationID = x.PCGeographicLocationId,
                        Region = x.Region == null ? null : new RegionModel
                        {
                            Region = x.Region.Region,
                            RegionId = x.Region.RegionId
                        },
                        Country = x.Country == null ? null : new CountryModel
                        {
                            Country = x.Country.Country,
                            CountryId = x.Country.CountryId
                        },
                        State = x.State == null ? null : new StateModel
                        {
                            State = x.State.State,
                            StateId = x.State.StateId
                        },
                        City = x.City == null ? null : new CityModel
                        {
                            City = x.City.City,
                            CityId = x.City.CityId
                        },
                        IsHeadquarter = x.IsHeadquarter
                    }).ToList(),

                    SectorDetail = portfolioCompanyDetails.Sector == null ? null : new SectorModel
                    {
                        Sector = portfolioCompanyDetails.Sector?.Sector,
                        SectorID = portfolioCompanyDetails.Sector.SectorId
                    },
                    SubSectorDetail = portfolioCompanyDetails.SubSector == null ? null : new SubSectorModel
                    {
                        SubSector = portfolioCompanyDetails.SubSector?.SubSector,
                        SubSectorID = portfolioCompanyDetails.SubSector.SubSectorId
                    },
                    ReportingCurrencyDetail = portfolioCompanyDetails.ReportingCurrency == null ? null : new CurrencyModel
                    {
                        CurrencyID = portfolioCompanyDetails.ReportingCurrency.CurrencyId,
                        CurrencyCode = portfolioCompanyDetails.ReportingCurrency?.CurrencyCode,
                        Currency = portfolioCompanyDetails.ReportingCurrency?.Currency
                    }
                });

            }
            catch (Exception exception)
            {
                _logger.LogError(exception.Message, exception.StackTrace);
            }
            return result;

        }
        public async Task<string> GetMasterCompaniesCount()
        {
            return await _dapperGenericRepository.QueryFirstAsync<string>(SqlConstants.QueryByMasterCompaniesCount, null);
        }
        public async Task<List<CompanyListModel>> GetCompanyList()
        {
            return await _dapperGenericRepository.Query<CompanyListModel>(SqlConstants.GetPCQuery);
        }

        public async Task<CompanyModelList> GetCompanyNameAndId(PortfolioCompanyFilter filter)
        {
            var result = new CompanyModelList();
            var query = await _dapperGenericRepository.Query<CompanyModel>(SqlConstants.QueryByCompanyList);
            var data = query?.Select(item => new CompanyModel
            {
                PortfolioCompanyID = item.PortfolioCompanyID,
                CompanyName = item.CompanyName,
                ReportingCurrencyID = item.ReportingCurrencyID,
            }).AsQueryable();
            result.PortfolioCompanyList = data?.ToList();
            return result;
        }
        public PortfolioCompanyListModel GetCompanyNames(PortfolioCompanyFilter filter)
        {
            var result = new PortfolioCompanyListModel();
            var data = _unitOfWork.PortfolioCompanyDetailRepository.GetManyQueryable(x => !x.IsDeleted).OrderByDescending(x => x.PortfolioCompanyId).AsQueryable();

            if (data.Any(x => x.EncryptedPortfolioCompanyId == null))
            {
                _unitOfWork.Save();
            }
            if (filter != null && !string.IsNullOrEmpty(filter.EncryptedPortfolioCompanyId))
            {
                var companyId = Convert.ToInt32(_encryption.Decrypt(filter.EncryptedPortfolioCompanyId));
                data = data.Where(x => x.PortfolioCompanyId == companyId);
            }

            var companyList = data.ToList();
            result.PortfolioCompanyList = companyList.ConvertAll(portfolioCompanyDetails => new PortfolioCompanyModel
            {
                PortfolioCompanyID = portfolioCompanyDetails.PortfolioCompanyId,
                CompanyName = portfolioCompanyDetails.CompanyName,
                EncryptedPortfolioCompanyId = portfolioCompanyDetails.EncryptedPortfolioCompanyId,
                SectorDetail = portfolioCompanyDetails.SectorId > 0 ? new SectorModel { SectorID = portfolioCompanyDetails.SectorId.Value } : null,
                IsActive = Convert.ToBoolean(portfolioCompanyDetails.IsActive),
                IsDeleted = portfolioCompanyDetails.IsDeleted,
                ReportingCurrencyDetail = portfolioCompanyDetails.ReportingCurrencyId > 0 ? new CurrencyModel
                {
                    Currency = portfolioCompanyDetails.ReportingCurrency.Currency,
                    CurrencyCode = portfolioCompanyDetails.ReportingCurrency.CurrencyCode,
                } : null
            });
            return result;

        }

        public List<PortfolioCompanyEmployeeModel> GetPortfolioCompanyEmployeeList()
        {

            var portfolioEmployeeList = _unitOfWork.PCEmployeeDetailRepository.GetMany(x => !x.IsDeleted && !x.EmployeeDetail.IsDeleted)
                .ToList();
            if (portfolioEmployeeList != null)
            {
                return portfolioEmployeeList.ConvertAll(portfolioCompanyDetails =>
                {
                    return new PortfolioCompanyEmployeeModel
                    {
                        PortfolioCompanyID = portfolioCompanyDetails.PortfolioCompanyId ?? 0,
                        CreatedBy = portfolioCompanyDetails.CreatedBy,
                        CreatedOn = portfolioCompanyDetails.CreatedOn,
                        ModifiedBy = portfolioCompanyDetails.ModifiedBy,
                        ModifiedOn = portfolioCompanyDetails.ModifiedOn,
                        IsActive = portfolioCompanyDetails.IsActive,
                        ContactNo = portfolioCompanyDetails.EmployeeDetail.ContactNo,

                        Designation = portfolioCompanyDetails.EmployeeDetail?.Designation == null ? null : new DesignationModel
                        {
                            Designation = portfolioCompanyDetails.EmployeeDetail?.Designation.Designation,
                            DesignationId = (portfolioCompanyDetails.EmployeeDetail?.Designation.DesignationId) ?? 0
                        },
                        Education = portfolioCompanyDetails.EmployeeDetail?.Education,
                        EmailId = portfolioCompanyDetails.EmployeeDetail?.Email,
                        EmployeeId = portfolioCompanyDetails.EmployeeId ?? 0,
                        EmployeeName = portfolioCompanyDetails.EmployeeDetail?.EmployeeName,
                        PastExperience = portfolioCompanyDetails.EmployeeDetail?.PastExperience,
                        PCEmployeeId = portfolioCompanyDetails.PCEmployeeId

                    };
                });
            }
            else
                return new List<PortfolioCompanyEmployeeModel>();

        }
        public async Task<LPReportConfigModels> GetLPReportConfiguration(string fundId)
        {
            var portfolioCompanies = await GetPortfolioCompaniesByFund(Convert.ToInt32(fundId));
            var lPReportConfigurationModels = portfolioCompanies.Select(x => new LPReportConfigurationModel()
            {
                PortfolioCompanyID = x.PortfolioCompanyID,
                CompanyName = x.CompanyName
            }).OrderBy(x => x.CompanyName).ToList();

            LPReportConfigModels lPReportConfigModels = new();
            var lpReportMaster = _unitOfWork.M_LPReportConfigurationRepository.GetMany(x => !x.IsDeleted)
                                 .Select(x => new LPReportConfigurationModel()
                                 {
                                     Feature = x.Feature,
                                     LPReportFeatureId = x.Id
                                 }).ToList();
            DataTable table = new DataTable();
            table.Columns.Add(new DataColumn("CompanyName", typeof(string)));
            foreach (var item in lpReportMaster)
            {
                table.Columns.Add(new DataColumn(item.Feature, typeof(bool)));
            }
            table.Columns.Add(new DataColumn("CompanyId", typeof(int)));
            table.Columns.Add(new DataColumn("Status", typeof(string)));
            List<string> columnsList = table.Columns.Cast<DataColumn>().Where(x => x.ColumnName.ToLower() != "company" && x.ColumnName.ToLower() != "status" && x.ColumnName.ToLower() != "mappingid" && x.ColumnName.ToLower() != "companyid").Select(x => x.ColumnName).ToList();
            if (lPReportConfigurationModels.Count > 0)
            {
                var dealDetails = await _dapperGenericRepository.Query<DealQueryModel>(SqlConstants.DealFundPCListQueryWithFundId, new { fundID = fundId });
                var fundholdingStatus = _unitOfWork.M_FundHoldingStatusRepository.GetQueryable();
                var mappingData = _unitOfWork.Mapping_LPReportConfigurationRepository.GetQueryable().ToList();
                foreach (var item in lPReportConfigurationModels)
                {
                    DataRow dr = table.NewRow();
                    foreach (var config in lpReportMaster)
                    {
                        var result = (from deal in dealDetails
                                      join fund in _unitOfWork.PortfolioCompanyFundHoldingDetailRepository.GetQueryable() on deal.DealID equals fund.DealId into kc
                                      from ckpi in kc.DefaultIfEmpty()
                                      join status in fundholdingStatus on ckpi?.FundHoldingStatusId equals status?.FundHoldingStatusId into kp
                                      from pkpi in kp.DefaultIfEmpty()
                                      where deal.PortfolioCompanyID == item.PortfolioCompanyID && deal.FundID == Convert.ToInt32(fundId)
                                      select new
                                      {
                                          FundId = deal?.FundID,
                                          CompanyId = deal?.PortfolioCompanyID,
                                          Status = pkpi?.Status,
                                          Year = ckpi?.Year,
                                          Quarter = ckpi?.Quarter
                                      }
                                    ).OrderByDescending(x => x.Year).ThenByDescending(x => x.Quarter).FirstOrDefault();
                        var mappingConfigdata = mappingData?.FirstOrDefault(x => x.FundId == Convert.ToInt32(fundId) && x.PortfolioCompanyID == item.PortfolioCompanyID && x.LPReportFeatureId == config.LPReportFeatureId && !x.IsDeleted);
                        dr[config.Feature] = mappingConfigdata != null && mappingConfigdata.IsEnabled;
                        dr["CompanyId"] = item?.PortfolioCompanyID;
                        dr["Status"] = result != null ? result.Status : null;
                        dr["CompanyName"] = item?.CompanyName;
                    }
                    table.Rows.Add(dr);
                }
                lPReportConfigModels.LPReportConfigList = table.AsDynamicEnumerable().ToList();
            }
            lPReportConfigModels.LPReportConfigHeaders = columnsList;
            return lPReportConfigModels;
        }
        public async Task<IngestionCompanyModel> GetPortfolioCompanyDetailsById(int PortfolioCompanyId)
        {
            CompanyListModel portfolioCompany = await _dapperGenericRepository.QueryFirstAsync<CompanyListModel>(SqlConstants.QueryByGetPCById, new { @Id = PortfolioCompanyId });
            return new IngestionCompanyModel()
            {
                CompanyListModel = portfolioCompany
            };
        }
        public async Task<PortfolioCompanyModel> GetPortfolioCompanyById(int PortfolioCompanyId, MSubSectionFields mSubSection = null)
        {
            var CommentaryData = GetLatestCommentary(PortfolioCompanyId, mSubSection);
            var portfolioCompany = await _dapperGenericRepository.QueryFirstAsync<PortfolioCompanyDetails>(SqlConstants.QueryByGetPCById, new { @Id = PortfolioCompanyId });
            if (portfolioCompany?.PortfolioCompanyId > 0)
            {
                PortfolioCompanyModel portfolioCompanyDetail = await GeneratePorfolioCompanyModel(portfolioCompany, CommentaryData);
                return portfolioCompanyDetail;
            }
            return null;
        }
        public async Task<DealDataModel> GetDealId(int PortfolioCompanyId)
        {
            return await _dapperGenericRepository.QueryFirstAsync<DealDataModel>(SqlConstants.ProcGetDealByCompanyId, new { @companyId = PortfolioCompanyId });
        }
        public async Task<PortfolioCompanyFundHoldingModel> GetFundHoldingValues(int dealId)
        {
            PortfolioCompanyFundHoldingModel holdingModel = new();
            var latestQuarter =  await _dapperGenericRepository.QuerySingleOrDefaultAsync<SelectionList>(SqlConstants.QueryByDealTrackRecordLatestQuarterYearByDealId, new {@dealId  = dealId});
            if(latestQuarter != null)
            {
                _=int.TryParse(latestQuarter.Label, out int year);

                var  result = await _dealService.GetPortfolioCompanyFundHolding(new PortfolioCompanyFundHoldingFilter()
                {
                    DealID = dealId,
                    Quarter = latestQuarter.Value,
                    Year = year
                });
                var trackRecords = await _dealService.GetDealHoldingDynamicRecord(dealId);
                if(result.PortfolioCompanyFundHoldingList.Count > 0)
                {
                    holdingModel = result.PortfolioCompanyFundHoldingList[0];
                    holdingModel.DealTrackRecordConfigurationData = trackRecords;
                    return holdingModel;
                }

            }
            return null;
        }
        public async Task<PortfolioCompanyModel> GetPortfolioCompanyDraftById(int PortfolioCompanyId)
        {
            var portfolioCompany = await _dapperGenericRepository.QueryFirstAsync<PortfolioCompanyDetails>(SqlConstants.QueryByGetPCById, new { @Id = PortfolioCompanyId });
            if (portfolioCompany?.PortfolioCompanyId > 0)
            {
                PortfolioCompanyModel portfolioCompanyDetail = await GeneratePorfolioCompanyDraftModel(portfolioCompany);
                return portfolioCompanyDetail;
            }
            return null;
        }

        public async Task<PortfolioCompanyModel> GetPortfolioCompanyDraftById(int PortfolioCompanyId, int workflowRequestId)
        {
            PortfolioCompanyModel portfolioCompanyDetail;
            var IsWorkFlow = _configuration.GetSection(Constants.IsWorkflow).Value;
            if (!Convert.ToBoolean(IsWorkFlow))
                portfolioCompanyDetail = await GetPortfolioCompanyById(PortfolioCompanyId);
            else
            {
                var portfolioCompany = _unitOfWork.PortfolioCompanyDetailsDraftRepository.Get(x => x.PortfolioCompanyID == PortfolioCompanyId && !x.IsDeleted && x.WorkfloRequestId == workflowRequestId);
                if (portfolioCompany == null)
                    portfolioCompanyDetail = await GetPortfolioCompanyById(PortfolioCompanyId);
                else
                {
                    portfolioCompanyDetail = await GeneratePorfolioCompanyModelDraft(portfolioCompany);
                    portfolioCompanyDetail.IsWorkflow = Convert.ToBoolean(IsWorkFlow);
                    return portfolioCompanyDetail;
                }
            }
            portfolioCompanyDetail.IsWorkflow = Convert.ToBoolean(IsWorkFlow);
            return portfolioCompanyDetail;
        }

        public async Task<List<PortfolioCompanyModel>> GetPortfolioCompaniesByFund(int fundId)
        {
            List<PortfolioCompanyModel> pccompanyList = new();
            var dealDetails = await _dapperGenericRepository.Query<DealQueryModel>(SqlConstants.DealFundPCQueryListWithFundId, new { fundID = fundId });
            foreach (var item in dealDetails)
            {
                var portfolioCompany = await _dapperGenericRepository.QueryFirstAsync<PortfolioCompanyDetails>(SqlConstants.QueryByGetPCById, new { @Id = item.PortfolioCompanyID });
                if (portfolioCompany?.PortfolioCompanyId > 0)
                {
                    var CommentaryData = GetLatestCommentary((int)item.PortfolioCompanyID);
                    PortfolioCompanyModel portfolioCompanyDetail = await GeneratePorfolioCompanyModel(portfolioCompany, CommentaryData);
                    pccompanyList.Add(portfolioCompanyDetail);
                }
            }
            return pccompanyList;
        }



        private async Task<PortfolioCompanyModel> GeneratePorfolioCompanyModel(PortfolioCompanyDetails portfolioCompany, CommentaryData CommentaryData)
        {
            var queryResult = await _dapperGenericRepository.Query<CompanyDetailModel>(SqlConstants.QueryByGetFundCurrencyByPC, new { @Id = portfolioCompany.PortfolioCompanyId });
            var locationModels = await _dapperGenericRepository.Query<MappingPCGeograpyLocModel>(SqlConstants.QueryByGetLocationByPC, new { @Id = portfolioCompany.PortfolioCompanyId });
            var employeedModels = await _dapperGenericRepository.Query<MappingPCEmployeeModel>(SqlConstants.QueryByGetEmployeesByPC, new { @Id = portfolioCompany.PortfolioCompanyId });
            var currencySectorModel = await _dapperGenericRepository.QueryFirstAsync<MappingPCSectorCurrencyModel>(SqlConstants.QueryByCurrencySectorByPC, new { @Id = portfolioCompany.PortfolioCompanyId });
            var commentaryDetails = await _dapperGenericRepository.Query<CommentaryDetailsModel>(SqlConstants.QueryByGetCommentaryByPC, new { @Id = portfolioCompany.PortfolioCompanyId, @quarter = "Q" + DateTime.Today.GetQuarter(), @year = DateTime.Today.Year });
            var profitabilityDetails = await _dapperGenericRepository.Query<PortfolioCompanyProfitabilityModel>(SqlConstants.QueryByGetProfitabilityByPC, new { @Id = portfolioCompany.PortfolioCompanyId });
            var detailModel = queryResult.FirstOrDefault();
            var companyGroup = await _unitOfWork.MGroupingListRepository.FindFirstAsync(x => !x.IsDeleted && x.GroupId == portfolioCompany.GroupId);
            var portfolioCompanyDetail = new PortfolioCompanyModel
            {
                PortfolioCompanyID = portfolioCompany.PortfolioCompanyId,
                CompanyName = portfolioCompany.CompanyName,
                Website = portfolioCompany.Website,
                BusinessDescription = portfolioCompany.BussinessDescription,
                SectorID = portfolioCompany.SectorId,
                Status = portfolioCompany.Status,
                StockExchange_Ticker = portfolioCompany.StockExchangeTicker,
                HeadquarterID = portfolioCompany.HeadquarterId,
                CreatedBy = portfolioCompany.CreatedBy,
                CreatedOn = portfolioCompany.CreatedOn,
                ModifiedBy = portfolioCompany.ModifiedBy,
                ModifiedOn = portfolioCompany.ModifiedOn,
                IsActive = portfolioCompany.IsActive ?? false,
                FinancialYearEnd = portfolioCompany.FinancialYearEnd,
                ReportingCurrencyID = portfolioCompany.ReportingCurrencyId,
                ImagePath = portfolioCompany.ImagePath,
                GroupName = companyGroup?.GroupName,
                CompanyGroupId = companyGroup?.GroupId,
                CompanyLegalName = portfolioCompany.CompanyLegalName,
                InvestmentDate = portfolioCompany.PCInvestmentDate,
                FundReportingCurrency = detailModel != null ? new CurrencyModel()
                {
                    Currency = detailModel.Currency,
                    CurrencyID = detailModel.CurrencyID,
                    CurrencyCode = detailModel.CurrencyCode
                } : null,
                FundDetail = detailModel != null ? new FundUploadModel()
                {
                    EncryptedFundId = detailModel.EncryptedFundID,
                    FundName = detailModel.FundName,
                } : null,
                DealDetail = detailModel != null ? new DealCompanyModel()
                {
                    DealCustomID = detailModel.DealCustomID,
                    EncryptedDealID = detailModel.EncryptedDealID,
                } : null,
                MasterCompanyName = portfolioCompany.MasterCompanyName,
                GeographicLocations = locationModels?.Select(loc => new LocationModel
                {
                    LocationID = (int)loc.PCGeographicLocationId,
                    Region = loc.RegionId == null ? null : new RegionModel
                    {
                        Region = loc.Region,
                        RegionId = (int)loc.RegionId
                    },
                    Country = loc.CountryId == null ? null : new CountryModel
                    {
                        Country = loc.Country,
                        CountryId = (int)loc.CountryId
                    },
                    State = loc.StateId == null ? null : new StateModel
                    {
                        State = loc.State,
                        StateId = (int)loc.StateId
                    },
                    City = loc.CityId == null ? null : new CityModel
                    {
                        City = loc.City,
                        CityId = (int)loc.CityId
                    },
                    IsHeadquarter = loc.IsHeadquarter is not null && (bool)loc.IsHeadquarter,
                    CreatedBy = portfolioCompany.CreatedBy,
                    CreatedOn = (DateTime)loc.CreatedOn
                }).ToList(),
                PCEmployees = employeedModels?.Select(emp => new PortfolioCompanyEmployeeModel
                {
                    PCEmployeeId = (int)emp.PCEmployeeID,
                    EmployeeId = (int)emp.EmployeeId,
                    EmployeeName = emp.EmployeeName,
                    ContactNo = emp.ContactNo,
                    Designation = emp.DesignationId == null ? null : new DesignationModel
                    {
                        Designation = emp.Designation,
                        DesignationId = (int)emp.DesignationId
                    },
                    EmailId = emp.Email,
                    Education = emp.Education,
                    PastExperience = emp.PastExperience,
                    CreatedBy = (int)emp.CreatedBy,
                    CreatedOn = (DateTime)emp.CreatedOn

                }).ToList(),
                SectorDetail = portfolioCompany.SectorId == null ? null : new SectorModel
                {
                    Sector = currencySectorModel?.Sector,
                    SectorID = (int)portfolioCompany.SectorId
                },
                SubSectorDetail = portfolioCompany.SubSectorId == null ? null : new SubSectorModel
                {
                    SubSector = currencySectorModel?.SubSector,
                    SubSectorID = (int)portfolioCompany.SubSectorId
                },
                ReportingCurrencyDetail = new CurrencyModel
                {
                    Currency = currencySectorModel?.Currency,
                    CurrencyID = portfolioCompany.ReportingCurrencyId,
                    CurrencyCode = currencySectorModel?.CurrencyCode
                },
                CommentaryDetails = commentaryDetails?.FirstOrDefault(),
                PortfolioCompanyProfitabilityList = profitabilityDetails,
                EncryptedPortfolioCompanyId = portfolioCompany.EncryptedPortfolioCompanyId
            };
            portfolioCompanyDetail.CommentaryData = CommentaryData;
            return portfolioCompanyDetail;
        }
        private async Task<PortfolioCompanyModel> GeneratePorfolioCompanyModelDraft(PortfolioCompanyDetailsDraft portfolioCompany)
        {
            var queryResult = await _dapperGenericRepository.Query<CompanyDetailModel>(SqlConstants.QueryByGetFundCurrencyByPC, new { @Id = portfolioCompany.PortfolioCompanyID });
            var detailModel = queryResult.FirstOrDefault();
            var portfolioCompanyDetail = new PortfolioCompanyModel
            {
                PortfolioCompanyID = portfolioCompany.PortfolioCompanyID,
                CompanyName = portfolioCompany.CompanyName,
                Website = portfolioCompany.Website,
                BusinessDescription = portfolioCompany.BussinessDescription,
                SectorID = portfolioCompany.SectorID,
                Status = portfolioCompany.Status,
                StockExchange_Ticker = portfolioCompany.StockExchange_Ticker,
                HeadquarterID = portfolioCompany.HeadquarterID,
                CreatedBy = portfolioCompany.CreatedBy,
                CreatedOn = portfolioCompany.CreatedOn,
                ModifiedBy = portfolioCompany.ModifiedBy,
                ModifiedOn = portfolioCompany.ModifiedOn,
                IsActive = portfolioCompany.IsActive ?? false,
                FinancialYearEnd = portfolioCompany.FinancialYearEnd,
                ReportingCurrencyID = portfolioCompany.ReportingCurrencyID,
                SectionStatus = true,
                ImagePath = portfolioCompany.ImagePath,
                MasterCompanyName = portfolioCompany.MasterCompanyName,
                FundReportingCurrency = detailModel != null ? new CurrencyModel()
                {
                    Currency = detailModel.Currency,
                    CurrencyID = detailModel.CurrencyID,
                    CurrencyCode = detailModel.CurrencyCode
                } : null,
                GeographicLocations = _unitOfWork.MappingPCGeographicLocationDraftRepository?.GetMany(l => !(bool)l.IsDeleted && l.PortfolioCompanyID == portfolioCompany.PortfolioCompanyID && l.WorkflowRequestId == portfolioCompany.WorkfloRequestId).Select(loc => new LocationModel
                {
                    LocationID = loc.PCGeographicLocationID,
                    Region = loc.Region == null ? null : new RegionModel
                    {
                        Region = loc.Region.Region,
                        RegionId = loc.Region.RegionId
                    },
                    Country = loc.Country == null ? null : new CountryModel
                    {
                        Country = loc.Country.Country,
                        CountryId = loc.Country.CountryId
                    },
                    State = loc.State == null ? null : new StateModel
                    {
                        State = loc.State.State,
                        StateId = loc.State.StateId
                    },
                    City = loc.City == null ? null : new CityModel
                    {
                        City = loc.City.City,
                        CityId = loc.City.CityId
                    },
                    IsHeadquarter = loc.IsHeadquarter,
                    CreatedBy = portfolioCompany.CreatedBy,
                    CreatedOn = loc.CreatedOn
                }).ToList(),
                PCEmployees = _unitOfWork.MappingPCEmployeeDraftRepository?.GetMany(e => !(bool)e.IsDeleted && e.PortfolioCompanyID == portfolioCompany.PortfolioCompanyID && e.WorkflowRequestId == portfolioCompany.WorkfloRequestId).Select(emp => new PortfolioCompanyEmployeeModel
                {
                    PCEmployeeId = emp.PCEmployeeID,
                    EmployeeId = emp.EmployeeDetailDraft.EmployeeID,
                    EmployeeName = emp.EmployeeDetailDraft.EmployeeName,
                    ContactNo = emp.EmployeeDetailDraft.ContactNo,

                    Designation = emp.EmployeeDetailDraft?.Designation == null ? null : new DesignationModel
                    {
                        Designation = emp.EmployeeDetailDraft.Designation.Designation,
                        DesignationId = emp.EmployeeDetailDraft.Designation.DesignationId
                    },
                    EmailId = emp.EmployeeDetailDraft.Email,
                    Education = emp.EmployeeDetailDraft.Education,
                    PastExperience = emp.EmployeeDetailDraft.PastExperience,
                    CreatedBy = emp.CreatedBy,
                    CreatedOn = emp.CreatedOn

                }).ToList(),
                SectorDetail = portfolioCompany.Sector == null ? null : new SectorModel
                {
                    Sector = portfolioCompany.Sector.Sector,
                    SectorID = portfolioCompany.Sector.SectorId
                },
                SubSectorDetail = portfolioCompany.SubSector == null ? null : new SubSectorModel
                {
                    SubSector = portfolioCompany.SubSector.SubSector,
                    SubSectorID = portfolioCompany.SubSector.SubSectorId
                },
                ReportingCurrencyDetail = portfolioCompany.ReportingCurrency == null ? null : new CurrencyModel
                {
                    Currency = portfolioCompany.ReportingCurrency.Currency,
                    CurrencyID = portfolioCompany.ReportingCurrency.CurrencyId,
                    CurrencyCode = portfolioCompany.ReportingCurrency.CurrencyCode
                },
                EncryptedPortfolioCompanyId = portfolioCompany.EncryptedPortfolioCompanyID
            };
            return portfolioCompanyDetail;
        }

        public IEnumerable<CommentaryDetails> FilterCommentaryDetails(IEnumerable<CommentaryDetails> commentaryDetails, string period, int year, int quarterNumber, int month)
        {
            IEnumerable<CommentaryDetails> data = new List<CommentaryDetails>();
            if (commentaryDetails.Any() && !string.IsNullOrEmpty(period))
            {
                switch (period)
                {
                    case "Monthly":
                        data = commentaryDetails.Where(x => x.Year == year && x.Month == month);
                        break;
                    case "Quarterly":
                        data = commentaryDetails.Where(x => x.Year == year && x.Quarter == "Q" + quarterNumber);
                        break;
                    case "Annual":
                        data = commentaryDetails.Where(x => x.Year == year);
                        break;
                    default:
                        break;
                }
            }
            return data;
        }
        private CommentaryData GetLatestCommentary(int portfolioCompanyId, MSubSectionFields mSubSection = null)
        {
            CommentaryData commentaryData = new CommentaryData();
            string period = GetPeriod(mSubSection);
            var data = _unitOfWork.CommentaryDetailsRepository.GetMany(x => x.PortfolioCompanyID == portfolioCompanyId && !x.IsDeleted);

            if (data?.Any() == true)
            {
                var significantEventData = GetFilteredCommentaryData(data, x => !string.IsNullOrEmpty(x.SignificantEventsSection), period);
                var assessmentData = GetFilteredCommentaryData(data, x => !string.IsNullOrEmpty(x.AssessmentSection), period);
                var exitPlansData = GetFilteredCommentaryData(data, x => !string.IsNullOrEmpty(x.ExitPlansSection), period);
                var impactData = GetFilteredCommentaryData(data, x => !string.IsNullOrEmpty(x.ImpactSection), period);
                var footNoteInvestmentKPIData = GetFilteredCommentaryData(data, x => !string.IsNullOrEmpty(x.FootNoteInvestmentKPI), period);
                var footNoteTradingRecordData = GetFilteredCommentaryData(data, x => !string.IsNullOrEmpty(x.FootNoteTradingRecord), period);

                commentaryData.FootNoteTradingRecordSectionData = GetSectionData(footNoteTradingRecordData, x => x.FootNoteTradingRecord);
                commentaryData.FootNoteInvestmentKPISectionData = GetSectionData(footNoteInvestmentKPIData, x => x.FootNoteInvestmentKPI);
                commentaryData.SignificantEventsSectionData = GetSectionData(significantEventData, x => x.SignificantEventsSection);
                commentaryData.AssessmentSectionData = GetSectionData(assessmentData, x => x.AssessmentSection);
                commentaryData.ExitPlansSectionData = GetSectionData(exitPlansData, x => x.ExitPlansSection);
                commentaryData.ImpactSectionData = GetSectionData(impactData, x => x.ImpactSection);
            }

            return commentaryData;
        }

        private string GetPeriod(MSubSectionFields mSubSection)
        {
            return mSubSection?.ChartValue.Split(',', StringSplitOptions.RemoveEmptyEntries).FirstOrDefault() ?? string.Empty;
        }

        private IEnumerable<CommentaryDetails> GetFilteredCommentaryData(IEnumerable<CommentaryDetails> data, Func<CommentaryDetails, bool> predicate, string period)
        {
            var filteredData = data.Where(predicate);
            if (filteredData.Any())
            {
                var maxYear = filteredData.Max(x => x.Year);
                var maxMonth = filteredData.Max(x => x.Month);
                var maxQuarter = filteredData.Where(x => x.Year == maxYear && !string.IsNullOrEmpty(x.Quarter)).Any() ? filteredData.Where(x => x.Year == maxYear && !string.IsNullOrEmpty(x.Quarter)).Max(x => Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1])) : 0;
                return FilterCommentaryDetails(filteredData, period, maxYear, maxQuarter, maxMonth);
            }
            return Enumerable.Empty<CommentaryDetails>();
        }

        private Tuple<string, int, string, string> GetSectionData(IEnumerable<CommentaryDetails> data, Func<CommentaryDetails, string> sectionSelector)
        {
            var sectionData = data.FirstOrDefault();
            if (sectionData != null)
            {
                return Tuple.Create(sectionData.EncryptedCommentaryID, sectionData.Year, sectionData.Quarter, sectionSelector(sectionData));
            }
            return null;
        }

        private CommentaryData GetLatestDraftCommentary(int portfolioCompanyId)
        {
            var commentaryData = new CommentaryData();

            var data = _unitOfWork.PortfolioCompanyCommentaryDraftRepository
                .GetMany(x => x.PortfolioCompanyID == portfolioCompanyId && !x.IsDeleted)
                .ToList();

            if (data.Any())
            {
                commentaryData.SignificantEventsSectionData = GetLatestSectionData(data, x => x.SignificantEventsSection);
                commentaryData.AssessmentSectionData = GetLatestSectionData(data, x => x.AssessmentSection);
                commentaryData.ExitPlansSectionData = GetLatestSectionData(data, x => x.ExitPlansSection);
                commentaryData.ImpactSectionData = GetLatestSectionData(data, x => x.ImpactSection);
            }

            return commentaryData;
        }

        private Tuple<string, int, string, string> GetLatestSectionData(List<PortfolioCompanyCommentaryDraft> data, Func<PortfolioCompanyCommentaryDraft, string> sectionSelector)
        {
            var sectionData = data.Where(x => !string.IsNullOrEmpty(sectionSelector(x)));

            if (sectionData.Any())
            {
                var latestData = sectionData
                    .OrderByDescending(x => x.Year)
                    .ThenByDescending(x => Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1]))
                    .FirstOrDefault();

                return Tuple.Create(latestData?.EncryptedCommentaryID, latestData?.Year ?? 0, latestData?.Quarter ?? string.Empty, sectionSelector(latestData));
            }

            return null;
        }



        public List<FundingTypeModel> GetAllFundingTypes()
        {

            var result = (List<FundingTypeModel>)_memoryCacher.GetValue(_allFundingTypesToken);
            if (result == null)
            {
                var fundingTypesList = _unitOfWork.FundingTypeRepository.GetMany(x => !x.IsDeleted).Select(x => new FundingTypeModel
                {
                    FundingTypeID = x.FundingTypeID,
                    FundingType = x.FundingType,
                    EncryptedFundingTypeID = x.EncryptedFundingTypeID

                }).ToList();
                _memoryCacher.Add(_allFundingTypesToken, fundingTypesList, DateTimeOffset.UtcNow.AddHours(_globalConfig.CacheTimoutHours));
                result = fundingTypesList;
            }
            return result;
        }

        #region "PCSectorwiseKPI"

        public List<SectorwiseKpiDetails> GetSectorwiseKPIListForPortfolioCompany(int portfolioCompanyId)
        {

            return _unitOfWork.M_SectorwiseKPIRepository.GetMany(x => x.PortfolioCompanyOperationalKPIValues.Any(y => !y.IsDeleted && !y.PortfolioCompanyOperationalKPIQuarter.IsDeleted && y.PortfolioCompanyOperationalKPIQuarter.PortfolioCompanyID == portfolioCompanyId) && !x.IsDeleted)
                .Select(x => new SectorwiseKpiDetails
                {
                    SectorwiseKPIID = x.SectorwiseOperationalKPIID,
                    SectorID = x.SectorId,
                    Description = x.Description,
                    KPI = x.Kpi
                }).ToList();

        }

        public int SavePCSectorwiseKPIMonthlyValue(List<PcSectorwiseKpiMonthlyValueModel> pcSectorwiseKPIMonthlyValueModelList)
        {
            int result = 0;
            if (pcSectorwiseKPIMonthlyValueModelList == null)
            {
                return 0;
            }
            using (var scope = new TransactionScope())
            {
                foreach (var sectorwiseKPIMonthlyValue in pcSectorwiseKPIMonthlyValueModelList)
                {
                    if (!sectorwiseKPIMonthlyValue.Month.IsValidMonth())
                    {
                        return 0;
                    }
                    if (!sectorwiseKPIMonthlyValue.Year.IsValidYearForEntry())
                    {
                        return 0;
                    }

                    if (sectorwiseKPIMonthlyValue.PCSectorwiseKPIMonthlyValueID > 0)
                    {
                        result = UpdatePortfolioCompanySectorwiseKPIValue(sectorwiseKPIMonthlyValue);
                        if (result == -1 || result == 0)
                        {
                            return result;
                        }

                    }
                    else
                    {
                        result = CreatePortfolioCompanySectorwiseKPIValue(sectorwiseKPIMonthlyValue);
                        if (result == -1 || result == 0)
                        {
                            return result;
                        }
                    }
                }
                scope.Complete();
            }
            return result;

        }

        private int CreatePortfolioCompanySectorwiseKPIValue(PcSectorwiseKpiMonthlyValueModel pcSectorwiseKPIMonthlyValueModel)
        {
            var ifExist = IfPortfolioCompanySectorwiseKPIMonthAlreadyExist(pcSectorwiseKPIMonthlyValueModel);
            if (!ifExist)
            {
                PCSectorwiseKPIMonthlyValue dbQuarter = new PCSectorwiseKPIMonthlyValue
                {
                    PortfolioCompanyId = pcSectorwiseKPIMonthlyValueModel.PortfolioCompanyID,
                    Month = pcSectorwiseKPIMonthlyValueModel.Month,
                    Year = pcSectorwiseKPIMonthlyValueModel.Year,
                    CreatedOn = System.DateTime.Now,
                    IsMapped = true,
                    CreatedBy = pcSectorwiseKPIMonthlyValueModel.CreatedBy,
                    IsDeleted = pcSectorwiseKPIMonthlyValueModel.IsDeleted,
                    IsActive = pcSectorwiseKPIMonthlyValueModel.IsActive,
                    KPIValue = pcSectorwiseKPIMonthlyValueModel.KPIValue,
                    KPIInfo = pcSectorwiseKPIMonthlyValueModel.KPIInfo,
                    SectorwiseKPIID = pcSectorwiseKPIMonthlyValueModel.SectorwiseKPI.SectorwiseKPIID
                };
                _unitOfWork.PCSectorwiseKPIMonthlyValueRepository.Insert(dbQuarter);
                _unitOfWork.Save();
                return 1;
            }
            else
            {
                return -1;
            }

        }

        private int UpdatePortfolioCompanySectorwiseKPIValue(PcSectorwiseKpiMonthlyValueModel pcSectorwiseKPIMonthlyValueModel)
        {
            var ifExist = IfPortfolioCompanySectorwiseKPIMonthAlreadyExist(pcSectorwiseKPIMonthlyValueModel);
            if (!ifExist)
            {
                var dbDetail = _unitOfWork.PCSectorwiseKPIMonthlyValueRepository.Get(x => x.PCSectorwiseKPIMonthlyValueID == pcSectorwiseKPIMonthlyValueModel.PCSectorwiseKPIMonthlyValueID);
                if (dbDetail != null)
                {

                    decimal? oldKPIValue = dbDetail.KPIValue;
                    decimal? newKPIValue = pcSectorwiseKPIMonthlyValueModel.KPIValue;

                    dbDetail.PortfolioCompanyId = pcSectorwiseKPIMonthlyValueModel.PortfolioCompanyID;
                    dbDetail.Month = pcSectorwiseKPIMonthlyValueModel.Month;
                    dbDetail.Year = pcSectorwiseKPIMonthlyValueModel.Year;
                    dbDetail.ModifiedOn = System.DateTime.Now;
                    dbDetail.IsMapped = true;
                    dbDetail.ModifiedBy = pcSectorwiseKPIMonthlyValueModel.ModifiedBy;
                    dbDetail.IsDeleted = pcSectorwiseKPIMonthlyValueModel.IsDeleted;
                    dbDetail.IsActive = pcSectorwiseKPIMonthlyValueModel.IsActive;
                    dbDetail.KPIValue = pcSectorwiseKPIMonthlyValueModel.KPIValue;
                    dbDetail.KPIInfo = pcSectorwiseKPIMonthlyValueModel.KPIInfo;
                    dbDetail.SectorwiseKPIID = pcSectorwiseKPIMonthlyValueModel.SectorwiseKPI.SectorwiseKPIID;
                    _unitOfWork.PCSectorwiseKPIMonthlyValueRepository.Update(dbDetail);
                    _unitOfWork.Save();
                    if (pcSectorwiseKPIMonthlyValueModel.IsDeleted && !pcSectorwiseKPIMonthlyValueModel.IsActive)
                    {

                        DateTime dateTime = new DateTime(pcSectorwiseKPIMonthlyValueModel.Year, pcSectorwiseKPIMonthlyValueModel.Month, 1);

                        var MonthAndyearparm = dateTime.ToString("MMM yyyy");

                        var dataAuditLogset = _unitOfWork.DataAuditLogRepository.GetMany(x => x.AttributeID == pcSectorwiseKPIMonthlyValueModel.PCSectorwiseKPIMonthlyValueID &&
                           x.AttributeName == "Sector KPIs" &&
                           x.FieldName == pcSectorwiseKPIMonthlyValueModel.SectorwiseKPI.KPI &&
                           x.MonthAndYear == MonthAndyearparm && !x.IsDeleted && x.PortfolioCompanyID == pcSectorwiseKPIMonthlyValueModel.PortfolioCompanyID).ToList();
                        foreach (var item in dataAuditLogset)
                        {
                            item.IsDeleted = true;
                            _unitOfWork.DataAuditLogRepository.Update(item);
                        }
                        _unitOfWork.Save();
                        return 3;
                    }
                    else
                    {
                        if (oldKPIValue != newKPIValue)
                        {
                            DateTime dateTime = new DateTime(pcSectorwiseKPIMonthlyValueModel.Year, pcSectorwiseKPIMonthlyValueModel.Month, 1);
                            DataAuditLog dataAuditLog = new DataAuditLog()
                            {
                                AttributeName = "Sector KPIs",
                                AttributeID = pcSectorwiseKPIMonthlyValueModel.PCSectorwiseKPIMonthlyValueID,
                                FieldName = pcSectorwiseKPIMonthlyValueModel.SectorwiseKPI.KPI,
                                MonthAndYear = dateTime.ToString("MMM yyyy"),
                                OldValue = decimal.Parse(oldKPIValue.ToString()).ToString("G29"),
                                NewValue = decimal.Parse(newKPIValue.ToString()).ToString("G29"),
                                Comments = "",
                                DynamicQueryId = 0,
                                OldCurrencyType = pcSectorwiseKPIMonthlyValueModel.KPIInfo,
                                NewCurrencyType = pcSectorwiseKPIMonthlyValueModel.KPIInfo,
                                PortfolioCompanyID = pcSectorwiseKPIMonthlyValueModel.PortfolioCompanyID,
                                CreatedOn = Convert.ToDateTime(pcSectorwiseKPIMonthlyValueModel.CreatedOn),
                                CreatedBy = pcSectorwiseKPIMonthlyValueModel.ModifiedBy == null ? 1 : pcSectorwiseKPIMonthlyValueModel.ModifiedBy.GetValueOrDefault()
                            };

                            _unitOfWork.DataAuditLogRepository.Insert(dataAuditLog);

                            _unitOfWork.Save();

                            return 2;
                        }

                        return 2;
                    }
                }
            }
            else
            {
                return -1;
            }

            return 0;

        }

        private bool IfPortfolioCompanySectorwiseKPIMonthAlreadyExist(PcSectorwiseKpiMonthlyValueModel pcSectorwiseKPIMonthlyValueModel)
        {
            var result = _unitOfWork.PCSectorwiseKPIMonthlyValueRepository.ExistsAny(x => x.PCSectorwiseKPIMonthlyValueID != pcSectorwiseKPIMonthlyValueModel.PCSectorwiseKPIMonthlyValueID && x.PortfolioCompanyId == pcSectorwiseKPIMonthlyValueModel.PortfolioCompanyID && x.SectorwiseKPIID == pcSectorwiseKPIMonthlyValueModel.SectorwiseKPI.SectorwiseKPIID && x.Year == pcSectorwiseKPIMonthlyValueModel.Year && x.Month == pcSectorwiseKPIMonthlyValueModel.Month && x.IsActive == true && !x.IsDeleted);
            return result;
        }

        public int SavePCFinancialKPIMonthlyValue(List<PcFinancialKpiMonthlyValueModel> pcFinancialKPIMonthlyValueModelList)
        {
            int result = 0;
            if (pcFinancialKPIMonthlyValueModelList == null)
            {
                return 0;
            }
            using (var scope = new TransactionScope())
            {
                foreach (var financialKPIMonthlyValue in pcFinancialKPIMonthlyValueModelList)
                {
                    if (!financialKPIMonthlyValue.Month.IsValidMonth())
                    {
                        return 0;
                    }
                    if (!financialKPIMonthlyValue.Year.IsValidYearForEntry())
                    {
                        return 0;
                    }

                    if (financialKPIMonthlyValue.PCFinancialKPIMonthlyValueID > 0)
                    {
                        result = UpdatePortfolioCompanyFinancialKPIValue(financialKPIMonthlyValue);
                        if (result == -1 || result == 0)
                        {
                            return result;
                        }

                    }
                    else
                    {
                        result = CreatePortfolioCompanyFinancialKPIValue(financialKPIMonthlyValue);
                        if (result == -1 || result == 0)
                        {
                            return result;
                        }
                    }
                }
                scope.Complete();
            }
            return result;

        }

        private int CreatePortfolioCompanyFinancialKPIValue(PcFinancialKpiMonthlyValueModel pcFinancialKPIMonthlyValueModel)
        {
            var ifExist = IfPortfolioCompanyFinancialKPIMonthAlreadyExist(pcFinancialKPIMonthlyValueModel);
            if (!ifExist)
            {
                PCFinancialKPIMonthlyValue dbQuarter = new PCFinancialKPIMonthlyValue
                {
                    PortfolioCompanyId = pcFinancialKPIMonthlyValueModel.PortfolioCompanyID,
                    Month = pcFinancialKPIMonthlyValueModel.Month,
                    Year = pcFinancialKPIMonthlyValueModel.Year,
                    CreatedOn = System.DateTime.Now,
                    IsMapped = true,
                    CreatedBy = pcFinancialKPIMonthlyValueModel.CreatedBy,
                    IsDeleted = pcFinancialKPIMonthlyValueModel.IsDeleted,
                    IsActive = pcFinancialKPIMonthlyValueModel.IsActive,
                    KPIValue = pcFinancialKPIMonthlyValueModel.KPIValue,
                    KPIInfo = pcFinancialKPIMonthlyValueModel.KPIInfo,
                    FinancialKPIID = pcFinancialKPIMonthlyValueModel.FinancialKPI.FinancialKPIId
                };
                _unitOfWork.PCFinancialKPIMonthlyValueRepository.Insert(dbQuarter);
                _unitOfWork.Save();
                return 1;
            }
            else
            {
                return -1;
            }

        }

        private int UpdatePortfolioCompanyFinancialKPIValue(PcFinancialKpiMonthlyValueModel pcFinancialKPIMonthlyValueModel)
        {
            var ifExist = IfPortfolioCompanyFinancialKPIMonthAlreadyExist(pcFinancialKPIMonthlyValueModel);
            if (!ifExist)
            {
                var dbDetail = _unitOfWork.PCFinancialKPIMonthlyValueRepository.Get(x => x.PCFinancialKPIMonthlyValueID == pcFinancialKPIMonthlyValueModel.PCFinancialKPIMonthlyValueID);
                if (dbDetail != null)
                {
                    decimal? oldKPIValue = dbDetail.KPIValue;
                    decimal? NewKPIValue = pcFinancialKPIMonthlyValueModel.KPIValue;

                    dbDetail.PortfolioCompanyId = pcFinancialKPIMonthlyValueModel.PortfolioCompanyID;
                    dbDetail.Month = pcFinancialKPIMonthlyValueModel.Month;
                    dbDetail.Year = pcFinancialKPIMonthlyValueModel.Year;
                    dbDetail.ModifiedOn = DateTime.Now;
                    dbDetail.ModifiedBy = pcFinancialKPIMonthlyValueModel.ModifiedBy;
                    dbDetail.IsDeleted = pcFinancialKPIMonthlyValueModel.IsDeleted;
                    dbDetail.IsMapped = true;
                    dbDetail.IsActive = pcFinancialKPIMonthlyValueModel.IsActive;
                    dbDetail.KPIValue = pcFinancialKPIMonthlyValueModel.KPIValue;
                    dbDetail.KPIInfo = pcFinancialKPIMonthlyValueModel.KPIInfo;
                    dbDetail.FinancialKPIID = pcFinancialKPIMonthlyValueModel.FinancialKPI.FinancialKPIId;
                    _unitOfWork.PCFinancialKPIMonthlyValueRepository.Update(dbDetail);
                    _unitOfWork.Save();

                    if (pcFinancialKPIMonthlyValueModel.IsDeleted && !pcFinancialKPIMonthlyValueModel.IsActive)
                    {

                        DateTime dateTime = new DateTime(pcFinancialKPIMonthlyValueModel.Year, pcFinancialKPIMonthlyValueModel.Month, 1);
                        var MonthAndyearparm = dateTime.ToString("MMM yyyy");
                        var dataAuditLogset = _unitOfWork.DataAuditLogRepository.GetMany(x => x.AttributeID == pcFinancialKPIMonthlyValueModel.PCFinancialKPIMonthlyValueID &&
                           x.AttributeName == "Financial KPIs" &&
                           x.FieldName == pcFinancialKPIMonthlyValueModel.FinancialKPI.KPI &&
                           x.MonthAndYear == MonthAndyearparm && !x.IsDeleted && x.PortfolioCompanyID == pcFinancialKPIMonthlyValueModel.PortfolioCompanyID).ToList();
                        foreach (var item in dataAuditLogset)
                        {
                            item.IsDeleted = true;
                            _unitOfWork.DataAuditLogRepository.Update(item);
                        }
                        _unitOfWork.Save();

                        return 3;
                    }
                    else
                    {

                        if (oldKPIValue != NewKPIValue)
                        {
                            DateTime dateTime = new DateTime(pcFinancialKPIMonthlyValueModel.Year, pcFinancialKPIMonthlyValueModel.Month, 1);
                            DataAuditLog dataAuditLog = new DataAuditLog()
                            {
                                AttributeName = "Financial KPIs",
                                AttributeID = pcFinancialKPIMonthlyValueModel.PCFinancialKPIMonthlyValueID,
                                FieldName = pcFinancialKPIMonthlyValueModel.FinancialKPI.KPI,
                                MonthAndYear = dateTime.ToString("MMM yyyy"),
                                OldValue = decimal.Parse(oldKPIValue.ToString()).ToString("G29"),
                                NewValue = decimal.Parse(NewKPIValue.ToString()).ToString("G29"),
                                Comments = "",
                                DynamicQueryId = 0,
                                OldCurrencyType = pcFinancialKPIMonthlyValueModel.KPIInfo,
                                NewCurrencyType = pcFinancialKPIMonthlyValueModel.KPIInfo,
                                PortfolioCompanyID = pcFinancialKPIMonthlyValueModel.PortfolioCompanyID,
                                CreatedOn = Convert.ToDateTime(pcFinancialKPIMonthlyValueModel.CreatedOn),
                                CreatedBy = pcFinancialKPIMonthlyValueModel.ModifiedBy == null ? 1 : pcFinancialKPIMonthlyValueModel.ModifiedBy.GetValueOrDefault()
                            };

                            _unitOfWork.DataAuditLogRepository.Insert(dataAuditLog);

                            _unitOfWork.Save();

                            return 2;
                        }

                        return 2;
                    }

                }
            }
            else
            {
                return -1;
            }

            return 0;

        }

        private bool IfPortfolioCompanyFinancialKPIMonthAlreadyExist(PcFinancialKpiMonthlyValueModel pcFinancialKPIMonthlyValueModel)
        {
            var result = _unitOfWork.PCFinancialKPIMonthlyValueRepository.ExistsAny(x => x.PCFinancialKPIMonthlyValueID != pcFinancialKPIMonthlyValueModel.PCFinancialKPIMonthlyValueID && x.PortfolioCompanyId == pcFinancialKPIMonthlyValueModel.PortfolioCompanyID && x.FinancialKPIID == pcFinancialKPIMonthlyValueModel.FinancialKPI.FinancialKPIId && x.Year == pcFinancialKPIMonthlyValueModel.Year && x.Month == pcFinancialKPIMonthlyValueModel.Month && x.IsActive == true && !x.IsDeleted);
            return result;
        }

        #endregion "PCFinancialKPI"
        #region "PCCompanywiseKPI"

        public int SavePCCompanywiseKPIMonthlyValue(List<PcCompanywiseKpiMonthlyValueModel> pcCompanywiseKPIMonthlyValueModelList)
        {
            int result = 0;
            if (pcCompanywiseKPIMonthlyValueModelList == null)
            {
                return 0;
            }
            using (var scope = new TransactionScope())
            {
                foreach (var companywiseKPIMonthlyValue in pcCompanywiseKPIMonthlyValueModelList)
                {
                    if (!companywiseKPIMonthlyValue.Month.IsValidMonth())
                    {
                        return 0;
                    }
                    if (!companywiseKPIMonthlyValue.Year.IsValidYearForEntry())
                    {
                        return 0;
                    }

                    if (companywiseKPIMonthlyValue.PCCompanywiseKPIMonthlyValueID > 0)
                    {
                        result = UpdatePortfolioCompanyCompanywiseKPIValue(companywiseKPIMonthlyValue);
                        if (result == -1 || result == 0)
                        {
                            return result;
                        }

                    }
                    else
                    {
                        result = CreatePortfolioCompanyCompanywiseKPIValue(companywiseKPIMonthlyValue);
                        if (result == -1 || result == 0)
                        {
                            return result;
                        }
                    }
                }
                scope.Complete();
            }
            return result;

        }

        private int CreatePortfolioCompanyCompanywiseKPIValue(PcCompanywiseKpiMonthlyValueModel pcCompanywiseKPIMonthlyValueModel)
        {
            var ifExist = IfPortfolioCompanyCompanywiseKPIMonthAlreadyExist(pcCompanywiseKPIMonthlyValueModel);
            if (!ifExist)
            {
                PcCompanywiseKpiMonthlyValue dbQuarter = new PcCompanywiseKpiMonthlyValue
                {
                    PortfolioCompanyId = pcCompanywiseKPIMonthlyValueModel.PortfolioCompanyID,
                    Month = pcCompanywiseKPIMonthlyValueModel.Month,
                    Year = pcCompanywiseKPIMonthlyValueModel.Year,
                    CreatedOn = System.DateTime.Now,
                    CreatedBy = pcCompanywiseKPIMonthlyValueModel.CreatedBy,
                    IsDeleted = pcCompanywiseKPIMonthlyValueModel.IsDeleted,
                    IsActive = true,
                    KPIValue = pcCompanywiseKPIMonthlyValueModel.KPIValue == null ? 0 : Convert.ToDecimal(pcCompanywiseKPIMonthlyValueModel.KPIValue),
                    KPIInfo = pcCompanywiseKPIMonthlyValueModel.KPIInfo,
                    IsMapped = true,
                    CompanywiseKPIID = pcCompanywiseKPIMonthlyValueModel.CompanywiseKPI.CompanywiseKPIID
                };
                _unitOfWork.PCCompanywiseKPIMonthlyValueRepository.Insert(dbQuarter);
                _unitOfWork.Save();
                return 1;
            }
            else
            {
                return -1;
            }

        }

        private int UpdatePortfolioCompanyCompanywiseKPIValue(PcCompanywiseKpiMonthlyValueModel pcCompanywiseKPIMonthlyValueModel)
        {
            var ifExist = IfPortfolioCompanyCompanywiseKPIMonthAlreadyExist(pcCompanywiseKPIMonthlyValueModel);
            if (!ifExist)
            {
                var dbDetail = _unitOfWork.PCCompanywiseKPIMonthlyValueRepository.Get(x => x.PCCompanywiseKPIMonthlyValueID == pcCompanywiseKPIMonthlyValueModel.PCCompanywiseKPIMonthlyValueID);
                if (dbDetail != null)
                {

                    decimal oldKPIValue = dbDetail.KPIValue;
                    decimal NewKPIValue = pcCompanywiseKPIMonthlyValueModel.KPIValue.GetValueOrDefault();

                    dbDetail.PortfolioCompanyId = pcCompanywiseKPIMonthlyValueModel.PortfolioCompanyID;
                    dbDetail.Month = pcCompanywiseKPIMonthlyValueModel.Month;
                    dbDetail.Year = pcCompanywiseKPIMonthlyValueModel.Year;
                    dbDetail.ModifiedOn = System.DateTime.Now;
                    dbDetail.ModifiedBy = pcCompanywiseKPIMonthlyValueModel.ModifiedBy;
                    dbDetail.IsDeleted = pcCompanywiseKPIMonthlyValueModel.IsDeleted;
                    dbDetail.IsActive = pcCompanywiseKPIMonthlyValueModel.IsActive;
                    dbDetail.KPIValue = pcCompanywiseKPIMonthlyValueModel.KPIValue == null ? 0 : Convert.ToDecimal(pcCompanywiseKPIMonthlyValueModel.KPIValue);
                    dbDetail.KPIInfo = pcCompanywiseKPIMonthlyValueModel.KPIInfo;
                    dbDetail.IsMapped = true;
                    dbDetail.CompanywiseKPIID = pcCompanywiseKPIMonthlyValueModel.CompanywiseKPI.CompanywiseKPIID;
                    _unitOfWork.PCCompanywiseKPIMonthlyValueRepository.Update(dbDetail);
                    _unitOfWork.Save();
                    if (pcCompanywiseKPIMonthlyValueModel.IsDeleted && !pcCompanywiseKPIMonthlyValueModel.IsActive)
                    {

                        DateTime dateTime = new DateTime(pcCompanywiseKPIMonthlyValueModel.Year, pcCompanywiseKPIMonthlyValueModel.Month, 1);
                        var MonthAndyearparm = dateTime.ToString("MMM yyyy");
                        var dataAuditLogset = _unitOfWork.DataAuditLogRepository.GetMany(x => x.AttributeID == pcCompanywiseKPIMonthlyValueModel.PCCompanywiseKPIMonthlyValueID &&
                           x.AttributeName == "Company KPIs" &&
                           x.FieldName == pcCompanywiseKPIMonthlyValueModel.CompanywiseKPI.KPI &&
                           x.MonthAndYear == MonthAndyearparm && !x.IsDeleted && x.PortfolioCompanyID == pcCompanywiseKPIMonthlyValueModel.PortfolioCompanyID).ToList();
                        foreach (var item in dataAuditLogset)
                        {
                            item.IsDeleted = true;
                            _unitOfWork.DataAuditLogRepository.Update(item);
                        }
                        _unitOfWork.Save();

                        return 3;
                    }
                    else
                    {
                        if (oldKPIValue != NewKPIValue)
                        {
                            DateTime dateTime = new DateTime(pcCompanywiseKPIMonthlyValueModel.Year, pcCompanywiseKPIMonthlyValueModel.Month, 1);
                            DataAuditLog dataAuditLog = new DataAuditLog()
                            {
                                AttributeName = "Company KPIs",
                                AttributeID = pcCompanywiseKPIMonthlyValueModel.PCCompanywiseKPIMonthlyValueID,
                                FieldName = pcCompanywiseKPIMonthlyValueModel.CompanywiseKPI.KPI,
                                MonthAndYear = dateTime.ToString("MMM yyyy"),
                                OldValue = decimal.Parse(oldKPIValue.ToString()).ToString("G29"),
                                NewValue = decimal.Parse(NewKPIValue.ToString()).ToString("G29"),
                                Comments = "",
                                DynamicQueryId = 0,
                                OldCurrencyType = pcCompanywiseKPIMonthlyValueModel.KPIInfo,
                                NewCurrencyType = pcCompanywiseKPIMonthlyValueModel.KPIInfo,
                                PortfolioCompanyID = pcCompanywiseKPIMonthlyValueModel.PortfolioCompanyID,
                                CreatedOn = Convert.ToDateTime(pcCompanywiseKPIMonthlyValueModel.CreatedOn),
                                CreatedBy = pcCompanywiseKPIMonthlyValueModel.ModifiedBy == null ? 1 : pcCompanywiseKPIMonthlyValueModel.ModifiedBy.GetValueOrDefault()
                            };

                            _unitOfWork.DataAuditLogRepository.Insert(dataAuditLog);

                            _unitOfWork.Save();

                            return 2;
                        }

                        return 2;
                    }
                }
            }
            else
            {
                return -1;
            }

            return 0;

        }

        private bool IfPortfolioCompanyCompanywiseKPIMonthAlreadyExist(PcCompanywiseKpiMonthlyValueModel pcCompanywiseKPIMonthlyValueModel)
        {
            var result = _unitOfWork.PCCompanywiseKPIMonthlyValueRepository.ExistsAny(x => x.PCCompanywiseKPIMonthlyValueID != pcCompanywiseKPIMonthlyValueModel.PCCompanywiseKPIMonthlyValueID && x.PortfolioCompanyId == pcCompanywiseKPIMonthlyValueModel.PortfolioCompanyID && x.CompanywiseKPIID == pcCompanywiseKPIMonthlyValueModel.CompanywiseKPI.CompanywiseKPIID && x.Year == pcCompanywiseKPIMonthlyValueModel.Year && x.Month == pcCompanywiseKPIMonthlyValueModel.Month && x.IsActive == true && !x.IsDeleted);
            return result;
        }

        #endregion "PCCompanywiseKPI"

        #region "ExportKPIData"


        public PcFinancialKpiMonthlyValueListModel GetPCFinancialKPIMonthlyValue(PckpiMonthlyValueFilter filter)
        {

            var result = new PcFinancialKpiMonthlyValueListModel();
            var data = _unitOfWork.PCFinancialKPIMonthlyValueRepository.GetManyQueryable(x => !x.IsDeleted && x.PortfolioCompanyId == filter.PortfolioCompanyId && x.IsMapped);
            if (filter.SearchFilter != null)
            {
                data = data.SortedResult(filter.SearchFilter.SortOrder);
            }

            result.PCFinancialKPIMonthlyValueList = data.ToList().ConvertAll(x => new PcFinancialKpiMonthlyValueModel
            {
                CreatedBy = x.CreatedBy,
                CreatedOn = x.CreatedOn,
                PCFinancialKPIMonthlyValueID = x.PCFinancialKPIMonthlyValueID,
                KPIInfo = x.KPIInfo,
                KPIValue = x.KPIValue,
                KPIValueStr = x.KPIValue != null ? Convert.ToString(x.KPIValue).TrimEnd('0') : Convert.ToString(x.KPIValue),
                FinancialKPIID = x.FinancialKPIID,
                FinancialKPI = new FinancialKpiDetails
                {
                    FinancialKPIId = x.FinancialKPIID,
                    KPI = x.FinancialKPI.KPI,
                },

                IsActive = x.IsActive ?? false,
                IsDeleted = x.IsDeleted,
                ModifiedBy = x.ModifiedBy,
                ModifiedOn = x.ModifiedOn,
                PortfolioCompanyID = x.PortfolioCompanyId,
                Month = x.Month,
                Year = x.Year
            });

            return result;

        }

        #endregion "ExportKPIData"

        public PortfolioCompanyListModel GetDuplicatePortFolioCompany(List<string> filter)
        {
            filter = filter.ConvertAll(d => d.ToLower());
            var result = new PortfolioCompanyListModel();
            result.PortfolioCompanyList = _unitOfWork.PortfolioCompanyDetailRepository.GetManyQueryable(x => !x.IsDeleted && filter.Contains(x.CompanyName.ToLower().Trim())).Select(x =>
           new PortfolioCompanyModel
           {
               CompanyName = x.CompanyName,

               PortfolioCompanyID = x.PortfolioCompanyId,
               EncryptedPortfolioCompanyId = x.EncryptedPortfolioCompanyId
           }
            ).ToList();
            return result;

        }
        public async Task<FinancialTypesModel> GetFinancialValueTypesList()
        {
            var result = new FinancialTypesModel();
            var financialValuetypes = await _unitOfWork.FinancialValueTypesRepository.FindAllAsync(x => !x.IsDeleted);
            result.FinancialTypesModelList = financialValuetypes
                .ConvertAll(x => new FinancialTypes
                {
                    Active = false,
                    TabID = x.TabID,
                    Name = x.Name,
                    Alias = x.Alias

                });
            return result;
        }

        public int TotalPortfolioCompaniesCount()
        {
            return _unitOfWork.PortfolioCompanyDetailRepository.GetManyQueryable(x => !x.IsDeleted).Count();
        }
        public async Task<DashboardTotalfundsCompaniesModel> GetLatestQuarterYearTotalPortfolioCompaniesFundsCount()
        {
            return await _dapperGenericRepository.QuerySingleOrDefaultAsync<DashboardTotalfundsCompaniesModel>(SqlConstants.QueryByMainDashboard, null);
        }
        #region "PCInvestmentKPI"
        public PcInvestmentKpiQuarterlyValueListModel GetPCInvestmentKPIQuarterlyValueForLpReport(PcInvestmentKpiQuarterlyValueFilter filter, ReportSectionWiseKpiList reportMappingKPIs)
        {
            var result = new PcInvestmentKpiQuarterlyValueListModel();
            try
            {
                var data = _unitOfWork.PCInvestmentKpiQuarterlyValueRepository.GetManyQueryable(x => !x.IsDeleted && x.PortfolioCompanyId == filter.PortfolioCompanyID);
                var mapping = _unitOfWork.PortfolioInvestmentKpiMappingRepository.GetMany(x => !x.IsDeleted && x.PortfolioCompanyId == filter.PortfolioCompanyID).Select(x => x.KpiId).ToList();
                data = data.Where(x => mapping.Contains(x.InvestmentKpIid));
                if (reportMappingKPIs.ReportSectionWiseKpiModel.Where(X => X.Section == "KPI - Investment Table")?.Any() == true)
                {
                    string Period = reportMappingKPIs.ReportSectionWiseKpiModel.FirstOrDefault(X => X.Section == "KPI - Investment Table").Period;
                    List<string> mappedInvestmentKpiId = reportMappingKPIs.ReportSectionWiseKpiModel.FirstOrDefault(X => X.Section == "KPI - Investment Table").KPIId.ToList();
                    if (Period != null)
                        filter.SearchFilter.PeriodType = Period;
                    data = data.Where(x => mappedInvestmentKpiId.Contains(x.InvestmentKpIid.ToString()));

                }
                if (filter.SearchFilter != null && data.Any())
                {
                    DateTime? toDate = null;
                    var maxYearData = data.Max(x => x.Year);
                    var maxQuarter = data.Where(x => x.Year == maxYearData).Max(x => Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1]));

                    toDate = Common.GetLastDayOfQuarter(maxYearData, maxQuarter);
                    toDate = toDate.Value.AddMonths(1).AddDays(-1);
                    if (toDate == null)
                        toDate = DateTime.Now;
                    if (filter.SearchFilter.PeriodType == Common.GetDescription(PeriodTypeQuarterConfig.CurrentQuarter))
                    {
                        int currentQtr = DateTime.Today.GetQuarter();
                        int currentYear = DateTime.Now.Year;
                        data = data.Where(x => (x.Quarter == "Q" + currentQtr) && (x.Year == currentYear));
                    }
                    if (filter.SearchFilter.PeriodType == Common.GetDescription(PeriodTypeQuarterConfig.LastQuarter))
                    {
                        DateTime startDate = new DateTime(toDate.Value.Year, toDate.Value.Month, 1);
                        int lastQtr = Common.PreviousQuarter(startDate);
                        int qtrYear = Common.GetYearOfQuarter(startDate, lastQtr);
                        data = data.Where(x => (x.Quarter == "Q" + lastQtr) && (x.Year == qtrYear));
                    }
                    if (filter.SearchFilter.PeriodType == Common.GetDescription(PeriodTypeFeature.Last1Year) || filter.SearchFilter.PeriodType == Common.GetDescription(PeriodTypeFeature.Last4Quarter))
                    {
                        DateTime endDate = new DateTime(toDate.Value.Year, toDate.Value.Month, 1);
                        DateTime startDate = endDate.AddMonths(-12);
                        data = data.Where(x => (Common.GetLastDayOfQuarter(x.Year, Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1])) >= startDate) && (Common.GetLastDayOfQuarter(x.Year, Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1])) <= endDate));
                    }
                    if (filter.SearchFilter.PeriodType == Common.GetDescription(PeriodTypeFeature.Last2Year))
                    {
                        DateTime endDate = new DateTime(toDate.Value.Year, toDate.Value.Month, 1);
                        DateTime startDate = endDate.AddMonths(-24);
                        data = data.Where(x => (Common.GetLastDayOfQuarter(x.Year, Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1])) >= startDate) && (Common.GetLastDayOfQuarter(x.Year, Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1])) <= endDate));
                    }
                    if (filter.SearchFilter.PeriodType == Common.GetDescription(PeriodTypeFeature.Last1Quarter))
                    {
                        DateTime endDate = new DateTime(toDate.Value.Year, toDate.Value.Month, 1);
                        DateTime startDate = endDate.AddMonths(-3);
                        data = data.Where(x => (Common.GetLastDayOfQuarter(x.Year, Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1])) >= startDate) && (Common.GetLastDayOfQuarter(x.Year, Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1])) <= endDate));
                    }
                    if (filter.SearchFilter.PeriodType == Common.GetDescription(PeriodTypeFeature.Last2Quarter))
                    {
                        DateTime endDate = new DateTime(toDate.Value.Year, toDate.Value.Month, 1);
                        DateTime startDate = endDate.AddMonths(-6);
                        data = data.Where(x => (Common.GetLastDayOfQuarter(x.Year, Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1])) >= startDate) && (Common.GetLastDayOfQuarter(x.Year, Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1])) <= endDate));
                    }
                    if (filter.SearchFilter.PeriodType == Common.GetDescription(PeriodTypeFeature.Last3Quarter))
                    {
                        DateTime endDate = new DateTime(toDate.Value.Year, toDate.Value.Month, 1);
                        DateTime startDate = endDate.AddMonths(-9);
                        data = data.Where(x => (Common.GetLastDayOfQuarter(x.Year, Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1])) >= startDate) && (Common.GetLastDayOfQuarter(x.Year, Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1])) <= endDate));
                    }
                    data = data.SortedResult(filter.SearchFilter.SortOrder);
                }

                var mappingData = _unitOfWork.PortfolioInvestmentKpiMappingRepository.GetMany(x => !x.IsDeleted && x.PortfolioCompanyId == filter.PortfolioCompanyID).ToList();
                var masterKPIData = _unitOfWork.M_InvestmentKPIRepository.GetMany(x => !x.IsDeleted).ToList();
                var investmentData = (from inValue in data
                                      join map in mappingData on inValue.InvestmentKpIid equals map.KpiId
                                      join masterKPI in masterKPIData on map.KpiId equals masterKPI.InvestmentKpiId
                                      select new PcInvestmentKpiQuarterlyValueModel
                                      {
                                          CreatedBy = inValue.CreatedBy,
                                          CreatedOn = inValue.CreatedOn,
                                          PCInvestmentKPIQuarterlyValueID = inValue.PCInvestmentKpiQuarterlyValueId,
                                          KPIInfo = masterKPI.KpiInfo != null ? masterKPI.KpiInfo.Trim() : masterKPI.KpiInfo,
                                          KPIActualValue = masterKPI.KpiInfo.Trim() == "Text" ? inValue.KpiActualValue : GetActVal(inValue),
                                          KPIActualValueUnit = inValue.KpiActualValueUnit,
                                          KPIBudgetValue = inValue.KpiBudgetValue,
                                          KPIBudgetValueUnit = inValue.KpiBudgetValueUnit,
                                          InvestmentKPIID = inValue.InvestmentKpIid,
                                          DisplayOrder = map.DisplayOrder,
                                          ParentKPIID = map.ParentKPIID,
                                          InvestmentKPI = new InvestmentKpiDetails
                                          {
                                              InvestmentKPIId = inValue.InvestmentKpIid,
                                              KPI = inValue.InvestmentKPI.Kpi,
                                              OrderBy = inValue.InvestmentKPI.OrderBy,
                                              IsNumeric = inValue.InvestmentKPI.IsNumeric
                                          },

                                          IsActive = inValue.IsActive ?? false,
                                          IsDeleted = inValue.IsDeleted,
                                          ModifiedBy = inValue.ModifiedBy,
                                          ModifiedOn = inValue.ModifiedOn,
                                          PortfolioCompanyID = inValue.PortfolioCompanyId,
                                          QuarterNumber = Convert.ToInt32(inValue.Quarter.Split("Q", StringSplitOptions.None)[1]),
                                          Quarter = inValue.Quarter,
                                          Year = inValue.Year,
                                          AuditLog = false

                                      }).OrderByDescending(x => x.DisplayOrder.HasValue).ThenBy(i => i.DisplayOrder).ToList();
                result.PCInvestmentKPIQuarterlyValueList = investmentData;
                return result;
            }
            catch (Exception exception)
            {
                _logger.LogError(exception.Message, exception.StackTrace);
            }
            return result;
        }

        private static string GetActVal(PCInvestmentKpiQuarterlyValue inValue)
        {
            return inValue.KpiActualValue != "" && inValue.KpiActualValue != null ? decimal.Parse(inValue.KpiActualValue).ToString("G29") : inValue.KpiActualValue;
        }
        #endregion
        #region "LP report Impact KPI Details"

        public List<LpReportImpactKpiData> GetLPReportImpactKPIColumns(int portfolioCompanyId)
        {
            List<LpReportImpactKpiData> lPReportImpactKPIDataList = new List<LpReportImpactKpiData>();
            int currentYear = DateTime.Now.Date.Year;
            int currentQuarter = DateTime.Now.Date.GetQuarter();
            var masterImpactKPIValues = _unitOfWork.M_ImpactKPIRepository.GetMany(x => !x.IsDeleted);
            var impactKPIValues = _unitOfWork.PCImpactKpiQuarterlyValueRepository.GetMany(x => !x.IsDeleted && x.PortfolioCompanyId == portfolioCompanyId);

            var impactKPIOrder = _unitOfWork.Mapping_ImpactKPI_OrderRepository.GetManyQueryable(x => !x.IsDeleted && x.PortfolioCompanyID == portfolioCompanyId).ToList();

            string startQuarter = nameof(QuarterEnum.Q1);
            string endQuarter = nameof(QuarterEnum.Q4);
            var idQtr = _globalConfig.GetValueByKey("RemoveColumnFromLPReport2020") ?? "0";
            var idAnnual = _globalConfig.GetValueByKey("RemoveColumnFromLPReport2019") ?? "0";

            var excludedIdListQtr = idQtr?.Split(',');
            var excludedIdListAnnual = (idAnnual is not null and not "0") ? idAnnual.Split(',') : null;

            var CurrentYearLastQuarterName = (excludedIdListQtr?.Contains(portfolioCompanyId.ToString()) == true) ? null : "YTD " + (currentQuarter - 1).ToString() + "Q " + (currentYear).ToString();
            var LastYearName = (excludedIdListAnnual?.Contains(portfolioCompanyId.ToString()) == true) ? null : (currentYear - 1).ToString();

            var impactKPIQtrData = (from mi in masterImpactKPIValues
                                    join ah in impactKPIValues on mi.ImpactKpiId equals ah.ImpactKpiId
                                    join o in impactKPIOrder on ah.ImpactKpiId equals o.ImpactKPIID
                                    orderby o.KPIOrder
                                    select new LpReportImpactKpiData
                                    {
                                        KPIId = mi.ImpactKpiId,
                                        KPI = mi.Kpi,
                                        Period = CurrentYearLastQuarterName,
                                        LastYearVal = ah.KpiActualValue,
                                        ParentID = mi.ParentId,
                                        KpiInfo = mi.KpiInfo,
                                        IsParent = mi.IsParent,
                                        IsHeader = mi.IsHeader
                                    }).ToList();

            var LPreportCurrentYearLastQuarterImpactKPIData = impactKPIValues.Where(x => x.Year == (currentYear) && x.Quarter.CompareTo(startQuarter) >= 0 && x.Quarter.CompareTo(endQuarter) <= 0).GroupBy(y => new { y.ImpactKpiId }).Select(g => new LpReportImpactKpiData
            {
                KPIId = g.Key.ImpactKpiId,
                KPI = g.Select(a => a.ImpactKpi.Kpi).FirstOrDefault(),
                Period = CurrentYearLastQuarterName,
                CurrentYearLastQuarterVal = g.Sum(b => Convert.ToDecimal(b.KpiActualValue)).ToString()
            }).ToList();

            var filteredKPIList = impactKPIQtrData;
            foreach (var item in filteredKPIList)
            {
                LpReportImpactKpiData lPReportImpactKPIItem = new LpReportImpactKpiData
                {
                    KPI = GetFormattedKPI(item.KPI, item.ParentID, item.IsHeader),
                    KPIId = item.KPIId,
                    Period = CurrentYearLastQuarterName,
                    KpiInfo = item.KpiInfo,
                    Value = LPreportCurrentYearLastQuarterImpactKPIData.Any() ?
                    LPreportCurrentYearLastQuarterImpactKPIData.Count(x => x.KPIId == item.KPIId) == 0 ? (dynamic)null :
                    string.IsNullOrEmpty(LPreportCurrentYearLastQuarterImpactKPIData.FirstOrDefault(x => x.KPIId == item.KPIId)?.CurrentYearLastQuarterVal) ?
                    LPreportCurrentYearLastQuarterImpactKPIData.FirstOrDefault(x => x.KPIId == item.KPIId)?.CurrentYearLastQuarterVal :
                    string.IsNullOrEmpty(LPreportCurrentYearLastQuarterImpactKPIData.FirstOrDefault(x => x.KPIId == item.KPIId)?.CurrentYearLastQuarterVal) ? item.IsParent ? (dynamic)null :
                    LPreportCurrentYearLastQuarterImpactKPIData.FirstOrDefault(x => x.KPIId == item.KPIId)?.CurrentYearLastQuarterVal :
                    LPreportCurrentYearLastQuarterImpactKPIData.FirstOrDefault(x => x.KPIId == item.KPIId)?.CurrentYearLastQuarterVal : (dynamic)null
                };
                lPReportImpactKPIDataList.Add(lPReportImpactKPIItem);
                lPReportImpactKPIItem = new LpReportImpactKpiData
                {
                    KPI = GetFormattedKPI(item.KPI, item.ParentID, item.IsHeader),
                    KPIId = item.KPIId,
                    Period = LastYearName,
                    KpiInfo = item.KpiInfo,
                    Value = (dynamic)null
                };
                lPReportImpactKPIDataList.Add(lPReportImpactKPIItem);
            }

            return lPReportImpactKPIDataList;
        }
        private string GetFormattedKPI(string KPI, int? ParentID, bool isHeader)
        {
            string resultKPI = string.Empty;
            if (isHeader && (string.IsNullOrEmpty(ParentID.ToString()) || ParentID == 0))
            {
                resultKPI = "<b>" + KPI.Trim() + "</b>";
            }
            else
            {
                resultKPI = KPI.Trim();
            }

            return resultKPI;
        }

        public PcMasterKpiValueListModel GetMasterKPIData(int PortfolioCompanyID, string ModuleName, ReportSectionWiseKpiList reportMappingKPIs)
        {

            var pCMasterKPIValueList = new PcMasterKpiValueListModel();
            int moduleId = _unitOfWork.M_KpiModulesRepository.GetFirstOrDefault(x => !x.IsDeleted && x.IsActive && x.Name == ModuleName).ModuleID;
            var masterKpiValues = _unitOfWork.M_MasterKpisRepository.GetManyQueryable(x => !x.IsDeleted && x.ModuleID == moduleId);
            SearchFilter search = new SearchFilter { PeriodType = Common.GetDescription(PeriodTypeQuarterConfig.Last1Year) };
            PcMasterKpiValueFilter filter = new PcMasterKpiValueFilter
            {
                SearchFilter = search,
            };
            var valueData = _unitOfWork.M_ValueTypesRepository.GetManyQueryable(x => !x.IsDeleted);
            var pCMasterKpiValues = _unitOfWork.PCMasterKpiValuesRepository.GetManyQueryable(x => !x.IsDeleted && x.PortfolioCompanyID == PortfolioCompanyID && x.ModuleID == moduleId);
            var mappingKpis = _unitOfWork.Mapping_KpisRepository.GetMany(x => !x.IsDeleted && x.PortfolioCompanyID == PortfolioCompanyID && x.ModuleID == moduleId).Select(x => x.Mapping_KpisID).ToList();
            pCMasterKpiValues = pCMasterKpiValues.Where(x => mappingKpis.Contains(x.MappingKpisID));
            if (ModuleName == "CreditKPI" && reportMappingKPIs.ReportSectionWiseKpiModel.Any(X => X.Section == "KPI - Credit Table"))
            {
                string Period = reportMappingKPIs?.ReportSectionWiseKpiModel?.FirstOrDefault(X => X.Section == "KPI - Credit Table")?.Period;
                if (Period != null)
                    filter.SearchFilter.PeriodType = Period;
            }
            if (ModuleName == "TradingRecords" && reportMappingKPIs.ReportSectionWiseKpiModel.Any(X => X.Section == "KPI - Trading Record Table"))
            {
                string Period = reportMappingKPIs?.ReportSectionWiseKpiModel?.FirstOrDefault(X => X.Section == "KPI - Trading Record Table")?.Period;
                if (Period != null)
                    filter.SearchFilter.PeriodType = Period;
            }
            if (filter.SearchFilter != null && pCMasterKpiValues.Any())
            {
                DateTime? toDate = DateTime.Now;
                var maxYearData = pCMasterKpiValues.Max(x => x.Year);
                var isQuarterDataExists = pCMasterKpiValues.Where(x => x.Year == maxYearData && x.Quarter != null).Count();
                var isMonthlyDataExists = pCMasterKpiValues.Where(x => x.Year == maxYearData && x.Month != null).Count();
                if (isQuarterDataExists > 0)
                {
                    var maxQuarter = pCMasterKpiValues.Where(x => x.Year == maxYearData).Max(x => Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1]));
                    toDate = Common.GetLastDayOfQuarter(maxYearData != null ? (int)maxYearData : 0, maxQuarter);
                    toDate = toDate.Value.AddMonths(1).AddDays(-1);
                    if (filter.SearchFilter.PeriodType == Common.GetDescription(PeriodTypeFeature.Last1Quarter))
                    {
                        DateTime endDate = new DateTime(toDate.Value.Year, toDate.Value.Month, 1);
                        DateTime startDate = endDate.AddMonths(-3);
                        pCMasterKpiValues = pCMasterKpiValues.Where(x => (Common.GetLastDayOfQuarter(x.Year != null ? (int)x.Year : 0, Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1])) >= startDate) && (Common.GetLastDayOfQuarter(x.Year != null ? (int)x.Year : 0, Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1])) <= endDate));
                    }
                    if (filter.SearchFilter.PeriodType == Common.GetDescription(PeriodTypeFeature.Last2Quarter))
                    {
                        DateTime endDate = new DateTime(toDate.Value.Year, toDate.Value.Month, 1);
                        DateTime startDate = endDate.AddMonths(-6);
                        pCMasterKpiValues = pCMasterKpiValues.Where(x => (Common.GetLastDayOfQuarter(x.Year != null ? (int)x.Year : 0, Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1])) >= startDate) && (Common.GetLastDayOfQuarter(x.Year != null ? (int)x.Year : 0, Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1])) <= endDate));
                    }
                    if (filter.SearchFilter.PeriodType == Common.GetDescription(PeriodTypeFeature.Last3Quarter))
                    {
                        DateTime endDate = new DateTime(toDate.Value.Year, toDate.Value.Month, 1);
                        DateTime startDate = endDate.AddMonths(-9);
                        pCMasterKpiValues = pCMasterKpiValues.Where(x => (Common.GetLastDayOfQuarter(x.Year != null ? (int)x.Year : 0, Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1])) >= startDate) && (Common.GetLastDayOfQuarter(x.Year != null ? (int)x.Year : 0, Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1])) <= endDate));
                    }
                    if (filter.SearchFilter.PeriodType == Common.GetDescription(PeriodTypeFeature.Last1Year) || filter.SearchFilter.PeriodType == Common.GetDescription(PeriodTypeFeature.Last4Quarter))
                    {
                        DateTime endDate = new DateTime(toDate.Value.Year, toDate.Value.Month, 1);
                        DateTime startDate = endDate.AddMonths(-12);
                        pCMasterKpiValues = pCMasterKpiValues.Where(x => (Common.GetLastDayOfQuarter(x.Year != null ? (int)x.Year : 0, Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1])) >= startDate) && (Common.GetLastDayOfQuarter(x.Year != null ? (int)x.Year : 0, Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1])) <= endDate));
                    }
                    if (filter.SearchFilter.PeriodType == Common.GetDescription(PeriodTypeFeature.Last2Year))
                    {
                        DateTime endDate = new DateTime(toDate.Value.Year, toDate.Value.Month, 1);
                        DateTime startDate = endDate.AddMonths(-24);
                        pCMasterKpiValues = pCMasterKpiValues.Where(x => (Common.GetLastDayOfQuarter(x.Year != null ? (int)x.Year : 0, Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1])) >= startDate) && (Common.GetLastDayOfQuarter(x.Year != null ? (int)x.Year : 0, Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1])) <= endDate));
                    }
                }
                else if (isMonthlyDataExists > 0)
                {
                    var maxMonth = pCMasterKpiValues.Where(x => x.Year == maxYearData).Max(x => x.Month);
                    toDate = new DateTime((int)maxYearData, (int)maxMonth, 1);
                    toDate = toDate.Value.AddMonths(1).AddDays(-1);
                    int defaultyear = -12;
                    if (filter.SearchFilter.PeriodType == Common.GetDescription(PeriodTypeFeature.Last3Month))
                        defaultyear = -3;
                    if (filter.SearchFilter.PeriodType == Common.GetDescription(PeriodTypeFeature.Last6Month))
                        defaultyear = -6;
                    if (filter.SearchFilter.PeriodType == Common.GetDescription(PeriodTypeFeature.Last9Month))
                        defaultyear = -9;
                    if (filter.SearchFilter.PeriodType == Common.GetDescription(PeriodTypeFeature.Last2Year))
                        defaultyear = -24;
                    pCMasterKpiValues = pCMasterKpiValues.Where(x => (Convert.ToDateTime(x.Month.ToString() + "/" + x.Year.ToString()) >= toDate.Value.AddMonths(defaultyear)));
                }

            }
            var mappingData = _unitOfWork.Mapping_KpisRepository.GetMany(x => !x.IsDeleted && x.PortfolioCompanyID == PortfolioCompanyID && x.ModuleID == moduleId);
            var data = (from pcValue in pCMasterKpiValues
                        join map in mappingData on pcValue.MappingKpisID equals map.Mapping_KpisID into kc
                        from ckpi in kc.DefaultIfEmpty()
                        join p in masterKpiValues on ckpi.KpiID equals p.MasterKpiID into kp
                        from pkpi in kp.DefaultIfEmpty()
                        join val in valueData on pcValue.ValueTypeID equals val.ValueTypeID
                        select new PcMasterKpiValueModel
                        {
                            PCMasterKpiValueID = (pcValue == null || pcValue.PCMasterKpiValueID == 0) ? 0 : pcValue.PCMasterKpiValueID,
                            PortfolioCompanyID = (pcValue == null || pcValue.PortfolioCompanyID == 0) ? 0 : pcValue.PortfolioCompanyID,
                            KPIInfo = (pkpi == null || pkpi.KpiInfo == null || pkpi.KpiInfo.Trim() == string.Empty) ? string.Empty : pkpi.KpiInfo.Trim(),
                            KPI = (pkpi == null || pkpi.KPI == null) ? string.Empty : FormatIfNumInfo(pkpi),
                            KPIValue = (pcValue == null || pcValue.KPIValue == "0") ? "0" : pcValue.KPIValue,
                            Month = pcValue.Month,
                            Quarter = pcValue.Quarter,
                            QuarterNumber = pcValue.Quarter != null ? Convert.ToInt32(pcValue.Quarter.Split("Q", StringSplitOptions.None)[1]) : 0,
                            Year = (pcValue == null || pcValue.Year == 0) ? 0 : pcValue.Year,
                            Half = (pcValue == null || pcValue.Half == 0) ? 0 : pcValue.Half,
                            MasterKpiID = (pkpi == null || pkpi.MasterKpiID == 0) ? 0 : pkpi.MasterKpiID,
                            ParentKPIID = (ckpi == null || ckpi.ParentKPIID == null) ? 0 : ckpi.ParentKPIID,
                            DisplayOrder = (ckpi == null || ckpi.DisplayOrder == null) ? 0 : ckpi.DisplayOrder,
                            ParentKPI = masterKpiValues.Where(x => x.MasterKpiID == ckpi.ParentKPIID).Select(x => x.KPI).FirstOrDefault(),
                            IsNumeric = pcValue.IsNumeric,
                            IsYTD = pcValue.IsYTD,
                            MappingKpisID = ckpi.Mapping_KpisID,
                            Header = val.HeaderValue,
                            AuditLog = _unitOfWork.MasterKpiAuditLogRepository.ExistsAny(s => s.ModuleId == moduleId && s.AttributeId == pcValue.PCMasterKpiValueID && !s.IsDeleted),
                            IsHeader = pkpi.IsHeader,
                            IsBoldKPI = pkpi.IsBoldKPI
                        }).OrderByDescending(x => x.DisplayOrder.HasValue).ThenBy(i => i.DisplayOrder).ToList();
            var parentKpiIdList = data.Where(x => x.ParentKPI != null).Select(x => x.ParentKPIID).ToList();

            foreach (var item in data)
            {
                if (parentKpiIdList.Contains(item.MasterKpiID))
                {
                    item.IsParent = true;
                }
            }
            if (ModuleName == "CreditKPI" && reportMappingKPIs.ReportSectionWiseKpiModel.Any(X => X.Section == "KPI - Credit Table"))
            {
                List<string> mappedInvestmentKpIid = reportMappingKPIs?.ReportSectionWiseKpiModel?.FirstOrDefault(X => X.Section == "KPI - Credit Table")?.KPIId.ToList();
                data = data.Where(x => mappedInvestmentKpIid.Contains(x.MasterKpiID.ToString())).ToList();
            }
            if (ModuleName == "TradingRecords" && reportMappingKPIs.ReportSectionWiseKpiModel.Any(X => X.Section == "KPI - Trading Record Table"))
            {
                List<string> mappedInvestmentKpIid = reportMappingKPIs?.ReportSectionWiseKpiModel?.FirstOrDefault(X => X.Section == "KPI - Trading Record Table")?.KPIId.ToList();
                data = data.Where(x => mappedInvestmentKpIid.Contains(x.MasterKpiID.ToString())).ToList();
            }
            pCMasterKPIValueList.PCMasterKPIValueModels = data;
            return pCMasterKPIValueList;
        }

        private static string FormatIfNumInfo(M_MasterKpis pkpi)
        {
            return pkpi.KpiInfo != "#" ? pkpi.KPI : pkpi.KPI + " (" + pkpi.KpiInfo + ")";
        }

        #endregion "LP report Impact KPI Details"
        #region OperationalKpi
        public PortfolioCompanyOperationalKpiQuarterListTranposeModel GetPortfolioOperationalKPIValuesLPReport(PortfolioCompanyOperationalKpiQuarterFilter filter, ReportSectionWiseKpiList reportMappingKPIs)
        {
            PortfolioCompanyOperationalKpiQuarterListModel result = GetPortfolioOperationalKPIValues(filter, reportMappingKPIs);
            PortfolioCompanyOperationalKpiQuarterListTranposeModel objTable = new PortfolioCompanyOperationalKpiQuarterListTranposeModel();
            DataTable dtResult = new DataTable();
            if (result?.PortfolioCompanyOperationalKPIQuarterList?.Any() == true)
            {
                result.PortfolioCompanyOperationalKPIQuarterList = result.PortfolioCompanyOperationalKPIQuarterList.OrderBy(x => x.Year).ThenBy(x => x.Quarter).ToList();

                string KPI = "KPI";
                string KPIInfo = "KPI Info";
                DataColumn dcKPI = new DataColumn(KPI, typeof(string));
                DataColumn dcKPIInfo = new DataColumn(KPIInfo, typeof(string));
                dcKPI.DefaultValue = "";
                dcKPIInfo.DefaultValue = "";
                dtResult.Columns.Add(dcKPI);
                foreach (PortfolioCompanyOperationalKpiQuarterModel PCQuarterList in result.PortfolioCompanyOperationalKPIQuarterList)
                {
                    DataColumn dcKPIValue = new DataColumn(PCQuarterList.Quarter + " " + PCQuarterList.Year, typeof(string));
                    dcKPIValue.DefaultValue = 0;
                    if (!dtResult.Columns.Contains(dcKPIValue.ToString()))
                    {
                        dtResult.Columns.Add(dcKPIValue);
                    }
                }

                foreach (PortfolioCompanyOperationalKpiQuarterModel PCQuarterList in result.PortfolioCompanyOperationalKPIQuarterList)
                {
                    foreach (var PCKPIValue in PCQuarterList.PortfolioCompanyOperationalKPIValues)
                    {

                        bool isKPIExists = false;
                        DataRow drKPI = null;
                        if (dtResult.Rows.Count > 0)
                        {
                            DataRow[] drKPIList = dtResult.Select($"{KPI}='{PCKPIValue.SectorwiseOperationalKPI.KPI.Replace("'", "''")}'");
                            if (drKPIList.Length > 0)
                            {
                                drKPI = drKPIList[0];
                                isKPIExists = true;
                            }
                        }

                        if (isKPIExists)
                        {
                            drKPI[PCQuarterList.Quarter + " " + PCQuarterList.Year] = !string.IsNullOrEmpty(PCKPIValue.KPIValue.ToString()) ? ConversionKMB(Convert.ToDecimal(PCKPIValue.KPIValue)).ToString() : PCKPIValue.KPIValue;
                            dtResult.AcceptChanges();
                        }
                        else
                        {
                            DataRow dr = dtResult.NewRow();
                            dr[KPI] = PCKPIValue.SectorwiseOperationalKPI.KPI;
                            dr[PCQuarterList.Quarter + " " + PCQuarterList.Year] = !string.IsNullOrEmpty(PCKPIValue.KPIValue.ToString()) ? ConversionKMB(Convert.ToDecimal(PCKPIValue.KPIValue)).ToString() : PCKPIValue.KPIValue;
                            dtResult.Rows.Add(dr);
                        }

                    }
                }
            }
            objTable.PortfolioCompanyOperationalKPIQuarterList = result?.PortfolioCompanyOperationalKPIQuarterList;
            objTable.PortfolioCompanyOperationalKPIQuarterDataTable = dtResult;
            objTable.PortfolioOperationalKPIQuarterDataTable = dtResult;
            return objTable;
        }
        private static string ConversionKMB(decimal? num)
        {
            if (num > ********* || num < -*********)

                return num?.ToString("0,,,.###B", CultureInfo.InvariantCulture);

            else if (num > 999999 || num < -999999)
                return num?.ToString("0,,.##M", CultureInfo.InvariantCulture);
            else if (num > 999 || num < -999)
                return num?.ToString("0,.#K", CultureInfo.InvariantCulture);
            else
                return num?.ToString("#");
        }
        public PortfolioCompanyOperationalKpiQuarterListModel GetPortfolioOperationalKPIValues(PortfolioCompanyOperationalKpiQuarterFilter filter, ReportSectionWiseKpiList reportMappingKPIs)
        {
            var result = new PortfolioCompanyOperationalKpiQuarterListModel();
            var data = _unitOfWork.PortfolioCompanyOperationalKPIQuarterRepository.GetManyQueryable(x => !x.IsDeleted);
            List<int> mapping = new();
            List<string> mappedKpIid = new();
            if (filter != null)
            {
                data = data.Where(x => x.PortfolioCompanyID == filter.PortfolioCompanyID);
                var sectorId = _unitOfWork.PortfolioCompanyDetailRepository.GetFirstOrDefault(x => !x.IsDeleted && x.PortfolioCompanyId == filter.PortfolioCompanyID).SectorId;
                var masterKPI = _unitOfWork.M_SectorwiseKPIRepository.GetMany(x => !x.IsDeleted && x.SectorId == sectorId).ToList();
                var mappedData = _unitOfWork.MappingPortfolioOperationalKpi_OrderRepository.GetMany(x => x.PortfolioCompanyId == filter.PortfolioCompanyID && !x.IsDeleted).ToList();
                var mappedKPI = (from mapped in mappedData
                                 join master in masterKPI
                                 on mapped.KpiId equals master.SectorwiseOperationalKPIID
                                 select new
                                 {
                                     KpiId = mapped.KpiId,
                                     DisplayOrder = mapped?.DisplayOrder,
                                     SectorId = master.SectorId
                                 }).Where(x => x.SectorId == sectorId).ToList();
                mapping = mappedKPI.Select(x => x.KpiId).ToList();
                if (reportMappingKPIs.ReportSectionWiseKpiModel.Any(X => X.Section == "KPI - Operational Table"))
                {
                    string Period = reportMappingKPIs.ReportSectionWiseKpiModel.FirstOrDefault(X => X.Section == "KPI - Operational Table").Period;
                    mappedKpIid = reportMappingKPIs.ReportSectionWiseKpiModel.FirstOrDefault(X => X.Section == "KPI - Operational Table").KPIId.ToList();
                    if (Period != null)
                        filter.SearchFilter.PeriodType = Period;
                    mapping = mapping.Where(x => mappedKpIid.Contains(x.ToString())).ToList();
                }
                if (filter.SearchFilter != null && data.Any())
                {
                    DateTime? toDate = null;
                    var maxYearData = data.Max(x => x.Year);
                    var maxQuarter = data.Where(x => x.Year == maxYearData).Max(x => Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1]));

                    toDate = Common.GetLastDayOfQuarter(maxYearData, maxQuarter);
                    toDate = toDate.Value.AddMonths(1).AddDays(-1);
                    if (toDate == null)
                        toDate = DateTime.Now;

                    if (filter.SearchFilter.PeriodType == Common.GetDescription(PeriodTypeQuarterConfig.CurrentQuarter))
                    {

                        int currentQtr = DateTime.Today.GetQuarter();
                        int currentYear = DateTime.Now.Year;
                        data = data.Where(x => (x.Quarter == "Q" + currentQtr) && (x.Year == currentYear));
                    }

                    if (filter.SearchFilter.PeriodType == Common.GetDescription(PeriodTypeQuarterConfig.LastQuarter))
                    {

                        DateTime startDate = new DateTime(toDate.Value.Year, toDate.Value.Month, 1);

                        int lastQtr = Common.PreviousQuarter(startDate);
                        int qtrYear = Common.GetYearOfQuarter(startDate, lastQtr);

                        data = data.Where(x => (x.Quarter == "Q" + lastQtr) && (x.Year == qtrYear));
                    }
                    if (filter.SearchFilter.PeriodType == Common.GetDescription(PeriodTypeFeature.Last1Year) || filter.SearchFilter.PeriodType == Common.GetDescription(PeriodTypeFeature.Last4Quarter))
                    {

                        DateTime endDate = new DateTime(toDate.Value.Year, toDate.Value.Month, 1);

                        DateTime startDate = endDate.AddMonths(-12);
                        data = data.Where(x => (Common.GetLastDayOfQuarter(x.Year, Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1])) >= startDate) && (Common.GetLastDayOfQuarter(x.Year, Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1])) <= endDate));
                    }
                    if (filter.SearchFilter.PeriodType == Common.GetDescription(PeriodTypeFeature.Last2Year))
                    {

                        DateTime endDate = new DateTime(toDate.Value.Year, toDate.Value.Month, 1);

                        DateTime startDate = endDate.AddMonths(-24);

                        data = data.Where(x => (Common.GetLastDayOfQuarter(x.Year, Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1])) >= startDate) && (Common.GetLastDayOfQuarter(x.Year, Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1])) <= endDate));
                    }
                    if (filter.SearchFilter.PeriodType == Common.GetDescription(PeriodTypeFeature.Last2Quarter))
                    {
                        DateTime endDate = new DateTime(toDate.Value.Year, toDate.Value.Month, 1);
                        DateTime startDate = endDate.AddMonths(-6);
                        data = data.Where(x => (Common.GetLastDayOfQuarter(x.Year, Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1])) >= startDate) && (Common.GetLastDayOfQuarter(x.Year, Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1])) <= endDate));
                    }
                    if (filter.SearchFilter.PeriodType == Common.GetDescription(PeriodTypeFeature.Last1Quarter))
                    {
                        DateTime endDate = new DateTime(toDate.Value.Year, toDate.Value.Month, 1);
                        DateTime startDate = endDate.AddMonths(-3);
                        data = data.Where(x => (Common.GetLastDayOfQuarter(x.Year, Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1])) >= startDate) && (Common.GetLastDayOfQuarter(x.Year, Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1])) <= endDate));
                    }
                    if (filter.SearchFilter.PeriodType == Common.GetDescription(PeriodTypeFeature.Last3Quarter))
                    {
                        DateTime endDate = new DateTime(toDate.Value.Year, toDate.Value.Month, 1);
                        DateTime startDate = endDate.AddMonths(-9);
                        data = data.Where(x => (Common.GetLastDayOfQuarter(x.Year, Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1])) >= startDate) && (Common.GetLastDayOfQuarter(x.Year, Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1])) <= endDate));
                    }
                }
                result.PortfolioCompanyOperationalKPIQuarterList = data.Select(x => new PortfolioCompanyOperationalKpiQuarterModel
                {
                    CreatedBy = x.CreatedBy,
                    CreatedOn = x.CreatedOn,
                    PortfolioCompanyOperationalKPIQuarterID = x.PortfolioCompanyOperationalKPIQuarterID,
                    PortfolioCompanyOperationalKPIValues = x.PortfolioCompanyOperationalKPIValues.Where(y => !y.IsDeleted && mapping.Contains(y.SectorwiseOperationalKPIID)).Select(y => new PortfolioCompanyOperationalKpiValueModel
                    {
                        KPIInfo = y.M_SectorwiseKPI.KpiInfo,
                        KPIValue = null,
                        SectorwiseOperationalKPIID = y.SectorwiseOperationalKPIID,
                        PortfolioCompanyOperationalKPIValueID = y.PortfolioCompanyOperationalKPIValueID,
                        SectorwiseOperationalKPI = new SectorwiseKpiDetails
                        {
                            SectorID = y.M_SectorwiseKPI.SectorId,
                            KPI = y.M_SectorwiseKPI.Kpi,
                            SectorwiseKPIID = y.SectorwiseOperationalKPIID
                        },
                        ParentId = mappedData.Where(x => x.KpiId == y.SectorwiseOperationalKPIID).Select(x => x.ParentKPIID).FirstOrDefault(),
                        DisplayOrder = mappedData.Where(x => x.KpiId == y.SectorwiseOperationalKPIID).Select(x => x.DisplayOrder).FirstOrDefault()

                    }).OrderByDescending(x => x.DisplayOrder.HasValue).OrderBy(x => x.DisplayOrder).ToList(),
                    IsActive = x.IsActive,
                    IsDeleted = x.IsDeleted,
                    ModifiedBy = x.ModifiedBy,
                    ModifiedOn = x.ModifiedOn,
                    PortfolioCompanyID = x.PortfolioCompanyID,
                    Quarter = x.Quarter,
                    Year = x.Year

                }).ToList();

            }
            return result;

        }
        #endregion
        #region CompanyKPI
        public DataSet GetCompanyKPI(PcCompanyKpiMonthlyValueFilter filter, ReportSectionWiseKpiList reportMappingKPIs)
        {
            List<PcCompanyKpiMonthlyValueTreeListModel> parentModelList = new();
            var KPIMonthlyValue = _unitOfWork.PCCompanyKpiMonthlyValueRepository.GetManyQueryable(x => (!x.IsDeleted || !x.IsBudgetDeleted) && x.IsActive == true && x.PortfolioCompanyId == filter.PortfolioCompanyID).AsQueryable().ToList();
            var companyKPI = _unitOfWork.M_CompanyKPIRepository.GetManyQueryable(x => !x.IsDeleted).AsQueryable().ToList();
            var MappingPortfolioCompanyKPIValue = _unitOfWork.PortfolioCompanyKpiMappingRepository.GetManyQueryable(x => x.PortfolioCompanyId == filter.PortfolioCompanyID && !x.IsDeleted).ToList();
            var companyKPIMonthlyValue = (from V in KPIMonthlyValue join M in MappingPortfolioCompanyKPIValue on V.CompanyKpiId equals M.KpiId select V).AsQueryable().ToList();
            if (reportMappingKPIs.ReportSectionWiseKpiModel.Any(X => X.Section == "KPI - Company Table"))
            {
                string Period = reportMappingKPIs.ReportSectionWiseKpiModel.FirstOrDefault(X => X.Section == "KPI - Company Table").Period;
                List<string> mappedInvestmentKpIid = reportMappingKPIs.ReportSectionWiseKpiModel.FirstOrDefault(X => X.Section == "KPI - Company Table").KPIId.ToList();
                if (Period != null)
                    filter.SearchFilter.PeriodType = Period;
                companyKPIMonthlyValue = companyKPIMonthlyValue.Where(x => mappedInvestmentKpIid.Contains(x.CompanyKpiId.ToString())).ToList();
            }
            if (companyKPIMonthlyValue.Any())
            {
                if (filter.SearchFilter != null)
                {
                    DateTime? toDate = null;
                    var maxYearData = companyKPIMonthlyValue.Max(x => x.Year);
                    var maxMonth = companyKPIMonthlyValue.Where(x => x.Year == maxYearData).Max(x => x.Month);
                    toDate = new DateTime(maxYearData, (int)maxMonth, 1);
                    toDate = toDate.Value.AddMonths(1).AddDays(-1);
                    if (filter.SearchFilter.PeriodType == Common.GetDescription(PeriodTypeFeature.LastQuarter))
                    {
                        DateTime startDate = new DateTime(toDate.Value.Year, toDate.Value.Month, 1);
                        int lastQtr = Common.PreviousQuarter(startDate);
                        int lastMonthQtr = Common.GetLastMonthOfQuarter(lastQtr);
                        int firstMonthQtr = Common.GetFirstMonthOfQuarter(lastQtr);
                        int qtrYear = Common.GetYearOfQuarter(startDate, lastQtr);
                        companyKPIMonthlyValue = companyKPIMonthlyValue.Where(x => (Convert.ToInt32(x.Month) >= firstMonthQtr && Convert.ToInt32(x.Month) <= lastMonthQtr) && (x.Year == qtrYear)).ToList();
                    }
                    if (filter.SearchFilter.PeriodType == Common.GetDescription(PeriodTypeFeature.Last1Year))
                    {
                        companyKPIMonthlyValue = companyKPIMonthlyValue.Where(x => (Convert.ToDateTime(x.Month.ToString() + "/" + x.Year.ToString()) >= toDate.Value.AddMonths(-12))).ToList();
                    }
                    if (filter.SearchFilter.PeriodType == Common.GetDescription(PeriodTypeFeature.Last2Year))
                    {
                        companyKPIMonthlyValue = companyKPIMonthlyValue.Where(x => (Convert.ToDateTime(x.Month.ToString() + "/" + x.Year.ToString()) >= toDate.Value.AddMonths(-24))).ToList();
                    }
                    if (filter.SearchFilter.PeriodType == Common.GetDescription(PeriodTypeFeature.Last3Month))
                    {
                        companyKPIMonthlyValue = companyKPIMonthlyValue.Where(x => (Convert.ToDateTime(x.Month.ToString() + "/" + x.Year.ToString()) >= toDate.Value.AddMonths(-3))).ToList();
                    }
                    if (filter.SearchFilter.PeriodType == Common.GetDescription(PeriodTypeFeature.Last6Month))
                    {
                        companyKPIMonthlyValue = companyKPIMonthlyValue.Where(x => (Convert.ToDateTime(x.Month.ToString() + "/" + x.Year.ToString()) >= toDate.Value.AddMonths(-6))).ToList();
                    }
                    if (filter.SearchFilter.PeriodType == Common.GetDescription(PeriodTypeFeature.Last9Month))
                    {
                        companyKPIMonthlyValue = companyKPIMonthlyValue.Where(x => (Convert.ToDateTime(x.Month.ToString() + "/" + x.Year.ToString()) >= toDate.Value.AddMonths(-9))).ToList();
                    }
                    companyKPIMonthlyValue = companyKPIMonthlyValue.AsQueryable().SortedResult(filter.SearchFilter.SortOrder).ToList();
                }
                var query = (from kpiVal in companyKPIMonthlyValue
                             join c in companyKPI on kpiVal.CompanyKpiId equals c.CompanywiseKPIID into kc
                             from ckpi in kc.DefaultIfEmpty()
                             join p in companyKPI on ckpi?.ParentKpiId equals p.CompanywiseKPIID into kp
                             from pkpi in kp.DefaultIfEmpty()
                             join mapkpi in MappingPortfolioCompanyKPIValue on kpiVal.CompanyKpiId equals mapkpi.KpiId
                             select new PcCompanyKpiMonthlyValueTreeListModel
                             {
                                 PcCompanyKpiMonthlyValueModel = new PcKpisValueModel
                                 {
                                     PortfolioCompanyID = kpiVal == null || kpiVal.PortfolioCompanyId == 0 ? 0 : kpiVal.PortfolioCompanyId,
                                     KPIInfo = kpiVal == null || (kpiVal.KpiInfo == null || kpiVal.KpiInfo.Trim() == string.Empty) ? string.Empty : kpiVal.KpiInfo.Trim(),
                                     KPIActualValue = kpiVal == null || Convert.ToDecimal(kpiVal.KpiActualValue) == 0 ? 0 : ValidateVal(kpiVal),
                                     KPIActualValueStr = kpiVal.IsDeleted ? null : Convert.ToString(kpiVal.KpiActualValue).TrimEnd('0').TrimEnd('.'),
                                     KPIActualValueUnit = kpiVal == null || (kpiVal.KpiActualValueUnit == null || kpiVal.KpiActualValueUnit.Trim() == string.Empty) ? string.Empty : kpiVal.KpiActualValueUnit,
                                     KPIBudgetValue = kpiVal == null || Convert.ToDecimal(kpiVal.KpiBudgetValue) == 0 ? 0 : ValidateBudVal(kpiVal),
                                     KPIBudgetValueStr = kpiVal.IsBudgetDeleted ? null : Convert.ToString(kpiVal.KpiBudgetValue).TrimEnd('0').TrimEnd('.'),
                                     KPIBudgetValueUnit = kpiVal == null || (kpiVal.KpiBudgetValueUnit == null || kpiVal.KpiBudgetValueUnit.Trim() == string.Empty) ? string.Empty : kpiVal.KpiBudgetValueUnit,
                                     SegmentType = ckpi == null || (ckpi.SegmentType == null || ckpi.SegmentType.Trim() == string.Empty) ? string.Empty : ckpi.SegmentType,
                                     Month = kpiVal.Month,
                                     Year = kpiVal == null || kpiVal.Year == 0 ? 0 : kpiVal.Year,
                                     CompanyKPIID = kpiVal == null || kpiVal.CompanyKpiId == 0 ? 0 : kpiVal.CompanyKpiId,
                                     KPI = ckpi == null || ckpi.Kpi == null ? string.Empty : ckpi.Kpi,
                                     ParentKPIID = mapkpi == null || mapkpi.ParentKPIID == null ? 0 : mapkpi.ParentKPIID,
                                     DisplayOrder = mapkpi == null || mapkpi.DisplayOrder == null ? 0 : mapkpi.DisplayOrder,
                                     ParentKPI = companyKPI.Where(x => x.CompanywiseKPIID == mapkpi.ParentKPIID).Select(x => x.Kpi).FirstOrDefault(),
                                     PCCompanyKPIMonthlyValueID = kpiVal == null || kpiVal.PCCompanyKpiMonthlyValueId == 0 ? 0 : kpiVal.PCCompanyKpiMonthlyValueId,
                                     IsActive = !(!(kpiVal == null || kpiVal.CompanyKpiId == 0) && kpiVal.IsActive == null) && (kpiVal.IsActive == true) ? true : false,
                                     IsDeleted = kpiVal == null || kpiVal.CompanyKpiId == 0 || kpiVal.IsDeleted,
                                     IsBudgetDeleted = kpiVal == null || kpiVal.CompanyKpiId == 0 || kpiVal.IsBudgetDeleted
                                 }

                             });
                var data = query.AsQueryable();
                var parentKPIData = data.Where(x => x.PcCompanyKpiMonthlyValueModel.ParentKPIID == 0 && x.PcCompanyKpiMonthlyValueModel.ParentKPIID != null).OrderByDescending(i => i.PcCompanyKpiMonthlyValueModel.DisplayOrder.HasValue).ThenBy(i => i.PcCompanyKpiMonthlyValueModel.DisplayOrder).ToList();
                var childKPIData = data.Where(x => x.PcCompanyKpiMonthlyValueModel.ParentKPIID != 0 && x.PcCompanyKpiMonthlyValueModel.ParentKPIID != null).OrderByDescending(i => i.PcCompanyKpiMonthlyValueModel.DisplayOrder.HasValue).ThenBy(i => i.PcCompanyKpiMonthlyValueModel.DisplayOrder).ToList();
                List<PcKpisValueModel> parentKPIWithNullValue = new List<PcKpisValueModel>();
                bool parentKPIIdFound = false;
                foreach (var child in childKPIData)
                {
                    parentKPIIdFound = false;
                    foreach (var parent in parentKPIData)
                    {
                        if (child.PcCompanyKpiMonthlyValueModel.ParentKPIID == parent.PcCompanyKpiMonthlyValueModel.CompanyKPIID)
                            parentKPIIdFound = true;
                    }
                    if (!parentKPIIdFound)
                    {
                        PcKpisValueModel companyData = PcCompanyKpiHelper.ConvertCompanyKPIChild(child.PcCompanyKpiMonthlyValueModel);
                        parentKPIWithNullValue.Add(companyData);
                    }
                }
                foreach (var parentKPI in parentKPIData)
                {
                    PcCompanyKpiMonthlyValueTreeListModel parentModel = new PcCompanyKpiMonthlyValueTreeListModel
                    {
                        PcCompanyKpiMonthlyValueModel = new PcKpisValueModel()
                    };
                    parentModel.PcCompanyKpiMonthlyValueModel.ParentKPIID = parentKPI.PcCompanyKpiMonthlyValueModel.ParentKPIID;
                    parentModel.PcCompanyKpiMonthlyValueModel.ParentKPI = parentKPI.PcCompanyKpiMonthlyValueModel.ParentKPI;
                    parentModel.PcCompanyKpiMonthlyValueModel.CompanyKPIID = parentKPI.PcCompanyKpiMonthlyValueModel.CompanyKPIID;
                    parentModel.PcCompanyKpiMonthlyValueModel.KPI = parentKPI.PcCompanyKpiMonthlyValueModel.KPI;
                    parentModel.PcCompanyKpiMonthlyValueModel.SegmentType = parentKPI.PcCompanyKpiMonthlyValueModel.SegmentType;
                    parentModel.PcCompanyKpiMonthlyValueModel.KPIActualValue = parentKPI.PcCompanyKpiMonthlyValueModel.IsDeleted ? null : parentKPI.PcCompanyKpiMonthlyValueModel.KPIActualValue;
                    parentModel.PcCompanyKpiMonthlyValueModel.KPIActualValueStr = parentKPI.PcCompanyKpiMonthlyValueModel.IsDeleted ? null : Convert.ToString(parentKPI.PcCompanyKpiMonthlyValueModel.KPIActualValue).TrimEnd('0').TrimEnd('.');
                    parentModel.PcCompanyKpiMonthlyValueModel.KPIActualValueUnit = parentKPI.PcCompanyKpiMonthlyValueModel.KPIActualValueUnit;
                    parentModel.PcCompanyKpiMonthlyValueModel.KPIBudgetValue = parentKPI.PcCompanyKpiMonthlyValueModel.IsBudgetDeleted ? null : parentKPI.PcCompanyKpiMonthlyValueModel.KPIBudgetValue;
                    parentModel.PcCompanyKpiMonthlyValueModel.KPIBudgetValueStr = parentKPI.PcCompanyKpiMonthlyValueModel.IsBudgetDeleted ? null : Convert.ToString(parentKPI.PcCompanyKpiMonthlyValueModel.KPIBudgetValue).TrimEnd('0').TrimEnd('.');
                    parentModel.PcCompanyKpiMonthlyValueModel.KPIBudgetValueUnit = parentKPI.PcCompanyKpiMonthlyValueModel.KPIBudgetValueUnit;
                    parentModel.PcCompanyKpiMonthlyValueModel.KPIInfo = parentKPI.PcCompanyKpiMonthlyValueModel.KPIInfo;
                    parentModel.PcCompanyKpiMonthlyValueModel.Year = parentKPI.PcCompanyKpiMonthlyValueModel.Year;
                    parentModel.PcCompanyKpiMonthlyValueModel.Month = parentKPI.PcCompanyKpiMonthlyValueModel.Month;
                    parentModel.PcCompanyKpiMonthlyValueModel.PCCompanyKPIMonthlyValueID = parentKPI.PcCompanyKpiMonthlyValueModel.PCCompanyKPIMonthlyValueID;
                    parentModel.PcCompanyKpiMonthlyValueModel.PortfolioCompanyID = parentKPI.PcCompanyKpiMonthlyValueModel.PortfolioCompanyID;
                    parentModel.PcCompanyKpiMonthlyValueModel.IsActive = parentKPI.PcCompanyKpiMonthlyValueModel.IsActive;
                    parentModel.PcCompanyKpiMonthlyValueModel.IsDeleted = parentKPI.PcCompanyKpiMonthlyValueModel.IsDeleted;
                    parentModel.PcCompanyKpiMonthlyValueModel.IsBudgetDeleted = parentKPI.PcCompanyKpiMonthlyValueModel.IsBudgetDeleted;
                    foreach (var childKPI in childKPIData)
                    {
                        if (parentKPI.PcCompanyKpiMonthlyValueModel.CompanyKPIID == childKPI.PcCompanyKpiMonthlyValueModel.ParentKPIID)
                        {

                            PcKpisValueModel childCompanyKPIMonthlyValueModel = PcCompanyKpiHelper.ConvertCompanyKPIChild(childKPI.PcCompanyKpiMonthlyValueModel);
                            parentModel.ChildPCCompanyKPIMonthlyValueList.Add(childCompanyKPIMonthlyValueModel);
                        }
                    }
                    parentModelList.Add(parentModel);
                }
                foreach (var item in parentKPIWithNullValue)
                {
                    PcCompanyKpiMonthlyValueTreeListModel parentModel = new PcCompanyKpiMonthlyValueTreeListModel
                    {
                        PcCompanyKpiMonthlyValueModel = new PcKpisValueModel
                        {
                            ParentKPIID = null,
                            ParentKPI = null,
                            CompanyKPIID = Convert.ToInt32(item.ParentKPIID),
                            KPI = item.ParentKPI,
                            KPIActualValue = 0,
                            KPIActualValueUnit = null,
                            KPIBudgetValue = 0,
                            KPIBudgetValueUnit = null,
                            KPIInfo = null,
                            SegmentType = item.SegmentType,
                            Year = 0,
                            Month = 0,
                            PortfolioCompanyID = item.PortfolioCompanyID,
                            PCCompanyKPIMonthlyValueID = item.PCCompanyKPIMonthlyValueID
                        }
                    };
                    PcKpisValueModel childCompanyKPIMonthlyValueModel = PcCompanyKpiHelper.ConvertCompanyKPIChild(item);
                    parentModel.ChildPCCompanyKPIMonthlyValueList.Add(childCompanyKPIMonthlyValueModel);
                    parentModelList.Add(parentModel);
                }
            }
            return ConvertCompanyKPIListTODataset(parentModelList);
        }

        private static decimal? ValidateBudVal(PCCompanyKpiMonthlyValue kpiVal)
        {
            return kpiVal.IsBudgetDeleted ? null : global::System.Convert.ToDecimal(kpiVal.KpiBudgetValue);
        }

        private static decimal? ValidateVal(PCCompanyKpiMonthlyValue kpiVal)
        {
            return kpiVal.IsDeleted ? null : Convert.ToDecimal(kpiVal.KpiActualValue);
        }

        public DataSet ConvertCompanyKPIListTODataset(List<PcCompanyKpiMonthlyValueTreeListModel> parentModelList)
        {
            DataSet ds = new();
            if (parentModelList != null)
            {
                DataTable dt = new DataTable("CompanyKPI");
                dt.Columns.Add("KPI", typeof(string));
                dt.Columns.Add("KPIID", typeof(int));
                var lstCol = new List<string>();
                foreach (var item in parentModelList)
                {
                    var parent = item.PcCompanyKpiMonthlyValueModel;
                    if (parent.Year > 0)
                    {
                        string monthName = Common.GetMonthName(parent.Month);
                        string colName = "(A) " + monthName + " " + Convert.ToString(parent.Year);
                        if (lstCol.IndexOf(colName) < 0)
                        {
                            DataColumn col = new DataColumn(colName, System.Type.GetType("System.String"));
                            col.AllowDBNull = true;
                            dt.Columns.Add(col);
                            lstCol.Add(colName);
                        }
                    }

                }
                foreach (var item in parentModelList)
                {
                    var parent = item.PcCompanyKpiMonthlyValueModel;
                    if (parent.Month > 0 && parent.Year > 0)
                    {
                        string monthName = Common.GetMonthName(parent.Month);
                        string colName = "(B) " + monthName + " " + Convert.ToString(parent.Year);

                        if (lstCol.IndexOf(colName) < 0)
                        {
                            DataColumn col = new DataColumn(colName, Type.GetType("System.String"));
                            col.AllowDBNull = true;
                            dt.Columns.Add(col);
                            lstCol.Add(colName);
                        }
                    }

                }
                int i = 1;
                var kpiList = new Dictionary<int, string>();
                foreach (var item in parentModelList)
                {
                    var parent = item.PcCompanyKpiMonthlyValueModel;
                    if (!string.IsNullOrEmpty(parent.KPI))
                    {
                        string info = string.Empty;
                        info = !string.IsNullOrEmpty(parent.KPIInfo) ? "(" + parent.KPIInfo + ")" : "";
                        var monthName = Common.GetMonthName(parent.Month);
                        var monthNameWithActualOrBudget = "(A) " + monthName;
                        var kpiExist = kpiList.FirstOrDefault(x => x.Value == parent.KPI + info);


                        if (kpiExist.Key == 0)
                        {
                            DataRow dr = dt.NewRow();
                            string kpiWithInfo = parent.KPI + info;
                            dr["KPI"] = kpiWithInfo;
                            dr["KPIID"] = i++;
                            kpiList.Add(i++, kpiWithInfo);
                            if (!string.IsNullOrEmpty(monthName))
                            {
                                dr[monthNameWithActualOrBudget + " " + Convert.ToString(parent.Year)] = parent.KPIInfo.ToUpper() == "TEXT" ? parent.KPIActualValue?.ToString("0.####") ?? "NA" : ConversionKMB(GetVal(parent.KPIActualValue)) ?? "NA";
                                dr["(B) " + Common.GetMonthName(parent.Month) + " " + Convert.ToString(parent.Year)] = parent.KPIInfo.ToUpper() == "TEXT" ? parent.KPIBudgetValue?.ToString("0.####") ?? "NA" : ConversionKMB(GetVal(parent.KPIBudgetValue)) ?? "NA";
                            }
                            dt.Rows.Add(dr);
                        }
                        else
                        {
                            if (!string.IsNullOrEmpty(monthName))
                            {
                                string kpiWithInfo = parent.KPI + info;
                                DataRow[] oldDr = dt.Select("KPI='" + kpiWithInfo + "'");
                                foreach (DataRow row in oldDr)
                                {
                                    row[monthNameWithActualOrBudget + " " + Convert.ToString(parent.Year)] = parent.KPIInfo.ToUpper() == "TEXT" ? parent.KPIActualValue?.ToString("0.####") ?? "N.A" : ConversionKMB(GetVal(parent.KPIActualValue)) ?? "N.A";
                                    row["(B) " + Common.GetMonthName(parent.Month) + " " + Convert.ToString(parent.Year)] = parent.KPIInfo.ToUpper() == "TEXT" ? parent.KPIBudgetValue?.ToString("0.####") ?? "N.A" : ConversionKMB(GetVal(parent.KPIBudgetValue)) ?? "N.A";
                                }
                            }
                        }

                    }
                    var childList = item.ChildPCCompanyKPIMonthlyValueList;
                    if (childList.Any())
                    {
                        foreach (var child in childList)
                        {
                            string info = string.Empty;
                            if (!string.IsNullOrEmpty(child.KPI) && child.Month > 0 && child.Year > 0)
                            {
                                string monthName = Common.GetMonthName(child.Month);
                                string colName = "(A) " + monthName + " " + Convert.ToString(child.Year);
                                if (lstCol.IndexOf(colName) < 0)
                                {

                                    DataColumn col = new DataColumn(colName, System.Type.GetType("System.String"));
                                    col.AllowDBNull = true;
                                    dt.Columns.Add(col);
                                    lstCol.Add(colName);
                                }
                            }
                            if (!string.IsNullOrEmpty(child.KPI) && child.Month > 0 && child.Year > 0)
                            {
                                string monthName = Common.GetMonthName(child.Month);
                                string colName = "(B) " + monthName + " " + Convert.ToString(child.Year);
                                if (lstCol.IndexOf(colName) < 0)
                                {

                                    DataColumn col = new DataColumn(colName, System.Type.GetType("System.String"));
                                    col.AllowDBNull = true;
                                    dt.Columns.Add(col);
                                    lstCol.Add(colName);
                                }
                            }
                            if (child.Month > 0 && child.Year > 0)
                            {
                                info = !string.IsNullOrEmpty(child.KPIInfo) ? "(" + child.KPIInfo + ")" : "";
                                var monthName = Common.GetMonthName(child.Month);
                                var monthNameWithActualOrBudget = "(A) " + monthName;
                                var kpiExist = kpiList.FirstOrDefault(x => x.Value == "  - " + child.KPI + info);
                                if (kpiExist.Key == 0)
                                {
                                    DataRow dr = dt.NewRow();
                                    dr["KPI"] = "  - " + child.KPI + info;
                                    dr["KPIID"] = i++;
                                    kpiList.Add(i++, "  - " + child.KPI + info);
                                    dr[monthNameWithActualOrBudget + " " + Convert.ToString(child.Year)] = child.KPIInfo.ToUpper() == "TEXT" ? child.KPIActualValue?.ToString("0.####") ?? "N.A" : ConversionKMB(GetVal(child.KPIActualValue)) ?? "N.A";
                                    dr["(B) " + Common.GetMonthName(child.Month) + " " + Convert.ToString(child.Year)] = child.KPIInfo.ToUpper() == "TEXT" ? child.KPIBudgetValue?.ToString("0.####") ?? "N.A" : ConversionKMB(GetVal(child.KPIBudgetValue)) ?? "N.A";
                                    dt.Rows.Add(dr);
                                }
                                else
                                {
                                    string kpiWithInfo = "  - " + child.KPI + info;
                                    kpiWithInfo = kpiWithInfo.Replace("'", "''");
                                    DataRow[] oldDr = dt.Select("KPI='" + kpiWithInfo + "'");
                                    foreach (DataRow row in oldDr)
                                    {
                                        row[monthNameWithActualOrBudget + " " + Convert.ToString(child.Year)] = child.KPIInfo.ToUpper() == "TEXT" ? child.KPIActualValue?.ToString("0.####") ?? "N.A" : ConversionKMB(GetVal(child.KPIActualValue)) ?? "N.A";
                                        row["(B) " + Common.GetMonthName(child.Month) + " " + Convert.ToString(child.Year)] = child.KPIInfo.ToUpper() == "TEXT" ? child.KPIBudgetValue?.ToString("0.####") ?? "N.A" : ConversionKMB(GetVal(child.KPIBudgetValue)) ?? "N.A";
                                    }
                                }
                            }
                        }
                    }
                }
                dt.Columns.Remove("KPIID");
                ds.Tables.Add(dt);
            }
            return ds;
        }

        private static decimal? GetVal(decimal? val)
        {
            return val == null ? 0 : val;
        }

        #endregion

        public FundStaticData GetFunds(int fundId)
        {
            var data = _unitOfWork.FundDetailRepository.GetManyQueryable(x => !x.IsDeleted && x.FundId == fundId).OrderByDescending(x => x.FundId).AsQueryable().ToList();
            FundStaticData fundStatic = new();
            foreach (var fundDetails in data)
            {
                fundStatic.FundID = fundDetails.FundId;
                fundStatic.FundName = fundDetails.FundName;
                fundStatic.VintageYear = fundDetails.VintageYear;
                fundStatic.FundSize = fundDetails.FundSize;
                fundStatic.StrategyDescription = fundDetails.StrategyDescription;
                fundStatic.VintageYear_Comment = fundDetails.VintageYear_Comment;
                fundStatic.GeographyDetail = new LocationModel
                {
                    Region = fundDetails.Region != null ? new RegionModel { RegionId = fundDetails.Region.RegionId, Region = fundDetails.Region.Region } : null,
                    City = fundDetails.City != null ? new CityModel { CityId = fundDetails.City.CityId, City = fundDetails.City.City } : null,
                    Country = fundDetails.Country != null ? new CountryModel { CountryId = fundDetails.Country.CountryId, Country = fundDetails.Country.Country } : null,
                    State = fundDetails.State != null ? new StateModel { StateId = fundDetails.State.StateId, State = fundDetails.State.State } : null
                };
                fundStatic.SectorDetail = fundDetails.Sector == null ? null : new SectorModel
                {
                    Sector = fundDetails.Sector.Sector,
                    SectorID = fundDetails.Sector.SectorId
                };
                fundStatic.StrategyDetail = fundDetails.Strategy == null ? null : new StrategyModel
                {
                    Strategy = fundDetails.Strategy.Strategy,
                    StrategyID = fundDetails.Strategy.StrategyId
                };
                fundStatic.AccountTypeDetail = fundDetails.AccountType == null ? null : new AccountTypeModel
                {
                    AccountType = fundDetails.AccountType.AccountType,
                    AccountTypeID = fundDetails.AccountType.AccountTypeId
                };
                fundStatic.FirmDetail = fundDetails.MappingFirmFund.Where(x => !x.IsDeleted).Select(x => new FirmModel
                {
                    FirmID = x.FirmId,
                    EncryptedFirmID = x.FirmDetail.EncryptedFirmId,
                    FirmName = x.FirmDetail.FirmName,
                    Website = x.FirmDetail.Website,
                    BusinessDescription = x.FirmDetail.BussinessDescription,
                }).FirstOrDefault();
                fundStatic.CurrencyDetail = fundDetails.Currency == null ? null : new CurrencyModel
                {
                    CurrencyID = fundDetails.Currency.CurrencyId,
                    CurrencyCode = fundDetails.Currency.CurrencyCode,
                    Currency = fundDetails.Currency.Currency
                };
            }
            return fundStatic;
        }

        private static StringBuilder SetExcelHeaders(List<string> ExcelColHeaders, ref string sFields)
        {
            StringBuilder sColumns;
            ExcelColHeaders.Add("KPI");
            ExcelColHeaders.AddRange(Constants.ImportExcelHeaders);

            foreach (string str in ExcelColHeaders)
            {
                sFields = $"{sFields}{str},";
            }
            sFields = sFields.TrimEnd(',');
            ExcelColHeaders.Clear();
            sColumns = CreateTableColumns(ExcelColHeaders, sFields);
            return sColumns;
        }

        private static StringBuilder CreateTableColumns(List<string> ExcelColHeaders, string sFields)
        {
            StringBuilder sColumns = new("");
            for (int iCnt = 0; iCnt <= sFields.Split(',').Length - 1; iCnt++)
            {
                string sFieldValue = sFields.Split(',')[iCnt];
                ExcelColHeaders.Add(sFieldValue);
                if (iCnt == 0)
                    sColumns.Append("[" + sFields.Split(',')[iCnt] + "] VARCHAR (100)");
                else
                {
                    if (sFieldValue.Contains("CreatedBy") || sFieldValue.Contains("Month") || sFieldValue.Contains("Year") || sFieldValue.Contains("Id"))
                        sColumns.Append(", [" + sFields.Split(',')[iCnt] + "] INT");
                    else if (sFieldValue.Contains("Quarter") || sFieldValue.Contains("KPIValue") || sFieldValue.Contains("Header"))
                    {
                        sColumns.Append(", [" + sFields.Split(',')[iCnt] + "] VARCHAR (max)");
                    }
                    else if (sFieldValue.Contains("CreatedOn"))
                    {
                        sColumns.Append(", [" + sFields.Split(',')[iCnt] + "] DATETIME");
                    }
                    else
                    {
                        sColumns.Append(", [" + sFields.Split(',')[iCnt] + "] DECIMAL(34,16)");
                    }
                }
            }
            return sColumns;
        }
        #region [PC StaticData]
        public async Task<List<RegionCountryMappingModel>> GetGeoLocationByPortfolioCompanyId(List<int> portfolioCompanyIDs)
        {
            var RegionWiseCountrymapping = await _dapperGenericRepository.Query<View_RegionCountryMapping>(SqlConstants.QueryByRegionCountryMapping);
            var pcGeoLocations = await _dapperGenericRepository.Query<View_RegionCountryMapping>((portfolioCompanyIDs == null || portfolioCompanyIDs.Count == 0) ? SqlConstants.QueryMappedPCGeoLocation : SqlConstants.QueryGeoLocationByPortoflioCompanyID, new { pcIds = portfolioCompanyIDs?.ToArray() });
            var countryIDs = pcGeoLocations.Select(x => x.CountryId);
            var regionIds = pcGeoLocations.Select(x => x.RegionId);
            var filteredResult = RegionWiseCountrymapping.Where(x => countryIDs.Contains(x.CountryId) && regionIds.Contains(x.RegionId)).ToList();
            return filteredResult.Select(x => _mapper.Map<RegionCountryMappingModel>(x)).ToList();
        }
        #endregion

        public async Task<bool> AddOrUpdatePortfolioCustomList(List<CustomPortfolioGroupList> customPortfolioGroupLists, int userId, int companyId)
        {
            List<PortfolioCompanyCustomListDetails> createList = new();
            List<PortfolioCompanyCustomListDetails> updateList = new();
            if (customPortfolioGroupLists.Count > 0)
            {
                foreach (var field in customPortfolioGroupLists)
                {
                    var exitingData = await _unitOfWork.PortfolioCustomListRepository.GetFirstOrDefaultAsync(x => !x.IsDeleted && x.FieldId == field.FieldId && x.FeatureId == field.FeatureId && x.GroupId == field.GroupId);
                    if (exitingData is not null)
                    {
                        UpdatePCCustomList(userId, updateList, field, exitingData);
                    }
                    else
                    {
                        field.FeatureId = companyId;
                        AddPCCustomList(userId, createList, field);
                    }
                }
            }
            await SaveDataBaseCustomListFields(createList, updateList);
            return true;
        }
        private async Task SaveDataBaseCustomListFields(List<PortfolioCompanyCustomListDetails> createList, List<PortfolioCompanyCustomListDetails> updateList)
        {
            if (createList.Any())
            {
                await _unitOfWork.PortfolioCustomListRepository.AddBulkAsyn(createList);
                _unitOfWork.Save();
            }
            if (updateList.Any())
            {
                _unitOfWork.PortfolioCustomListRepository.UpdateBulk(updateList);
                _unitOfWork.Save();
            }
        }
        private static void UpdatePCCustomList(int userId, List<PortfolioCompanyCustomListDetails> updateList, CustomPortfolioGroupList field, PortfolioCompanyCustomListDetails exitingData)
        {
            exitingData.GroupName = field.GroupName;
            exitingData.ModifiedOn = DateTime.Now;
            exitingData.DisplayOrder = field.DisplayOrder;
            exitingData.IsDeleted = field.IsActive;
            exitingData.ModifiedBy = userId;
            updateList.Add(exitingData);
        }
        private static void AddPCCustomList(int userId, List<PortfolioCompanyCustomListDetails> createList, CustomPortfolioGroupList field)
        {
            PortfolioCompanyCustomListDetails data = new()
            {
                FieldId = field.FieldId,
                FeatureId = field.FeatureId,
                GroupName = field.GroupName,
                IsDeleted = field.IsActive,
                DisplayOrder = field.DisplayOrder,
                CreatedOn = DateTime.Now,
                CreatedBy = userId
            };
            createList.Add(data);
        }
        public async Task<List<CustomPortfolioGroupList>> GetPortfolioCompanyTypeListDetails(int featureId)
        {
            List<PortfolioCompanyCustomListDetails> mGroupingLists = await _unitOfWork.PortfolioCustomListRepository.FindAllAsync(x => !x.IsDeleted && x.FeatureId == featureId);
            return mGroupingLists.OrderBy(x => x.FieldId).ThenBy(x => x.DisplayOrder).Select(x => new CustomPortfolioGroupList
            {
                DisplayOrder = x.DisplayOrder,
                GroupId = x.GroupId,
                GroupName = x.GroupName,
                IsActive = x.IsDeleted,
                IsEditable = false,
                FieldId = x.FieldId,
                FeatureId = x.FeatureId
            }).ToList();
        }

        private async Task<PortfolioCompanyModel> GeneratePorfolioCompanyDraftModel(PortfolioCompanyDetails portfolioCompany)
        {

            var portfolioCompanyDetail = new PortfolioCompanyModel
            {
                PortfolioCompanyID = portfolioCompany.PortfolioCompanyId
            };
            return portfolioCompanyDetail;
        }

        public List<PageFieldValueModel> GetPageFieldModelDTOtDetails(List<SubPageFieldModel> pcStaticFields)
        {

            List<PageFieldValueModel> _PageFieldModelDTO = pcStaticFields.Select(x => new PageFieldValueModel()
            {
                SubPageID = x.SubPageID,
                FieldID = x.Id,
                Name = x.Name,
                Value = Constants.NotAvailable,
                DisplayName = x.DisplayName,
                Sequence = x.SequenceNo,
                IsActive = x.IsActive,
                DataTypeId = x.DataTypeId,
            }).ToList();

            return _PageFieldModelDTO;
        }
        public async Task<List<int>> GetCompanyListByUserId(int userId)
        {
            var result = await _dapperGenericRepository.Query<int>(SqlConstants.QuerGetPortfolioCompanyByUserId, new { @userID = userId });

            return result;

        }
        public async Task<PortfolioCompanyDetails> FetchCompanyDetailsById(int PortfolioCompanyId)
        {
            var portfolioCompany = await _dapperGenericRepository.QueryFirstAsync<PortfolioCompanyDetails>(SqlConstants.QueryByGetPCById, new { @Id = PortfolioCompanyId });
            return portfolioCompany;
        }
    }
}