﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using AutoMapper;
using Contract.Configuration;
using Contract.Funds;
using Contract.Utility;
using DataAccessLayer.DBModel;
using DataAccessLayer.Models.PageSettings;
using DataAccessLayer.UnitOfWork;
using DocumentFormat.OpenXml.Bibliography;
using Microsoft.Extensions.Logging;
using Shared;
namespace Master
{
    public class PageDetailsConfigurationService : IPageDetailsConfigurationService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<IPageDetailsConfigurationService> _logger;
        private readonly IMapper _mapper;
        private readonly IMemoryCacher _memoryCacher = null;
        private readonly IGlobalConfigurations _globalConfig = null;
        private const string _cacheKeyName = "allPageConfigDetails";

        public PageDetailsConfigurationService(IUnitOfWork unitOfWork, IMapper mapper, ILogger<IPageDetailsConfigurationService> logger, IGlobalConfigurations globalConfig, IMemoryCacher memoryCacher)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
            _mapper = mapper;
            _memoryCacher = memoryCacher;
            _globalConfig = globalConfig;
        }
        #region GetAllPageConfigSettings
        public async Task<List<PageDetailModel>> GetConfiguration()
        {
            var result = (List<PageDetailModel>)_memoryCacher.GetValue(_cacheKeyName);
            if (result == null)
            {
                var pageDetails = await _unitOfWork.PageDetailsRepository.GetManyAsync(x => !x.IsDeleted);
                var subPageDetails = await _unitOfWork.SubPageDetailsRepository.GetManyAsync(x => !x.IsDeleted);
                var subPageFields = await _unitOfWork.SubPageFieldsRepository.GetManyAsync(x => !x.IsDeleted);
                subPageFields = subPageFields.OrderBy(x => x.SequenceNo).OrderByDescending(p => p.SequenceNo.HasValue).ToList();
                var mSubsectionFields= await _unitOfWork.MSubSectionFieldsRepository.GetManyAsync(x => !x.IsDeleted);
                var set1 = (from s in subPageDetails
                            join f in subPageFields on s.SubPageID equals f.SubPageID into ca
                            from x in ca.DefaultIfEmpty()
                            orderby s.SequenceNo
                            group x by s into g
                            select new { g }).ToList();
                List<SubPageDetailModel> subPageDetailModels = new List<SubPageDetailModel>();
                foreach (var item in set1)
                {
                    SubPageDetailModel subPageDetailModel = _mapper.Map<SubPageDetailModel>(item.g.Key);
                    List<SubPageFieldModel> subPageFieldModels = new List<SubPageFieldModel>();

                    foreach (var field in item.g)
                    {
                        SubPageFieldModel subPageFieldModel = _mapper.Map<SubPageFieldModel>(field);
                        subPageFieldModel.DataTypeId = subPageFieldModel.SubPageID == 1 && subPageFieldModel.IsCustom && subPageFieldModel.DataTypeId == 0 ? 1 : subPageFieldModel.DataTypeId;
                        subPageFieldModel.MSubFields = mSubsectionFields.Where(x => x.SubPageID == field.SubPageID && x.FieldID == field.FieldID).Select(x => new MSubFields { Name=x.Name, AliasName = x.AliasName, ChartValue =string.IsNullOrEmpty(x.ChartValue)? new(): x.ChartValue?.Split(',').ToList<string>(), SectionID = x.SectionID, SubPageID = x.SubPageID, FieldID = x.FieldID, Options =x.Options.Split(',').ToList<string>() }).ToList();
                        subPageFieldModels.Add(subPageFieldModel);
                    }
                    subPageDetailModel.SubPageFieldList = subPageFieldModels;
                    subPageDetailModels.Add(subPageDetailModel);
                }

                var set2 = (from m in pageDetails
                            join c in subPageDetailModels on m.PageID equals c.ParentId into ca
                            from x in ca.DefaultIfEmpty()
                            group x by m into g
                            select new { g }).ToList();

                List<PageDetailModel> pageDetailModels = new List<PageDetailModel>();
                foreach (var item in set2)
                {
                    PageDetailModel pageDetailModel = _mapper.Map<PageDetailModel>(item.g.Key);
                    List<SubPageDetailModel> SubPageDetailModels = new List<SubPageDetailModel>();

                    foreach (var field in item.g)
                    {
                        SubPageDetailModel subPageFieldModel = _mapper.Map<SubPageDetailModel>(field);
                        SubPageDetailModels.Add(subPageFieldModel);
                    }
                    pageDetailModel.SubPageDetailList = SubPageDetailModels;

                    pageDetailModels.Add(pageDetailModel);
                }
                _memoryCacher.Add(_cacheKeyName, pageDetailModels, DateTimeOffset.UtcNow.AddHours(_globalConfig.CacheTimoutHours));

                result = pageDetailModels;
            }
            return result;


        }
        #endregion
        #region GetPageDetailsByID
        public async Task<PageDetailModel> GetPageDetailsByID(int pageID)
        {
            var configurations = await GetConfiguration();

            var result = configurations.Find(x => x.Id == pageID);

            return result == null ? null : _mapper.Map<PageDetailModel>(result);

        }

        #endregion

        #region GetAllActiveFieldsBySubPageID
        public async Task<List<SubPageFieldModel>> GetAllActiveFieldsBySubPageID(int pageID, int subPageID)
        {
            var pageDetails = await GetPageDetailsByID(pageID);
            SubPageDetailModel subPageDetailList = pageDetails?.SubPageDetailList.FirstOrDefault(x => !x.IsDeleted && x.IsActive && x.Id == subPageID);
            return subPageDetailList?.SubPageFieldList.FindAll(x => !x.IsDeleted && x.IsActive);
        }

        /// <summary>
        /// Retrieves the active subpage sections by page ID.
        /// </summary>
        /// <param name="pageId">The ID of the page.</param>
        /// <param name="isPortfolioCompany">Flag indicating whether it is a portfolio company.</param>
        /// <returns>A list of active subpage sections.</returns>
        public async Task<List<SubPageDetailModel>> GetActiveSubPageSectionByPageId(int pageId, bool isPortfolioCompany = false)
        {
            var configurations = await _unitOfWork.SubPageDetailsRepository.FindAllAsync(x => !x.IsDeleted && x.IsActive && x.PageID == pageId);
            var subPageDetailList = configurations.OrderBy(x => x.SubPageID).Select(x => _mapper.Map<SubPageDetailModel>(x)).ToList();

            if (isPortfolioCompany)
            {
                await PopulateSubPageDetails(subPageDetailList);
            }

            return subPageDetailList;
        }

        /// <summary>
        /// Populates the subpage details by populating the subpage fields for the specified subpage detail list.
        /// </summary>
        /// <param name="subPageDetailList">The list of subpage details.</param>
        public async Task PopulateSubPageDetails(List<SubPageDetailModel> subPageDetailList)
        {
            foreach (var subPageDetail in subPageDetailList)
            {
                if (subPageDetail?.Name == Constants.SustainableDevelopmentGoalsImages || subPageDetail?.Name == Constants.ValuationSummary)
                {
                    await PopulateSubPageFields(subPageDetail);
                }
            }
        }

        /// <summary>
        /// Populates the subpage fields for a given subpage detail.
        /// </summary>
        /// <param name="subPageDetail">The subpage detail.</param>
        public async Task PopulateSubPageFields(SubPageDetailModel subPageDetail)
        {
            var subPageFields = await _unitOfWork.SubPageFieldsRepository.FindAllAsync(x => !x.IsDeleted && x.IsActive && x.SubPageID == subPageDetail.Id);
            if (subPageFields != null && subPageFields.Any())
            {
                subPageDetail.SubPageFieldList = subPageFields.OrderBy(x => x.SequenceNo).Select(x => _mapper.Map<SubPageFieldModel>(x)).ToList();
                await PopulateMSubFields(subPageDetail);
            }
        }

        /// <summary>
        /// Populates the MSubFields property of the given SubPageDetailModel.
        /// </summary>
        /// <param name="subPageDetail">The SubPageDetailModel to populate MSubFields for.</param>
        public async Task PopulateMSubFields(SubPageDetailModel subPageDetail)
        {
            foreach (var field in subPageDetail.SubPageFieldList)
            {
                var subSections = await _unitOfWork.MSubSectionFieldsRepository.FindAllAsync(x => !x.IsDeleted && x.IsActive && x.SubPageID == subPageDetail.Id);
                if (subSections != null)
                {
                    field.MSubFields = subSections.Select(x => new MSubFields { Name = x.Name, AliasName = x.AliasName, ChartValue = string.IsNullOrEmpty(x.ChartValue) ? new() : x.ChartValue?.Split(',').ToList<string>(), SectionID = x.SectionID, SubPageID = x.SubPageID, FieldID = x.FieldID, Options = x.Options.Split(',').ToList<string>() }).ToList();
                }
            }
        }
        public async Task<List<SubPageDetailModel>> GeAllSubPageSectionByPageId(int pageId)
        {
            var configurations = await _unitOfWork.SubPageDetailsRepository.FindAllAsync(x => !x.IsDeleted && x.PageID == pageId);
            return configurations.OrderBy(x => x.SubPageID).Select(x => _mapper.Map<SubPageDetailModel>(x)).ToList();
        }

        #endregion
        public async Task<List<SubPageFieldModel>> GetAllSubPageFieldsByPageId(int pageId)
        {
            var list1 = await this.GetActiveSubPageSectionByPageId(pageId);

            var subPageIDList = list1.Select(x => x.Id);

            var configurations = await _unitOfWork.SubPageFieldsRepository.FindAllAsync(x => !x.IsDeleted);

            var fieldValueList = configurations.OrderBy(x => x.SequenceNo).Select(x => _mapper.Map<SubPageFieldModel>(x)).ToList();

            return fieldValueList.Where(x => subPageIDList.Contains(x.SubPageID)).ToList();

        }

        public async Task<List<SubPageFieldModel>> GetActiveFieldsByPageId(int pageId)
        {
            var result = await GetAllSubPageFieldsByPageId(pageId);
            return result.FindAll(x => x.IsActive);
        }
        public async Task<List<SubPageFieldModel>> GetActiveFieldsBySubPageId(int subPageId)
        {
            return await GetActiveFieldsBySubPageIdWithSequenceNo(subPageId);
        }
        public async Task<List<SubPageFieldModel>> GetActiveFieldsBySubPageIdWithSequenceNo(int subPageId)
        {
            var configurations = await _unitOfWork.SubPageFieldsRepository.FindAllAsync(x => !x.IsDeleted && x.IsActive && x.SubPageID == subPageId);
            return configurations.OrderBy(x => x.SequenceNo).Select(x => _mapper.Map<SubPageFieldModel>(x)).ToList();
        }
        public async Task<List<SubPageFieldModel>> GetActiveFieldsBySubPageIdWithMultpleSubpageId(List<int> subPageId)
        {
            var configurations = await _unitOfWork.SubPageFieldsRepository.FindAllAsync(x => !x.IsDeleted && x.IsActive && subPageId.Contains(x.SubPageID));
            return configurations.OrderBy(x => x.SequenceNo).Select(x => _mapper.Map<SubPageFieldModel>(x)).ToList();
        }
        public async Task<List<SubPageFieldModel>> GetActiveFieldsBySubPageIdWithSequenceNoActiveInactive(int subPageId)
        {
            var configurations = await _unitOfWork.SubPageFieldsRepository.FindAllAsync(x => !x.IsDeleted && x.SubPageID == subPageId);
            return configurations.OrderBy(x => x.SequenceNo).Select(x => _mapper.Map<SubPageFieldModel>(x)).ToList();
        }
        public async Task<List<SubPageFieldModel>> GetAllFieldsBySubPageId(int sectionId)
        {
            var configurations = await _unitOfWork.SubPageFieldsRepository.FindAllAsync(x => !x.IsDeleted && x.SubPageID == sectionId);
            return configurations.OrderBy(x => x.SubPageID).Select(x => _mapper.Map<SubPageFieldModel>(x)).ToList();
        }
        public async Task<List<PageFieldValueModel>> GetPageConfigFieldValueByFeatureId(int pageID, int entityID)
        {
            var configurations = await _unitOfWork.PageConfigurationFieldValueRepository.FindAllAsync(x => x.PageID == pageID && x.PageFeatureId == entityID);
            return configurations.OrderBy(x => x.SubPageID).Select(x => _mapper.Map<PageFieldValueModel>(x)).ToList();
        }

        public async Task<List<PageFieldValueModelTrackRecord>> GetPageConfigFieldTrackRecordValueByFeatureId(int pageID, int entityID)
        {
            var configurations = await _unitOfWork.PageConfigurationTrackRecordFieldValueRepository.FindAllAsync(x => x.PageID == pageID && x.PageFeatureId == entityID);
            return configurations.OrderBy(x => x.SubPageID).Select(x => _mapper.Map<PageFieldValueModelTrackRecord>(x)
            ).ToList();
        }
        public async Task<List<PageFieldValueModelTrackRecord>> GetPageConfigFieldMultipleTrackRecordValueByFeatureId(int pageID, List<int> entityID)
        {
            var configurations = await _unitOfWork.PageConfigurationTrackRecordFieldValueRepository.FindAllAsync(x => x.PageID == pageID && entityID.Contains(x.PageFeatureId));
            return configurations.OrderBy(x => x.SubPageID).Select(x => _mapper.Map<PageFieldValueModelTrackRecord>(x)
            ).ToList();
        }
        public async Task<Status> SavePageConfigFields(List<PageDetailModel> pageDetail, int userId)
        {
            var subpageDetailsList = await _unitOfWork.SubPageDetailsRepository.FindAllAsync(x => !x.IsDeleted);
            var subpageFieldsList = await _unitOfWork.SubPageFieldsRepository.FindAllAsync(x => !x.IsDeleted);
            var mSubsectionFieldValuesList = await _unitOfWork.MSubSectionFieldsRepository.GetManyAsync(x => !x.IsDeleted);
            List<M_SubPageDetails> subPageDetails = new();
            List<M_SubPageFields> subPageFieldsUpdate = new();
            List<M_SubPageFields> subPageFieldsInsert = new();
            List<MSubSectionFields> mSubSectionFields = new();
            int sequenceSubPageNo = 1;
            foreach (var item in pageDetail)
            {
                PageConfigPageDetails(userId, subpageDetailsList, subPageDetails, item,sequenceSubPageNo);
                int SequenceNo = 0;
                foreach (var values in item.SubPageFieldList)
                {
                    SequenceNo++;
                    PageConfigExistsSubPagePageFields(userId, subpageFieldsList, subPageFieldsUpdate, item, SequenceNo, values);
                    PageConfigNotExistsSubPageFields(userId, subpageFieldsList, subPageFieldsInsert, item, SequenceNo, values);
                    KeyPerformanceAndFinancialDataTypes(userId, mSubsectionFieldValuesList, mSubSectionFields, values);
                }
                sequenceSubPageNo++;
            }

            if (subPageFieldsUpdate.Any())
            {
                _unitOfWork.SubPageFieldsRepository.UpdateBulk(subPageFieldsUpdate);
                await _unitOfWork.SaveAsync();
                _logger.LogInformation("PageConfig subPageFields updated sucessfully.");
            }
            if (subPageFieldsInsert.Any())
            {
                await _unitOfWork.SubPageFieldsRepository.AddBulkAsyn(subPageFieldsInsert);
                await _unitOfWork.SaveAsync();
                _logger.LogInformation("PageConfig subPageFields added sucessfully.");
            }
            if (mSubSectionFields.Any())
            {
                _unitOfWork.MSubSectionFieldsRepository.UpdateBulk(mSubSectionFields);
                await _unitOfWork.SaveAsync();
                _logger.LogInformation("PageConfig subSectionFields updated sucessfully.");
            }
            return await Task.FromResult(new Status() { Code = "ok", Message = "Updated" });
        }
        /// <summary>
        /// This method adds a sub-page field to the list of sub-page fields if it does not already exist.
        /// </summary>
        /// <param name="userId">The ID of the user who is adding the sub-page field.</param>
        /// <param name="subpageFieldsList">The list of existing sub-page fields.</param>
        /// <param name="addSubPageFields">The list of sub-page fields to add.</param>
        /// <param name="item">The page detail model for the page that the sub-page field is being added to.</param>
        /// <param name="SequenceNo">The sequence number of the sub-page field.</param>
        /// <param name="values">The values for the sub-page field.</param>
        /// <returns>
        /// The updated list of sub-page fields.
        /// </returns>
        public void PageConfigNotExistsSubPageFields(int userId, List<M_SubPageFields> subpageFieldsList, List<M_SubPageFields> addSubPageFields, PageDetailModel item, int SequenceNo, SubPageFieldModel values)
        {
            if (!subpageFieldsList.Exists(x => x.FieldID == values.Id))
            {
                M_SubPageFields m_SubPageFields = new();
                m_SubPageFields = _mapper.Map(values, m_SubPageFields);
                m_SubPageFields.SubPageID = item.Id;
                m_SubPageFields.CreatedBy = userId;
                m_SubPageFields.CreatedOn = DateTime.Now;
                m_SubPageFields.SequenceNo = SequenceNo;
                m_SubPageFields.IsHighLight = values.IsHighLight;
                m_SubPageFields.ShowOnList = !(item.Id != (int)PageConfigurationSubFeature.StaticInformation && item.Id != (int)PageConfigurationSubFeature.FundStaticInformation) && values.ShowOnList;
                m_SubPageFields.IsListData = !(item.Id != (int)PageConfigurationSubFeature.StaticInformation && item.Id != (int)PageConfigurationSubFeature.FundStaticInformation) && values.IsListData;
                addSubPageFields.Add(m_SubPageFields);
            }
        }
        /// <summary>
        /// This method checks if the sub-page field exists and updates it if it does.
        /// </summary>
        /// <param name="userId">The ID of the user who is updating the sub-page field.</param>
        /// <param name="subpageFieldsList">The list of existing sub-page fields.</param>
        /// <param name="updateSubPageFields">The list of sub-page fields to update.</param>
        /// <param name="item">The page detail model.</param>
        /// <param name="SequenceNo">The sequence number of the sub-page field.</param>
        /// <param name="values">The sub-page field values.</param>
        /// </summary>
        public void PageConfigExistsSubPagePageFields(int userId, List<M_SubPageFields> subpageFieldsList, List<M_SubPageFields> updateSubPageFields, PageDetailModel item, int SequenceNo, SubPageFieldModel values)
        {
            if (subpageFieldsList.Exists(x => x.FieldID == values.Id))
            {
                M_SubPageFields m_SubPageFields = subpageFieldsList?.FirstOrDefault(x => x.FieldID == values.Id);
                m_SubPageFields = _mapper.Map(values, m_SubPageFields);
                m_SubPageFields.ModifiedBy = userId;
                m_SubPageFields.SequenceNo = SequenceNo;
                m_SubPageFields.IsHighLight = values.IsHighLight;
                m_SubPageFields.IsListData = !(item.Id != (int)PageConfigurationSubFeature.StaticInformation && item.Id != (int)PageConfigurationSubFeature.FundStaticInformation) && values.IsListData;
                m_SubPageFields.ShowOnList = !(item.Id != (int)PageConfigurationSubFeature.StaticInformation && item.Id != (int)PageConfigurationSubFeature.FundStaticInformation) && values.ShowOnList;
                updateSubPageFields.Add(m_SubPageFields);
            }
        }
        /// <summary>
        /// This method is used to update the page details
        /// </summary>
        /// <param name="userId">he ID of the user who is making the update</param>
        /// <param name="subpageDetailsList">The list of existing subpage details</param>
        /// <param name="updateSubPageDetails"> The list of subpage details that need to be updated</param>
        /// <param name="item">he page detail model that contains the new values</param>
        public void PageConfigPageDetails(int userId, List<M_SubPageDetails> subpageDetailsList, List<M_SubPageDetails> updateSubPageDetails, PageDetailModel item,int sequenceSubPageNo = 0)
        {
            if (subpageDetailsList.Exists(x => x.SubPageID == item.Id))
            {
                var subpageDetails = subpageDetailsList?.FirstOrDefault(x => x.SubPageID == item.Id);
                subpageDetails = _mapper.Map(item, subpageDetails);
                subpageDetails.ModifiedBy = userId;
                subpageDetails.IsActive = item.IsActive;
                subpageDetails.IsFootNote = item.IsFootNote;
                subpageDetails.SequenceNo = subpageDetails.PageID == (int) PageConfigurationFeature.PortfolioCompany ?  sequenceSubPageNo : subpageDetails.SequenceNo;
                updateSubPageDetails.Add(subpageDetails);
            }
        }
        /// <summary>
        /// This method updates the chart value and modified by field for the sub-section fields of the key performance indicator and company financials sub-pages.
        /// </summary>
        /// <param name="userId">The ID of the user who is updating the fields.</param>
        /// <param name="mSubsectionFieldValueList">The list of existing sub-section fields.</param>
        /// <param name="updateSubSectionFields">The list of sub-section fields to be updated.</param>
        /// <param name="values">The sub-page field model containing the updated chart values.</param>
        public static void KeyPerformanceAndFinancialDataTypes(int userId, IEnumerable<MSubSectionFields> mSubsectionFieldValueList, List<MSubSectionFields> updateSubSectionFields, SubPageFieldModel values)
        {
            if (values.SubPageID == (int)PageConfigurationSubFeature.CompanyFinancials || values.SubPageID == (int)PageConfigurationSubFeature.KeyPerformanceIndicator
               || values.SubPageID == (int)PageConfigurationSubFeature.OtherKPIs || values.SubPageID== (int)PageConfigurationSubFeature.CapTable || values.SubPageID == (int)PageConfigurationSubFeature.OtherCapTable || values.SubPageID == (int)PageConfigurationSubFeature.Commentary || values.SubPageID == (int)PageConfigurationSubFeature.SustainableDevelopmentGoalsImages || values.SubPageID == (int)PageConfigurationSubFeature.FundKpis || values.SubPageID == (int)PageConfigurationSubFeature.FundKeyKpis)
            {
                foreach (var subFieldValues in values.MSubFields)
                {
                    MSubSectionFields subFields = mSubsectionFieldValueList?.FirstOrDefault(x => x.SectionID == subFieldValues.SectionID);
                    subFields.AliasName = subFieldValues.AliasName;
                    subFields.ModifiedBy = userId;
                    subFields.ChartValue = subFieldValues.ChartValue.Count == 0 ? null : string.Join(",", PageConfigCustomOrder(subFieldValues.ChartValue));
                    subFields.ModifiedOn = DateTime.Now;
                    updateSubSectionFields.Add(subFields);
                }
            }
        }
        public static List<string> PageConfigCustomOrder(List<string> chartValue)
        {
            // Create a custom comparison function
            Func<string, int> customComparer = (value) =>
            {
                switch (value)
                {
                    case "Monthly": return 1;
                    case "Quarterly": return 2;
                    case "Annual": return 3;
                    default: return 4;
                }
            };
            // Order the chartValue list using the custom comparison function
            var orderedChartValue = chartValue.OrderBy(s => customComparer(s));
            // Return the ordered list
            return orderedChartValue.ToList();
        }

        public async Task<int> SavePageConfigurationFieldValue(List<PageFieldValueModel> dealConfigurationData, int pageFeatureId,
            int userId, int pageId)
        {
            try
            {
                var createList = new List<PageConfigurationFieldValue>();
                var updateList = new List<PageConfigurationFieldValue>();
                foreach (var field in dealConfigurationData)
                {
                    var exitingData = await _unitOfWork.PageConfigurationFieldValueRepository.FindAllAsync(x => (bool)!x.IsDeleted && (bool)x.IsActive && x.PageFeatureId == pageFeatureId && x.FieldID == field.FieldID);

                    if (exitingData.Any())
                    {
                        var data = exitingData?.FirstOrDefault();
                        if (data != null)
                        {
                            data.FieldValue = field.Value;
                            data.ModifiedOn = DateTime.Now;
                            data.ModifiedBy = userId;
                            updateList.Add(data);
                        }
                    }
                    else
                    {
                        var data = new PageConfigurationFieldValue()
                        {
                            PageFeatureId = pageFeatureId,
                            FieldID = field.FieldID,
                            FieldValue = field.Value,
                            IsActive = true,
                            PageID = pageId,
                            SubPageID = field.SubPageID,
                            IsDeleted = false,
                            CreatedOn = DateTime.Now,
                            CreatedBy = userId
                        };
                        createList.Add(data);
                    }
                }
                if (createList.Any())
                {
                    await _unitOfWork.PageConfigurationFieldValueRepository.AddBulkAsyn(createList);
                    await _unitOfWork.SaveAsync();
                }
                else if (updateList.Any())
                {
                    _unitOfWork.PageConfigurationFieldValueRepository.UpdateBulk(updateList);
                   await  _unitOfWork.SaveAsync();
                }

            }
            catch (Exception ex)
            {
                return 1;
            }

            return 1;
        }

        public async Task<List<M_TrackRecordDataTypes>> TrackrecordDataTypes()
        {
            var configurations = await _unitOfWork.M_TrackRecordDataTypesRepository.FindAllAsync(x => !x.IsActive && !x.IsDeleted);
            return configurations.Select(x => _mapper.Map<M_TrackRecordDataTypes>(x)).ToList();
        }
        public async Task<List<PageFieldValueModelTrackRecord>> CheckDataExitsOnCustomFields()
        {

            var configurations = await _unitOfWork.SubPageFieldsRepository.FindAllAsync(x => !x.IsDeleted && (bool)x.IsCustom && x.DataTypeId != 0 && x.DataTypeId != null);
            var PageFieldValueModelList = configurations.OrderBy(x => x.SubPageID).Select(x => _mapper.Map<SubPageFieldModel>(x)).ToList();
            var configurationsTrackRecord = await _unitOfWork.PageConfigurationTrackRecordFieldValueRepository.FindAllAsync(x => (bool)!x.IsDeleted);
            var configurationsTrackRecordList = configurationsTrackRecord.OrderBy(x => x.SubPageID).Select(x => _mapper.Map<PageFieldValueModelTrackRecord>(x)).ToList();
            List<PageFieldValueModelTrackRecord> tracklist = (from p in PageFieldValueModelList
                                                              join q in configurationsTrackRecordList
                                                              on p.Id equals q.FieldID
                                                              select new PageFieldValueModelTrackRecord
                                                              {
                                                                  DisplayName = p.DisplayName,
                                                                  Name = p.Name,
                                                                  SubPageID = p.SubPageID,
                                                                  FieldID = p.Id,
                                                                  Value = q.Value,
                                                                  DataType = p.DataTypeId,
                                                                  PageFeatureId = q.PageFeatureId,
                                                                  IsCustom = p.IsCustom
                                                              }).ToList();
            return await CheckExceptListPortfolioCustomData(tracklist);
        }
        private async Task<List<PageFieldValueModelTrackRecord>> CheckExceptListPortfolioCustomData(List<PageFieldValueModelTrackRecord> tracklist)
        {
            List<SubPageFieldModel> subPageFieldModels = await GetActiveFieldsByPageId((int)PageConfigurationFeature.PortfolioCompany);
            subPageFieldModels.AddRange(await GetActiveFieldsByPageId((int)PageConfigurationFeature.Funds));
            List<SubPageFieldModel> subPageFieldModelsStaticInfo = subPageFieldModels.Where(x => (x.SubPageID == (int)PageConfigurationSubFeature.StaticInformation || x.SubPageID == (int)PageConfigurationSubFeature.FundStaticInformation) && x.IsCustom && x.DataTypeId != 7).ToList();
            List<int> customStaticIds = subPageFieldModelsStaticInfo.Select(x => x.Id).ToList();
            var configurationsPortfolioStaticInformation = await _unitOfWork.PageConfigurationFieldValueRepository.FindAllAsync(x => (bool)!x.IsDeleted && customStaticIds.Contains(x.FieldID)&&x.FieldValue!=Constants.NotAvailable);
            var listObj = (from p in subPageFieldModelsStaticInfo
                           join q in configurationsPortfolioStaticInformation
                           on p.Id equals q.FieldID
                           select _mapper.Map<PageFieldValueModelTrackRecord>(q)).ToList();
            listObj.ForEach(x => tracklist.Add(x));
            return await CheckOnlyPortfolioCustomList(tracklist, subPageFieldModels);
        }
        private async Task<List<PageFieldValueModelTrackRecord>> CheckOnlyPortfolioCustomList(List<PageFieldValueModelTrackRecord> tracklist, List<SubPageFieldModel> subPageFieldModels)
        {
            List<SubPageFieldModel> subPageFieldModelsStaticInfo = subPageFieldModels.Where(x => x.SubPageID == (int)PageConfigurationSubFeature.StaticInformation && x.IsCustom && x.DataTypeId == 7).ToList();
            List<int> customStaticIds = subPageFieldModelsStaticInfo.Select(x => x.Id).ToList();
            var configurationsPortfolioStaticInformation = await _unitOfWork.PortfolioCustomListRepository.FindAllAsync(x =>!x.IsDeleted && customStaticIds.Contains(x.FieldId));
            var listObj = (from p in subPageFieldModelsStaticInfo
                           join q in configurationsPortfolioStaticInformation
                           on p.Id equals q.FieldId
                           select new PageFieldValueModelTrackRecord
                           {
                               DisplayName = p.DisplayName,
                               Name = p.Name,
                               SubPageID = p.SubPageID,
                               FieldID = p.Id,
                               Value = q.GroupName,
                               DataType = p.DataTypeId,
                               PageFeatureId = q.FeatureId,
                               IsCustom = p.IsCustom
                           }).ToList();
            listObj.ForEach(x => tracklist.Add(x));
            return tracklist;
        }
        private static string RemoveNonNumeric(string value) => Regex.Replace(value, "[^0-9.]", "", RegexOptions.None, TimeSpan.FromSeconds(5));
        private static string AllowNumeric(string value) => Regex.Replace(value, "[^0-9-.]", "", RegexOptions.None, TimeSpan.FromSeconds(5));

        public string ConvertCustomValues(PageFieldValueModelTrackRecord field)
        {
            string Value = Convert.ToString(field.Value);
            switch (field.DataType)
            {
                case (int)PageSubFieldsDatatTypes.Number:
                case (int)PageSubFieldsDatatTypes.Currency:
                case (int)PageSubFieldsDatatTypes.Percentage:
                    Value = AllowNumeric(Value);
                    break;
                case (int)PageSubFieldsDatatTypes.Multiple:
                    Value = RemoveNonNumeric(Value);
                    break;
            }
            return Value;
        }        
        public async Task<int> TrackRecordSavePageConfigurationFieldValue(List<PageFieldValueModelTrackRecord> configurationData, int pageFeatureId,
         int userId, int pageId)
        {
            try
            {
                var createList = new List<PageConfigurationTrackRecordFieldValue>();
                var updateList = new List<PageConfigurationTrackRecordFieldValue>();
                foreach (var field in configurationData)
                {
                    field.Value = !string.IsNullOrEmpty(field?.Value?.ToString()) ? ConvertCustomValues(field) : field?.Value?.ToString();
                    var updateddata = _unitOfWork.PageConfigurationTrackRecordFieldValueRepository.GetFirstOrDefault(x => (bool)!x.IsDeleted && (bool)x.IsActive && x.PageFeatureId == pageFeatureId && x.FieldID == field.FieldID && x.Quarter == field.Quarter && x.Year == field.Year);
                    if (updateddata != null)
                    {
                        updateddata.FieldValue = field.Value;
                        updateddata.ModifiedOn = DateTime.Now;
                        updateddata.ModifiedBy = userId;
                        updateddata.Year = field.Year;
                        updateddata.Quarter = field.Quarter;
                        updateList.Add(updateddata);
                    }
                    else
                    {
                        var data = new PageConfigurationTrackRecordFieldValue()
                        {
                            PageFeatureId = pageFeatureId,
                            FieldID = field.FieldID,
                            FieldValue = field.Value,
                            IsActive = true,
                            PageID = pageId,
                            SubPageID = field.SubPageID,
                            IsDeleted = false,
                            CreatedOn = DateTime.Now,
                            CreatedBy = userId,
                            Year = field.Year,
                            Quarter = field.Quarter,
                        };
                        createList.Add(data);
                    }
                }
                if (createList.Any())
                {
                    await _unitOfWork.PageConfigurationTrackRecordFieldValueRepository.AddBulkAsyn(createList);
                    _unitOfWork.Save();
                }
                else if (updateList.Any())
                {
                    _unitOfWork.PageConfigurationTrackRecordFieldValueRepository.UpdateBulk(updateList);
                    _unitOfWork.Save();
                }

            }
            catch (Exception ex)
            {
                return 1;
            }

            return 1;
        }
        public async Task<string> GetsubpagedetailsText(int subPageId)
        {
            var configurations = await _unitOfWork.SubPageDetailsRepository.GetFirstOrDefaultAsync(x => !x.IsDeleted && x.IsActive && x.SubPageID == subPageId);
            return configurations?.AliasName;
        }
        public async Task<string> GetFieldValue(int subPageId,string name)
        {
            var configurations = await _unitOfWork.SubPageFieldsRepository.FindFirstAsync(x => !x.IsDeleted && x.IsActive && x.SubPageID == subPageId && x.Name == name);
            return configurations?.AliasName;
        }
        public async Task<string> GetCommonSheetNameformPageConfig(int subPageId, string name)
        {
            var sheetName = await GetFieldValue(subPageId,name);
            return sheetName?.Length > 24 ? sheetName[..25] : sheetName;
        }
        /// <summary>
        /// Get the page config sub-section fields for a list of kpi types.
        /// </summary>
        /// <param name="kpiTypes">The list of kpi types.</param>
        /// <returns>The page config sub-section fields for the list of kpi types.</returns>
        public async Task<List<KpiConfig>> GetPageConfigSubSectionFields(List<string> kpiTypes)
        {
            // Get the sub-page field datas for the list of kpi types.
            // This query will find all sub-page field datas whose name is in the list of kpi types.
            var subPageFieldData = await _unitOfWork.SubPageFieldsRepository.FindAllAsync(x => kpiTypes.Contains(x.Name));
            return subPageFieldData?.Select(x =>
            {
                return GetKpiSectionConfigData(x);
            }).ToList();
        }
        /// <summary>
        /// Get the page config sub-section fields for a list of kpi types
        /// </summary>
        /// <param name="x"></param>
        /// <returns></returns>
        private KpiConfig GetKpiSectionConfigData(M_SubPageFields x)
        {
            var subSectionFields = _unitOfWork.MSubSectionFieldsRepository.GetQueryable().Where(y => !y.IsDeleted && y.SubPageID == x.SubPageID && y.FieldID == x.FieldID).ToList();
            var data = subSectionFields.OrderBy(x => x.SequenceNo).ToList().ConvertAll(x => new MSubFields { AliasName = x.Name, ChartValue = string.IsNullOrEmpty(x.ChartValue) ? new() : x.ChartValue?.Split(',').ToList<string>(), SectionID = x.SectionID, SubPageID = x.SubPageID, FieldID = x.FieldID, SubFieldAliasName=x.AliasName });
            return new KpiConfig() { KpiType = x.Name, KpiConfigurationData = data?.Where(x => x.ChartValue.Count > 0).ToList(), HasChart = !x.IsChart };
        }
        /// <summary>
        /// Retrieves a list of active sub-section fields associated with a given module ID.
        /// </summary>
        /// <param name="moduleId">The ID of the module for which to retrieve sub-section fields.</param>
        /// <returns>A list of <see cref="MSubFields"/> representing the active sub-section fields associated with the specified module ID.
        /// If no fields are found or the module does not have an associated page configuration field, an empty list is returned.</returns>
        /// <remarks>
        public List<MSubFields> GetActiveMSubSectionFieldsByModuleId(int moduleId)
        {
            string pageConfigName = _unitOfWork.M_KpiModulesRepository.GetFirstOrDefault(x => !x.IsDeleted && x.IsActive && x.ModuleID == moduleId)?.PageConfigFieldName;
            int? fieldId = _unitOfWork.SubPageFieldsRepository.GetFirstOrDefault(x => !x.IsDeleted && x.IsActive && x.Name == pageConfigName)?.FieldID;
            if (fieldId.HasValue)
            {
                var subSectionFields = _unitOfWork.MSubSectionFieldsRepository
                    .GetManyQueryable(y => !y.IsDeleted && y.FieldID == fieldId)
                    .OrderBy(x => x.SequenceNo)
                    .Select(x => new MSubFields
                    {
                        Name = x.Name,
                        AliasName = x.AliasName
                    })
                    .ToList();
                return subSectionFields;
            }
            else
            {
                return new List<MSubFields>();
            }
        }

        public async Task<bool> GetWorkFlowPCDetails()
        {          
            var workFlow = await _unitOfWork.PageDetailsRepository.FindFirstAsync(x =>x.Name== Constants.PortfolioCompany);
            return workFlow.IsWorkFlow;
        }

        /// Updates the workflow status for the Portfolio Company page details.
        /// </summary>
        /// <param name="isWorkFlowEnable">A boolean value indicating whether the workflow should be enabled or disabled.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains a boolean value indicating whether the update was successful.</returns>
        public async Task<bool> UpdateWorkFlowPCDetails(bool isWorkFlowEnable)
        {
            var workFlow = await _unitOfWork.PageDetailsRepository.FindFirstAsync(x => x.Name == Constants.PortfolioCompany);
            if (workFlow != null)
            {
                workFlow.IsWorkFlow = isWorkFlowEnable;
                _unitOfWork.PageDetailsRepository.Update(workFlow);
                _unitOfWork.Save();
                return true;
            }
            return false;
        }
    }
}
