﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
 
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Deal\Deal.csproj" />
    <ProjectReference Include="..\Financials\Financials.csproj" />
    <ProjectReference Include="..\FormulaCalculator\FormulaCalculator.csproj" />
    <ProjectReference Include="..\XLHelper\XLHelper.csproj" />
    <ProjectReference Include="..\Contract\Contract.csproj" />
    <ProjectReference Include="..\Currency\CurrencyRates.csproj" />
    <ProjectReference Include="..\DapperRepository\DapperRepository.csproj" />
    <ProjectReference Include="..\DataAccessLayer\DataAccessLayer.csproj" />
    <ProjectReference Include="..\Exports\Exports.csproj" />
    <ProjectReference Include="..\Fund\Fund.csproj" />
    <ProjectReference Include="..\Master\Master.csproj" />
    <ProjectReference Include="..\Report\Report.csproj" />
    <ProjectReference Include="..\Utility\Utility.csproj" />
  </ItemGroup>

</Project>
