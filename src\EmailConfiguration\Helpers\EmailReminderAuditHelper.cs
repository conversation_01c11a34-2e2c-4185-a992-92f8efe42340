using System;
using DataAccessLayer.Models.EmailNotifications;

namespace EmailConfiguration.Helpers
{
    /// <summary>
    /// Helper class for managing audit fields in email reminder entities
    /// </summary>
    public static class EmailReminderAuditHelper
    {
        /// <summary>
        /// Sets audit fields for entity creation
        /// </summary>
        /// <param name="entity">Entity to set audit fields on</param>
        /// <param name="userId">User ID performing the action</param>
        public static void SetCreateAuditFields<T>(T entity, int userId) where T : class
        {
            var entityType = typeof(T);
            
            // Set CreatedBy
            var createdByProperty = entityType.GetProperty("CreatedBy");
            createdByProperty?.SetValue(entity, userId);
            
            // Set CreatedOn
            var createdOnProperty = entityType.GetProperty("CreatedOn");
            createdOnProperty?.SetValue(entity, DateTime.UtcNow);
            
            // Set IsDeleted to false
            var isDeletedProperty = entityType.GetProperty("IsDeleted");
            isDeletedProperty?.SetValue(entity, false);
        }

        /// <summary>
        /// Sets audit fields for entity update
        /// </summary>
        /// <param name="entity">Entity to set audit fields on</param>
        /// <param name="userId">User ID performing the action</param>
        public static void SetUpdateAuditFields<T>(T entity, int userId) where T : class
        {
            var entityType = typeof(T);
            
            // Set ModifiedBy
            var modifiedByProperty = entityType.GetProperty("ModifiedBy");
            modifiedByProperty?.SetValue(entity, userId);
            
            // Set ModifiedOn
            var modifiedOnProperty = entityType.GetProperty("ModifiedOn");
            modifiedOnProperty?.SetValue(entity, DateTime.UtcNow);
        }

        /// <summary>
        /// Sets audit fields for soft delete
        /// </summary>
        /// <param name="entity">Entity to set audit fields on</param>
        /// <param name="userId">User ID performing the action</param>
        public static void SetSoftDeleteAuditFields<T>(T entity, int userId) where T : class
        {
            var entityType = typeof(T);
            
            // Set IsDeleted to true
            var isDeletedProperty = entityType.GetProperty("IsDeleted");
            isDeletedProperty?.SetValue(entity, true);
            
            // Set ModifiedBy
            var modifiedByProperty = entityType.GetProperty("ModifiedBy");
            modifiedByProperty?.SetValue(entity, userId);
            
            // Set ModifiedOn
            var modifiedOnProperty = entityType.GetProperty("ModifiedOn");
            modifiedOnProperty?.SetValue(entity, DateTime.UtcNow);
        }

        /// <summary>
        /// Creates a new EmailReminder entity with audit fields set
        /// </summary>
        /// <param name="featureId">Feature ID</param>
        /// <param name="entityIds">Comma-separated entity IDs</param>
        /// <param name="documentTypeIds">Comma-separated document type IDs</param>
        /// <param name="userId">User ID for audit</param>
        /// <returns>EmailReminder entity with audit fields set</returns>
        public static EmailReminder CreateEmailReminderWithAudit(int featureId, string entityIds, string documentTypeIds, int userId)
        {
            var emailReminder = new EmailReminder
            {
                ReminderId = Guid.NewGuid(),
                FeatureID = featureId,
                EntityIDs = entityIds,
                DocumentTypeIds = documentTypeIds,
                IsDeleted = false
            };

            SetCreateAuditFields(emailReminder, userId);
            return emailReminder;
        }

        /// <summary>
        /// Creates a new EmailReminderConfig entity with audit fields set
        /// </summary>
        /// <param name="reminderId">Reminder ID</param>
        /// <param name="frequencyType">Frequency type</param>
        /// <param name="totalRemindersPerCycle">Total reminders per cycle</param>
        /// <param name="remainder1Date">First reminder date</param>
        /// <param name="remainder2">Second reminder days</param>
        /// <param name="remainder3">Third reminder days</param>
        /// <param name="remainder4">Fourth reminder days</param>
        /// <param name="remainder5">Fifth reminder days</param>
        /// <param name="userId">User ID for audit</param>
        /// <returns>EmailReminderConfig entity with audit fields set</returns>
        public static EmailReminderConfig CreateEmailReminderConfigWithAudit(Guid reminderId, int frequencyType, 
            int totalRemindersPerCycle, DateTime remainder1Date, string remainder2, string remainder3, 
            string remainder4, string remainder5, int userId)
        {
            var config = new EmailReminderConfig
            {
                ReminderId = reminderId,
                FrequencyType = frequencyType,
                TotalRemindersPerCycle = totalRemindersPerCycle,
                Remainder1Date = remainder1Date,
                Remainder2 = remainder2,
                Remainder3 = remainder3,
                Remainder4 = remainder4,
                Remainder5 = remainder5,
                IsDeleted = false
            };

            SetCreateAuditFields(config, userId);
            return config;
        }

        /// <summary>
        /// Creates a new EmailReminderSchedule entity with audit fields set
        /// </summary>
        /// <param name="configId">Config ID</param>
        /// <param name="reminderDate">Reminder date</param>
        /// <param name="scheduleOccurrence">Schedule occurrence number</param>
        /// <param name="userId">User ID for audit</param>
        /// <returns>EmailReminderSchedule entity with audit fields set</returns>
        public static EmailReminderSchedule CreateEmailReminderScheduleWithAudit(int configId, DateTime reminderDate, 
            int scheduleOccurrence, int userId)
        {
            var schedule = new EmailReminderSchedule
            {
                ReminderConfigId = configId,
                ReminderDate = reminderDate,
                ScheduleOccurrence = scheduleOccurrence,
                Status = 0, // 0=Pending
                IsActive = true,
                IsDeleted = false
            };

            SetCreateAuditFields(schedule, userId);
            return schedule;
        }
    }
}
