namespace DataAccessLayer.DBModel
{
    public partial class Mapping_CompanyProfitAndLossLineItems : BaseModel
    {
        public int CompanyProfitAndLossLineItemMappingID { get; set; }
        public int ProfitAndLossLineItemID { get; set; }
        public int PortfolioCompanyID { get; set; }
        public int? ParentLineItemID { get; set; }
        public int? DisplayOrder { get; set; }
        public bool IsDeleted { get; set; }
        public bool IsActive { get; set; }
        public string SegmentType { get; set; }
        public string EncryptedCompanyProfitAndLossLineItemMappingID { get; set; }
        public string Formula { get; set; }
        public string FormulaKPIId { get; set; }
        public bool IsExtraction { get; set; }
        public string Synonym { get; set; }
        public string Definition { get; set; }
    }

}
