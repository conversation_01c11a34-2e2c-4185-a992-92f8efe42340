CREATE PROCEDURE [dbo].[sp_SaveDashboardTrackerConfig]
    @ID INT = NULL,
    @FieldType INT,
    @DataType INT,
    @Name NVARCHAR(200),
    @FrequencyType INT = NULL,
    @StartPeriod NVARCHAR(50) = NULL,
    @EndPeriod NVARCHAR(50) = NULL,
    @MapTo INT = NULL,
    @IsActive BIT = 1,
    @IsDeleted BIT = 0,
    @CreatedBy INT = NULL,
    @ModifiedBy INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    IF @ID IS NULL
    BEGIN
        INSERT INTO [dbo].[DashboardTrackerConfig]
        (
            FieldType, DataType, Name, FrequencyType, StartPeriod, EndPeriod, MapTo, IsActive, IsDeleted, CreatedOn, CreatedBy
        )
        VALUES
        (
            @FieldType, @DataType, @Name, @FrequencyType, @StartPeriod, @EndPeriod, @MapTo, @IsActive, @IsDeleted, GETUTCDATE(), @CreatedBy
        );
        SELECT SCOPE_IDENTITY();
    END
    ELSE
    BEGIN
        UPDATE [dbo].[DashboardTrackerConfig]
        SET
            FieldType = @FieldType,
            DataType = @DataType,
            Name = @Name,
            FrequencyType = @FrequencyType,
            StartPeriod = @StartPeriod,
            EndPeriod = @EndPeriod,
            MapTo = @MapTo,
            IsActive = @IsActive,
            IsDeleted = @IsDeleted,
            ModifiedOn = GETUTCDATE(),
            ModifiedBy = @ModifiedBy
        WHERE ID = @ID;
        SELECT @ID;
    END
END
