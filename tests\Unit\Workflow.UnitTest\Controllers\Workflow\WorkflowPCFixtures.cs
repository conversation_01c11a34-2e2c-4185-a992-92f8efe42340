﻿using API.Controllers.Workflow;
using API.Helpers;
using AutoMapper;
using Contract.Deals;
using Contract.Funds;
using Contract.PortfolioCompany;
using Contract.Utility;
using DapperRepository;
using DataAccessLayer.DBModel;
using DataAccessLayer.GenericRepository;
using DataAccessLayer.Models;
using DataAccessLayer.UnitOfWork;
using Master;
using Master.DtoProfiles;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PortfolioCompany;
using System.Collections.Generic;
using Workflow.Interface.Draft;

namespace Workflow.UnitTest.Controllers.Workflow
{
    public class WorkflowPCFixtures
    {
        public Mock<IInjectedParameters> InjectedParameters;
        public Mock<IEncryption> Encryption;
        public Mock<IUnitOfWork> UnitOfWork;
        public Mock<ILogger> Logger;
        public Mock<IPortfolioCompanyService> _IPortfolioCompanyService;
        public readonly PortfolioCompanyService _PortfolioCompanyService;
        public IWorkflowPCService _IworkflowPCService;
        public readonly WorkflowPCService _WorkflowPCService;
        public readonly PageDetailsConfigurationService PageDetailsConfigurationService;
        public readonly Mock<IGenericRepository<PCInvestmentKpiQuarterlyValueDraft>> _PCInvestmentKpiQuarterlyValueDraft;
        public readonly Mock<IGenericRepository<MappingPortfolioInvestmentKpi>> _MappingPortfolioInvestmentKpi;
        public readonly Mock<IGenericRepository<M_InvestmentKpi>> _M_InvestmentKpi;
        public readonly Mock<ILogger<IPageDetailsConfigurationService>> Mocklogger;
        public readonly Mock<IGlobalConfigurations> MockglobalConfig;
        public readonly Mock<IMemoryCacher> MockmemoryCacher;
        public readonly Mock<IGenericRepository<DraftAuditLog>> _DraftAuditLog;
        public readonly Mock<IUnitOfWork> MockunitOfWork;
        public WorkflowPortfolioCompanyController controller;
        public Mock<IHelperService> helperService;
        public Mock<IDealService> dealService;
        public Mock<ILogger> logger;
        public Mock<IPageDetailsConfigurationService> MockPageDetailsConfigurationService = new();
        public Mock<IFundService> MockIFundService = new();
        public Mock<IDapperGenericRepository> dapperGenericRepository = new();
        public Mock<IPcKpiDraftService> MockPcKpiDraftService = new();

        public Mock<IPCTradingRecordsDraft> MockPcTradingRecordsDraftService = new();
        public Mock<IPcOperationalKpiDraft> MockPcOperationalKpiDraftService = new();
        public Mock<IPcInvestmentKpiDraft> MockPcInvestmentKpiDraftService = new();
        public WorkflowPCFixtures()
        {
            InjectedParameters = new Mock<IInjectedParameters>();
            UnitOfWork = new Mock<IUnitOfWork>();
            MockunitOfWork = new Mock<IUnitOfWork>();
            helperService = new();
            logger = new();
            MockmemoryCacher = new Mock<IMemoryCacher>();
            MockglobalConfig = new Mock<IGlobalConfigurations>();
            Mocklogger = new Mock<ILogger<IPageDetailsConfigurationService>>();
            dapperGenericRepository = new Mock<IDapperGenericRepository>();
            _PCInvestmentKpiQuarterlyValueDraft = new Mock<IGenericRepository<PCInvestmentKpiQuarterlyValueDraft>>();
            UnitOfWork.Setup(s => s.PCInvestmentKpiQuarterlyValueDraftRepository).Returns(_PCInvestmentKpiQuarterlyValueDraft.Object);
            _MappingPortfolioInvestmentKpi = new Mock<IGenericRepository<MappingPortfolioInvestmentKpi>>();
            UnitOfWork.Setup(s => s.PortfolioInvestmentKpiMappingRepository).Returns(_MappingPortfolioInvestmentKpi.Object);
            _M_InvestmentKpi = new Mock<IGenericRepository<M_InvestmentKpi>>();
            UnitOfWork.Setup(s => s.M_InvestmentKPIRepository).Returns(_M_InvestmentKpi.Object);
            _DraftAuditLog = new Mock<IGenericRepository<DraftAuditLog>>();
            UnitOfWork.Setup(s => s.DraftAuditLogRepository).Returns(_DraftAuditLog.Object);
            Encryption = new Mock<IEncryption>();
            Logger = new Mock<ILogger>();
            dealService = new Mock<IDealService>();
            InjectedParameters.Setup(S => S.UnitOfWork).Returns(UnitOfWork.Object);
            InjectedParameters.Setup(S => S.Logger).Returns(Logger.Object);
            var configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddJsonFile("appsettings.Development.json", optional: true)
                .Build();
            InjectedParameters.Setup(S => S.Configuration).Returns(configuration);
            Encryption.Setup(S => S.Encrypt(It.IsAny<string>())).Returns("66F63D87E53A6F638CBDBE6B3B83AEA3");
            Mock<IDapperGenericRepository> dappermock = new Mock<IDapperGenericRepository>();
            dappermock.Setup(x => x.Query<PortfolioCompanyQueryModel>(It.IsAny<string>(), new { }));
            dappermock.Setup(S => S.QueryFirstAsync<CompanyExcelModel>(It.IsAny<string>(), It.IsAny<object>())).ReturnsAsync(new CompanyExcelModel() { CompanyName = "samsung", CurrencyCode = "USD" });
            _PortfolioCompanyService = new PortfolioCompanyService(dappermock.Object,dealService.Object);
            _PortfolioCompanyService.InjectedParameters = InjectedParameters.Object;
            _IworkflowPCService = new WorkflowPCService();
            _IPortfolioCompanyService = new Mock<IPortfolioCompanyService>();
            var config = new MapperConfiguration(cfg => {
                cfg.AddProfile(new MasterMappingProfile());
                cfg.AddProfile(new PageSettingsProfiles());
            });
            var mapper = config.CreateMapper();
            PageDetailsConfigurationService = new(MockunitOfWork.Object, mapper, Mocklogger.Object, MockglobalConfig.Object, MockmemoryCacher.Object);
            _WorkflowPCService = new WorkflowPCService(InjectedParameters.Object, _IPortfolioCompanyService.Object, dapperGenericRepository.Object, MockPcKpiDraftService.Object, MockPcTradingRecordsDraftService.Object,MockPcOperationalKpiDraftService.Object, MockPcInvestmentKpiDraftService.Object, PageDetailsConfigurationService);
            controller = new WorkflowPortfolioCompanyController(InjectedParameters.Object, helperService.Object, _WorkflowPCService, _IPortfolioCompanyService.Object, MockPageDetailsConfigurationService.Object, MockPcOperationalKpiDraftService.Object);
        }
        public List<MappingPortfolioInvestmentKpi> GetMappingPortfolioInvestmentKpiList1()
        {
            return new List<MappingPortfolioInvestmentKpi>()
           { new MappingPortfolioInvestmentKpi
            {
                MappingPortfolioInvestmentKpiId=124,
                KpiTypeId=2,
                KpiId=100,
                ParentKPIID=null,
                DisplayOrder=1,
                PortfolioCompanyId=1,
                IsDeleted=false
            }
           };
        }
        public List<M_InvestmentKpi> GetInvestment()
        {
            return new List<M_InvestmentKpi>() {
             new M_InvestmentKpi
            {
                Kpi = "M_InvestmentKpi",
                ParentKpiId = 100,
                KpiInfo = "$",
                InvestmentKpiId = 100,
                IsDeleted = false,
                Formula = It.IsAny<string>(),
                FormulaKPIId = It.IsAny<string>()
            }
        };
        }
       public List<MappingWorkflowRequest> IsMarkedforReview()
        {
            return new List<MappingWorkflowRequest>()
            {
                new MappingWorkflowRequest
                {
                    WorkflowMappingId = 4548,
                    IsDeleted= false,
                    IsMarkedForReview = false
                }
            };
        }
        public List<CompanyExcelModel> companyData()
        {
            return new List<CompanyExcelModel>()
            {
                new CompanyExcelModel
                {
                    CompanyName = "Samsung",
                    CurrencyCode = "USD"
                }
            };
        }
        public List<PCInvestmentKpiQuarterlyValueDraft> GetInvestDat()
        {
            return new List<PCInvestmentKpiQuarterlyValueDraft>()
            {
                new PCInvestmentKpiQuarterlyValueDraft
                {
                    PortfolioCompanyID = 1,
                    WorkflowMappingId = 3,
                    InvestmentKPIID = 100,
                    KPIInfo = "%",
                    KpiActualValue = "32323",
                    PCInvestmentKpiQuarterlyValueID = 14797,
                    Quarter = "Q1",
                    Year = 2022
                },                new PCInvestmentKpiQuarterlyValueDraft
                {
                    PortfolioCompanyID = 1,
                    WorkflowMappingId = 3,
                    InvestmentKPIID = 101,
                    KPIInfo = "$",
                    KpiActualValue = "3767",
                    PCInvestmentKpiQuarterlyValueID = 14787,
                    Quarter = "Q2",
                    Year = 2022
                }
            };
        }

    }
}
