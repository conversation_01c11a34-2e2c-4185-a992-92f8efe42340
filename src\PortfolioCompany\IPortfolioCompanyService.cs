﻿
using Contract.Funds;
using Contract.ReportTemplateConfig;
using System.Collections.Generic;
using System.Threading.Tasks;
using Contract.Configuration;
using Contract.MasterMapping;
using Contract.Reports;
using Contract.MasterKPI;
using DataAccessLayer.Models.PageSettings;
using DataAccessLayer.DBModel;
using Contract.Deals;

namespace Contract.PortfolioCompany
{
    public interface IPortfolioCompanyService : ICompanyFinancialServices, ILimitedPartnerReportingServices
    {
        int AddPortfolioCompany(PortfolioCompanyModel PortfolioCompanyModel);
        int EditPortfolioCompany(PortfolioCompanyModel portfolioCompanyModel);

        int AddPortfolioCompanyList(List<BulkUploadPortFolioCompanyDetails> portfolioCompanyModelList);

        PortfolioCompanyListModel GetPortfolioCompanyList(PortfolioCompanyFilter filter);

        PortfolioCompanyListModel GetCompanyNames(PortfolioCompanyFilter filter);

        Task<PortfolioCompanyModel> GetPortfolioCompanyById(int PortfolioCompanyId, MSubSectionFields mSubSection = null);
        Task<PortfolioCompanyModel> GetPortfolioCompanyDraftById(int PortfolioCompanyId, int workflowRequestId);

        Task<List<PortfolioCompanyModel>> GetPortfolioCompaniesByFund(int fundId);

        List<PortfolioCompanyEmployeeModel> GetPortfolioCompanyEmployeeList();

        List<SectorwiseKpiDetails> GetSectorwiseKPIListForPortfolioCompany(int portfolioCompanyId);

        PortfolioCompanyListModel GetDuplicatePortFolioCompany(List<string> filter);
        Task<PortfolioCompanyQueryListModel> GetPortfolioCompanyListQuery(PortfolioCompanyFilter filter, bool isAccessFilterEnable = false);

        int TotalPortfolioCompaniesCount();

        List<FundingTypeModel> GetAllFundingTypes();
        Task<LPReportConfigModels> GetLPReportConfiguration(string fundId);

        Task<FinancialTypesModel> GetFinancialValueTypesList();
        PcMasterKpiValueListModel GetMasterKPIData(int PortfolioCompanyID, string ModuleName, ReportSectionWiseKpiList reportMappingKPIs);
        System.Data.DataSet GetCompanyKPI(PcCompanyKpiMonthlyValueFilter filter, ReportSectionWiseKpiList reportMappingKPIs);
        FundStaticData GetFunds(int fundId);
        Task<List<CompanyListModel>> GetCompanyList();
        Task<List<RegionCountryMappingModel>> GetGeoLocationByPortfolioCompanyId(List<int> portfolioCompanyIDs);
        Task<List<PageColumnModel>> GetPCConfigurationDataForCompanyList(List<SubPageFieldModel> companyStaticFieldsData, PortfolioCompanyQueryListModel companyData, int sectionId);
        List<PageColumnModel> FilterAndSort(List<PageColumnModel> companyStaticConfiguartionData, PortfolioCompanyFilter filter, ref int totalCompanyRows);
        List<PageColumnHeaderModel> GetListHeaders(List<SubPageFieldModel> headers);
        Task<DashboardTotalfundsCompaniesModel> GetLatestQuarterYearTotalPortfolioCompaniesFundsCount();
        Task<string> GetMasterCompaniesCount();
        Task<bool> UpdateEncryptCompanyId();
        Task<bool> UpdateEncryptFundId();
        Task<bool> UpdateEncryptDealId();
        Task<bool> AddOrUpdatePortfolioCustomList(List<CustomPortfolioGroupList> customPortfolioGroupLists, int userId, int companyId);
        Task<List<CustomPortfolioGroupList>> GetPortfolioCompanyTypeListDetails(int featureId);
        Task<CompanyModelList> GetCompanyNameAndId(PortfolioCompanyFilter filter);
        Task<PortfolioCompanyModel> GetPortfolioCompanyDraftById(int PortfolioCompanyId);
        List<PageFieldValueModel> GetPageFieldModelDTOtDetails(List<SubPageFieldModel> pcStaticFields);
        Task<List<int>> GetCompanyListByUserId(int userId);
        Task<PortfolioCompanyDetails> FetchCompanyDetailsById(int PortfolioCompanyId);
        Task<IngestionCompanyModel> GetPortfolioCompanyDetailsById(int PortfolioCompanyId);
        Task<DealDataModel> GetDealId(int PortfolioCompanyId);
        Task<PortfolioCompanyFundHoldingModel> GetFundHoldingValues(int dealId);

    }
}