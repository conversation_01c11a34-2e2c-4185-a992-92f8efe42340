﻿using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Audit.Enums;
using Contract.Account.Model;
using Contract.BulkUpload;
using Contract.Deals;
using Contract.Funds;
using Contract.KPI;
using Contract.MasterKPI;
using Contract.PortfolioCompany;
using Contract.Utility;
using DapperRepository;
using DapperRepository.Constants;
using DataAccessLayer.DBModel;
using DataAccessLayer.Models;
using DataAccessLayer.UnitOfWork;
using Imports.Helpers;
using Microsoft.Extensions.Logging;
using OfficeOpenXml;
using Shared;
using Utility.Helpers;
using Utility.Resource;

namespace Master
{
    public class MasterKpiService : IMasterKpiService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<MasterKpiService> _logger;
        private readonly IDapperGenericRepository _dapperGenericRepository;
        public MasterKpiService(IUnitOfWork unitOfWork, ILogger<MasterKpiService> logger, IDapperGenericRepository dapperGenericRepository)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
            _dapperGenericRepository = dapperGenericRepository;
        }

        #region Add KPI
        public int AddOrUpdateKPI(KpiModel kpiModel, int userId)
        {
            if (kpiModel?.MasterKpiDetails?.MasterKpiID > 0)
            {
                kpiModel.MasterKpiDetails.ModifiedBy = userId;
                UpdateMasterKPIs(kpiModel);
            }
            else
            {
                if (_unitOfWork.M_MasterKpisRepository.ExistsAny(x => x.KPI.ToLower() == kpiModel.MasterKpiDetails.KPI.ToLower() && !x.IsDeleted && x.ModuleID == kpiModel.MasterKpiDetails.ModuleID))
                    return -1;
                else
                {
                    kpiModel.MasterKpiDetails.CreatedBy = userId;
                    AddMasterKPIs(kpiModel);
                }

            }
            return 1;
        }
        private void AddMasterKPIs(KpiModel kpiModel)
        {
            M_MasterKpis masterKpis = new()
            {
                KPI = kpiModel.MasterKpiDetails.KPI,
                ModuleID = kpiModel.MasterKpiDetails.ModuleID,
                KpiInfo = kpiModel.MasterKpiDetails.KpiInfo,
                Description = kpiModel.MasterKpiDetails.Description,
                CreatedBy = kpiModel.MasterKpiDetails.CreatedBy,
                MethodologyID = (kpiModel.MasterKpiDetails.KpiInfo != Constants.KpiInfoText) ? kpiModel.MasterKpiDetails.MethodologyID : 0,
                CreatedOn = DateTime.Now,
                IsDeleted = false,
                IsBoldKPI = kpiModel.MasterKpiDetails.IsBoldKPI,
                IsHeader = kpiModel.MasterKpiDetails.IsHeader,
                Synonym = kpiModel.Synonym
            };
            _unitOfWork.M_MasterKpisRepository.Insert(masterKpis);
            _unitOfWork.Save();
        }
        #endregion       

        #region GetKPIs
        public MasterKpiListModel GetMasterKPIList(string module)
        {
            MasterKpiListModel listModel = new();
            var methodologies = _unitOfWork.M_MethodologyRepository.GetQueryable().Where(y => !y.IsDeleted).ToList();
            listModel.MasterKpis = _unitOfWork.M_MasterKpisRepository.GetMany(x => !x.IsDeleted && x.ModuleID == Convert.ToInt32(module)).Select(x => new M_MasterKpis
            {
                MasterKpiID = x.MasterKpiID,
                KPI = x.KPI,
                Description = x.Description,
                KpiInfo = x.KpiInfo,
                CreatedBy = x.CreatedBy,
                CreatedOn = x.CreatedOn,
                MethodologyID = x.MethodologyID,
                ModuleID = x.ModuleID,
                Formula = x.Formula,
                FormulaKPIId = x.FormulaKPIId,
                MethodologyName = methodologies.FirstOrDefault(y => y.MethodologyID == x.MethodologyID)?.MethodologyName,
                KpiInfoType = GetKpiInfoType(x.KpiInfo),
                IsBoldKPI = x.IsBoldKPI,
                IsHeader = x.IsHeader,
                Synonym = x.Synonym
            }).OrderBy(x => x.KPI).ToList();

            return listModel;
        }
        public MasterKpiListModel GetMasterKPITabs(string Type)
        {
            MasterKpiListModel listModel = new();
            switch (Type)
            {
                case "Financials":
                    {
                        listModel.KpiListModel = _unitOfWork.M_KpiModulesRepository.GetMany(x => !x.IsDeleted && x.IsFinacials && x.IsActive && x.Name != "StaticInformation").OrderByDescending(s => s.OrderBy.HasValue).ThenBy(s => s.OrderBy).Select(x => new KpiListModel
                        {
                            Name = x.TabName == null ? x.Name : x.TabName,
                            Active = false,
                            IsFinacials = x.IsFinacials,
                            TabAliasName = x.AliasName,
                            ModuleId = x.ModuleID
                        }).ToList();
                    }
                    break;
                default:
                    {
                        listModel.KpiListModel = _unitOfWork.M_KpiModulesRepository.GetMany(x => !x.IsDeleted && !x.IsFinacials && x.IsActive && x.Name != "StaticInformation").OrderByDescending(s => s.OrderBy.HasValue).ThenBy(s => s.OrderBy).Select(x => new KpiListModel
                        {
                            Name = x.TabName == null ? x.Name : x.TabName,
                            Active = false,
                            IsFinacials = x.IsFinacials,
                            ModuleId = x.ModuleID,
                            TabAliasName = x.AliasName
                        }).ToList();

                    }
                    break;
            }
            return listModel;

        }
        public  List<BulkUploadModules> GetBulkUploadKpiModules()
        {
            List<BulkUploadModules> bulkUploads = new() {
                new BulkUploadModules {ModuleID = 1, ModuleName = "User", Alias = "", PageConfigAliasName = "User"},
                new BulkUploadModules {ModuleID = 2, ModuleName = "Deal", Alias = "", PageConfigAliasName = "Deal"},
                new BulkUploadModules {ModuleID = 3, ModuleName = "Firm", Alias = "", PageConfigAliasName = "Firm"},
                new BulkUploadModules {ModuleID = 4, ModuleName = "Fund", Alias = "", PageConfigAliasName = "Fund"},
                new BulkUploadModules {ModuleID = 5, ModuleName = "Portfolio Company", Alias = "", PageConfigAliasName = "Portfolio Company"},
                new BulkUploadModules {ModuleID = 6, ModuleName = "Financials", Alias = "Financials", PageConfigAliasName = "Financials"},
                new BulkUploadModules {ModuleID = 29, ModuleName = "Exchange Rates", Alias = "", PageConfigAliasName = "Exchange Rates"},
                new BulkUploadModules {ModuleID = 30, ModuleName = "Valuation Table", Alias = "ValuationData", PageConfigAliasName = "Valuation Table"},
                new BulkUploadModules {ModuleID = 31, ModuleName = "Adhoc", Alias = "Adhoc", PageConfigAliasName = "Adhoc"},
                new BulkUploadModules {ModuleID = 32, ModuleName = "Investor", Alias = "Investor", PageConfigAliasName = "Investor"},
                new BulkUploadModules {ModuleID = 33, ModuleName = "ESG", Alias = "ESG", PageConfigAliasName = "ESG" }

            };
            AddMonthlyReport(bulkUploads);
            List<BulkUploadModules> bulkKpis = new() {
                new BulkUploadModules {ModuleID = 7, ModuleName = "Company KPI", Alias = "CompanyKPIs", PageConfigAliasName = ""},
                new BulkUploadModules {ModuleID = 8, ModuleName = "Impact KPI", Alias = "ImpactKPIs", PageConfigAliasName = ""},
                new BulkUploadModules {ModuleID = 9, ModuleName = "Investment KPI", Alias = "InvestmentKPIs", PageConfigAliasName = ""},
                new BulkUploadModules {ModuleID = 11, ModuleName = "Operational KPI", Alias = "OperationalKPIs", PageConfigAliasName = ""},
                new BulkUploadModules {ModuleID = 13, ModuleName = "Trading Records", Alias = "TradingRecords", PageConfigAliasName = ""},
                new BulkUploadModules {ModuleID = 14, ModuleName = "Credit KPI", Alias = "CreditKPI", PageConfigAliasName = "Financials"},
            };
            AddCustomAndOtherKpis(bulkKpis);
            List<KpiModules> kpis = GetPageConfigByKpiModules(new() { (int)PageConfigurationSubFeature.KeyPerformanceIndicator, (int)PageConfigurationSubFeature.OtherKPIs, (int)PageConfigurationSubFeature.CompanyFinancials }).Where(x => x.SubPageId != (int)PageConfigurationSubFeature.CompanyFinancials).ToList();
            List<BulkUploadModules> bulkMappingkpis = bulkKpis.Join(kpis, e1 => e1.ModuleName, e2 => e2.name, (e1, e2) => new BulkUploadModules { ModuleID = e1.ModuleID, ModuleName = e1.ModuleName, Alias = e1.Alias, PageConfigAliasName = e2.PageConfigAliasName }).ToList();
            if (bulkMappingkpis.Count > 0)
                bulkUploads.AddRange(bulkMappingkpis);
            var capTable = GetCapTable();
            if (capTable.Count > 0)
                bulkUploads.AddRange(capTable);
            return bulkUploads.OrderBy(x => x.ModuleID).ToList();
        }

        /// <summary>
        /// AddCustomAndOtherKpis
        /// </summary>
        /// <param name="bulkKpis"></param>
        private static void AddCustomAndOtherKpis(List<BulkUploadModules> bulkKpis)
        {
            bulkKpis.Add(new BulkUploadModules { ModuleID = 15, ModuleName = Constants.Custom_Table1, Alias = Constants.CustomTable1, PageConfigAliasName = string.Empty });
            bulkKpis.Add(new BulkUploadModules { ModuleID = 16, ModuleName = Constants.Custom_Table2, Alias = Constants.CustomTable2, PageConfigAliasName = string.Empty });
            bulkKpis.Add(new BulkUploadModules { ModuleID = 17, ModuleName = Constants.Custom_Table3, Alias = Constants.CustomTable3, PageConfigAliasName = string.Empty });
            bulkKpis.Add(new BulkUploadModules { ModuleID = 18, ModuleName = Constants.Custom_Table4, Alias = Constants.CustomTable4, PageConfigAliasName = string.Empty });
            bulkKpis.Add(new BulkUploadModules { ModuleID = 19, ModuleName = Constants.Other_KPI1, Alias = Constants.OtherKPI1, PageConfigAliasName = string.Empty });
            bulkKpis.Add(new BulkUploadModules { ModuleID = 20, ModuleName = Constants.Other_KPI2, Alias = Constants.OtherKPI2, PageConfigAliasName = string.Empty });
            bulkKpis.Add(new BulkUploadModules { ModuleID = 21, ModuleName = Constants.Other_KPI3, Alias = Constants.OtherKPI3, PageConfigAliasName = string.Empty });
            bulkKpis.Add(new BulkUploadModules { ModuleID = 22, ModuleName = Constants.Other_KPI4, Alias = Constants.OtherKPI4, PageConfigAliasName = string.Empty });
            bulkKpis.Add(new BulkUploadModules { ModuleID = 23, ModuleName = Constants.Other_KPI5, Alias = Constants.OtherKPI5, PageConfigAliasName = string.Empty });
            bulkKpis.Add(new BulkUploadModules { ModuleID = 24, ModuleName = Constants.Other_KPI6, Alias = Constants.OtherKPI6, PageConfigAliasName = string.Empty });
            bulkKpis.Add(new BulkUploadModules { ModuleID = 25, ModuleName = Constants.Other_KPI7, Alias = Constants.OtherKPI7, PageConfigAliasName = string.Empty });
            bulkKpis.Add(new BulkUploadModules { ModuleID = 26, ModuleName = Constants.Other_KPI8, Alias = Constants.OtherKPI8, PageConfigAliasName = string.Empty });
            bulkKpis.Add(new BulkUploadModules { ModuleID = 27, ModuleName = Constants.Other_KPI9, Alias = Constants.OtherKPI9, PageConfigAliasName = string.Empty });
            bulkKpis.Add(new BulkUploadModules { ModuleID = 28, ModuleName = Constants.Other_KPI10, Alias = Constants.OtherKPI10, PageConfigAliasName = string.Empty });
        }

        private void AddMonthlyReport(List<BulkUploadModules> bulkUploads)
        {
            var monthlyReportAliasName = _unitOfWork.SubPageFieldsRepository.GetQueryable().FirstOrDefault(x => !x.IsDeleted && x.IsActive && (x.SubPageID == (int)PageConfigurationSubFeature.Reports) && x.Name == Constants.MonthlyReportName)?.AliasName;
            if (!string.IsNullOrEmpty(monthlyReportAliasName))
            {
                bulkUploads.Add(new BulkUploadModules { ModuleID = 10, ModuleName = "Monthly Report", Alias = "", PageConfigAliasName = monthlyReportAliasName });
            }
        }

        public List<KpiModules> GetPageConfigByKpiModules(List<int> subPageIds)
        {
            List<M_SubPageFields> m_SubPageFields = _unitOfWork.SubPageFieldsRepository.GetManyQueryable(x => !x.IsDeleted && x.IsActive && subPageIds.Contains(x.SubPageID)).ToList();
            return _unitOfWork.M_KpiModulesRepository.GetManyQueryable(x => !x.IsDeleted && x.IsActive).Join(
                                     m_SubPageFields, e1 => e1.PageConfigFieldName,
                                            e2 => e2.Name,
                                            (e1, e2) => new KpiModules { ModuleID = e1.ModuleID, name = e1.AliasName, field = e1.Name, PageConfigAliasName = e2.AliasName, SubPageId = e2.SubPageID, Order = e2.SequenceNo }
                                     ).OrderBy(x => subPageIds.IndexOf(x.SubPageId)).ThenBy(x => x.Order).ToList();
        }
        public List<KpiModules> GetPageConfigByFundKpiModules(List<int> subPageIds)
        {
            List<M_SubPageFields> m_SubPageFields = _unitOfWork.SubPageFieldsRepository.GetManyQueryable(x => !x.IsDeleted && x.IsActive && subPageIds.Contains(x.SubPageID)).ToList();
            return _unitOfWork.MFundKpiModulesRepository.GetManyQueryable(x => !x.IsDeleted).Join(
                                     m_SubPageFields, e1 => e1.PageConfigFieldName,
                                            e2 => e2.Name,
                                            (e1, e2) => new KpiModules { ModuleID = e1.ModuleId, name = e1.AliasName, field = e1.Name, PageConfigAliasName = e2.AliasName, SubPageId = e2.SubPageID, Order = e2.SequenceNo }
                                     ).OrderBy(x => subPageIds.IndexOf(x.SubPageId)).ThenBy(x => x.Order).ToList();
        }
        public async Task<List<KpiModules>> GetEsgKpiByPageId()
        {
            var result = await _unitOfWork.SubPageDetailsRepository.FindAllAsync(x => x.IsActive && !x.IsDeleted && x.PageID == (int)PageConfigurationFeature.ESG);
            return result.Select(x => new KpiModules
            {
                field = x.Name,
                name = x.AliasName,
                Order = x.SequenceNo,
                SubPageId = x.SubPageID,
                PageConfigAliasName = x.AliasName
            }).ToList();
        }
        /// <summary>
        /// Retrieves the Cap Table bulk upload modules.
        /// </summary>
        /// <returns>A list of <see cref="BulkUploadModules"/> representing the Cap Table bulk upload modules.</returns>
        public List<BulkUploadModules> GetCapTable()
        {
            var result =  _unitOfWork.SubPageDetailsRepository.GetQueryable().FirstOrDefault(x => x.IsActive && !x.IsDeleted && x.SubPageID == (int)PageConfigurationSubFeature.CapTable);
            if(result!=null)
            {
                var isFieldExist =  _unitOfWork.SubPageFieldsRepository.ExistsAny(x=>!x.IsDeleted && x.IsActive && x.SubPageID == result.SubPageID);
                if(isFieldExist)
                {
                    return [new BulkUploadModules { ModuleID = result.SubPageID, ModuleName = result.Name, Alias = result.AliasName, PageConfigAliasName = result.AliasName }];
                }
            }
            return [];
        }
        public MethodologyModelList GetMethodology()
        {
            MethodologyModelList listModel = new();
            listModel.ModuleList = GetPageConfigByKpiModules(new() { (int)PageConfigurationSubFeature.KeyPerformanceIndicator, (int)PageConfigurationSubFeature.CompanyFinancials });
            listModel.MethodologyModel = _unitOfWork.M_MethodologyRepository.GetManyQueryable(x => !x.IsDeleted).Select(x => new MethodologyModel
            {
                Id = x.MethodologyID,
                Name = x.MethodologyName,

            }).ToList();
            return listModel;
        }
        public async Task<GroupKpiModel> GetMethodologyByGroup()
        {
            GroupKpiModel groupKpiModel = new();
            List<GroupModule> groupModules =
            [
                new GroupModule()
                {
                    AliasName = Constants.PortfolioCompany,
                    Name = Constants.PortfolioCompany,
                    Items = GetPageConfigByKpiModules([(int)PageConfigurationSubFeature.KeyPerformanceIndicator, (int)PageConfigurationSubFeature.OtherKPIs, (int)PageConfigurationSubFeature.CompanyFinancials])
                },
                new GroupModule()
                {
                    AliasName = Constants.ESG,
                    Name = Constants.ESG,
                    Items = await GetEsgKpiByPageId()
                },
                GetGroupModule(Constants.MCapTable,(int)PageConfigurationSubFeature.CapTable),
                GetGroupModule(Constants.MOtherCapTable,(int)PageConfigurationSubFeature.OtherCapTable),
                GetGroupModule(Constants.Reports,(int)PageConfigurationSubFeature.Reports),
                new GroupModule()
                {
                    AliasName = Constants.Fund,
                    Name = Constants.Fund,
                    Items = GetPageConfigByFundKpiModules([(int)PageConfigurationSubFeature.FundKpis,(int)PageConfigurationSubFeature.FundKeyKpis])
                },
            ];

            var methodology = await _unitOfWork.M_MethodologyRepository.FindAllAsync(x => !x.IsDeleted);
            groupKpiModel.MethodologyModel = methodology.Select(x => new MethodologyModel
            {
                Id = x.MethodologyID,
                Name = x.MethodologyName,

            }).ToList();
            groupKpiModel.GroupModel = groupModules.Where(x => x.Items.Count > 0).ToList();
            return groupKpiModel;
        }
        /// <summary>
        /// Represents a group module.
        /// </summary>
        private GroupModule GetGroupModule(string name,int subPageId)
        {
                return new GroupModule()
                {
                    AliasName = _unitOfWork.SubPageDetailsRepository.GetQueryable().FirstOrDefault(x => x.IsActive && !x.IsDeleted && x.Name == name)?.AliasName,
                    Name = name,
                    Items = GetPageConfigByKpiModules([subPageId])
                };
        }
        public async Task<PcMasterKpiValueListModel> GetPCMasterKPIValue(PcMasterKpiValueFilter filter)
        {
            var pCMasterKPIValueList = new PcMasterKpiValueListModel();
            var masterKpiValues = await _dapperGenericRepository.Query<M_MasterKpis>(SqlConstants.QueryByMasterKpis, new { @moduleId = filter.ModuleID });
            var valueData = await _dapperGenericRepository.Query<M_ValueTypes>(SqlConstants.QueryByValueTypes);
            var pCMasterKpiValues = await _dapperGenericRepository.Query<PCMasterKpiValues>(SqlConstants.QueryByMasterKpiValues, new { @moduleId = filter.ModuleID, @companyId = filter.PortfolioCompanyID });
            var mappingKpisList = await _dapperGenericRepository.Query<Mapping_Kpis>(SqlConstants.QueryByMappingKPIs, new { @moduleId = filter.ModuleID, @companyId = filter.PortfolioCompanyID });
            var mappingKpis = mappingKpisList.Select(x => x.Mapping_KpisID).ToList();
            pCMasterKpiValues = pCMasterKpiValues.Where(x => mappingKpis.Contains(x.MappingKpisID)).ToList();
            if (filter.SearchFilter != null && pCMasterKpiValues.Any())
            {
                DateTime? toDate = DateTime.Now;
                var maxYearData = pCMasterKpiValues.Max(x => x.Year);
                var isQuarterDataExists = pCMasterKpiValues.Count(x => x.Year == maxYearData && x.Quarter != null);
                var isMonthlyDataExists = pCMasterKpiValues.Count(x => x.Year == maxYearData && x.Month != null);
                if (isQuarterDataExists > 0)
                {
                    pCMasterKpiValues = pCMasterKpiValues.Where(x => !string.IsNullOrEmpty(x.Quarter)).ToList();
                    var maxQuarter = pCMasterKpiValues.Where(x => x.Year == maxYearData).Max(x => Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1]));
                    toDate = Common.GetLastDayOfQuarter(maxYearData != null ? (int)maxYearData : 0, maxQuarter);
                    toDate = toDate.Value.AddMonths(1).AddDays(-1);
                    pCMasterKpiValues = FilterData(filter, pCMasterKpiValues.AsQueryable(), toDate)?.ToList();
                }
                else if (isMonthlyDataExists > 0)
                {
                    pCMasterKpiValues = pCMasterKpiValues.Where(x => x.Month > 0).ToList();
                    var maxMonth = pCMasterKpiValues.Where(x => x.Year == maxYearData).Max(x => x.Month);
                    toDate = new DateTime((int)maxYearData, (int)maxMonth, 1);
                    toDate = toDate.Value.AddMonths(1).AddDays(-1);
                    pCMasterKpiValues = pCMasterKpiValues.Where(x => (Convert.ToDateTime(x.Month.ToString() + "/" + x.Year.ToString()) >= toDate.Value.AddMonths(-12))).ToList();//Default 1 year filter
                }
            }
            var data = (from pcValue in pCMasterKpiValues
                        join map in mappingKpisList on pcValue.MappingKpisID equals map.Mapping_KpisID into kc
                        from ckpi in kc.DefaultIfEmpty()
                        join p in masterKpiValues on ckpi.KpiID equals p.MasterKpiID into kp
                        from pkpi in kp.DefaultIfEmpty()
                        join val in valueData on pcValue.ValueTypeID equals val.ValueTypeID
                        select new PcMasterKpiValueModel
                        {
                            PCMasterKpiValueID = (pcValue == null || pcValue.PCMasterKpiValueID == 0) ? 0 : pcValue.PCMasterKpiValueID,
                            PortfolioCompanyID = (pcValue == null || pcValue.PortfolioCompanyID == 0) ? 0 : pcValue.PortfolioCompanyID,
                            KPIInfo = (pkpi == null || pkpi.KpiInfo == null || pkpi.KpiInfo.Trim() == string.Empty) ? string.Empty : pkpi.KpiInfo.Trim(),
                            KPI = (pkpi == null || pkpi.KPI == null) ? string.Empty : FormatIfNumInfo(pkpi),
                            KPIValue = (pcValue == null || pcValue.KPIValue == "0") ? "0" : pcValue.KPIValue,
                            QuarterMonth = pcValue.Month != null ? $"({val.HeaderValue}) {Common.GetMonthName((int)pcValue.Month)}" : $"({val.HeaderValue}) {pcValue.Quarter}",
                            QuarterNumber = pcValue.Quarter != null ? Convert.ToInt32(pcValue.Quarter.Split("Q", StringSplitOptions.None)[1]) : 0,
                            Year = (pcValue == null || pcValue.Year == 0) ? 0 : pcValue.Year,
                            Half = (pcValue == null || pcValue.Half == 0) ? 0 : pcValue.Half,
                            MasterKpiID = (pkpi == null || pkpi.MasterKpiID == 0) ? 0 : pkpi.MasterKpiID,
                            ParentKPIID = (ckpi == null || ckpi.ParentKPIID == null) ? 0 : ckpi.ParentKPIID,
                            DisplayOrder = (ckpi == null || ckpi.DisplayOrder == null) ? 0 : ckpi.DisplayOrder,
                            ParentKPI = masterKpiValues.Where(x => x.MasterKpiID == ckpi.ParentKPIID).Select(x => x.KPI).FirstOrDefault(),
                            IsNumeric = pcValue.IsNumeric,
                            IsYTD = pcValue.IsYTD,
                            MappingKpisID = ckpi.Mapping_KpisID,
                            Header = val.HeaderValue,
                            Month = (pcValue == null || pcValue.Month == 0) ? 0 : pcValue.Month,
                            AuditLog = _unitOfWork.MasterKpiAuditLogRepository.ExistsAny(s => s.ModuleId == filter.ModuleID && s.AttributeId == pcValue.PCMasterKpiValueID && !s.IsDeleted),
                            IsBoldKPI = pkpi.IsBoldKPI,
                            IsHeader = pkpi.IsHeader
                        }).OrderBy(x => x.Year).ThenBy(x => x.QuarterNumber != 0 ? (int)x.QuarterNumber : (int)x.Month).OrderByDescending(x => x.DisplayOrder.HasValue).ThenBy(i => i.DisplayOrder).ThenBy(x => x.QuarterMonth.ToLower().Contains(Constants.Budget)).ThenBy(x => x.QuarterMonth.ToLower().Contains(Constants.Actual)).ToList();
            pCMasterKPIValueList.PCMasterKPIValueModels = data;
            return pCMasterKPIValueList;
        }

        private static string FormatIfNumInfo(M_MasterKpis pkpi)
        {
            return (pkpi.KpiInfo == "#" ? pkpi.KPI + " (" + pkpi.KpiInfo + ")" : pkpi.KPI);
        }

        private async Task<ExcelCodeStatus> ValidateBulkDataAsync(string fullPath)
        {
            ExcelCodeStatus status = new()
            {
                Code = "ok"
            };
            bool ErrorStatus = false;
            try
            {
                var masterKPis = await _dapperGenericRepository.Query<M_MasterKpis>(SqlConstants.QueryByMasterKpisWithModuleId, new { moduleId = 1 });
                var investmentKPis = await _dapperGenericRepository.Query<M_InvestmentKpi>(SqlConstants.QueryByInvestmentKPIS);
                FileInfo excelFile = new(fullPath);
                using ExcelPackage package = new(excelFile);
                var workSheet = package.Workbook.Worksheets[Constants.FundPcSheetName];
                var start = workSheet.Dimension.Start;
                var end = workSheet.Dimension.End;
                var TradingKPIInformation = workSheet.Cells[1, 1, 1, workSheet.Dimension.End.Column].Where(x => x.Value?.ToString()?.Contains(Constants.TradingKPIPrefix) ?? false).Select((item, index) => new { Id = item.LocalAddress.ToString().Replace("1", String.Empty), Value = item.Value.ToString().Replace(Constants.TradingKPIPrefix, String.Empty) });
                var InvestmentKPIInformation = workSheet.Cells[1, 1, 1, workSheet.Dimension.End.Column].Where(x => x.Value?.ToString()?.Contains(Constants.InvestmentKPIPrefix) ?? false).Select((item, index) => new { Id = item.LocalAddress.ToString().Replace("1", String.Empty), Value = item.Value.ToString().Replace(Constants.InvestmentKPIPrefix, String.Empty) });
                for (int row = start.Row + 2; row <= end.Row; row++)
                {
                    var rowNum = workSheet.Cells[string.Format("{0}:{0}", row)];
                    bool allEmpty = rowNum.All(c => string.IsNullOrWhiteSpace(c.Text));
                    if (allEmpty)
                        throw new ArgumentNullException($"Row is empty for :{row}");
                    if (ErrorStatus)
                        break;
                    foreach (var col in TradingKPIInformation)
                    {
                        var cell = $"{col.Id}{row}";
                        var kpiInfo = "";
                        var kpiName = "";
                        var kpi = masterKPis.FirstOrDefault(x => !x.IsDeleted && x.MasterKpiID == Convert.ToInt32(col.Value));
                        kpiInfo = kpi != null ? kpi.KpiInfo : null;
                        kpiName = kpi != null ? kpi.KPI : null;
                        if (workSheet.Cells[cell].Value != null)
                        {
                            var excelValue = workSheet.Cells[cell].Value?.ToString()?.Trim();
                            var CheckIfNumeric = ImportHelper.IsNumeric(excelValue);
                            excelValue = CheckIfNumeric ? decimal.Parse(excelValue, System.Globalization.NumberStyles.Any).ToString() : excelValue;
                            var pattern = @"^[+-]?\$?((\d{1,3})(?:,[0-9]{3}){0,1}|^[+-]?(\d{1})(?:,[0-9]{3}){0,2}|^[+-]?(\d{0,12}))(\.\d{0,16})?$";
                            if (!Regex.IsMatch(excelValue, pattern, RegexOptions.None, TimeSpan.FromSeconds(5)) && (kpiInfo != null && kpiInfo.ToLower() != "text"))
                            {
                                string[] arrDigit = excelValue.Split(".");
                                decimal? arrNumber = 0;
                                if (arrDigit[0] != null)
                                {
                                    var isKpivalue = decimal.TryParse(arrDigit[0], out decimal kpiValue);
                                    arrNumber = kpiValue;
                                    if (arrNumber?.ToString().Length > 34 && isKpivalue)
                                    {
                                        status.Message = $"{Messages.Utility_InvalidDigit}";
                                        ErrorStatus = UpdateErrorStatus(status, cell);
                                    }
                                    if (!isKpivalue)
                                    {
                                        status.Message = $"Data is invalid KPI -{kpiName}";
                                        ErrorStatus = UpdateErrorStatus(status, cell);
                                    }
                                }
                            }
                        }
                    }
                    foreach (var col in InvestmentKPIInformation)
                    {
                        var cell = $"{col.Id}{row}";
                        var kpiInfo = "";
                        var kpiName = "";
                        var kpi = investmentKPis.FirstOrDefault(x => !x.IsDeleted && x.InvestmentKpiId == Convert.ToInt32(col.Value));
                        kpiInfo = kpi != null ? kpi.KpiInfo : null;
                        kpiName = kpi != null ? kpi.Kpi : null;
                        if (workSheet.Cells[cell].Value != null)
                        {
                            var excelValue = workSheet.Cells[cell].Value?.ToString()?.Trim();
                            var CheckIfNumeric = ImportHelper.IsNumeric(excelValue);
                            excelValue = CheckIfNumeric ? decimal.Parse(excelValue, System.Globalization.NumberStyles.Any).ToString() : excelValue;
                            var pattern = @"^[+-]?\$?((\d{1,3})(?:,[0-9]{3}){0,1}|^[+-]?(\d{1})(?:,[0-9]{3}){0,2}|^[+-]?(\d{0,12}))(\.\d{0,16})?$";
                            if (!Regex.IsMatch(excelValue, pattern, RegexOptions.None, TimeSpan.FromSeconds(5)) && (kpiInfo != null && kpiInfo.ToLower() != "text"))
                            {
                                string[] arrDigit = excelValue.Split(".");
                                decimal? arrNumber = 0;
                                if (arrDigit[0] != null)
                                {
                                    var isKpivalue = decimal.TryParse(arrDigit[0], out decimal kpiValue);
                                    arrNumber = kpiValue;
                                    if (arrNumber?.ToString().Length > 12 && isKpivalue)
                                    {
                                        status.Message = $"{Messages.Utility_InvalidDigit}";
                                        ErrorStatus = UpdateErrorStatus(status, cell);
                                    }
                                    if (!isKpivalue)
                                    {
                                        status.Message = $"Data is invalid KPI -{kpiName}";
                                        ErrorStatus = UpdateErrorStatus(status, cell);
                                    }
                                }
                            }
                        }
                    }

                }
                package.Save();
            }
            catch (Exception exception)
            {
                _logger.LogError(exception, "ERROR");
                status.Message = Messages.SomethingWentWrong + "<br/>";
                status.Code = "error";
                status.CellCode = null;
                return await Task.FromResult(status).ConfigureAwait(false);
            }
            return await Task.FromResult(status).ConfigureAwait(false);
        }
        /// <summary>
        /// add common method for update status
        /// </summary>
        /// <param name="status"></param>
        /// <param name="cell"></param>
        /// <returns></returns>
        private static bool UpdateErrorStatus(ExcelCodeStatus status, string cell)
        {
            bool ErrorStatus;
            status.Code = "error";
            status.CellCode = cell;
            ErrorStatus = true;
            return ErrorStatus;
        }
        private static bool ValidateQuarter(string quarter) => quarter?.ToUpper() switch
        {
            "Q1" => true,
            "Q2" => true,
            "Q3" => true,
            "Q4" => true,
            _ => false
        };
        private List<string> GetColumnsUnpivotForKpis(MasterKpiFundModel masterKpiResult, StringBuilder sColumns, CompanyDataModel compData)
        {
            List<string> result = new();
            List<string> ExcelColHeaders = new();
            string ColumnsName = string.Empty;
            string ColumnsUnpivot = string.Empty;
            string sFields = string.Empty;
            sColumns = new("");
            masterKpiResult.BulkUploadDataModel.FirstColumnName = "KPI";
            ExcelColHeaders.Add(masterKpiResult.BulkUploadDataModel.FirstColumnName);
            ExcelColHeaders.Add("Id");
            ExcelColHeaders.Add("(Actual) " + compData.Quarter + "-" + compData.Year);
            ExcelColHeaders.Add("CreatedBy");
            ExcelColHeaders.Add("CreatedOn");
            masterKpiResult.BulkUploadDataModel.ModuleName = EnumHelper.GetEnumDescription(KpiModuleFeature.InvestmentKPI);
            masterKpiResult.BulkUploadDataModel.TableName = TableName.InvestmentKPI + masterKpiResult.BulkUploadDataModel.UserID.ToString() + EnumHelper.GetEnumDescription(TableStaging.Staging);
            foreach (string str in ExcelColHeaders)
            {
                sFields = $"{sFields}{str},";
            }
            sFields = sFields.TrimEnd(',');
            ExcelColHeaders.Clear();
            for (int iCnt = 0; iCnt <= sFields.Split(',').Length - 1; iCnt++)
            {
                string sFieldValue = sFields.Split(',')[iCnt];
                ExcelColHeaders.Add(sFieldValue);
                if (iCnt == 0)
                {
                    sColumns.Append("[" + sFields.Split(',')[iCnt] + "] VARCHAR (100)");
                }
                else
                {
                    if (sFieldValue.Contains("CreatedBy") || sFieldValue.Contains("Id"))
                    {
                        sColumns.Append(", [" + sFields.Split(',')[iCnt] + "] INT");
                    }
                    else if (sFieldValue.Contains("Quarter") || sFieldValue.Contains("Actual"))//for investment kpi
                    {
                        sColumns.Append(", [" + sFields.Split(',')[iCnt] + "] NVARCHAR(1000)");
                    }
                    else if (sFieldValue.Contains("CreatedOn"))
                    {
                        sColumns.Append(", [" + sFields.Split(',')[iCnt] + "] DATETIME");
                    }
                    else
                    {
                        sColumns.Append(", [" + sFields.Split(',')[iCnt] + "] DECIMAL(34,16)");
                    }
                }
            }

            foreach (string column in ExcelColHeaders)
            {
                if (column == masterKpiResult.BulkUploadDataModel.FirstColumnName) continue;
                if (column == "CreatedBy") continue;
                if (column == "CreatedOn") continue;
                ColumnsName += $"[{column}],";
                ColumnsUnpivot += $"ISNULL([{column}],0.00000000) AS [{column}],";
            }
            ColumnsName = ColumnsName.TrimEnd(',');
            ColumnsUnpivot = ColumnsUnpivot.TrimEnd(',');
            result.Add(ColumnsName);
            result.Add(ColumnsUnpivot);
            result.Add(sColumns.ToString());
            result.AddRange(ExcelColHeaders);
            return result;
        }
        public DataTable GetMasterKPIDataTable(PcMasterKpiValueListModel result, PcMasterKpiValueFilter filter)
        {
            DataTable dt = new("TradingRecords");
            if (result != null)
            {
                dt.Columns.Add("KPI", typeof(string));
                dt.Columns.Add("IsHeader", typeof(bool));
                dt.Columns.Add("IsBoldKPI", typeof(bool));

                var lstCol = new List<string>();
                foreach (var item in result.PCMasterKPIValueModels)
                {
                    if (item.Year > 0)
                    {
                        var colName = $"{item.QuarterMonth} {Convert.ToString(item.Year)}";
                        if (lstCol.IndexOf(colName) < 0)
                        {

                            DataColumn dataColumn = new(colName, System.Type.GetType("System.String"));
                            dataColumn.AllowDBNull = true;
                            dt.Columns.Add(dataColumn);
                            lstCol.Add(colName);
                        }
                    }
                }
                var kpiList = new List<string>();
                foreach (var item in result.PCMasterKPIValueModels)
                {
                    string info = string.Empty;
                    info = !string.IsNullOrEmpty(item.KPIInfo) ? item.KPIInfo : "";
                    if (item.Year > 0)
                    {
                        var quarterName = item.QuarterMonth;
                        if (kpiList.IndexOf(item.KPI) < 0)
                        {
                            DataRow dr = dt.NewRow();
                            dr["KPI"] = !item.IsHeader && item.ParentKPI != null ? $"    - {item.KPI}" : item.KPI;
                            dr["IsHeader"] = item.IsHeader;
                            dr["IsBoldKPI"] = item.IsBoldKPI;
                            kpiList.Add(item.KPI);
                            if (!string.IsNullOrEmpty(quarterName))
                            {
                                if (string.IsNullOrEmpty(item.KPIValue))
                                {
                                    dr[quarterName + " " + Convert.ToString(item.Year)] = "NA";
                                }
                                else if (item.KPIInfo == "x" && item.KPIValue != null && item.KPIValue != "" && item.KPIValue != "NA")
                                    dr[quarterName + " " + Convert.ToString(item.Year)] = !item.IsHeader ? Math.Round(Convert.ToDecimal(item.KPIValue), 2).ToString("F1") + item.KPIInfo : null;
                                else
                                {
                                    string value;
                                    if (info == "%")
                                        value = !item.IsHeader ? item.KPIValue + info : null;
                                    else if (info == "Text" || info == "#")
                                        value = !item.IsHeader ? GetNonCurrencyVal(item) : null;
                                    else
                                        value = !item.IsHeader ? GetCurrencyVal(filter, item) : null;
                                    dr[quarterName + " " + Convert.ToString(item.Year)] = value;
                                }
                            }
                            dt.Rows.Add(dr);
                        }
                        else
                        {
                            if (!string.IsNullOrEmpty(quarterName))
                            {
                                string kpiWithInfo = !item.IsHeader && item.ParentKPI != null ? $"    - {item.KPI}" : item.KPI;
                                kpiWithInfo = kpiWithInfo.Replace("'", "''");
                                DataRow[] oldDr = dt.Select("KPI='" + kpiWithInfo + "'");

                                foreach (DataRow row in oldDr)
                                {
                                    if (string.IsNullOrEmpty(item.KPIValue))
                                    {
                                        row[quarterName + " " + Convert.ToString(item.Year)] = "NA";
                                    }
                                    else if (item.KPIInfo == "x" && item.KPIValue != null && item.KPIValue != "" && item.KPIValue != "NA")
                                        row[quarterName + " " + Convert.ToString(item.Year)] = !item.IsHeader ? Math.Round(Convert.ToDecimal(item.KPIValue), 2).ToString("F1") + item.KPIInfo : null;
                                    else
                                    {
                                        string value;
                                        if (info == "%")
                                            value = !item.IsHeader ? item.KPIValue + info : null;
                                        else if (info == "Text" || info == "#")
                                            value = !item.IsHeader ? GetNonCurrencyVal(item) : null;
                                        else
                                            value = !item.IsHeader ? GetCurrencyVal(filter, item) : null;

                                        row[quarterName + " " + Convert.ToString(item.Year)] = value;
                                    }
                                }
                            }
                        }
                    }
                }

            }
            return dt;
        }
        #endregion

        #region Update KPI Details
        private void UpdateMasterKPIs(KpiModel kpiModel)
        {
            M_MasterKpis masterKpis = _unitOfWork.M_MasterKpisRepository.GetByID(kpiModel.MasterKpiDetails.MasterKpiID);
            masterKpis.KPI = kpiModel.MasterKpiDetails.KPI;
            masterKpis.KpiInfo = kpiModel.MasterKpiDetails.KpiInfo;
            masterKpis.Description = kpiModel.MasterKpiDetails.Description;
            masterKpis.ModifiedBy = kpiModel.MasterKpiDetails.ModifiedBy;
            masterKpis.ModifiedOn = DateTime.Now;
            masterKpis.IsBoldKPI = kpiModel.MasterKpiDetails.IsBoldKPI;
            masterKpis.IsHeader = kpiModel.MasterKpiDetails.IsHeader;
            masterKpis.MethodologyID = (kpiModel.MasterKpiDetails.KpiInfo != Constants.KpiInfoText) ? kpiModel.MasterKpiDetails.MethodologyID : 0;
            masterKpis.Synonym = kpiModel.Synonym;
            _unitOfWork.M_MasterKpisRepository.Update(masterKpis);
            _unitOfWork.Save();
        }
        #endregion

        #region Update KPI Values

        public async Task<bool> UpdateKpiValues(MasterKpiModel model, int userId)
        {
            try
            {
                var KpiValue = await _unitOfWork.PCMasterKpiValuesRepository.GetFirstOrDefaultAsync(x => !x.IsDeleted && x.PortfolioCompanyID == model.PortfolioCompanyID
                                                                                            && x.ModuleID == model.ModuleId && x.PCMasterKpiValueID == model.AttributeId);
                string previousValue = null, updatedValue = null;
                GetUpdatedAndPreviousValues(model, KpiValue, out previousValue, out updatedValue);
                MasterKpiAuditLog auditdata = new()
                {
                    AttributeId = KpiValue.PCMasterKpiValueID,
                    ModuleId = model.ModuleId,
                    OldValue = previousValue,
                    NewValue = updatedValue,
                    OldCurrency = KpiValue.KPIInfo,
                    NewCurrency = KpiValue.KPIInfo,
                    PortfolioCompanyId = model.PortfolioCompanyID,
                    AuditType = "Manual",
                    IsDeleted = false,
                    CreatedOn = DateTime.UtcNow,
                    CreatedBy = userId
                };
                _unitOfWork.MasterKpiAuditLogRepository.Insert(auditdata);
                _unitOfWork.PCMasterKpiValuesRepository.Update(KpiValue);
                _unitOfWork.Save();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"UpdateKpiValues|Exception:{ex.Message}");
            }
            return false;
        }

        private static void GetUpdatedAndPreviousValues(MasterKpiModel model, PCMasterKpiValues KpiValue, out string previousValue, out string updatedValue)
        {
            if (KpiValue.KPIInfo == Constants.KpiInfoText)
            {
                previousValue = KpiValue.KPIValue;
                KpiValue.KPIValue = updatedValue = model.Value;
            }
            else
            {
                _ = decimal.TryParse(KpiValue.KPIValue, out decimal oldValue);
                previousValue = !string.IsNullOrEmpty(KpiValue.KPIValue) && KpiValue.KPIValue != Constants.NotAvailable ? oldValue.ToString("G29") : null;
                _ = decimal.TryParse(model.Value, out decimal newValue);
                KpiValue.KPIValue = updatedValue = !string.IsNullOrEmpty(model.Value) && model.Value != Constants.NotAvailable ? newValue.ToString("G29") : null;
            }
        }

        #endregion

        #region AuditData

        public async Task<List<MasterKpiAuditValueModel>> GetAuditData(MasterKpiModel model)
        {
            var KpiValue = await _unitOfWork.PCMasterKpiValuesRepository.GetFirstOrDefaultAsync(x => !x.IsDeleted && x.PortfolioCompanyID == model.PortfolioCompanyID && x.ModuleID == model.ModuleId && x.PCMasterKpiValueID == model.AttributeId);
            var mappingKpis = await _unitOfWork.Mapping_KpisRepository.GetFirstOrDefaultAsync(x => !x.IsDeleted && x.PortfolioCompanyID == model.PortfolioCompanyID && x.ModuleID == model.ModuleId && x.Mapping_KpisID == KpiValue.MappingKpisID);
            var masterKpiValues = await _unitOfWork.M_MasterKpisRepository.GetFirstOrDefaultAsync(x => !x.IsDeleted && x.ModuleID == model.ModuleId && x.MasterKpiID == mappingKpis.KpiID);
            var dataset = _unitOfWork.MasterKpiAuditLogRepository.GetMany(x => !x.IsDeleted &&
                   x.ModuleId == model.ModuleId &&
                   x.AttributeId == model.AttributeId && x.PortfolioCompanyId == model.PortfolioCompanyID).OrderByDescending(x => x.CreatedOn).ToList();
            string MonthAndYear = GetMonthYear(KpiValue);
            return dataset.Select(x => new MasterKpiAuditValueModel()
            {
                AuditId = x.AuditId,
                AttributeId = x.AttributeId,
                ModuleId = x.ModuleId,
                PortfolioCompanyId = x.PortfolioCompanyId,
                FieldName = masterKpiValues.KPI,
                MonthAndYear = MonthAndYear,
                OldValue = x.OldValue,
                NewValue = x.NewValue,
                CreatedOn = x.CreatedOn,
                OldCurrency = x.OldCurrency,
                NewCurrency = x.NewCurrency,
                CreatedBy = _unitOfWork.UserRepository.GetFirstOrDefault(y => y.UserId == x.CreatedBy)?.FirstName + " " + _unitOfWork.UserRepository.GetFirstOrDefault(y => y.UserId == x.CreatedBy)?.LastName,
                AuditType = x.AuditType
            }).Take(1000).ToList();
        }

        #endregion

        #region PrivateMethods

        private static string GetKpiInfoType(string kpiInfo)
        {
            if (kpiInfo == "$")
                return "Currency";
            else if (kpiInfo == "#")
                return "Number";
            else
                return kpiInfo ?? "";
        }

        private static string GetMonthYear(PCMasterKpiValues value)
        {
            string MonthYear = string.Empty;
            string Year = value.Year?.ToString();
            if (value.Month == null && value.Quarter == null)
                MonthYear = Year;
            else if (value.Month == null && value.Quarter != null)
                MonthYear = $"{value.Quarter} {Year}";
            else if (value.Month != null && value.Quarter == null)
                MonthYear = $"{Common.GetMonthName((int)value.Month)} {Year}";
            return MonthYear;
        }

        private static IQueryable<PCMasterKpiValues> FilterData(PcMasterKpiValueFilter filter, IQueryable<PCMasterKpiValues> pCMasterKpiValues, DateTime? toDate)
        {
            if (filter.SearchFilter.PeriodType == Common.GetDescription(PeriodTypeQuarterConfig.CurrentQuarter))
            {
                int currentQtr = DateTime.Today.GetQuarter();
                int currentYear = DateTime.Now.Year;
                pCMasterKpiValues = pCMasterKpiValues.Where(x => (x.Quarter == "Q" + currentQtr) && (x.Year == currentYear));
            }
            else if (filter.SearchFilter.PeriodType == Common.GetDescription(PeriodTypeQuarterConfig.LastQuarter))
            {
                DateTime startDate = new DateTime(toDate.Value.Year, toDate.Value.Month, 1);
                int lastQtr = Common.PreviousQuarter(startDate);
                int qtrYear = Common.GetYearOfQuarter(startDate, lastQtr);
                pCMasterKpiValues = pCMasterKpiValues.Where(x => (x.Quarter == "Q" + lastQtr) && (x.Year == qtrYear));
            }
            else if (filter.SearchFilter.PeriodType == Common.GetDescription(PeriodTypeFeature.Last1Year))
            {
                DateTime endDate = new DateTime(toDate.Value.Year, toDate.Value.Month, 1);
                DateTime startDate = endDate.AddMonths(-12);
                pCMasterKpiValues = pCMasterKpiValues.Where(x => (Common.GetLastDayOfQuarter(x.Year != null ? (int)x.Year : 0, Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1])) >= startDate) && (Common.GetLastDayOfQuarter(x.Year != null ? (int)x.Year : 0, Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1])) <= endDate));
            }
            else if (filter.SearchFilter.PeriodType == Common.GetDescription(PeriodTypeFeature.DateRange))
                pCMasterKpiValues = pCMasterKpiValues.Where(x => (Common.GetLastDayOfQuarter(x.Year != null ? (int)x.Year : 0, Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1])) >= Convert.ToDateTime(filter.SearchFilter.StartPeriod).Date) && (Common.GetLastDayOfQuarter(x.Year != null ? (int)x.Year : 0, Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1])) <= Convert.ToDateTime(filter.SearchFilter.EndPeriod).Date.AddMonths(1)));
            else if (filter.SearchFilter.PeriodType == Common.GetDescription(PeriodTypeFeature.Custom))
                pCMasterKpiValues = pCMasterKpiValues.Where(x => (Common.GetLastDayOfQuarter(x.Year != null ? (int)x.Year : 0, Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1])) >= Common.GetLastDayOfQuarter(Convert.ToInt32(filter.SearchFilter.FromYear), Convert.ToInt32(filter.SearchFilter.FromQuarter.Split('Q', StringSplitOptions.None)[1]))) && (Common.GetLastDayOfQuarter(x.Year != null ? (int)x.Year : 0, Convert.ToInt32(x.Quarter.Split('Q', StringSplitOptions.None)[1])) <= Common.GetLastDayOfQuarter(Convert.ToInt32(filter.SearchFilter.ToYear), Convert.ToInt32(filter.SearchFilter.ToQuarter.Split('Q', StringSplitOptions.None)[1])).Date.AddMonths(1)));
            pCMasterKpiValues = pCMasterKpiValues.SortedResult(filter.SearchFilter.SortOrder);
            return pCMasterKpiValues;
        }
        public MasterKpiListModel GetPortfolioCompanyLevelKpisListByPageConfiguration(string type)
        {
            List<int> subPageIds = new() { (int)PageConfigurationSubFeature.KeyPerformanceIndicator, (int)PageConfigurationSubFeature.OtherKPIs, (int)PageConfigurationSubFeature.CompanyFinancials };
            List<M_SubPageFields> m_SubPageFields = _unitOfWork.SubPageFieldsRepository.GetManyQueryable(x => !x.IsDeleted && x.IsActive && subPageIds.Contains(x.SubPageID)).ToList();
            MasterKpiListModel listModel = new()
            {
                KpiListModel = type switch
                {
                    Constants.KpiFinancials => _unitOfWork.M_KpiModulesRepository.GetManyQueryable(x => !x.IsDeleted && x.IsActive).Join(
                                     m_SubPageFields.Where(x => x.SubPageID == (int)PageConfigurationSubFeature.CompanyFinancials).ToList(), e1 => e1.PageConfigFieldName,
                                            e2 => e2.Name,
                                            (e1, e2) => new KpiListModel { ModuleId = e1.ModuleID, TabAliasName = e2.AliasName, IsFinacials = true, Name = e1.TabName == null ? e1.Name : e1.TabName, Active = false, Order = e2.SequenceNo }
                                     ).OrderBy(x => x.Order).ToList(),
                    Constants.OtherKPIs => _unitOfWork.M_KpiModulesRepository.GetManyQueryable(x => !x.IsDeleted && x.IsActive).Join(
                                    m_SubPageFields.Where(x => x.SubPageID == (int)PageConfigurationSubFeature.OtherKPIs).ToList(), e1 => e1.PageConfigFieldName,
                                           e2 => e2.Name,
                                           (e1, e2) => new KpiListModel { ModuleId = e1.ModuleID, TabAliasName = e2.AliasName, IsFinacials = false, Name = e1.TabName == null ? e1.Name : e1.TabName, Active = false, Order = e2.SequenceNo }
                                    ).OrderBy(x => x.Order).ToList(),
                    _ => _unitOfWork.M_KpiModulesRepository.GetManyQueryable(x => !x.IsDeleted && x.IsActive).Join(
                                    m_SubPageFields.Where(x => x.SubPageID == (int)PageConfigurationSubFeature.KeyPerformanceIndicator).ToList(), e1 => e1.PageConfigFieldName,
                                           e2 => e2.Name,
                                           (e1, e2) => new KpiListModel { ModuleId = e1.ModuleID, TabAliasName = e2.AliasName, IsFinacials = false, Name = e1.TabName == null ? e1.Name : e1.TabName, Active = false, Order = e2.SequenceNo }
                                    ).OrderBy(x => x.Order).ToList(),
                }
            };
            return listModel;
        }

        private static string GetNonCurrencyVal(PcMasterKpiValueModel item)
        {
            if (item.KPIInfo == Constants.KpiInfoNumber || item.KPIValue == null)
            {
                return item.KPIValue;
            }

            if (Common.IsInteger(item.KPIValue))
            {
                return item.KPIValue.ToString();
            }

            return item.KPIValue.Replace("</br>", "");

        }

        private static string GetCurrencyVal(PcMasterKpiValueFilter filter, PcMasterKpiValueModel item)
        {
            if (item.KPIInfo == Constants.KpiInfoCurrency && item.KPIValue != null)
            {
                if (Common.IsInteger(item.KPIValue))
                {
                    return Common.ConvertValues(filter.KPIFilter.ValueType, Convert.ToDecimal(item.KPIValue), filter.KPIFilter.DecimaPlace).ToString();
                }

                return item.KPIValue.Replace("</br>", "");
            }

            return item.KPIValue;
        }
        #endregion

    }
}
