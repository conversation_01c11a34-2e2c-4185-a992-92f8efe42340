using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DataAccessLayer.DBModel
{
    public partial class M_MasterKpis : BaseModel
    {
        [Key]
        public int MasterKpiID { get; set; }
        public int ModuleID { get; set; }
        public int MethodologyID { get; set; }
        public string KPI { get; set; }
        public string Description { get; set; }
        public string KpiInfo { get; set; }
        public bool IsDeleted { get; set; }
        public bool IsHeader { get; set; }
        public bool IsBoldKPI { get; set; }
        [NotMapped]
        public string KpiInfoType { get; set; }
        [NotMapped]
        public string MethodologyName { get; set; }
        public string Formula { get; set; }
        public string FormulaKPIId { get; set; }
        public string Synonym { get; set; }
    }
}
