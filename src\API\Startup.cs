using API.Extensions;
using API.Middlewares;
using AspNetCoreRateLimit;
using BackGroundJob.BackgroundTasks;
using CurrencyRates;
using Elastic.Apm.NetCoreAll;
using HealthChecks.UI.Client;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Notification;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using API.SignalRHub;
using ESG.BackgroundProcess;
using Microsoft.AspNetCore.Http;
using System;
using DinkToPdf.Contracts;
using DinkToPdf;
using Exports.Helpers;
using Serilog;
using System.Reflection;
using System.IO;
using API.Pdf;
using Microsoft.AspNetCore.ResponseCompression;
using System.Linq;
using EmailConfiguration.Services;

namespace API
{
    [ExcludeFromCodeCoverage]
    public class Startup
    {
        public IConfiguration Configuration { get; }
        public IServiceCollection _services { get; set; }

        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public void ConfigureServices(IServiceCollection services)
        {
            services.AddDistributedMemoryCache();
            services.AddMemoryCache();
            // Add the rate limiter to the dependency injection container.
            AddRateLimiter(Configuration, services);
            services.AddInMemoryRateLimiting();
            services.AddSingleton<IRateLimitConfiguration, RateLimitConfiguration>();
            services.AddResponseCompression(options =>
            {
                options.EnableForHttps = true;
                options.Providers.Add<GzipCompressionProvider>();
            });
            services.Configure<GzipCompressionProviderOptions>(options =>
            {
                options.Level = System.IO.Compression.CompressionLevel.Fastest;
            });

            //ended rate limiter
            services.AddRevealConfiguration(Configuration);
            services.AddServiceDependancy(Configuration);
            services.AddCLOServiceDependancy(Configuration);
            services.AddPageConfigServiceDependancy(Configuration);
            services.AddKPIConfigServiceDependancy(Configuration);
            services.AddFSServices(Configuration);
            services.AddHostedService<CurrencyRatesBackgroundService>();
            services.ConfigureServicesInAssembly(Configuration);
            services.AddHostedService<QueueService>();
            services.AddHostedService<BackgroundHostedService>();
            services.AddHostedService<EmailReminderBackgroundService>();
            services.AddSingleton<IBackgroundQueue, BackgroundQueue>();
            services.AddSingleton<IBackgroundJobQueue, BackgroundJobQueue>();
            services.AddSingleton(typeof(IConverter), new SynchronizedConverter(new PdfTools()));
            services.AddScoped<PdfGenerator>();
            pdfExportDependency(services);
            _services = services;
        }

        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, ILoggerFactory loggerFactory)
        {
            var environment = Configuration["Environment"] ?? "unknown";
            if (env.IsDevelopment())
                app.UseDeveloperExceptionPage();
            else
            {
                app.UseExceptionHandler("/error");
                app.UseHsts();
            }
            app.Use(async (context, next) =>
            {  context.Response.OnStarting(state =>
                {
                    var httpContext = (HttpContext)state;
                    httpContext.Response.Headers.Remove("Server");
                    if (httpContext.Request.Path.ToString().Contains("/negotiate", StringComparison.OrdinalIgnoreCase))
                    {
                        httpContext.Response.Headers.ContentType = "application/json;charset=utf-8";
                    };
                    return System.Threading.Tasks.Task.CompletedTask;
                }, context);

                await next();
            });
            //Add response compression early in the pipeline
            app.UseResponseCompression();
            app.UseIpRateLimiting();
            app.UseInputValidation();
            app.UseMiddleware<GlobalExceptionHandler>();
            app.UseMiddleware<CorsMiddleware>();
            app.UseMiddleware<RestrictMethodMiddleware>();
            app.UseMiddleware<UrlValidationMiddleware>();
            app.UseMiddleware<TokenManagerMiddleware>();
            app.UseStaticFiles();
            app.UseAuthentication();
            if (environment != "PROD")
            {
                app.UseSwagger();
                app.UseSwaggerUI(c =>
                {
                    c.SwaggerEndpoint("./swagger/v1/swagger.json", "Beat Foliosure APIS");
                    c.RoutePrefix = string.Empty;
                    c.ConfigObject.DeepLinking = false;
                });
            }
            app.UseAllElasticApm(Configuration);
            app.UseRouting();
            app.UseCors("CorsPolicy");
            app.UseAuthorization();
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapHub<BroadcastHub>("/notify");
                endpoints.MapHub<FileStatusHub>("/file-upload-notification");
            });
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
                endpoints.MapHealthChecks("/health", new HealthCheckOptions
                {
                    ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
                });

                endpoints.MapHealthChecksUI();
            });

        }
        private static void AddRateLimiter(IConfiguration configuration, IServiceCollection services)
        {
            var RateLimitingOptions = new RateLimiting();
            // Get the rate limiting configuration from the application configuration.
            configuration.GetSection("RateLimiting").Bind(RateLimitingOptions);

            services.Configure<IpRateLimitOptions>(options =>
            {
                options.EnableEndpointRateLimiting = true;
                options.StackBlockedRequests = false;
                options.HttpStatusCode = 429;
                options.RealIpHeader = "X-Real-IP";
                options.ClientIdHeader = "X-ClientId";
                options.GeneralRules = new List<RateLimitRule>
                {
                    new RateLimitRule { Endpoint = "*",
                    Period = (RateLimitingOptions.Window==0 ? 5 : RateLimitingOptions.Window)+"s",
                    Limit = RateLimitingOptions.PermitLimit==0 ? 100 : RateLimitingOptions.PermitLimit
                    }
                };
            });
            services.AddSingleton<IRateLimitCounterStore, MemoryCacheRateLimitCounterStore>();
            services.AddSingleton<IRateLimitConfiguration, RateLimitConfiguration>();
            services.AddInMemoryRateLimiting();
        }
        private static void pdfExportDependency(IServiceCollection services)
        {
            var context = new CustomAssemblyLoadContext();
            try
            {
                string rootDirectory = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
                string libraryPath = string.Empty;

                switch (Environment.OSVersion.Platform)
                {
                    case PlatformID.Unix:
                        libraryPath = Path.Combine(rootDirectory, "lib", "libwkhtmltox.dylib");
                        break;
                    default:
                        libraryPath = Path.Combine(rootDirectory, "lib", "libwkhtmltox.dll");
                        break;
                }
                context.LoadUnmanagedLibrary(libraryPath);
            }
            catch (Exception exception)
            {
                Log.Logger.Error(exception, "ExportDependency:Exception: {ExceptionMessage}", exception.Message);
            }
            services.AddSingleton(typeof(IConverter), new SynchronizedConverter(new PdfTools()));
        }

        internal class RateLimiting
        {
            public int Window { get; internal set; }
            public int PermitLimit { get; internal set; }
            public int QueueLimit { get; internal set; }
        }
    }
}
