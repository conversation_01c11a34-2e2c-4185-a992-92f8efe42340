using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DataAccessLayer.Models.EmailNotifications;
using EmailConfiguration.DTOs;

namespace EmailConfiguration.Interfaces
{
    public interface IEmailRemainderService
    {
        /// <summary>
        /// Creates a new email reminder
        /// </summary>
        /// <param name="dto">Email reminder creation data</param>
        /// <param name="userId">ID of the user creating the reminder</param>
        /// <returns>The ID of the created reminder</returns>
        Task<Guid> CreateRemainderAsync(CreateEmailRemainderDto dto, int userId);

        /// <summary>
        /// Gets an email reminder by ID
        /// </summary>
        /// <param name="reminderId">The reminder ID</param>
        /// <returns>Email reminder response DTO</returns>
        Task<EmailRemainderDefaultDataResponseDto> GetRemainderDefaultsByIdAsync(Guid reminderId);

        /// <summary>
        /// Updates an existing email reminder
        /// </summary>
        /// <param name="reminderId">The reminder ID</param>
        /// <param name="dto">Updated reminder data</param>
        /// <param name="userId">ID of the user updating the reminder</param>
        /// <returns>True if successful</returns>
        Task<bool> UpdateRemainderAsync(Guid reminderId , UpdateEmailRemainderDto dto, int userId);

        /// <summary>
        /// Soft deletes an email reminder
        /// </summary>
        /// <param name="reminderId">The reminder ID</param>
        /// <param name="userId">ID of the user deleting the reminder</param>
        /// <returns>True if successful</returns>
        Task<bool> DeleteRemainderAsync(Guid reminderId, int userId);

        /// <summary>
        /// Gets pending email reminders for the specified date
        /// </summary>
        /// <param name="targetDate">The target date to check for pending reminders</param>
        /// <returns>List of pending email reminder notifications</returns>
        Task<List<EmailReminderNotificationDto>> GetPendingRemindersAsync(DateTime targetDate);

        /// <summary>
        /// Updates email reminder schedule status
        /// </summary>
        /// <param name="scheduleId">The schedule ID</param>
        /// <param name="status">The new status (0=Pending, 1=Sent, 2=Failed)</param>
        /// <param name="error">Error message if status is Failed</param>
        /// <returns>True if successful</returns>
        Task<bool> UpdateScheduleStatusAsync(int scheduleId, ReminderStatus status, string error = null);

        /// <summary>
        /// Skips pending reminders in current cycle and creates next cycle schedules
        /// </summary>
        /// <param name="reminderId">The reminder ID</param>
        /// <param name="userId">User ID for audit</param>
        /// <returns>True if successful</returns>
        Task<bool> SkipReminderCycleAsync(Guid reminderId, int userId);

        /// <summary>
        /// Gets a list of all email reminders with associated company names and document types
        /// </summary>
        /// <returns>List of email reminder details</returns>
        Task<List<EmailReminderListResponseDto>> GetEmailReminderListAsync();

        /// <summary>
        /// Gets email reminder details including TO, CC recipients, Subject , MessageBody , Follow-up reminder stats.
        /// </summary>
        /// <param name="reminderId">The reminder ID</param>
        /// <returns>Email reminder details with recipients</returns>
        Task<EmailReminderDetailsDto> GetEmailReminderDetailsAsync(Guid reminderId);

        /// <summary>
        /// Gets email reminder details including TO, CC recipients, Subject , MessageBody , follow-up section details
        /// </summary>
        /// <param name="reminderId"></param>
        /// <returns></returns>
        Task<UpdateEmailRemainderDto> GetEmailRemainderDetailsForEditAsync(Guid reminderId);
    }
}
