﻿using API.Controllers.EmailNotification;
using API.Helpers;
using EmailConfiguration.DTOs;
using EmailConfiguration.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using Xunit;

namespace DataCollection.UnitTest.Controller
{
    public class UserInformationControllerTests
    {
        private readonly Mock<IUserInformationService> _userInfoServiceMock;        
        private readonly Mock<ILogger<UserInformationController>> _loggerMock;
        private readonly Mock<IHelperService> _helperServiceMock;
        private readonly UserInformationController _controller;

        public UserInformationControllerTests()
        {
            _userInfoServiceMock = new Mock<IUserInformationService>();            
            _loggerMock = new Mock<ILogger<UserInformationController>>();
            _helperServiceMock = new Mock<IHelperService>();

            _controller = new UserInformationController(
                _userInfoServiceMock.Object,                
                _loggerMock.Object,
                _helperServiceMock.Object
            );
        }

        [Fact]
        public async Task CreateUserInformation_ValidModel_ReturnsOkResult()
        {
            // Arrange
            var createUserDto = new CreateUserInformationDto
            {
                Name = "Test User",
                Email = "<EMAIL>",
                CategoryId = 1,
                EntityId = 123,
                DocumentTypeIds = new List<int> { 1, 2, 3 }
            };

            int expectedUserId = 10;
            int currentUserId = 5;

            var responseDto = new EmailConfiguration.DTOs.ResponseDto<int>
            {
                IsSuccess = true,
                Data = expectedUserId,
                Message = "Success"
            };

            _helperServiceMock.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>()))
                .Returns(currentUserId);

            _userInfoServiceMock.Setup(s => s.CreateUserInformationWithDocumentTypesAsync(
                    It.IsAny<CreateUserInformationDto>(), currentUserId))
                .ReturnsAsync(responseDto);

            // Act
            var result = await _controller.CreateUserInformation(createUserDto);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);            

            _userInfoServiceMock.Verify(s => s.CreateUserInformationWithDocumentTypesAsync(
                createUserDto, currentUserId), Times.Once);
        }

        [Fact]
        public async Task CreateUserInformation_InvalidModel_ReturnsBadRequest()
        {
            // Arrange
            var createUserDto = new CreateUserInformationDto
            {
                // Missing required Name property
                Email = "<EMAIL>",
                CategoryId = 1,
                DocumentTypeIds = new List<int> { 1, 2, 3 }
            };

            _controller.ModelState.AddModelError("Name", "The Name field is required.");

            // Act
            var result = await _controller.CreateUserInformation(createUserDto);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);
        }

        [Fact]
        public async Task GetUserInformationByCompany_InvalidModelState_ReturnsBadRequest()
        {
            // Arrange
            int companyId = 0;
            _controller.ModelState.AddModelError("companyId", "CompanyId must be greater than 0");

            // Act
            var result = await _controller.GetUserInformationByCompany(companyId);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);
        }

        [Fact]
        public async Task GetCategoriesAndDocumentTypes_InvalidModelState_ReturnsBadRequest()
        {
            // Arrange
            int companyId = 0;
            _controller.ModelState.AddModelError("companyId", "CompanyId must be greater than 0");

            // Act
            var result = await _controller.GetCategoriesAndDocumentTypes(companyId);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);
        }

        [Fact]
        public async Task CreateUserInformation_WithNullDocumentTypeIds_ReturnsBadRequest()
        {
            // Arrange
            var createUserDto = new CreateUserInformationDto
            {
                Name = "Test User",
                Email = "<EMAIL>",
                CategoryId = 1,
                DocumentTypeIds = null // Required field is null
            };

            _controller.ModelState.AddModelError("DocumentTypeIds", "The DocumentTypeIds field is required.");

            // Act
            var result = await _controller.CreateUserInformation(createUserDto);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);
        }

        [Fact]
        public async Task CreateUserInformation_ServiceThrowsException_PropagatesException()
        {
            // Arrange
            var createUserDto = new CreateUserInformationDto
            {
                Name = "Test User",
                Email = "<EMAIL>",
                CategoryId = 1,
                DocumentTypeIds = new List<int> { 1, 2, 3 }
            };

            int currentUserId = 5;

            _helperServiceMock.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>()))
                .Returns(currentUserId);

            _userInfoServiceMock.Setup(s => s.CreateUserInformationWithDocumentTypesAsync(
                    It.IsAny<CreateUserInformationDto>(), currentUserId))
                .ThrowsAsync(new System.Exception("Service error"));

            // Act & Assert
            await Assert.ThrowsAsync<System.Exception>(
                () => _controller.CreateUserInformation(createUserDto));

        }        

        [Fact]
        public async Task GetUserInformationById_ValidId_ReturnsOkResult()
        {
            // Arrange
            int userInformationId = 10;
            var userInfoResponse = new UserInformationResponseDto
            {
                UserInformationID = userInformationId,
                Name = "Test User",
                Email = "<EMAIL>",                                
            };

            _userInfoServiceMock.Setup(s => s.GetUserInformationByIdAsync(userInformationId))
                .ReturnsAsync(userInfoResponse);

            // Act
            var result = await _controller.GetUserInformationById(userInformationId);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnValue = Assert.IsType<UserInformationResponseDto>(okResult.Value);
            Assert.Equal(userInformationId, returnValue.UserInformationID);
            Assert.Equal("Test User", returnValue.Name);
            Assert.Equal("<EMAIL>", returnValue.Email);

            _userInfoServiceMock.Verify(s => s.GetUserInformationByIdAsync(userInformationId), Times.Once);
        }

        [Fact]
        public async Task GetUserInformationById_InvalidId_ReturnsBadRequest()
        {
            // Arrange
            int invalidId = 0;
            _controller.ModelState.AddModelError("userInformationId", "ID must be greater than 0");

            // Act
            var result = await _controller.GetUserInformationById(invalidId);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);
        }

        [Fact]
        public async Task GetUserInformationById_UserNotFound_ReturnsNotFound()
        {
            // Arrange
            int nonExistentId = 999;
            _userInfoServiceMock.Setup(s => s.GetUserInformationByIdAsync(nonExistentId))
                .ReturnsAsync((UserInformationResponseDto)null);

            // Act
            var result = await _controller.GetUserInformationById(nonExistentId);

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result);
            Assert.Contains("not found", notFoundResult.Value.ToString());
        }
        
        [Fact]
        public async Task UpdateUserInformation_InvalidModel_ReturnsBadRequest()
        {
            // Arrange
            var updateUserDto = new UpdateUserInformationDto
            {
                // Missing required Name property
                UserInformationID = 10,
                Email = "<EMAIL>",
                CategoryId = 1,
                DocumentTypeIds = new List<int> { 1, 2, 3 }
            };

            _controller.ModelState.AddModelError("Name", "The Name field is required.");

            // Act
            var result = await _controller.UpdateUserInformation(updateUserDto);

            // Assert
            Assert.IsType<BadRequestObjectResult>(result);
        }

        [Fact]
        public async Task UpdateUserInformation_UserNotFound_ReturnsNotFound()
        {
            // Arrange
            var updateUserDto = new UpdateUserInformationDto
            {
                UserInformationID = 999, // Non-existent ID
                Name = "Updated User",
                Email = "<EMAIL>",
                CategoryId = 2,
                DocumentTypeIds = new List<int> { 2, 3, 4 }
            };

            int currentUserId = 5;

            _helperServiceMock.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>()))
                .Returns(currentUserId);

            _userInfoServiceMock.Setup(s => s.UpdateUserInformationAsync(updateUserDto, currentUserId))
                .ReturnsAsync(new EmailConfiguration.DTOs.ResponseDto<bool>
                {
                    IsSuccess = false,
                    Data = false,
                    Message = "User not found"
                });

            // Act
            var result = await _controller.UpdateUserInformation(updateUserDto);

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result);
            Assert.Contains("not found", notFoundResult.Value.ToString());
        }

        [Fact]
        public async Task UpdateUserInformation_ServiceThrowsException_PropagatesException()
        {
            // Arrange
            var updateUserDto = new UpdateUserInformationDto
            {
                UserInformationID = 10,
                Name = "Updated User",
                Email = "<EMAIL>",
                CategoryId = 2,
                DocumentTypeIds = new List<int> { 2, 3, 4 }
            };

            int currentUserId = 5;

            _helperServiceMock.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>()))
                .Returns(currentUserId);

            _userInfoServiceMock.Setup(s => s.UpdateUserInformationAsync(updateUserDto, currentUserId))
                .ThrowsAsync(new System.Exception("Service error"));

            // Act & Assert
            await Assert.ThrowsAsync<System.Exception>(
                () => _controller.UpdateUserInformation(updateUserDto));
        }

    }
}
