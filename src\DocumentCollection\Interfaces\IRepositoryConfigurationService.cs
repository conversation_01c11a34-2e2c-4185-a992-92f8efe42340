﻿using DocumentCollection.DTOs;
using DocumentCollection.Models;

namespace DocumentCollection.Interfaces
{
    public interface IRepositoryConfigurationService
    {
        /// <summary>
        /// Gets the document collection configuration.
        /// </summary>
        /// <returns>The document collection configuration.</returns>
        Task<ResponseDto<List<DocumentConfigurationDto>>> GetRepositoryConfigurationByCompany(int entityId, int featureId);

        /// <summary>
        /// Gets the document configurations for multiple company IDs, grouped by company ID.
        /// </summary>
        /// <param name="companyIds">A list of company IDs.</param>
        /// <returns>A dictionary where the key is the company ID and the value is a list of DocumentConfigurationDto objects.</returns>
        Task<ResponseDto<List<DocumentConfigurationDto>>> GetRepositoryConfigurationByCompanies(List<int> entityIds, int featureId);

        /// <summary>
        /// Updates a list of document configurations.
        /// </summary>
        /// <param name="configurations">A list of DocumentConfigurationDto objects to update.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        Task<ResponseDto<List<DocumentConfigurationDto>>> UpdateRepositoryConfigurations(DocumentConfigurationModel configurations , int userId);

        /// <summary>
        /// Gets the document configuration data for a specific company.
        /// This method retrieves the document configuration data for a given company ID.
        /// </summary>
        /// <param name="CompanyId"></param>
        /// <returns></returns>
        Task<List<DocumentConfigurationDto>> GetRepositoryConfigurationDataByCompany(int CompanyId);


        /// <summary>
        /// Gets the list of companies for which the user has access to the document configuration.
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        Task<List<PortfolioCompanyModel>> GetPortfolioCompaniesList(int userId);

        /// <summary>
        /// Gets the list of documentTypes supported
        /// <param name="featureId"></param>
        /// </summary>
        /// <returns></returns>
        Task<List<DocumentTypes>> GetDocumentTypes(int featureId);
        /// <summary>
        /// Retrieves a list of fund data models for a specific user.
        /// </summary>
        /// <param name="userId">The ID of the user for whom to retrieve the fund list.</param>
        /// <returns>A task that represents the asynchronous operation, containing a list of FundDataModel objects.</returns>
        Task<List<FundDataModel>> GetFundList(int userId);
    }
}
