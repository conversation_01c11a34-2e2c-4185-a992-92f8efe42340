using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace EmailConfiguration.DTOs
{
    /// <summary>
    /// DTO for updating email reminders
    /// </summary>
    public class UpdateEmailRemainderDto
    {
        /// <summary>
        /// The feature ID that this email reminder is associated with
        /// </summary>
        public int FeatureID { get; set; }

        /// <summary>
        /// GUID to identify the unique reminder
        /// </summary>
        public Guid ReminderID { get; set; }
        /// <summary>
        /// List of Portfolio Company IDs
        /// </summary>
        public List<EntityDetails> PortfolioCompanys { get; set; }

        /// <summary>
        /// List of Document Type IDs
        /// </summary>
        public List<EntityDetails> DocumentTypes { get; set; }


        /// <summary>
        /// List of To Reciepients
        /// </summary>
        /// /// <summary>
        /// List of To Reciepients
        /// </summary>
        [Required]
        [MinLength(1, ErrorMessage = "At least one recipient is required")]
        public List<EmailRecipientsDto> ToRecipients { get; set; }

        /// <summary>
        /// List of Cc Reciepients
        /// </summary>
        public List<EmailRecipientsDto> CcReciepients { get; set; }

        /// <summary>
        /// Subject of the email
        /// </summary>
        public string Subject { get; set; }

        /// <summary>
        /// Body of the email
        /// </summary>
        public string EmailBody { get; set; }

        /// <summary>
        /// Frequency type for the reminder (1=Monthly, 2=Quarterly, 3=Yearly)
        /// </summary>        
        public int FrequencyType { get; set; }

        /// <summary>
        /// Total number of reminders to send per cycle
        /// </summary>
        public int TotalRemindersPerCycle { get; set; }

        /// <summary>
        /// Date for the first reminder
        /// </summary>
        public DateTime Remainder1Date { get; set; }

        /// <summary>
        /// Text content for the second reminder
        /// </summary>
        public string Remainder2 { get; set; }

        /// <summary>
        /// Text content for the third reminder
        /// </summary>
        public string Remainder3 { get; set; }

        /// <summary>
        /// Text content for the fourth reminder
        /// </summary>
        public string Remainder4 { get; set; }

        /// <summary>
        /// Text content for the fifth reminder
        /// </summary>
        public string Remainder5 { get; set; }
    }
}
