-- Add 'Documents' SubFeature for ParentFeatureID = 13 if not exists
IF NOT EXISTS (
    SELECT 1 FROM M_SubFeature 
    WHERE SubFeature = 'Documents' AND ParentFeatureID = 13
)
BEGIN
    INSERT [dbo].[M_SubFeature] (
        [SubFeatureID], [SubFeature], [ParentFeatureID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
        [ModifiedOn], [ModifiedBy], [EncryptedSubFeatureID], [IsLocked], [IsRequiredInLPReport], [PageConfigName]
    )
    VALUES (
        93, N'Documents', 13, NULL, 1, 0, GETDATE(), 49, NULL, NULL, NULL, 0, 0, N'Documents'
    )
END
GO
 
-- Add 'Documents' SubPageDetails for PageID = 2 if not exists
IF NOT EXISTS (
    SELECT 1 FROM [dbo].[M_SubPageDetails]
    WHERE Name = 'Documents' AND PageID = 2
)
BEGIN
	SET IDENTITY_INSERT [dbo].[M_SubPageDetails] ON;
    INSERT [dbo].[M_SubPageDetails] ([SubPageID],
        [Name], [AliasName], [PageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
        [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [IsDynamicFieldSupported]
    )
    VALUES (48, N'Documents', N'Documents', 2, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 7, NULL, 0, 0
    )
	SET IDENTITY_INSERT [dbo].[M_SubPageDetails] OFF;
END
GO
 
-- Add Mapping_SubFeatureAction for SubFeatureID = 93 if not exists
IF NOT EXISTS (
    SELECT 1 FROM [dbo].[Mapping_SubFeatureAction] WHERE [SubFeatureID] = 93
)
BEGIN
    INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy])
    VALUES (93, 2, 0, GETUTCDATE(), 1);
 
    INSERT INTO [dbo].[Mapping_SubFeatureAction] ([SubFeatureID], [ActionID], [IsDeleted], [CreatedOn], [CreatedBy])
    VALUES (93, 3, 0, GETUTCDATE(), 1);
END
GO
 
-- Add 'All Folders' and 'Documents List' fields for the new Documents SubPage if not exists
DECLARE @SubPageID INT = (
    SELECT TOP 1 SubPageID FROM [dbo].[M_SubPageDetails]
    WHERE Name = 'Documents' AND PageID = 2
);
 
IF EXISTS (
    SELECT 1 FROM [dbo].[M_SubPageDetails] WHERE SubPageID = @SubPageID
)
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM M_SubPageFields WHERE Name = 'All Folders' AND SubPageID = @SubPageID
    )
    BEGIN
        INSERT [dbo].[M_SubPageFields] (
            [Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
            [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [ShowOnList]
        )
        VALUES (
            N'All Folders', N'All Folders', @SubPageID, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 1, NULL, 0, 0
        )
    END
 
    IF NOT EXISTS (
        SELECT 1 FROM M_SubPageFields WHERE Name = 'Documents List' AND SubPageID = @SubPageID
    )
    BEGIN
        INSERT [dbo].[M_SubPageFields] (
            [Name], [AliasName], [SubPageID], [Description], [isActive], [isDeleted], [CreatedOn], [CreatedBy],
            [ModifiedOn], [ModifiedBy], [EncryptedSubPageID], [SequenceNo], [PagePath], [IsCustom], [ShowOnList]
        )
        VALUES (
            N'Documents List', N'Documents List', @SubPageID, NULL, 1, 0, GETDATE(), 1, NULL, NULL, NULL, 1, NULL, 0, 0
        )
    END
END
GO
 
IF OBJECT_ID('[dbo].[SpGetSubFeatureListByPageconfig]', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE [dbo].[SpGetSubFeatureListByPageconfig]
END
GO
CREATE PROCEDURE [dbo].[SpGetSubFeatureListByPageconfig] ( @FeatureId INT ) AS
BEGIN   IF (@FeatureId = 14)
BEGIN
SELECT       f.SubFeatureID,      f.SubFeature As SubFeatureName, COALESCE(s.AliasName, sf.AliasName) AS SubFeatureAliasName,
f.isDeleted AS IsDeleted, f.ParentFeatureID AS ParentFeatureId FROM M_SubFeature f
LEFT JOIN M_SubPageDetails s ON f.PageConfigName = s.Name AND s.SubPageID IN (1, 4,(Select SubPageID from M_SubPageDetails WHERE Name='Documents' AND PageID=1)) AND s.isDeleted = 0
LEFT JOIN M_SubPageFields sf ON f.PageConfigName = sf.Name AND sf.SubPageID IN (2, 3, 38, 41,47) AND sf.isDeleted = 0
WHERE       (s.SubPageID IS NOT NULL OR sf.SubPageID IS NOT NULL) and ParentFeatureID = 14
END
 
ELSE IF(@FeatureId = 13)
BEGIN
SELECT       f.SubFeatureID,      f.SubFeature As SubFeatureName,
   COALESCE(s.AliasName, s.AliasName) AS SubFeatureAliasName, f.isDeleted AS IsDeleted, ParentFeatureId
   FROM       M_SubFeature f     LEFT JOIN M_SubPageDetails s ON f.PageConfigName = s.Name AND s.SubPageID IN (7,8,9,15,12,(Select SubPageID from M_SubPageDetails WHERE Name='Documents' AND PageID=2)) AND s.isDeleted = 0
   WHERE s.SubPageID IS NOT NULL and ParentFeatureID = 13
   END
ELSE IF(@FeatureId = 15)
BEGIN
SELECT f.SubFeatureID, f.SubFeature As SubFeatureName, COALESCE(s.AliasName, s.AliasName) AS SubFeatureAliasName, f.isDeleted AS IsDeleted,
f.ParentFeatureID AS ParentFeatureId FROM       M_SubFeature f
LEFT JOIN M_SubPageDetails s ON f.PageConfigName = s.Name AND s.SubPageID IN (5, 6) AND s.isDeleted = 0
WHERE s.SubPageID IS NOT NULL
END
ELSE IF(@FeatureId = 50)
BEGIN
SELECT  f.SubFeatureID, f.SubFeature As SubFeatureName, COALESCE(s.AliasName, sf.AliasName) AS SubFeatureAliasName, f.isDeleted AS IsDeleted,
f.ParentFeatureID AS ParentFeatureId     FROM       M_SubFeature f
LEFT JOIN M_SubPageDetails s ON f.PageConfigName = s.Name AND s.SubPageID IN (19) AND s.isDeleted = 0
LEFT JOIN M_SubPageFields sf ON f.PageConfigName = sf.Name AND sf.SubPageID IN (19) AND sf.isDeleted = 0
WHERE       (s.SubPageID IS NOT NULL OR sf.SubPageID IS NOT NULL) and  ParentFeatureID = 50
END
ELSE IF(@FeatureId = 42)
BEGIN
SELECT f.SubFeatureID, f.SubFeature As SubFeatureName, COALESCE(s.AliasName, s.AliasName) AS SubFeatureAliasName, f.isDeleted AS IsDeleted,
f.ParentFeatureID AS ParentFeatureId FROM   M_SubFeature f
LEFT JOIN M_SubPageDetails s ON f.PageConfigName = s.Name AND s.SubPageID IN (13,14,15,16,17,18) AND s.isDeleted = 0
WHERE s.SubPageID IS NOT NULL  AND s.SubPageID IN (13,14,15,16,17,18) AND ParentFeatureID = 42
 
UNION ALL
 
SELECT    f.SubFeatureID,      f.SubFeature As SubFeatureName,COALESCE(f.SubFeature, f.SubFeature) AS SubFeatureAliasName,f.isDeleted AS IsDeleted,f.ParentFeatureID AS ParentFeatureId     FROM M_SubFeature f
WHERE ParentFeatureID = 42 AND F.isDeleted=0 AND PageConfigName IS NULL
END
ELSE IF(@FeatureId = 19)
BEGIN
SELECT       SubFeatureID,      SubFeature As SubFeatureName, COALESCE(PageConfigName, PageConfigName) AS SubFeatureAliasName,isDeleted AS IsDeleted,
ParentFeatureID AS ParentFeatureId  FROM  M_SubFeature
WHERE  SubFeatureID  BETWEEN 33 AND 48
END
ELSE IF (@FeatureId = 57)
		BEGIN
			 SELECT
					SubFeatureID,
					SubFeature As SubFeatureName,
					COALESCE(PageConfigName, PageConfigName) AS SubFeatureAliasName,
					isDeleted AS IsDeleted,
					ParentFeatureID AS ParentFeatureId
				FROM
					M_SubFeature
				WHERE  SubFeatureID  BETWEEN 71 AND 81
		END
		ELSE IF (@FeatureId = 58)
		BEGIN
			 SELECT
					SubFeatureID,
					SubFeature As SubFeatureName,
					COALESCE(PageConfigName, PageConfigName) AS SubFeatureAliasName,
					isDeleted AS IsDeleted,
					ParentFeatureID AS ParentFeatureId
				FROM
					M_SubFeature
				WHERE  SubFeatureID  BETWEEN 82 AND 92
		END
END
