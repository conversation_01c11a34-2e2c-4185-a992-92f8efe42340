using Contract.PortfolioCompany;
using Contract.Repository;
using DataAccessLayer.DBModel;
using DataAccessLayer.UnitOfWork;
using DocumentCollection.DashboardTracker.DTOs;
using DocumentCollection.DashboardTracker.Interfaces;
using DocumentCollection.DashboardTracker.Models;
using PortfolioCompany.Helper;
using Workflow;

namespace DocumentCollection.DashboardTracker.Services
{
    public class DashboardTrackerService : IDashboardTrackerService
    {
        private readonly IWorkflowPCService _workflowPCService;
        private readonly IFileService _fileService;
        private readonly IUnitOfWork _unitOfWork;

        public DashboardTrackerService(IWorkflowPCService workflowPCService, IFileService fileService, IUnitOfWork unitOfWork, IDashboardTrackerRepository dashboardTrackerRepository)
        {
            _workflowPCService = workflowPCService;
            _fileService = fileService;
            _unitOfWork = unitOfWork;
        }

        public async Task<List<PortfolioCompanyViewModel>> GetPortfolioCompanies(PortfolioCompanyFilter portfolioCompanyFilter) 
        {
            var workflowResult = await _workflowPCService.GetPortfolioCompanies(portfolioCompanyFilter);

            if (workflowResult == null || workflowResult.PortfolioCompanyQueryListModel == null || workflowResult.PortfolioCompanyQueryListModel.PortfolioCompanyList.Count == 0)
            {
                return new List<PortfolioCompanyViewModel>(); ;
            }

            // Map to PortfolioCompanyViewModel
            var viewModelList = workflowResult.PortfolioCompanyQueryListModel.PortfolioCompanyList.Select(pc => new PortfolioCompanyViewModel
            {
                CompanyId = pc.PortfolioCompanyID,
                PortfolioCompanyName = pc.CompanyName,
                FundName = pc.FundName,
            }).ToList();


            // Only fetch image paths for the first 50 companies (simplified loop)
            var companiesToFetchLogo = viewModelList.Take(50).ToList();
            foreach (var viewModel in companiesToFetchLogo)
            {
                var res = await _unitOfWork.PortfolioCompanyDetailRepository.FindFirstAsync(x => x.PortfolioCompanyId == viewModel.CompanyId);
                if (res != null)
                {
                    var imgPath = await PortfolioCompanyHelper.GetCompanyLogo(_fileService, viewModel.CompanyId, res.ImagePath);
                    viewModel.CompanyLogo = imgPath;
                }
            }

            return viewModelList;
        }

        public async Task<int> SaveDashboardTrackerConfigAsync(DashboardTrackerConfigDto dto)
        {
            return await _dashboardTrackerRepository.SaveDashboardTrackerConfigAsync(dto);
        }
    }
}
