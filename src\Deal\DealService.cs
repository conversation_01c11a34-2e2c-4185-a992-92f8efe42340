﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Dynamic;
using System.Globalization;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Transactions;
using Contract.Configuration;
using Contract.Currency;
using Contract.Deals;
using Contract.Employee;
using Contract.Funds;
using Contract.Investor;
using Contract.KPI;
using Contract.Master;
using Contract.PortfolioCompany;
using Contract.Reports;
using Contract.Utility;
using DapperRepository;
using DapperRepository.Constants;
using DataAccessLayer.DBModel;
using DataAccessLayer.UnitOfWork;
using Master;
using Microsoft.Extensions.Logging;
using Shared;
using Utility.Helpers;
namespace Deals;
public class DealService : IDealService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IFundService _fundService;
    private readonly IPageDetailsConfigurationService _pageDetailsConfigurationService;
    private readonly IDapperGenericRepository _dapperGenericRepository;
    private readonly IEncryption _encryption;
    private readonly IGlobalConfigurations _global;
    private readonly ILogger<DealService> _logger;
    const string NA = "NA";
    public DealService(IPageDetailsConfigurationService pageDetailsConfigurationService,
     IDapperGenericRepository dapperGenericRepository,
     IFundService fundService, IUnitOfWork unitOfWork,
      IEncryption encryption, IGlobalConfigurations global,
       ILogger<DealService> logger)
    {
        _pageDetailsConfigurationService = pageDetailsConfigurationService;
        _dapperGenericRepository = dapperGenericRepository;
        _fundService = fundService;
        _global = global;
        _unitOfWork = unitOfWork;
        _encryption = encryption;
        _logger = logger;
    }
    public async Task<DealListQueryModel> GetDealsListByQuery(DealFilter filter)
    {
        _logger.LogInformation("Getting deals list by query with filter: {Filter}", filter);
        var result = new DealListQueryModel();
        try
        {
            int userId = filter?.CreatedBy ?? 0;
            var queryData = await _dapperGenericRepository.Query<DealsListModel>(SqlConstants.QueryDealsMappingByUserID, new { userID = userId });
            var data = queryData.AsQueryable();
            if (filter != null && filter.PaginationFilter != null)
            {
                int totalRows = 0;
                if (!string.IsNullOrEmpty(filter.PaginationFilter.GlobalFilter))
                {
                    var lowerGlobalFilter = filter.PaginationFilter.GlobalFilter.ToLower();
                    data = data.Where(u => u.DealCustomID.ToLower().Contains(lowerGlobalFilter) ||
                       u.CompanyName.ToLower().Contains(lowerGlobalFilter) ||
                       u.FundName.ToLower().Contains(lowerGlobalFilter));
                }
                if (!filter.PaginationFilter.FilterWithoutPaging)
                    data = data.PagedResult(filter.PaginationFilter.First, filter.PaginationFilter.Rows, filter.PaginationFilter.MultiSortMeta, out totalRows);
                else
                    data = data.SortedResult(filter.PaginationFilter.MultiSortMeta);
                result.TotalRecords = totalRows;
            }
            result.DealList = [.. data];
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetDealsListByQuery");
        }
        return result;

    }
    public async Task<DealListModel> GetDeals(DealFilter filter)
    {
        _logger.LogInformation("Getting deals with filter: {Filter}", filter);
        var result = new DealListModel();
        try
        {
            var query = !string.IsNullOrEmpty(filter.EncryptedDealID) ? $"{SqlConstants.DealListQuery} Where EncryptedDealID=@dealId and companyIsDeleted=0 and dealIsDeleted=0" : $"{SqlConstants.DealListQuery} Where companyIsDeleted=0 and dealIsDeleted=0";
            var data = await _dapperGenericRepository.Query<DealModel>(query, new { dealId = filter?.EncryptedDealID });
            if (filter != null)
            {
                if (!string.IsNullOrEmpty(filter.EncryptedDealID))
                {
                    var dealId = Convert.ToInt32(_encryption.Decrypt(filter.EncryptedDealID));
                    data = data.Where(x => x.DealID == dealId).ToList();
                }

                if (!string.IsNullOrEmpty(filter.EncryptedFundDetailsID))
                {
                    var fundId = Convert.ToInt32(_encryption.Decrypt(filter.EncryptedFundDetailsID));
                    data = data.Where(x => x.FundID == fundId).ToList();
                }
                if (filter.FundID > 0)
                {
                    data = data.Where(x => x.FundID == filter.FundID).ToList();
                }
                if (filter.PortfolioCompanyIds != null && filter.PortfolioCompanyIds.Any())
                {
                    data = data.Where(x => filter.PortfolioCompanyIds.Contains(x.PortfolioCompanyID ?? 0)).ToList();
                }
                if (filter.InvestmentDateRange != null)
                {
                    if (filter.InvestmentDateRange.Max.HasValue)
                    {
                        data = data.Where(x => x.InvestmentDate <= filter.InvestmentDateRange.Max).ToList();
                    }
                    if (filter.InvestmentDateRange.Min.HasValue)
                    {
                        data = data.Where(x => x.InvestmentDate >= filter.InvestmentDateRange.Min).ToList();
                    }
                }
                if (filter.PaginationFilter != null)
                {
                    int totalRows = 0;
                    if (!string.IsNullOrEmpty(filter.PaginationFilter.GlobalFilter))
                    {
                        var lowerGlobalFilter = filter.PaginationFilter.GlobalFilter.ToLower();
                        data = data.Where(u => u.DealID.ToString().ToLower().Contains(lowerGlobalFilter) || u.DealCustomID.ToLower().Contains(lowerGlobalFilter) ||
                           u.CompanyName.ToLower().Contains(lowerGlobalFilter) ||
                           u.FundName.ToLower().Contains(lowerGlobalFilter)).ToList();
                    }
                    if (!filter.PaginationFilter.FilterWithoutPaging)
                        data = [.. data.AsQueryable().PagedResult(filter.PaginationFilter.First, filter.PaginationFilter.Rows, filter.PaginationFilter.MultiSortMeta, out totalRows)];
                    else
                        data = [.. data.AsQueryable().SortedResult(filter.PaginationFilter.MultiSortMeta)];
                    result.TotalRecords = totalRows;
                }
            }
            result.DealList = data.Select(deal => new DealModel
            {
                EncryptedDealID = deal.EncryptedDealID,
                DealID = deal.DealID,
                DealCustomID = deal.DealCustomID,
                PortfolioCompanyID = deal.PortfolioCompanyID,
                DealName = deal.DealName,
                FundID = deal.FundID,
                EntryMultiple = deal.EntryMultiple,
                EntryOwnershipPercent = deal.EntryOwnershipPercent,
                CurrentExitOwnershipPercent = deal.CurrentExitOwnershipPercent,
                EnterpriseValue = deal.EnterpriseValue,
                DealExitMethodID = deal.DealExitMethodID,
                DealBoardSeatID = deal.DealBoardSeatID,
                DealInvestmentStageID = deal.DealInvestmentStageID,
                SourcingProfessionalEmployeeID = deal.SourcingProfessionalEmployeeID,
                LeadProfessionalEmployeeID = deal.LeadProfessionalEmployeeID,
                DealTransactionRoleID = deal.DealTransactionRoleID,
                NumberOfCoInvestors = deal.NumberOfCoInvestors,
                DealSecurityTypeID = deal.DealSecurityTypeID,
                DealSourcingID = deal.DealSourcingID,
                DealValuationMethodologyID = deal.DealValuationMethodologyID,
                ReportingCurrencyID = deal.ReportingCurrencyID,
                InvestmentDate = deal.InvestmentDate.HasValue ? TimeZoneInfo.ConvertTimeFromUtc(deal.InvestmentDate.Value, TimeZoneInfo.Utc) : null,
                CreatedOn = deal.CreatedOn,
                DealBoardSeatDetail = filter?.IncludeAllDetails == true && deal.DealBoardSeatID.HasValue ? new DealBoardSeatModel
                {
                    BoardSeat = deal.BoardSeat,
                    DealBoardSeatID = (int)deal.DealBoardSeatID
                } : new DealBoardSeatModel(),
                DealExitMethodDetail = filter?.IncludeAllDetails == true && deal.DealExitMethodID.HasValue ? new DealExitMethodModel
                {
                    DealExitMethodID = (int)deal.DealExitMethodID,
                    ExitMethod = deal.ExitMethod
                } : new DealExitMethodModel(),
                DealInvestmentStageDetail = filter?.IncludeAllDetails == true && deal.DealInvestmentStageID.HasValue ? new DealInvestmentStageModel
                {
                    DealInvestmentStageID = (int)deal.DealInvestmentStageID,
                    InvestmentStage = deal.InvestmentStage
                } : new DealInvestmentStageModel(),
                DealSecurityTypeDetail = filter?.IncludeAllDetails == true && deal.DealSecurityTypeID.HasValue ? new DealSecurityTypeModel
                {
                    DealSecurityTypeID = (int)deal.DealSecurityTypeID,
                    SecurityType = deal.SecurityType
                } : new DealSecurityTypeModel(),
                DealSourcingDetail = filter?.IncludeAllDetails == true && deal.DealSourcingID.HasValue ? new DealSourcingModel
                {
                    DealSourcingID = (int)deal.DealSourcingID,
                    DealSourcing = deal.DealSourcing
                } : new DealSourcingModel(),
                DealTransactionRoleDetail = filter?.IncludeAllDetails == true && deal.DealTransactionRoleID.HasValue ? new DealTransactionRoleModel
                {
                    DealTransactionRoleID = (int)deal.DealTransactionRoleID,
                    TransactionRole = deal.TransactionRole
                } : new DealTransactionRoleModel(),
                DealValuationMethodologyDetail = filter?.IncludeAllDetails == true && deal.DealValuationMethodologyID.HasValue ? new DealValuationMethodologyModel
                {
                    DealValuationMethodologyID = (int)deal.DealValuationMethodologyID,
                    ValuationMethodology = deal.ValuationMethodology
                } : new DealValuationMethodologyModel(),
                FundDetails = deal.FundID.HasValue ? new FundModel
                {
                    FundID = (int)deal.FundID,
                    FundName = deal.FundName,
                    EncryptedFundId = deal.EncryptedFundId
                } : new FundModel(),
                LeadProfessionalDetail = filter?.IncludeAllDetails == true && deal.LeadProfessionalEmployeeID.HasValue ? new EmployeeModel
                {
                    EmployeeId = (int)deal.LeadProfessionalEmployeeID,
                    EmployeeName = deal.EmployeeName
                } : new EmployeeModel(),
                PortfolioCompanyDetails = deal.PortfolioCompanyID.HasValue ? new PortfolioCompanyModel
                {
                    PortfolioCompanyID = (int)deal.PortfolioCompanyID,
                    CompanyName = deal.CompanyName,
                    EncryptedPortfolioCompanyId = deal.EncryptedPortfolioCompanyId
                } : new PortfolioCompanyModel(),
                CurrencyDetail = filter?.IncludeAllDetails == true && deal.ReportingCurrencyID.HasValue ? new CurrencyModel
                {
                    CurrencyID = (int)deal.ReportingCurrencyID,
                    Currency = deal.Currency,
                    CurrencyCode = deal.CurrencyCode
                } : new CurrencyModel(),
                SourcingProfessionalDetail = filter?.IncludeAllDetails == true && deal.SourcingProfessionalEmployeeID.HasValue ? new EmployeeModel
                {
                    EmployeeId = (int)deal.SourcingProfessionalEmployeeID,
                    EmployeeName = deal.SourcingProfessionalEmployeeName
                } : new EmployeeModel()

            }).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetDeals");
        }
        return result;

    }

    public async Task<DealModel> GetDealById(int dealId)
    {
        try
        {
            var deal = await _dapperGenericRepository.QueryFirstAsync<DealModel>(SqlConstants.DealListQueryWithDealId, new { dealId });
            if (deal != null && deal.DealID > 0)
            {
                var dealDetail = new DealModel
                {
                    EncryptedDealID = deal.EncryptedDealID,
                    DealID = deal.DealID,
                    DealCustomID = deal.DealCustomID,
                    PortfolioCompanyID = deal.PortfolioCompanyID,
                    DealName = deal.DealName,
                    FundID = deal.FundID,
                    EntryMultiple = deal.EntryMultiple,
                    EntryOwnershipPercent = deal.EntryOwnershipPercent,
                    CurrentExitOwnershipPercent = deal.CurrentExitOwnershipPercent,
                    EnterpriseValue = deal.EnterpriseValue,
                    DealExitMethodID = deal.DealExitMethodID,
                    DealBoardSeatID = deal.DealBoardSeatID,
                    DealInvestmentStageID = deal.DealInvestmentStageID,
                    SourcingProfessionalEmployeeID = deal.SourcingProfessionalEmployeeID,
                    LeadProfessionalEmployeeID = deal.LeadProfessionalEmployeeID,
                    DealTransactionRoleID = deal.DealTransactionRoleID,
                    NumberOfCoInvestors = deal.NumberOfCoInvestors,
                    DealSecurityTypeID = deal.DealSecurityTypeID,
                    DealSourcingID = deal.DealSourcingID,
                    DealValuationMethodologyID = deal.DealValuationMethodologyID,
                    ReportingCurrencyID = deal.ReportingCurrencyID,
                    InvestmentDate = deal.InvestmentDate.HasValue ? TimeZoneInfo.ConvertTimeFromUtc(deal.InvestmentDate.Value, TimeZoneInfo.Utc) : null,
                    DealBoardSeatDetail = deal.DealBoardSeatID.HasValue ? new DealBoardSeatModel
                    {
                        BoardSeat = deal.BoardSeat,
                        DealBoardSeatID = (int)deal.DealBoardSeatID
                    } : new DealBoardSeatModel(),
                    DealExitMethodDetail = deal.DealExitMethodID.HasValue ? new DealExitMethodModel
                    {
                        DealExitMethodID = (int)deal.DealExitMethodID,
                        ExitMethod = deal.ExitMethod
                    } : new DealExitMethodModel(),
                    DealInvestmentStageDetail = deal.DealInvestmentStageID.HasValue ? new DealInvestmentStageModel
                    {
                        DealInvestmentStageID = (int)deal.DealInvestmentStageID,
                        InvestmentStage = deal.InvestmentStage
                    } : new DealInvestmentStageModel(),
                    DealSecurityTypeDetail = deal.DealSecurityTypeID.HasValue ? new DealSecurityTypeModel
                    {
                        DealSecurityTypeID = (int)deal.DealSecurityTypeID,
                        SecurityType = deal.SecurityType
                    } : new DealSecurityTypeModel(),
                    DealSourcingDetail = deal.DealSourcingID.HasValue ? new DealSourcingModel
                    {
                        DealSourcingID = (int)deal.DealSourcingID,
                        DealSourcing = deal.DealSourcing
                    } : new DealSourcingModel(),
                    DealTransactionRoleDetail = deal.DealTransactionRoleID.HasValue ? new DealTransactionRoleModel
                    {
                        DealTransactionRoleID = (int)deal.DealTransactionRoleID,
                        TransactionRole = deal.TransactionRole
                    } : new DealTransactionRoleModel(),
                    DealValuationMethodologyDetail = deal.DealValuationMethodologyID.HasValue ? new DealValuationMethodologyModel
                    {
                        DealValuationMethodologyID = (int)deal.DealValuationMethodologyID,
                        ValuationMethodology = deal.ValuationMethodology
                    } : new DealValuationMethodologyModel(),
                    FundDetails = deal.FundID.HasValue ? new FundModel
                    {
                        FundID = (int)deal.FundID,
                        FundName = deal.FundName,
                        EncryptedFundId = deal.EncryptedFundId
                    } : new FundModel(),
                    LeadProfessionalDetail = deal.LeadProfessionalEmployeeID.HasValue ? new EmployeeModel
                    {
                        EmployeeId = (int)deal.LeadProfessionalEmployeeID,
                        EmployeeName = deal.EmployeeName
                    } : new EmployeeModel(),
                    PortfolioCompanyDetails = deal.PortfolioCompanyID.HasValue ? new PortfolioCompanyModel
                    {
                        PortfolioCompanyID = (int)deal.PortfolioCompanyID,
                        CompanyName = deal.CompanyName,
                        EncryptedPortfolioCompanyId = deal.EncryptedPortfolioCompanyId
                    } : new PortfolioCompanyModel(),
                    CurrencyDetail = deal.ReportingCurrencyID.HasValue ? new CurrencyModel
                    {
                        CurrencyID = (int)deal.ReportingCurrencyID,
                        Currency = deal.Currency,
                        CurrencyCode = deal.CurrencyCode
                    } : new CurrencyModel(),
                    SourcingProfessionalDetail = deal.SourcingProfessionalEmployeeID.HasValue ? new EmployeeModel
                    {
                        EmployeeId = (int)deal.SourcingProfessionalEmployeeID,
                        EmployeeName = deal.SourcingProfessionalEmployeeName
                    } : new EmployeeModel()
                };
                return dealDetail;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetDealById");
        }
        return null;
    }

    public async Task<DealListModel> GetDealsWithMinimalProperties(DealFilter filter)
    {
        var result = new DealListModel();
        try
        {
            var query = $"{SqlConstants.DealListQuery} Where dealIsDeleted=0";
            var queryData = await _dapperGenericRepository.Query<DealModel>(query);
            var data = queryData.AsQueryable();
            if (filter != null)
            {
                if (filter.FundID > 0)
                    data = data.Where(x => x.FundID == filter.FundID);
                if (filter.PortfolioCompanyIds?.Any() == true)
                    data = data.Where(x => filter.PortfolioCompanyIds.Contains(x.PortfolioCompanyID ?? 0));
            }
            var fundList = data.ToList();
            result.DealList = fundList.ConvertAll(deal => new DealModel
            {
                DealID = deal.DealID,
                PortfolioCompanyID = deal.PortfolioCompanyID,
                InvestmentDate = deal.InvestmentDate.HasValue ? TimeZoneInfo.ConvertTimeFromUtc(deal.InvestmentDate.Value, TimeZoneInfo.Utc) : null
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetDealsWithMinimalProperties");
        }
        return result;
    }

    public async Task<PortfolioCompanyFundHoldingListModel> GetPortfolioCompanyFundHolding(PortfolioCompanyFundHoldingFilter filter)
    {

        var result = new PortfolioCompanyFundHoldingListModel();
        try
        {
            var query = $"{((filter != null && filter.DealID > 0) ? SqlConstants.DealFundHoldingQueryWithDealId : SqlConstants.DealFundHoldingQuery)}";
            var queryData = await _dapperGenericRepository.Query<PortfolioCompanyFundHoldingModel>(query, new { dealId = filter.DealID });
            var data = queryData.AsQueryable();
            if (data.Any(x => x.EncryptedPortfolioCompanyFundHoldingID == null))
                _unitOfWork.Save();
            if (filter != null)
            {
                if (filter.DealID > 0)
                    data = data.Where(x => x.DealID == filter.DealID);
                if (filter.FundIds != null && filter.FundIds.Any())
                    data = data.Where(x => filter.FundIds.Contains(x.FundID));
                if (filter.PortfolioCompanyIds != null && filter.PortfolioCompanyIds.Any())
                    data = data.Where(x => filter.PortfolioCompanyIds.Contains(x.PortfolioCompanyID));
                if (!string.IsNullOrEmpty(filter.Quarter))
                    data = data.Where(x => x.Quarter == filter.Quarter);
                if (filter.Year > 0)
                    data = data.Where(x => x.Year == filter.Year);
                if (filter.PaginationFilter != null)
                {
                    int totalRows = 0;
                    if (!string.IsNullOrEmpty(filter.PaginationFilter.GlobalFilter))
                    {
                        var lowerGlobalFilter = filter.PaginationFilter.GlobalFilter.ToLower();
                        data = data.Where(u => u.Year.ToString().ToLower().Contains(lowerGlobalFilter) ||
                           (u.FundHoldingStatus != null && u.FundHoldingStatus.Status.ToLower().Contains(lowerGlobalFilter)) ||
                           (u.GrossIRR != null && u.GrossIRR.ToString().ToLower().Contains(lowerGlobalFilter)) ||
                           (u.GrossMultiple != null && u.GrossMultiple.ToString().ToLower().Contains(lowerGlobalFilter)) ||
                           (u.InvestementDate != null && u.InvestementDate.ToString().ToLower().Contains(lowerGlobalFilter)) ||
                           (u.InvestmentCost != null && u.InvestmentCost.ToString().ToLower().Contains(lowerGlobalFilter)) ||
                           (u.Quarter != null && u.Quarter.ToString().ToLower().Contains(lowerGlobalFilter)) ||
                           (u.RealizedValue != null && u.RealizedValue.ToString().ToLower().Contains(lowerGlobalFilter)) ||
                           (u.TotalValue != null && u.TotalValue.ToString().ToLower().Contains(lowerGlobalFilter)) ||
                           (u.UnrealizedValue != null && u.UnrealizedValue.ToString().ToLower().Contains(lowerGlobalFilter)) ||
                           (u.ValuationDate != null && u.ValuationDate.ToString().ToLower().Contains(lowerGlobalFilter)));
                    }
                    if (!filter.PaginationFilter.FilterWithoutPaging)
                        data = data.PagedResult(filter.PaginationFilter.First, filter.PaginationFilter.Rows, filter.PaginationFilter.MultiSortMeta, out totalRows);
                    else
                        data = data.SortedResult(filter.PaginationFilter.MultiSortMeta);
                    result.TotalRecords = totalRows;
                }
            }
            result.PortfolioCompanyFundHoldingList = [.. data.Select(x => new PortfolioCompanyFundHoldingModel
        {
            CreatedBy = x.CreatedBy,
            CreatedOn = x.CreatedOn,
            DealID = x.DealID,
            FundHoldingStatusID = x.FundHoldingStatusID,
            FundHoldingStatus = new FundHoldingStatusModel
            {
                FundHoldingStatusID = x.FundHoldingStatusID != null ? (int)x.FundHoldingStatusID : 0,
                Status = x.Status,
            },
            PortfolioCompany = new PortfolioCompanyModel
            {
                CompanyName = x.CompanyName,
                PortfolioCompanyID = x.PortfolioCompanyID
            },
            GrossIRR = x.GrossIRR,
            GrossMultiple = x.GrossMultiple,
            InvestementDate = x.InvestementDate,
            InvestmentCost = x.InvestmentCost,
            IsActive = x.IsActive,
            IsDeleted = x.IsDeleted,
            ModifiedBy = x.ModifiedBy,
            ModifiedOn = x.ModifiedOn,
            PortfolioCompanyFundHoldingID = x.PortfolioCompanyFundHoldingID,
            Quarter = x.Quarter,
            RealizedValue = x.RealizedValue,
            TotalValue = x.TotalValue,
            UnrealizedValue = x.UnrealizedValue,
            ValuationDate = x.ValuationDate,
            Year = x.Year,
            EncryptedPortfolioCompanyFundHoldingID = x.EncryptedPortfolioCompanyFundHoldingID,
            FundID = x.FundID,
            PortfolioCompanyID = x.PortfolioCompanyID,
            CompanyName = x.CompanyName,
            Dpi = x.Dpi == null && x.InvestmentCost != 0 ? x.RealizedValue / x.InvestmentCost : ReturnValIfNotNull(x.Dpi),
            Rvpi = x.Rvpi == null && x.InvestmentCost != 0 ? x.UnrealizedValue / x.InvestmentCost : ReturnValIfNotNull(x.Rvpi)
        })];
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetPortfolioCompanyFundHolding");
        }
        return result;
    }

    private static decimal? ReturnValIfNotNull(decimal? val)
    {
        return val != null ? val : null;
    }

    public async Task<int> SaveDeal(DealModel dealModel)
    {
        try
        {
            if (!string.IsNullOrEmpty(dealModel?.EncryptedDealID))
            {
                var dealId = Convert.ToInt32(_encryption.Decrypt(dealModel.EncryptedDealID));
                if (dealId > 0)
                {
                    return await UpdateDeal(dealModel);
                }
            }
            else
            {
                return await CreateDeal(dealModel);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in SaveDeal");
        }
        return 0;

    }

    private async Task<int> CreateDeal(DealModel dealModel)
    {
        if (dealModel.InvestmentDate.HasValue)
        {
            dealModel.InvestmentDate = TimeZoneInfo.ConvertTimeToUtc(dealModel.InvestmentDate.Value);

        }
        var ifAlreadyExists = await _dapperGenericRepository.Query<DealModel>(SqlConstants.DealFundPCListQueryWithId, new { companyId = dealModel?.PortfolioCompanyDetails?.PortfolioCompanyID, fundId = dealModel?.FundDetails?.FundID });
        var ifDealIdAlreadyExists = await _dapperGenericRepository.Query<DealModel>(SqlConstants.GetDealsData, new { dealModel.DealCustomID });
        if (ifAlreadyExists?.Count == 0 && ifDealIdAlreadyExists?.Count == 0)
        {
            DealDetails dbDetail = await InsertIntoDealDetailRepository(dealModel);
            await _unitOfWork.SaveAsync();
            var idLength = 10;
            var prefix = _global.GetValueByKey("DealIDPrefix");
            if (dbDetail != null)
            {
                dbDetail.EncryptedDealId = _encryption.Encrypt(dbDetail.DealId.ToString());
                if(string.IsNullOrEmpty(dealModel.DealCustomID))
                    dbDetail.DealCustomId = prefix + dbDetail.DealId.ToString().PadLeft(idLength - prefix.Length, '0');
            }
            await _unitOfWork.SaveAsync();
            return dbDetail != null ? dbDetail.DealId : 0;
        }
        else
        {
            return ifDealIdAlreadyExists?.Count > 0 ? -3 : -1;
        }

    }

    public async Task<int> AddDealList(List<BulkUploadDealValidationDetails> dealModelList)
    {
        var deals = await _dapperGenericRepository.Query<DealQueryModel>(SqlConstants.DealFundPCListQuery);
        var isDealExist = deals?.Exists(x => dealModelList.Any(y => x.FundID == y?.FundDetails?.FundID && x.PortfolioCompanyID == y?.PortfolioCompanyDetails?.PortfolioCompanyID));
        if (isDealExist != null && !(bool)isDealExist)
        {
            var dbList = new List<Tuple<DealDetails, DealModel>>();
            foreach (var dealModel in dealModelList)
            {
                var dealDetail = await InsertIntoDealDetailRepository(dealModel);
                dbList.Add(new Tuple<DealDetails, DealModel>(dealDetail, dealModel));
            }
            await _unitOfWork.SaveAsync();

            var idLength = 10;
            var prefix = _global.GetValueByKey("DealIDPrefix");

            _ = dbList.Select(x =>
            {
                x.Item1.DealCustomId = prefix + x.Item1.DealId.ToString().PadLeft(idLength - prefix.Length, '0');
                x.Item1.EncryptedDealId = _encryption.Encrypt(x.Item1.DealId.ToString());
                x.Item1.PortfolioCompanyFundHoldingDetails = x.Item1.PortfolioCompanyFundHoldingDetails.Select(y =>
                {
                    y.EncryptedPortfolioCompanyFundHoldingId = _encryption.Encrypt(y.PortfolioCompanyFundHoldingId.ToString());
                    return y;
                }).ToList();
                x.Item2.DealID = x.Item1.DealId;
                return x;
            }).ToList();

            await _unitOfWork.SaveAsync();
            return 1;
        }
        else
        {
            return -1;
        }

    }

    private async Task<DealDetails> InsertIntoDealDetailRepository(DealModel dealModel)
    {
        DealDetails dbDetail = new()
        {
            DealCustomId = string.IsNullOrEmpty(dealModel.DealCustomID) ? Guid.NewGuid().ToString() : dealModel.DealCustomID,
            PortfolioCompanyId = dealModel.PortfolioCompanyDetails?.PortfolioCompanyID,
            DealName = dealModel.DealName,
            FundId = dealModel.FundDetails?.FundID,
            EntryMultiple = dealModel.EntryMultiple,
            EntryOwnershipPercent = dealModel.EntryOwnershipPercent,
            CurrentExitOwnershipPercent = dealModel.CurrentExitOwnershipPercent,
            EnterpriseValue = dealModel.EnterpriseValue,
            DealExitMethodId = dealModel.DealExitMethodDetail?.DealExitMethodID,
            DealBoardSeatId = dealModel.DealBoardSeatDetail?.DealBoardSeatID,
            DealInvestmentStageId = dealModel.DealInvestmentStageDetail?.DealInvestmentStageID,
            SourcingProfessionalEmployeeId = dealModel.SourcingProfessionalDetail?.EmployeeId,
            LeadProfessionalEmployeeId = dealModel.LeadProfessionalDetail?.EmployeeId,
            DealTransactionRoleId = dealModel.DealTransactionRoleDetail?.DealTransactionRoleID,
            NumberOfCoInvestors = dealModel.NumberOfCoInvestors,
            DealSecurityTypeId = dealModel.DealSecurityTypeDetail?.DealSecurityTypeID,
            DealSourcingId = dealModel.DealSourcingDetail?.DealSourcingID,
            DealValuationMethodologyId = dealModel.DealValuationMethodologyDetail?.DealValuationMethodologyID,
            InvestmentDate = dealModel.InvestmentDate,
            CreatedOn = dealModel.CreatedOn,
            CreatedBy = dealModel.CreatedBy,
            IsDeleted = dealModel.IsDeleted,
            IsActive = dealModel.IsActive,
            ReportingCurrencyId = dealModel.CurrencyDetail?.CurrencyID,
            PortfolioCompanyFundHoldingDetails = dealModel.PortfolioCompanyFundHoldingList != null ? dealModel.PortfolioCompanyFundHoldingList.Select(portfolioCompanyFundHoldingModel => new PortfolioCompanyFundHoldingDetails
            {
                FundHoldingStatusId = portfolioCompanyFundHoldingModel.FundHoldingStatus?.FundHoldingStatusID,
                GrossIrr = portfolioCompanyFundHoldingModel.GrossIRR,
                GrossMultiple = portfolioCompanyFundHoldingModel.GrossMultiple,
                InvestementDate = portfolioCompanyFundHoldingModel.InvestementDate,
                InvestmentCost = portfolioCompanyFundHoldingModel.InvestmentCost,
                Quarter = portfolioCompanyFundHoldingModel.Quarter,
                RealizedValue = portfolioCompanyFundHoldingModel.RealizedValue,
                TotalValue = portfolioCompanyFundHoldingModel.TotalValue,
                UnrealizedValue = portfolioCompanyFundHoldingModel.UnrealizedValue,
                ValuationDate = portfolioCompanyFundHoldingModel.ValuationDate,
                Year = portfolioCompanyFundHoldingModel.Year,
                CreatedOn = portfolioCompanyFundHoldingModel.CreatedOn,
                CreatedBy = portfolioCompanyFundHoldingModel.CreatedBy,
                ModifiedOn = portfolioCompanyFundHoldingModel.ModifiedOn,
                ModifiedBy = portfolioCompanyFundHoldingModel.ModifiedBy,
                IsDeleted = portfolioCompanyFundHoldingModel.IsDeleted,
                IsActive = portfolioCompanyFundHoldingModel.IsActive
            }).ToList() : null
        };

        await _unitOfWork.DealDetailRepository.AddAsyn(dbDetail);
        return dbDetail;
    }

    private async Task<int> UpdateDeal(DealModel dealModel)
    {
        var dealID = Convert.ToInt32(_encryption.Decrypt(dealModel.EncryptedDealID));
        var ifAlreadyExists = await _dapperGenericRepository.Query<DealModel>(SqlConstants.DealFundPCListQueryWithIds, new { companyId = dealModel.PortfolioCompanyDetails.PortfolioCompanyID, fundId = dealModel.FundDetails.FundID, DealId = dealID });
        var ifDealIdAlreadyExists = await _dapperGenericRepository.Query<DealModel>(SqlConstants.GetDealsDeatils, new {  DealId = dealID, dealModel.DealCustomID });
        if (!ifAlreadyExists?.Any() == true && !ifDealIdAlreadyExists?.Any() == true)
        {
            var dbDeals = await _dapperGenericRepository.QueryFirstAsync<DealDetails>(SqlConstants.DealTableQueryListWithId, new { dealId = dealID });
            if (dbDeals.DealId != dealModel.DealID)
                return 0;
            bool dateChangeFlag = false;
            if (dbDeals.InvestmentDate != dealModel.InvestmentDate)
                dateChangeFlag = true;
            dbDeals.PortfolioCompanyId = dealModel.PortfolioCompanyDetails?.PortfolioCompanyID;
            dbDeals.DealCustomId = dealModel.DealCustomID;
            dbDeals.FundId = dealModel.FundDetails?.FundID;
            dbDeals.EntryMultiple = dealModel.EntryMultiple;
            dbDeals.EntryOwnershipPercent = dealModel.EntryOwnershipPercent;
            dbDeals.CurrentExitOwnershipPercent = dealModel.CurrentExitOwnershipPercent;
            dbDeals.EnterpriseValue = dealModel.EnterpriseValue;
            dbDeals.DealExitMethodId = dealModel.DealExitMethodDetail?.DealExitMethodID == 0 ? null : dealModel.DealExitMethodDetail?.DealExitMethodID;
            dbDeals.DealBoardSeatId = dealModel.DealBoardSeatDetail?.DealBoardSeatID == 0 ? null : dealModel.DealBoardSeatDetail?.DealBoardSeatID;
            dbDeals.DealInvestmentStageId = dealModel.DealInvestmentStageDetail?.DealInvestmentStageID == 0 ? null : dealModel.DealInvestmentStageDetail?.DealInvestmentStageID;
            dbDeals.SourcingProfessionalEmployeeId = dealModel.SourcingProfessionalDetail?.EmployeeId == 0 ? null : dealModel.SourcingProfessionalDetail?.EmployeeId;
            dbDeals.LeadProfessionalEmployeeId = dealModel.LeadProfessionalDetail?.EmployeeId == 0 ? null : dealModel.LeadProfessionalDetail?.EmployeeId;
            dbDeals.DealTransactionRoleId = dealModel.DealTransactionRoleDetail?.DealTransactionRoleID == 0 ? null : dealModel.DealTransactionRoleDetail?.DealTransactionRoleID;
            dbDeals.NumberOfCoInvestors = dealModel.NumberOfCoInvestors;
            dbDeals.DealSecurityTypeId = dealModel.DealSecurityTypeDetail?.DealSecurityTypeID == 0 ? null : dealModel.DealSecurityTypeDetail?.DealSecurityTypeID;
            dbDeals.DealSourcingId = dealModel.DealSourcingDetail?.DealSourcingID == 0 ? null : dealModel.DealSourcingDetail?.DealSourcingID;
            dbDeals.DealValuationMethodologyId = dealModel.DealValuationMethodologyDetail?.DealValuationMethodologyID == 0 ? null : dealModel.DealValuationMethodologyDetail?.DealValuationMethodologyID;
            dbDeals.ModifiedOn = dealModel.ModifiedOn;
            dbDeals.ModifiedBy = dealModel.ModifiedBy;
            dbDeals.IsDeleted = dealModel.IsDeleted;
            dbDeals.IsActive = dealModel.IsActive;
            dbDeals.InvestmentDate = dealModel.InvestmentDate;
            dbDeals.ReportingCurrencyId = dealModel.CurrencyDetail?.CurrencyID == 0 ? null : dealModel.CurrencyDetail?.CurrencyID;

            _unitOfWork.DealDetailRepository.Update(dbDeals);
            await _unitOfWork.SaveAsync();

            if (dateChangeFlag)
            {
                var dbDetail = await _dapperGenericRepository.Query<PortfolioCompanyFundHoldingDetails>(SqlConstants.DealFundHoldingTableWithDealIdNotDeleted, new { dealId = dealID });
                if (dbDetail != null && dbDetail?.Any() == true)
                {
                    foreach (var holding in dbDetail)
                    {
                        holding.InvestementDate = dealModel.InvestmentDate;
                        _unitOfWork.PortfolioCompanyFundHoldingDetailRepository.Update(holding);
                    }
                    await _unitOfWork.SaveAsync();
                }

            }
            return -2;
        }
        else
        {
            return ifDealIdAlreadyExists?.Any() == true ? -3 : -1;
        }
    }

    public async Task<bool> SavePortfolioCompanyFundHolding(PortfolioCompanyFundHoldingModel portfolioCompanyFundHoldingModel)
    {
        if (portfolioCompanyFundHoldingModel.PortfolioCompanyFundHoldingID != 0)
            return await UpdatePortfolioCompanyFundHolding(portfolioCompanyFundHoldingModel);
        else
            return CreatePortfolioCompanyFundHolding(portfolioCompanyFundHoldingModel);
    }

    public int AddPortfolioCompanyFundHoldingList(List<PortfolioCompanyFundHoldingModel> portfolioCompanyFundHoldingModelList)
    {

        var ifExist = _unitOfWork.PortfolioCompanyFundHoldingDetailRepository.ExistsAny(x => portfolioCompanyFundHoldingModelList.Exists(holdingModel => x.DealId == holdingModel.PortfolioCompanyID && x.Year == holdingModel.Year && x.Quarter.ToLower() == holdingModel.Quarter.ToLower()));
        if (!ifExist)
        {
            using (var scope = new TransactionScope())
            {
                var dbList = new List<PortfolioCompanyFundHoldingDetails>();
                foreach (var portfolioCompanyFundHoldingModel in portfolioCompanyFundHoldingModelList)
                {
                    InsertIntoPortfolioCompanyFundHoldingDetailRepository(portfolioCompanyFundHoldingModel);
                }
                _unitOfWork.Save();

                _ = dbList.Select(x => { x.EncryptedPortfolioCompanyFundHoldingId = _encryption.Encrypt(x.PortfolioCompanyFundHoldingId.ToString()); return x; }).ToList();
                _unitOfWork.Save();
                scope.Complete();
            }
            return 1;
        }
        else
        {
            return -1;
        }

    }

    private bool CreatePortfolioCompanyFundHolding(PortfolioCompanyFundHoldingModel portfolioCompanyFundHoldingModel)
    {
        using TransactionScope scope = new();
        PortfolioCompanyFundHoldingDetails dbDetail = InsertIntoPortfolioCompanyFundHoldingDetailRepository(portfolioCompanyFundHoldingModel);
        _unitOfWork.Save();
        dbDetail.EncryptedPortfolioCompanyFundHoldingId = _encryption.Encrypt(dbDetail.PortfolioCompanyFundHoldingId.ToString());
        _unitOfWork.Save();
        scope.Complete();
        return true;
    }

    private PortfolioCompanyFundHoldingDetails InsertIntoPortfolioCompanyFundHoldingDetailRepository(PortfolioCompanyFundHoldingModel portfolioCompanyFundHoldingModel)
    {
        PortfolioCompanyFundHoldingDetails dbDetail = new PortfolioCompanyFundHoldingDetails
        {
            DealId = portfolioCompanyFundHoldingModel.DealID,
            FundHoldingStatusId = portfolioCompanyFundHoldingModel.FundHoldingStatus?.FundHoldingStatusID,
            GrossIrr = portfolioCompanyFundHoldingModel.GrossIRR,
            GrossMultiple = portfolioCompanyFundHoldingModel.GrossMultiple,
            InvestementDate = portfolioCompanyFundHoldingModel.InvestementDate,
            InvestmentCost = portfolioCompanyFundHoldingModel.InvestmentCost,
            Quarter = portfolioCompanyFundHoldingModel.Quarter,
            RealizedValue = portfolioCompanyFundHoldingModel.RealizedValue,
            TotalValue = portfolioCompanyFundHoldingModel.TotalValue,
            UnrealizedValue = portfolioCompanyFundHoldingModel.UnrealizedValue,
            ValuationDate = portfolioCompanyFundHoldingModel.ValuationDate,
            Year = portfolioCompanyFundHoldingModel.Year,
            CreatedOn = portfolioCompanyFundHoldingModel.CreatedOn,
            CreatedBy = portfolioCompanyFundHoldingModel.CreatedBy,
            ModifiedOn = portfolioCompanyFundHoldingModel.ModifiedOn,
            ModifiedBy = portfolioCompanyFundHoldingModel.ModifiedBy,
            IsDeleted = portfolioCompanyFundHoldingModel.IsDeleted,
            IsActive = portfolioCompanyFundHoldingModel.IsActive,
            Dpi = null,
            Rvpi = null

        };

        _unitOfWork.PortfolioCompanyFundHoldingDetailRepository.Insert(dbDetail);
        return dbDetail;
    }

    private Task<bool> UpdatePortfolioCompanyFundHolding(PortfolioCompanyFundHoldingModel portfolioCompanyFundHoldingModel)
    {
        var dbDetail = _unitOfWork.PortfolioCompanyFundHoldingDetailRepository.GetFirstOrDefault(x => x.PortfolioCompanyFundHoldingId == portfolioCompanyFundHoldingModel.PortfolioCompanyFundHoldingID);
        dbDetail.DealId = portfolioCompanyFundHoldingModel.DealID;
        dbDetail.FundHoldingStatusId = portfolioCompanyFundHoldingModel.FundHoldingStatus?.FundHoldingStatusID;
        dbDetail.GrossIrr = portfolioCompanyFundHoldingModel.GrossIRR;
        dbDetail.GrossMultiple = portfolioCompanyFundHoldingModel.GrossMultiple;
        dbDetail.InvestementDate = portfolioCompanyFundHoldingModel.InvestementDate;
        dbDetail.InvestmentCost = portfolioCompanyFundHoldingModel.InvestmentCost;
        dbDetail.Quarter = portfolioCompanyFundHoldingModel.Quarter;
        dbDetail.RealizedValue = portfolioCompanyFundHoldingModel.RealizedValue;
        dbDetail.TotalValue = portfolioCompanyFundHoldingModel.TotalValue;
        dbDetail.UnrealizedValue = portfolioCompanyFundHoldingModel.UnrealizedValue;
        dbDetail.ValuationDate = portfolioCompanyFundHoldingModel.ValuationDate;
        dbDetail.Year = portfolioCompanyFundHoldingModel.Year;
        dbDetail.ModifiedOn = portfolioCompanyFundHoldingModel.ModifiedOn;
        dbDetail.ModifiedBy = portfolioCompanyFundHoldingModel.ModifiedBy;
        dbDetail.IsDeleted = portfolioCompanyFundHoldingModel.IsDeleted;
        dbDetail.IsActive = portfolioCompanyFundHoldingModel.IsActive;
        dbDetail.Rvpi = null;
        dbDetail.Dpi = null;
        _unitOfWork.PortfolioCompanyFundHoldingDetailRepository.Update(dbDetail);
        _unitOfWork.Save();
        return Task.FromResult(true);
    }

    public async Task<bool> SavePortfolioCompanyFundHoldingList(List<PortfolioCompanyFundHoldingModel> portfolioCompanyFundHoldingModels)
    {
        var result = false;
        foreach (var portfolioCompanyFundHoldingModel in portfolioCompanyFundHoldingModels)
        {
            if (portfolioCompanyFundHoldingModel.PortfolioCompanyFundHoldingID > 0)
            {
                portfolioCompanyFundHoldingModel.EncryptedPortfolioCompanyFundHoldingID = _encryption.Encrypt(portfolioCompanyFundHoldingModel.PortfolioCompanyFundHoldingID.ToString());
                result = await UpdatePortfolioCompanyFundHolding(portfolioCompanyFundHoldingModel);
            }
            else
            {
                result = CreatePortfolioCompanyFundHolding(portfolioCompanyFundHoldingModel);
            }
            if (!result)
            {
                break;
            }
            CalculateFundTrackRecord(portfolioCompanyFundHoldingModel.DealID, result);
        }
        return result;

    }
    private void CalculateFundTrackRecord(int dealId, bool isExecute)
    {
        if (isExecute)
            _fundService.CalculateFundTrackRecordDerivedFromDealAsync(dealId);
    }
    public async Task<DealListModel> GetDuplicateDeals(List<DealModel> checkDuplicateDealName)
    {
        var result = new DealListModel();
        try
        {
            var deals = await _dapperGenericRepository.Query<DealQueryModel>(SqlConstants.DealFundPCQueryListWithParams);
            result.DealList = [.. deals.AsQueryable().Where(x => checkDuplicateDealName.Exists(p => p.FundDetails.FundName.ToLower() == x.FundName.ToLower() && p.PortfolioCompanyDetails.CompanyName.ToLower() == x.CompanyName.ToLower())).Select(x =>
         new DealModel
         {
             FundDetails = x.FundID.HasValue ? new FundModel
             {
                 FundID = (int)x.FundID,
                 FundName = x.FundName,
                 EncryptedFundId = x.EncryptedFundDetailsID
             } : new FundModel(),
             PortfolioCompanyDetails = x.PortfolioCompanyID.HasValue ? new PortfolioCompanyModel
             {
                 PortfolioCompanyID = (int)x.PortfolioCompanyID,
                 CompanyName = x.CompanyName,
                 EncryptedPortfolioCompanyId = x.EncryptedPortfolioCompanyID
             } : new PortfolioCompanyModel(),
             InvestmentDate = x.InvestmentDate.Value,
             DealID = x.DealID
         }
        )];
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in GetDuplicateDeals");
        }
        return result;

    }

    public PortfolioCompanyFundHoldingListModel GetDuplicateCompanyFundHolding(List<PortfolioCompanyFundHoldingModel> portfolioCompanyFundHoldingModelList)
    {
        return new PortfolioCompanyFundHoldingListModel
        {
            PortfolioCompanyFundHoldingList = [.. _unitOfWork.PortfolioCompanyFundHoldingDetailRepository.GetManyQueryable(x => !x.IsDeleted && portfolioCompanyFundHoldingModelList.Exists(y => y.Year == x.Year && y.Quarter.ToLower() == x.Quarter.ToLower() && y.DealID == x.DealId)).Select(x =>
           new PortfolioCompanyFundHoldingModel
           {
               DealID = x.DealId,
               Year = x.Year,
               Quarter = x.Quarter
           })]
        };
    }
    public async Task<List<PageFieldValueModelTrackRecord>> GetDealTradingRecordDynamicColumns()
    {
        List<SubPageFieldModel> trackDelaFields = await _pageDetailsConfigurationService.GetActiveFieldsBySubPageIdWithSequenceNo((int)PageConfigurationSubFeature.PortfolioCompanyFundHoldingDetails);
        List<PageFieldValueModelTrackRecord> dynamicCoulmns = trackDelaFields.Select(x => new PageFieldValueModelTrackRecord()
        {
            SubPageID = x.SubPageID,
            FieldID = x.Id,
            Name = x.Name,
            Value = NA,
            DisplayName = x.DisplayName,
            Sequence = x.SequenceNo,
            IsActive = x.IsActive,
            IsCustom = x.IsCustom,
            DataType = x.DataTypeId
        }).ToList();
        return dynamicCoulmns;
    }
    public static void AddProperty(IDictionary<string, object> expando, string propertyName, object propertyValue)
    {
        var expandoDict = expando;
        if (expandoDict.ContainsKey(propertyName))
            expandoDict[propertyName] = propertyValue;
        else
            expandoDict.Add(propertyName, propertyValue);
    }

    public async Task<List<PageFieldValueModelTrackRecord>> GetDealHoldingDynamicRecord(int dealId)
    {
        var customFields = await _pageDetailsConfigurationService.GetPageConfigFieldTrackRecordValueByFeatureId((int)PageConfigurationFeature.Deals, dealId);
        return [.. customFields.Where(x => x.PageID == (int)PageConfigurationFeature.Deals && x.SubPageID == (int)PageConfigurationSubFeature.PortfolioCompanyFundHoldingDetails)];
    }
    public async Task<List<IDictionary<string, object>>> DealsTrackRecordDynamicData(PortfolioCompanyFundHoldingListModel fundHoldingListModel, PortfolioCompanyFundHoldingFilter fundHoldingFilter)
    {
        List<IDictionary<string, object>> dynamicTrackRecord = new();
        var customFields = new List<PageFieldValueModelTrackRecord>();
        if (fundHoldingFilter != null)
        {
            if (fundHoldingFilter.DealID > 0)
            {
                customFields = await _pageDetailsConfigurationService.GetPageConfigFieldTrackRecordValueByFeatureId((int)PageConfigurationFeature.Deals, fundHoldingFilter.DealID);
                customFields = customFields.Where(x => x.PageID == (int)PageConfigurationFeature.Deals && x.SubPageID == (int)PageConfigurationSubFeature.PortfolioCompanyFundHoldingDetails).ToList();
            }
            else
            {
                List<int> DealList = fundHoldingListModel?.PortfolioCompanyFundHoldingList.Select(x => x.DealID).ToList();
                customFields = await _pageDetailsConfigurationService.GetPageConfigFieldMultipleTrackRecordValueByFeatureId((int)PageConfigurationFeature.Deals, DealList);
                customFields = customFields.Where(x => x.PageID == (int)PageConfigurationFeature.Deals && x.SubPageID == (int)PageConfigurationSubFeature.PortfolioCompanyFundHoldingDetails).ToList();
            }
        }
        List<SubPageFieldModel> trackDelaFields = await _pageDetailsConfigurationService.GetActiveFieldsBySubPageIdWithSequenceNo((int)PageConfigurationSubFeature.PortfolioCompanyFundHoldingDetails);
        List<PageFieldValueModelTrackRecord> _PageFieldModelDTO = trackDelaFields.Select(x => new PageFieldValueModelTrackRecord()
        {
            SubPageID = x.SubPageID,
            FieldID = x.Id,
            Name = x.Name,
            Value = NA,
            DisplayName = x.DisplayName,
            Sequence = x.SequenceNo,
            IsActive = x.IsActive,
            IsCustom = x.IsCustom,
            DataType = x.DataTypeId
        }).ToList();

        if ((bool)fundHoldingListModel?.PortfolioCompanyFundHoldingList.Any())
        {
            fundHoldingListModel?.PortfolioCompanyFundHoldingList.ForEach(x =>
            {
                IDictionary<string, object> expando = new ExpandoObject();
                int indexTracker = 0;
                _PageFieldModelDTO.ForEach(item =>
                {
                    if (indexTracker == 0)
                    {
                        AddProperty(expando, "dealId", x.DealID);
                        AddProperty(expando, "Year", x.Year);
                        AddProperty(expando, "fundHoldingStatusID", x.FundHoldingStatusID);
                        AddProperty(expando, "portfolioCompanyFundHoldingID", x.PortfolioCompanyFundHoldingID);
                        AddProperty(expando, "portfolioCompanyID", x.PortfolioCompanyID);
                        AddProperty(expando, "encryptedPortfolioCompanyFundHoldingID", x.EncryptedPortfolioCompanyFundHoldingID);
                        if (fundHoldingFilter.Year != 0)
                            AddProperty(expando, "companyName", x.PortfolioCompany?.CompanyName);
                    }
                    indexTracker++;
                    if (x != null && !item.IsCustom)
                    {
                        if (item.Name == Constants.InvestmentDate)
                            item.Value = x.InvestementDate == null ? NA : x.InvestementDate.Value.ToString("MM/dd/yyyy");
                        if (item.Name == Constants.RealizedValue)
                            item.Value = x.RealizedValue == null ? NA : x.RealizedValue.ToString();
                        if (item.Name == Constants.UnrealizedValue)
                            item.Value = x.UnrealizedValue == null ? NA : x.UnrealizedValue.ToString();
                        if (item.Name == Constants.Dpi)
                            item.Value = x.Dpi == null ? NA : x.Dpi.ToString();
                        if (item.Name == Constants.Rvpi)
                            item.Value = x.Rvpi == null ? NA : x.Rvpi.ToString();
                        if (item.Name == "Year")
                            item.Value = x.Year == 0 ? NA : x.Year.ToString();
                        if (item.Name == "Quarter")
                            item.Value = x.Quarter == null ? NA : x.Quarter.ToString() + " " + x.Year.ToString();
                        if (item.Name == Constants.GrossIRR)
                            item.Value = x.GrossIRR == null ? NA : x.GrossIRR.ToString();
                        if (item.Name == Constants.GrossMultiple)
                            item.Value = x.GrossMultiple == null ? NA : x.GrossMultiple.ToString();
                        if (item.Name == Constants.TotalValue)
                            item.Value = x.TotalValue == null ? NA : x.TotalValue.ToString();
                        if (item.Name == Constants.ValuationDate)
                            item.Value = x.ValuationDate == null ? NA : x.ValuationDate.Value.ToString("MM/dd/yyyy");
                        if (item.Name == Constants.InvestmentCost)
                            item.Value = x.InvestmentCost == null ? NA : x.InvestmentCost.ToString();
                        if (item.Name == Constants.DealStatus)
                            item.Value = x.FundHoldingStatus == null ? NA : x.FundHoldingStatus?.Status?.ToString();
                        AddProperty(expando, item.DisplayName, item.Value);
                    }
                    if (item.IsCustom)
                    {
                        var value = customFields.FirstOrDefault(y => y.Year == x.Year && y.Quarter == x.Quarter && y.FieldID == item.FieldID && y.PageFeatureId == x.DealID)?.Value;
                        value = string.IsNullOrEmpty(value) ? NA : value;
                        AddProperty(expando, item.DisplayName, value);
                    }
                });
                dynamicTrackRecord.Add(expando);
            });
        }
        return dynamicTrackRecord;
    }
    public async Task<DataTable> GetFundHoldingDataTable(PortfolioCompanyFundHoldingListModel result, PortfolioCompanyFundHoldingFilterExt filter, string header)
    {
        var decimalFraction = filter.ShowDecimalPlace ? "0.0" : "0";
        var decimalFractionWithHash = filter.ShowDecimalPlace ? "#,0.0" : "#,0";
        var numberValueType = filter?.ValueType?.ToLower().Trim() switch
        {
            Constants.Thousands => ValueTypeFeature.Thousands,
            Constants.Millions => ValueTypeFeature.Millions,
            Constants.Billions => ValueTypeFeature.Billions,
            Constants.Absolute => ValueTypeFeature.Absolute,
            _ => ValueTypeFeature.Absolute
        };
        var customFields = new List<PageFieldValueModelTrackRecord>();
        if (filter != null && filter.DealID > 0)
        {
            customFields = await _pageDetailsConfigurationService.GetPageConfigFieldTrackRecordValueByFeatureId((int)PageConfigurationFeature.Deals, filter.DealID);
            customFields = customFields.Where(x => x.PageID == (int)PageConfigurationFeature.Deals && x.SubPageID == (int)PageConfigurationSubFeature.PortfolioCompanyFundHoldingDetails).ToList();
        }
        List<SubPageFieldModel> trackDelaFields = await _pageDetailsConfigurationService.GetActiveFieldsBySubPageIdWithSequenceNo((int)PageConfigurationSubFeature.PortfolioCompanyFundHoldingDetails);
        List<PageFieldValueModelTrackRecord> _PageFieldModelDTO = trackDelaFields.Select(x => new PageFieldValueModelTrackRecord()
        {
            SubPageID = x.SubPageID,
            FieldID = x.Id,
            Name = x.Name,
            Value = NA,
            DisplayName = x.DisplayName,
            Sequence = x.SequenceNo,
            IsActive = x.IsActive,
            IsCustom = x.IsCustom,
            DataType = x.DataTypeId
        }).ToList();
        header = Regex.Replace(header, @"\s", "", RegexOptions.None, TimeSpan.FromSeconds(5));

        DataTable dt = new(header);

        _PageFieldModelDTO.ForEach(field =>
        {
            if (field.IsActive)
            {
                dt.Columns.Add(field.DisplayName, typeof(string));

            }
        });

        if (result?.PortfolioCompanyFundHoldingList != null && result.PortfolioCompanyFundHoldingList.Any())
        {
            result?.PortfolioCompanyFundHoldingList.ForEach(x =>
            {
                DataRow dr = dt.NewRow();
                _PageFieldModelDTO.ForEach(item =>
                {

                    if (x != null && !item.IsCustom)
                    {
                        if (item.Name == Constants.InvestmentDate)
                            item.Value = x.InvestementDate == null ? NA : Convert.ToDateTime(x.InvestementDate).ToLocalTime().ToString("MM/dd/yyyy");
                        if (item.Name == Constants.RealizedValue)
                            item.Value = x.RealizedValue == null ? NA : Common.ConvertValuesWithDecimal(numberValueType, x.RealizedValue ?? 0).ToString(ShowDeciPlaces(filter));
                        if (item.Name == Constants.UnrealizedValue)
                            item.Value = x.UnrealizedValue == null ? NA : Common.ConvertValuesWithDecimal(numberValueType, x.UnrealizedValue ?? 0).ToString(ShowDeciPlaces(filter));
                        if (item.Name == Constants.Dpi)
                            item.Value = x.Dpi == null ? NA : Common.ConvertToDecimals(x.Dpi ?? 0).ToString(ShowDeciPlacesN(filter)) + "x";
                        if (item.Name == Constants.Rvpi)
                            item.Value = x.Rvpi == null ? NA : Common.ConvertToDecimals(x.Rvpi ?? 0).ToString(ShowDeciPlacesN(filter)) + "x";
                        if (item.Name == Constants.Year)
                            item.Value = x.Year.ToString();
                        if (item.Name == Constants.Quarter)
                            item.Value = x.Quarter == null ? NA : x.Quarter.ToString() + " " + x.Year.ToString();
                        if (item.Name == Constants.GrossIRR)
                            item.Value = x.GrossIRR == null ? NA : Common.ConvertToDecimals(x.GrossIRR ?? 0).ToString(ShowDeciPlacesN(filter)) + "%";
                        if (item.Name == Constants.GrossMultiple)
                            item.Value = x.GrossMultiple == null ? NA : Common.ConvertToDecimals(x.GrossMultiple ?? 0).ToString(ShowDeciPlacesN(filter)) + "x";
                        if (item.Name == Constants.TotalValue)
                            item.Value = x.TotalValue == null ? NA : Common.ConvertValuesWithDecimal(numberValueType, x.TotalValue ?? 0).ToString(ShowDeciPlaces(filter));
                        if (item.Name == Constants.ValuationDate)
                            item.Value = x.ValuationDate == null ? NA : x.ValuationDate.Value.ToString("MM/dd/yyyy");
                        if (item.Name == Constants.InvestmentCost)
                            item.Value = x.InvestmentCost == null ? NA : Common.ConvertValuesWithDecimal(numberValueType, x.InvestmentCost ?? 0).ToString(ShowDeciPlaces(filter));
                        if (item.Name == Constants.DealStatus)
                            item.Value = x.FundHoldingStatus == null ? NA : x.FundHoldingStatus.Status.ToString();
                        dr[item.DisplayName] = item.Value;
                    }
                    if (item.IsCustom)
                    {
                        var pageField = item;
                        int updatedValue = 0;
                        decimal updatedDecimalVal = 0;
                        var val = customFields.FirstOrDefault(y => y.Year == x.Year && y.Quarter == x.Quarter && y.FieldID == item.FieldID)?.Value;
                        val = string.IsNullOrEmpty(val) ? NA : val;
                        if (val != NA)
                        {
                            switch (pageField.DataType)
                            {
                                case (int)PageSubFieldsDatatTypes.Number:
                                    val = int.TryParse(Convert.ToString(val), out updatedValue) ? updatedValue.ToString("#,0") : val;
                                    break;
                                case (int)PageSubFieldsDatatTypes.Currency:
                                    val = decimal.TryParse(val.Replace("x", string.Empty).Replace("%", string.Empty).Replace("$", string.Empty), NumberStyles.Any, CultureInfo.CurrentCulture, out updatedDecimalVal) ? Common.ConvertValuesWithDecimal(numberValueType, updatedDecimalVal).ToString(decimalFractionWithHash) : val;
                                    break;
                                case (int)PageSubFieldsDatatTypes.Percentage:
                                    val = decimal.TryParse(val.Trim().Replace("%", string.Empty), NumberStyles.Any, CultureInfo.CurrentCulture, out updatedDecimalVal) ? Common.ConvertToDecimals(updatedDecimalVal).ToString(decimalFraction) + "%" : val;
                                    break;
                                case (int)PageSubFieldsDatatTypes.Multiple:
                                    val = decimal.TryParse(val.Trim().Replace("x", string.Empty).Replace("X", string.Empty), NumberStyles.Any, CultureInfo.CurrentCulture, out updatedDecimalVal) ? Common.ConvertToDecimals(updatedDecimalVal).ToString(decimalFraction) + "x" : val;
                                    break;
                                case (int)PageSubFieldsDatatTypes.Date:
                                    if (!string.IsNullOrEmpty(val?.ToString()))
                                    {
                                        DateTime dDate;
                                        if (DateTime.TryParse(val?.ToString(), out dDate))
                                        {
                                            val = DateTime.Parse(dDate.ToString()).ToString("MM/dd/yyyy");
                                        }
                                    }
                                    break;
                            }
                        }
                        dr[item.DisplayName] = val;
                    }

                });
                dt.Rows.Add(dr);
            });
        }

        return dt;
    }

    private static string ShowDeciPlaces(PortfolioCompanyFundHoldingFilterExt filter)
    {
        return filter.ShowDecimalPlace ? "#,0.0" : "#,0";
    }

    private static string ShowDeciPlacesN(PortfolioCompanyFundHoldingFilterExt filter)
    {
        return filter.ShowDecimalPlace ? "0.0" : "0";
    }
    public async Task<SelectionList> GetCommonDealTrackRecordLatestQuarter()
    {
        return await _dapperGenericRepository.QuerySingleOrDefaultAsync<SelectionList>(SqlConstants.QueryByDealTrackRecordLatestQuarterYear, null);
    }
    public async Task<string> GetCommonDealTrackRecordQuarterYearCount(ReportQueryModel model)
    {
        if (model?.InvestorId == 0)
            return Convert.ToString(model?.SelectedReportTypes[0]) == Constants.CompanyValuation ? await _dapperGenericRepository.QuerySingleOrDefaultAsync<string>(SqlConstants.QueryByCompanyValuationCountByQuarterYear, new { QuarterYear = model?.QuarterYear }) : await _dapperGenericRepository.QuerySingleOrDefaultAsync<string>(SqlConstants.QueryByMasterCompaniesCountByQuarterYear, new { QuarterYear = model?.QuarterYear });
        else
            return await _dapperGenericRepository.QuerySingleOrDefaultAsync<string>(SqlConstants.QueryByMasterCompaniesCountByTopHoldingInvestor, new { QuarterYear = model?.QuarterYear, InvestorId = model?.InvestorId });
    }
    public async Task<List<DealTrackRecordModel>> GetAllFundsByInvestors()
    {
        return await _dapperGenericRepository.Query<DealTrackRecordModel>(SqlConstants.QueryByAllDeals);
    }
    public async Task<List<FundInvestorsModel>> GetAllDeals()
    {
        return await _dapperGenericRepository.Query<FundInvestorsModel>(SqlConstants.QueryByAllFundsByInvestor);
    }

    public DataTable GetDealTransactionTable(DateTime toDate, DateTime fromDate, string quarter, int year)
    {
        var DynamicQuery = _dapperGenericRepository.QueryNonAsync<dynamic>(SqlConstants.QueryByDealNewTransaction, new { @toDate = toDate, @fromDate = fromDate, @quarter = quarter, @year = year }).ToList();
        var json = Newtonsoft.Json.JsonConvert.SerializeObject(DynamicQuery);
        DataTable dataTable = (DataTable)Newtonsoft.Json.JsonConvert.DeserializeObject(json, typeof(DataTable));
        dataTable.TableName = "Deals";
        DataColumnCollection columns = dataTable.Columns;
        if (!columns.Contains("InvestmentCost(m)") && dataTable.Rows.Count > 0)
        {
            dataTable.Columns.Add("InvestmentCost(m)", typeof(string)).SetOrdinal(4);
        }
        foreach (DataRow row in dataTable.Rows)
        {
            foreach (DataColumn col in dataTable.Columns)
            {
                if (row[col] == DBNull.Value || string.IsNullOrEmpty(row[col].ToString()))
                {
                    row[col] = NA;
                }
            }
        }
        return dataTable;
    }
    public async Task<List<DealsList>> GetRepositoryDeals()
    {
        return await _dapperGenericRepository.Query<DealsList>(SqlConstants.DealTableQueryList);
    }
    public async Task<List<SubFeatureAccessPermissionsModel>> GetDealPermissions(int userId, int dealId, int featureId)
    {
        return await _dapperGenericRepository.Query<SubFeatureAccessPermissionsModel>(SqlConstants.QueryGetDealPermissions, new { userId, dealId, featureId });
    }
    public async Task<List<Mapping_GroupFeature>> GetDealsPermissions(int userId, int featureId)
    {
        return await _dapperGenericRepository.Query<Mapping_GroupFeature>(SqlConstants.QueryGetDealsPermission, new { userId, featureId });
    }
}

