using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using DataAccessLayer.DBModel;

namespace DataAccessLayer.Models
{
    public class MappingCapTable : BaseModel
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int MappingId { get; set; }
        public int PortfolioCompanyId { get; set; }
        public int KpiId { get; set; }
        public bool IsDeleted { get; set; }
        public int? ParentKpiId { get; set; }
        public int DisplayOrder { get; set; }
        public bool IsHeader { get; set; }
        public int ModuleId { get; set; }
        public string Formula { get; set; }
        public string FormulaKPIId { get; set; }
        public bool IsExtraction { get; set; }
        public string Synonym { get; set; }
        public string Definition { get; set; }
    }
}