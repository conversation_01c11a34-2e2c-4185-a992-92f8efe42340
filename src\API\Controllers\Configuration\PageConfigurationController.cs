﻿using System;
using API.Helpers;
using Contract.Configuration;
using Contract.Utility;
using Master;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using API.Filters.CustomAuthorization;
using Contract.Account;

namespace API.Controllers.Configuration;


public class PageConfigurationController : BaseController
{
    private readonly IPageDetailsConfigurationService _pageConfigurationService;
    public PageConfigurationController(IPageDetailsConfigurationService pageConfigurationService, IInjectedParameters InjectedParameters, IHelperService helperService) : base(InjectedParameters, helperService)
    {
        _pageConfigurationService = pageConfigurationService;
    }
    [Route("pageconfiguration/get")]
    [UserFeatureAuthorize((int)Features.PageConfig,(int)Features.PortfolioCompany)]
    [HttpGet]
    public async Task<IActionResult> GetConfiguration()
    {
        var pageConfigurations = await _pageConfigurationService.GetConfiguration();

        if (pageConfigurations.Count > 0)
            return Ok(pageConfigurations);
        else
            return NoContent();
    }
    [Route("pageconfiguration/SaveSettings")]
    [UserFeatureAuthorize((int)Features.PageConfig)]
    [HttpPost]
    public async Task<IActionResult> SavePageConfiguration(List<PageDetailModel> pageDetailModel)
    {
        if (ModelState.IsValid)
        {
            var pageConfigurations = await _pageConfigurationService.SavePageConfigFields(pageDetailModel, GetCurrentUserId());
            return Ok(pageConfigurations);
        }
        else
            return BadRequest();

    }
    [Route("pageconfigsetting/get")]
    [UserFeatureAuthorize((int)Features.PageConfig,(int)Features.PortfolioCompany)]
    [HttpPost]
    public async Task<IActionResult> GetPageSettingsByID([FromBody] StringValueModel pageID)
    {
        var subPageConfigSetting = await _pageConfigurationService.GetActiveSubPageSectionByPageId(Convert.ToInt32(pageID.Value));
        var pageFieldConfigSetting = await _pageConfigurationService.GetActiveFieldsByPageId(Convert.ToInt32(pageID.Value));

        if (subPageConfigSetting.Any())
            return Ok(new
            {
                SubPageList = subPageConfigSetting,
                FieldValueList = pageFieldConfigSetting

            });
        else
            return NoContent();
    }
    [Route("pageconfiguration/getTrackrecordDataTypes")]
    [UserFeatureAuthorize((int)Features.PageConfig)]
    [HttpGet]
    public async Task<IActionResult> getTrackrecordDataTypes()
    {
        var pageConfigurations = await _pageConfigurationService.TrackrecordDataTypes();
        var pageConfigurationsDatalist = await _pageConfigurationService.CheckDataExitsOnCustomFields();
        if (pageConfigurations.Count > 0)
            return Ok(new { pageConfigurations, pageConfigurationsDatalist });
        else
            return NoContent();
    }
    
    [HttpGet("page-config-setting/is-WorkFlow")]
    public async Task<IActionResult> GetWorkFlowDetails()
    {
        return Ok(await _pageConfigurationService.GetWorkFlowPCDetails());
    }

    /// <summary>
    /// Updates the workflow details based on the provided flag.
    /// </summary>
    /// <param name="isWorkFlowEnable">A boolean flag indicating whether the workflow should be enabled or not.</param>
    /// <returns>An <see cref="IActionResult"/> indicating the result of the update operation.</returns>  

    [UserFeatureAuthorize((int)Features.PageConfig)] 
    [HttpPost("page-config-setting/update-WorkFlow/{isWorkFlowEnable}")]
  
    public async Task<IActionResult> UpdateWorkFlowDetails(bool isWorkFlowEnable)
    {
        return Ok(await _pageConfigurationService.UpdateWorkFlowPCDetails(isWorkFlowEnable));
    }
    [Route("page-config/subpage-fields/{subPageId}")]
    [UserFeatureAuthorize((int)Features.PageConfig, (int)Features.PortfolioCompany)]
    [HttpGet]
    public async Task<IActionResult> GetSubPageFieldsBySubPageId(int subPageId)
    {
        var pageConfigurations = await _pageConfigurationService.GetActiveFieldsBySubPageIdWithSequenceNoActiveInactive(subPageId);
        return Ok(pageConfigurations);
    }
}

