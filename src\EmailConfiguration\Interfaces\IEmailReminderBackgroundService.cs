using System.Threading.Tasks;

namespace EmailConfiguration.Interfaces
{
    /// <summary>
    /// Interface for email reminder background service
    /// </summary>
    public interface IEmailReminderBackgroundService
    {
        /// <summary>
        /// Processes pending email reminders for the current date
        /// </summary>
        /// <returns>Task representing the asynchronous operation</returns>
        Task ProcessPendingRemindersAsync();
    }
}
