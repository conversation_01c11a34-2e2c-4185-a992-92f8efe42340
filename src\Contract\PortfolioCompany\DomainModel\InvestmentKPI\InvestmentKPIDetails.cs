using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Contract.PortfolioCompany
{
    public class InvestmentKpiDetails : BaseModel
    {
        public int InvestmentKPIId { get; set; }
        [MaxLength(200)]
        public string KPI { get; set; }
        [MaxLength(20)]
        public string KpiInfo { get; set; }
        public int OrderBy { get; set; }
        [MaxLength(500)]
        public string Description { get; set; }
        public bool IsDeleted { get; set; }
        public bool IsNumeric { get; set; }
        [MaxLength(500)]
        public string EncryptedInvestmentKPIId { get; set; }
        [NotMapped]
        public string KpiInfoType { get; set; }
        public string Formula { get; set; }
        public string MethodologyName { get; set; }
        public string FormulaKPIId { get; set; }
        public bool? IsBoldKPI { get; set; }
        public bool? IsHeader { get; set; }
        public int MethodologyID { get; set; }
        public string Synonym { get; set; }
    }
}
