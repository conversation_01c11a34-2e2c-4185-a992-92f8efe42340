using System;

namespace Contract.FxRates
{
    public class FxRateFilterModel
    {
        public string FromDate { get; set; } = string.Empty;
        public string ToDate { get; set; } = string.Empty;
        public string FilterSource { get; set; } = string.Empty;
        public string FromCurrencyCode { get; set; } = string.Empty;
        public string ToCurrencyCode { get; set; } = string.Empty;
    }
}