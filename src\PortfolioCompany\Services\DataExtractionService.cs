﻿using Contract.Account;
using Contract.Documents;
using Contract.Utility;
using DataAccessLayer.Models.DataExtraction;
using DataAccessLayer.UnitOfWork;
using PortfolioCompany.Interfaces;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PortfolioCompany.Services
{
    public class DataExtractionService(IUnitOfWork unitOfWork) : IDataExtraction
    {
        private readonly IUnitOfWork _unitOfWork = unitOfWork;        

        /// <summary>
        /// Retrieves a list of active data extraction document types from the database.
        /// </summary>
        /// <returns>
        /// A Task containing a List of <see cref="DocumentsInformationDto"/> objects with the following properties:
        /// - Id: Unique identifier of the document type
        /// - DocumentName: Name of the document type
        /// </returns>
        /// <remarks>
        /// This method only returns non-deleted document types (where IsDeleted = false).
        /// The returned data is mapped from DataExtractionTypes entities to DocumentsInformationDto objects.
        /// </remarks>
        public async Task<List<DocumentsInformationDto>> GetDataExtractionTypes(int featureId)
        {
            List<DataExtractionTypes> documentTypes = await _unitOfWork.DataExtractionTypesRepository.FindAllAsync(x => !x.IsDeleted && x.FeatureId == featureId);
            return documentTypes.Select(d => new DocumentsInformationDto
            {
                Id = d.Id,
                DocumentName = d.DocumentName,               
            }).ToList();
        }
        public async Task<List<SourceTypes>> GetSourceTypes()
        {
            List<MSourceTypes> sourceTypes = await _unitOfWork.MSourceTypesRepository.FindAllAsync(x => !x.IsDeleted);
            return [.. sourceTypes.Select(d => new SourceTypes
            {
                Id = d.Id,
                Name = d.Name,
            })];
        }
        public async Task<DocumentsInformationDto> AddDocumentTypeAsync(DocumentsInformationDto documentType)
        {
            var newDocumentType = new DataExtractionTypes
            {
                DocumentName = documentType.DocumentName,
                IsDeleted = false,
                FeatureId = documentType.FeatureId
            };

            await _unitOfWork.DataExtractionTypesRepository.AddAsyn(newDocumentType);
            await _unitOfWork.SaveAsync();

            documentType.Id = newDocumentType.Id;
            return documentType;
        }
    }
}
