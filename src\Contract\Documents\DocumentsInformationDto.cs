using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Http;
using OfficeOpenXml;

namespace Contract.Documents
{
	public class DocumentsInformationDto
	{
		public long Id { get; set; }
		public string DocumentId { get; set; }
		public string DocumentName { get; set; }
		public int CreatedBy { get; set; }
        public int FeatureId { get; set; }
    }
	public class DocumentsInformationModel
    {
		public IFormFile[] SupportingDocuments { get; set; } = [];
		[Required]
		public IFormFile Template { get; set; } = null;
		[Required]
		public string ModuleName { get; set; }
		public int SubPageId { get; set; }
		[Required]
		public int CompanyId { get; set; }
		public string Comments { get; set; }
		public long TemplateDocumentId { get; set; } = 0;
		public string SupportingDocumentIds { get; set; }
		public long CommentId { get; set; } = 0;
		public int UserId { get; set; } = 0;
		public string MonthlyReportPeriod { get; set; }
        public string ConnectionString { get; set; }=string.Empty;
    }
	public class DocumentsInformationStatusModel
	{
		public long DocumentId { get; set; }
		public string FileId { get; set; }
		public string FileName { get; set; }
		public bool IsUploaded { get; set; }
	}
	public class DocumentsDeletionStatusModel
	{
		public string FileId { get; set; }
		public string FileName { get; set; }
		public bool IsDeleted { get; set; }
	}

	public class DocumentUploadModel
	{
		public int UserId { get; set; }
		public string ConnectionString { get; set; }
		public string FullPath { get; set; }
		public ExcelWorksheet Worksheet { get; set; }
		public int ModuleId { get; set; }
		public string ModuleName { get; set; }
	}

	public enum UploadType
	{
		None = 0,
		Manual = 1,
		DataIngestion = 2,
	}
}