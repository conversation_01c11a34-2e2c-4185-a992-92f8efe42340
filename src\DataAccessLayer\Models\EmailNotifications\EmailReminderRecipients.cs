using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Diagnostics.CodeAnalysis;
using DataAccessLayer.DBModel;

namespace DataAccessLayer.Models.EmailNotifications
{
    [Table("EmailReminderRecipients")]
    [ExcludeFromCodeCoverage]
    public class EmailReminderRecipients : BaseCommonModel
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Required]
        public Guid ReminderId { get; set; }

        [Required]
        public RecipientTypes RecipientType { get; set; } // 1 for To, 2 for CC

        public int? RecipientId { get; set; } // User ID if user email present in User_Information table

        [MaxLength(255)]
        public string EmailAddress { get; set; } // Email if user email not present in User_Information table

        [Required]
        public bool IsGroupMember { get; set; } = false;

        public int? GroupID { get; set; } // Group ID if group member, else null. Maps to EmailNotificationGroups table.
    }

    public enum RecipientTypes
    {
        To = 1,
        Cc = 2
    }
}
