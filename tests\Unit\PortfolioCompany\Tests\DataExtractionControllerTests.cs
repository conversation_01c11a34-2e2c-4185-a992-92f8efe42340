﻿using API.Controllers.DataExtraction;
using Contract.Documents;
using Contract.Utility;
using DataAccessLayer.Models.DataExtraction;
using DataAccessLayer.UnitOfWork;
using FluentAssertions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using PortfolioCompany.Services;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Xunit;

namespace PortfolioCompany.UnitTest.Tests
{
    public class DataExtractionControllerTests
    {
        public readonly DataExtractionController controller;
        public readonly Mock<ILogger<DataExtractionController>> logger;
        public readonly Mock<IUnitOfWork> unitOfWork;
        public readonly DataExtractionService service;
        public DataExtractionControllerTests()
        {
            logger = new Mock<ILogger<DataExtractionController>>();
            unitOfWork = new Mock<IUnitOfWork>();
            service = new(unitOfWork.Object);
            controller = new(service, logger.Object);
        }
        [Fact]
        public async Task FetchDataExtractionTypes_ReturnsOkResult_WithListOfDocumentsInformationDto()
        {
            // Arrange
            var mockData = new List<DataExtractionTypes>
            {
                new() { Id = 1, DocumentName = "Doc1",IsDeleted=false,CreatedBy=3,CreatedOn=DateTime.UtcNow,ModifiedBy=null,ModifiedOn=null },
                new() { Id = 2, DocumentName = "Doc2",IsDeleted=false,CreatedBy=3,CreatedOn=DateTime.UtcNow,ModifiedBy=null,ModifiedOn=null }
            };

            const int TEST_FEATURE_ID = 14;

            unitOfWork.Setup(x => x.DataExtractionTypesRepository.FindAllAsync(It.IsAny<Expression<Func<DataExtractionTypes, bool>>>())).Returns(Task.FromResult(mockData));
            var result = await controller.FetchDataExtractionTypes(TEST_FEATURE_ID);
            // Assert
            var okResult = result.Should().BeOfType<OkObjectResult>().Subject;
            var returnValue = okResult.Value.Should().BeAssignableTo<List<DocumentsInformationDto>>().Subject;
            returnValue[0].DocumentName.Should().Be(mockData[0].DocumentName);
            returnValue[1].DocumentName.Should().Be(mockData[1].DocumentName);
        }
        [Fact]
        public async Task FetchDataExtractionTypes_WhenNoData_ReturnsEmptyList()
        {
            // Arrange
            var emptyList = new List<DataExtractionTypes>();
            const int TEST_FEATURE_ID = 14;

            unitOfWork.Setup(x => x.DataExtractionTypesRepository.FindAllAsync(
                It.IsAny<Expression<Func<DataExtractionTypes, bool>>>()))
                .ReturnsAsync(emptyList);

            var result = await controller.FetchDataExtractionTypes(TEST_FEATURE_ID);
            var okResult = Assert.IsType<OkObjectResult>(result);
            var returnValue = Assert.IsType<List<DocumentsInformationDto>>(okResult.Value);
            Assert.Empty(returnValue);
        }
        [Fact]
        public async Task FetchSourceTypes_ReturnsOkResult_WithList()
        {
            // Arrange
            var mockData = new List<MSourceTypes>
            {
                new() { Id = 1, Name = "Doc1",IsDeleted=false,CreatedBy=3,CreatedOn=DateTime.UtcNow,ModifiedBy=null,ModifiedOn=null },
                new() { Id = 2, Name = "Doc2",IsDeleted=false,CreatedBy=3,CreatedOn=DateTime.UtcNow,ModifiedBy=null,ModifiedOn=null }
            };
            unitOfWork.Setup(x => x.MSourceTypesRepository.FindAllAsync(It.IsAny<Expression<Func<MSourceTypes, bool>>>())).Returns(Task.FromResult(mockData));
            var result = await controller.FetchSourceTypes();
            // Assert
            var okResult = result.Should().BeOfType<OkObjectResult>().Subject;
            var returnValue = okResult.Value.Should().BeAssignableTo<List<SourceTypes>>().Subject;
            returnValue[0].Name.Should().Be(mockData[0].Name);
            returnValue[1].Name.Should().Be(mockData[1].Name);
        }
        [Fact]
        public async Task AddDocumentType_ValidInput_ReturnsOkResult()
        {
            // Arrange
            var documentType = new DocumentsInformationDto { DocumentName = "New Document" };
            unitOfWork.Setup(x => x.DataExtractionTypesRepository.Insert(It.IsAny<DataExtractionTypes>()));
            unitOfWork.Setup(x => x.SaveAsync()).ReturnsAsync(1);

            // Act
            var result = await controller.AddDocumentType(documentType);

            // Assert
            var okResult = result.Should().BeOfType<OkObjectResult>().Subject;
            var returnValue = okResult.Value.Should().BeAssignableTo<DocumentsInformationDto>().Subject;
            returnValue.DocumentName.Should().Be(documentType.DocumentName);
        }
    }
}
