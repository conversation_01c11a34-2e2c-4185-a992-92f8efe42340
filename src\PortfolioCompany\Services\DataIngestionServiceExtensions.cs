using DataAccessLayer.DBModel;
using DataAccessLayer.Models;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Shared;
using Contract.DataIngestion;
using Contract.Funds;

namespace PortfolioCompany.Services
{
    /// <summary>
    /// Extension methods for DataIngestionService to support bulk operations
    /// </summary>
    public static class DataIngestionServiceExtensions
    {
        /// <summary>
        /// Bulk saves static fields data for multiple portfolio companies
        /// </summary>
        /// <param name="companiesData">List of company data with static fields</param>
        /// <param name="userId">ID of the user performing the operation</param>
        /// <param name="unitOfWork">Unit of work instance</param>
        /// <param name="logger">Logger instance</param>
        /// <returns>True if operation is successful; otherwise, false</returns>
        public static async Task<bool> SavePortfolioCompanyStaticFieldsBulk(
            List<PCDataModel> companiesData,
            int userId,
            DataAccessLayer.UnitOfWork.IUnitOfWork unitOfWork,
            ILogger logger)
        {
            try
            {
                logger.LogInformation("Starting bulk save of static fields data for {Count} companies", companiesData?.Count ?? 0);
                if (companiesData == null || companiesData.Count == 0)
                {
                    logger.LogWarning("No company data provided for bulk saving");
                    return false;
                }

                // Filter out companies with no static fields
                companiesData = companiesData.Where(c => c.StaticFields != null && c.StaticFields.Count > 0).ToList();
                if (companiesData.Count == 0)
                {
                    logger.LogWarning("No static fields data found in any of the companies");
                    return false;
                }

                // Get all company IDs to retrieve in a single query
                var companyIds = companiesData.Select(c => c.CompanyId).Distinct().ToList();
                
                // Get all portfolio companies in a single database call
                var portfolioCompanies = await unitOfWork.PortfolioCompanyDetailRepository.GetManyAsync(
                    x => companyIds.Contains(x.PortfolioCompanyId) && !x.IsDeleted);

                if (portfolioCompanies == null || !portfolioCompanies.Any())
                {
                    logger.LogError("No portfolio companies found with the provided IDs");
                    return false;
                }

                // Get field mappings once for all operations
                var fieldMappings = await unitOfWork.SubPageFieldsRepository.GetManyAsync(
                    x => x.SubPageID == (int)PageConfigurationSubFeature.StaticInformation && !x.IsDeleted);

                var fieldIdToNameMap = fieldMappings.ToDictionary(f => f.FieldID, f => f.Name);

                // Get all existing custom field values for these companies in a single query
                var allExistingCustomValues = await unitOfWork.PageConfigurationFieldValueRepository.GetManyAsync(
                    x => x.PageID == (int)PageConfigurationFeature.PortfolioCompany && 
                    companyIds.Contains(x.PageFeatureId) && 
                    x.SubPageID == (int)PageConfigurationSubFeature.StaticInformation);

                // Group custom values by company ID for faster lookup
                var customValuesByCompany = allExistingCustomValues
                    .GroupBy(x => x.PageFeatureId)
                    .ToDictionary(g => g.Key, g => g.ToList());

                // Lists to track changes
                var companiesToUpdate = new List<PortfolioCompanyDetails>();
                var customFieldsToCreate = new List<PageConfigurationFieldValue>();
                var customFieldsToUpdate = new List<PageConfigurationFieldValue>();

                // Process each company
                foreach (var companyData in companiesData)
                {
                    var portfolioCompany = portfolioCompanies.FirstOrDefault(pc => pc.PortfolioCompanyId == companyData.CompanyId);
                    if (portfolioCompany == null)
                    {
                        logger.LogWarning("Portfolio company with ID {CompanyId} not found in retrieved companies", companyData.CompanyId);
                        continue;
                    }

                    // Get existing custom values for this company
                    customValuesByCompany.TryGetValue(companyData.CompanyId, out var existingCustomValues);
                    existingCustomValues ??= new List<PageConfigurationFieldValue>();

                    // Process each static field
                    foreach (var field in companyData.StaticFields)
                    {
                        if (!fieldIdToNameMap.TryGetValue(field.FieldId, out string fieldName))
                        {
                            logger.LogWarning("Field mapping not found for FieldId: {FieldId}", field.FieldId);
                            continue;
                        }

                        // Update company properties or custom fields
                        SetPortfolioCompanyFieldValue(
                            portfolioCompany, 
                            fieldName, 
                            field, 
                            existingCustomValues, 
                            customFieldsToCreate, 
                            customFieldsToUpdate, 
                            userId);
                    }

                    // Update modification information
                    portfolioCompany.ModifiedBy = userId;
                    portfolioCompany.ModifiedOn = DateTime.Now;

                    // Add to the list of companies to update
                    companiesToUpdate.Add(portfolioCompany);
                }

                // Bulk update all portfolio companies
                foreach (var company in companiesToUpdate)
                {
                    unitOfWork.PortfolioCompanyDetailRepository.Update(company);
                }

                // Bulk create new custom fields
                foreach (var field in customFieldsToCreate)
                {
                    await unitOfWork.PageConfigurationFieldValueRepository.AddAsyn(field);
                }

                // Bulk update existing custom fields
                foreach (var field in customFieldsToUpdate)
                {
                    unitOfWork.PageConfigurationFieldValueRepository.Update(field);
                }

                // Save all changes in a single database call
                await unitOfWork.SaveAsync();

                logger.LogInformation("Successfully bulk saved static fields data for {Count} companies", companiesToUpdate.Count);
                return true;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error bulk saving static fields data: {Message}", ex.Message);
                return false;
            }
        }

        /// <summary>
        /// Sets a property value on the PortfolioCompany object based on field name
        /// </summary>
        /// <param name="portfolioCompany">The PortfolioCompany object to update</param>
        /// <param name="fieldName">The name of the field to set</param>
        /// <param name="field">The field data model with the value to set</param>
        /// <param name="existingCustomValues">List of existing custom field values</param>
        /// <param name="customFieldsToCreate">List to store custom fields that need to be created</param>
        /// <param name="customFieldsToUpdate">List to store custom fields that need to be updated</param>
        /// <param name="userId">ID of the user performing the operation</param>
        private static void SetPortfolioCompanyFieldValue(
            PortfolioCompanyDetails portfolioCompany, 
            string fieldName, 
            StaticDataModel field,
            List<PageConfigurationFieldValue> existingCustomValues,
            List<PageConfigurationFieldValue> customFieldsToCreate,
            List<PageConfigurationFieldValue> customFieldsToUpdate,
            int userId)
        {
            string fieldValue = field?.FieldValue;
            if (string.IsNullOrWhiteSpace(fieldValue))
            {
                return;
            }
            
            try
            {
                switch (fieldName)
                {
                    case Constants.CompanyName:
                        portfolioCompany.CompanyName = fieldValue;
                        break;
                    case Constants.CompanyLegalName:
                        portfolioCompany.CompanyLegalName = fieldValue;
                        break;
                    case Constants.BusinessDescription:
                        portfolioCompany.BussinessDescription = fieldValue;
                        break;
                    case Constants.CompanyStatus:
                        portfolioCompany.Status = fieldValue;
                        break;
                    case Constants.Sector:
                        if (int.TryParse(fieldValue, out int sectorId))
                            portfolioCompany.SectorId = sectorId;
                        break;
                    case Constants.SubSector:
                        if (int.TryParse(fieldValue, out int subSectorId))
                            portfolioCompany.SubSectorId = subSectorId;
                        break;
                    case Constants.Currency:
                        if (int.TryParse(fieldValue, out int currencyId))
                            portfolioCompany.ReportingCurrencyId = currencyId;
                        break;
                    case Constants.Website:
                        portfolioCompany.Website = fieldValue;
                        break;
                    case Constants.HeadquarterID:
                        if (int.TryParse(fieldValue, out int hqId))
                            portfolioCompany.HeadquarterId = hqId;
                        break;
                    case Constants.StockExchange_Ticker:
                        portfolioCompany.StockExchangeTicker = fieldValue;
                        break;
                    case Constants.Customfield:
                        UpsertCustomFieldValue(portfolioCompany, field, existingCustomValues, customFieldsToCreate, customFieldsToUpdate, userId);
                        break;
                }
            }
            catch (FormatException)
            {
                // Log error or handle invalid format
            }
        }

        /// <summary>
        /// Creates or updates a custom field value
        /// </summary>
        /// <param name="portfolioCompany">The portfolio company object</param>
        /// <param name="field">The field data model</param>
        /// <param name="existingCustomValues">List of existing custom field values</param>
        /// <param name="customFieldsToCreate">List to store custom fields that need to be created</param>
        /// <param name="customFieldsToUpdate">List to store custom fields that need to be updated</param>
        /// <param name="userId">ID of the user performing the operation</param>
        private static void UpsertCustomFieldValue(
            PortfolioCompanyDetails portfolioCompany,
            StaticDataModel field,
            List<PageConfigurationFieldValue> existingCustomValues,
            List<PageConfigurationFieldValue> customFieldsToCreate,
            List<PageConfigurationFieldValue> customFieldsToUpdate,
            int userId)
        {
            var existingField = existingCustomValues.FirstOrDefault(x => 
                x.FieldID == field.FieldId && 
                x.PageID == (int)PageConfigurationFeature.PortfolioCompany && 
                x.SubPageID == (int)PageConfigurationSubFeature.StaticInformation && 
                x.PageFeatureId == portfolioCompany.PortfolioCompanyId);
            
            if (existingField != null)
            {
                existingField.FieldValue = field.FieldValue;
                existingField.ModifiedBy = userId;
                existingField.ModifiedOn = DateTime.Now;
                customFieldsToUpdate.Add(existingField);
            }
            else
            {
                customFieldsToCreate.Add(new PageConfigurationFieldValue
                {
                    FieldID = field.FieldId,
                    FieldValue = field.FieldValue,
                    PageID = (int)PageConfigurationFeature.PortfolioCompany,
                    SubPageID = (int)PageConfigurationSubFeature.StaticInformation,
                    PageFeatureId = portfolioCompany.PortfolioCompanyId,
                    CreatedBy = userId,
                    CreatedOn = DateTime.Now,
                    IsActive = true,
                    IsDeleted = false
                });
            }
        }
    }
}
