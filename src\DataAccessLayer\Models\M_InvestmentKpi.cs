﻿
using System.Collections.Generic;

namespace DataAccessLayer.DBModel
{
    public partial class M_InvestmentKpi : BaseModel
    {
        public int InvestmentKpiId { get; set; }
        public string Kpi { get; set; }
        public string KpiInfo { get; set; }
        public int OrderBy { get; set; }
        public string Description { get; set; }
        public bool IsDeleted { get; set; }
        public bool IsNumeric { get; set; }
        public string AnnualCalculationMethod { get; set; }
        public int? ParentKpiId { get; set; }
        public string EncryptedInvestmentKpiId { get; set; }
        public string Formula { get; set; }
        public string FormulaKPIId { get; set; }
        public int MethodologyId { get; set; }
        public bool IsBoldKPI { get; set; }
        public bool IsHeader { get; set; }
        public string Synonym { get; set; }
    }
}
