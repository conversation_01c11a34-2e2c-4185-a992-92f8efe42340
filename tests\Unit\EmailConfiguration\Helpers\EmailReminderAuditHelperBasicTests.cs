using EmailConfiguration.Helpers;
using DataAccessLayer.Models.EmailNotifications;
using Xunit;

namespace EmailConfiguration.UnitTest.Helpers
{
    /// <summary>
    /// Basic unit tests for EmailReminderAuditHelper that compile and run successfully
    /// </summary>
    public class EmailReminderAuditHelperBasicTests
    {
        [Fact]
        public void SetCreateAuditFields_ValidEntity_SetsCorrectFields()
        {
            // Arrange
            var entity = new TestEntity();
            var userId = 123;

            // Act
            EmailReminderAuditHelper.SetCreateAuditFields(entity, userId);

            // Assert
            Assert.Equal(userId, entity.CreatedBy);
            Assert.True(entity.CreatedOn <= DateTime.UtcNow);
            Assert.True(entity.CreatedOn > DateTime.UtcNow.AddMinutes(-1));
            Assert.False(entity.IsDeleted);
        }

        [Fact]
        public void SetUpdateAuditFields_ValidEntity_SetsCorrectFields()
        {
            // Arrange
            var entity = new TestEntity
            {
                CreatedBy = 100,
                CreatedOn = DateTime.UtcNow.AddDays(-1),
                IsDeleted = false
            };
            var userId = 123;

            // Act
            EmailReminderAuditHelper.SetUpdateAuditFields(entity, userId);

            // Assert
            Assert.Equal(userId, entity.ModifiedBy);
            Assert.True(entity.ModifiedOn <= DateTime.UtcNow);
            Assert.True(entity.ModifiedOn > DateTime.UtcNow.AddMinutes(-1));
            // Original fields should remain unchanged
            Assert.Equal(100, entity.CreatedBy);
            Assert.False(entity.IsDeleted);
        }

        [Fact]
        public void SetSoftDeleteAuditFields_ValidEntity_SetsCorrectFields()
        {
            // Arrange
            var entity = new TestEntity
            {
                CreatedBy = 100,
                CreatedOn = DateTime.UtcNow.AddDays(-1),
                IsDeleted = false
            };
            var userId = 123;

            // Act
            EmailReminderAuditHelper.SetSoftDeleteAuditFields(entity, userId);

            // Assert
            Assert.True(entity.IsDeleted);
            Assert.Equal(userId, entity.ModifiedBy);
            Assert.True(entity.ModifiedOn <= DateTime.UtcNow);
            Assert.True(entity.ModifiedOn > DateTime.UtcNow.AddMinutes(-1));
            // Original fields should remain unchanged
            Assert.Equal(100, entity.CreatedBy);
        }

        [Fact]
        public void CreateEmailReminderWithAudit_ValidParameters_ReturnsCorrectEntity()
        {
            // Arrange
            var featureId = 1;
            var entityIds = "1,2,3";
            var documentTypeIds = "10,20,30";
            var userId = 123;

            // Act
            var result = EmailReminderAuditHelper.CreateEmailReminderWithAudit(featureId, entityIds, documentTypeIds, userId);

            // Assert
            Assert.NotEqual(Guid.Empty, result.ReminderId);
            Assert.Equal(featureId, result.FeatureID);
            Assert.Equal(entityIds, result.EntityIDs);
            Assert.Equal(documentTypeIds, result.DocumentTypeIds);
            Assert.Equal(userId, result.CreatedBy);
            Assert.True(result.CreatedOn <= DateTime.UtcNow);
            Assert.True(result.CreatedOn > DateTime.UtcNow.AddMinutes(-1));
            Assert.False(result.IsDeleted);
        }

        [Fact]
        public void CreateEmailReminderConfigWithAudit_ValidParameters_ReturnsCorrectEntity()
        {
            // Arrange
            var reminderId = Guid.NewGuid();
            var frequencyType = 1;
            var totalRemindersPerCycle = 5;
            var remainder1Date = new DateTime(2024, 1, 1);
            var remainder2 = "5";
            var remainder3 = "10";
            var remainder4 = "15";
            var remainder5 = "20";
            var userId = 123;

            // Act
            var result = EmailReminderAuditHelper.CreateEmailReminderConfigWithAudit(
                reminderId, frequencyType, totalRemindersPerCycle, remainder1Date,
                remainder2, remainder3, remainder4, remainder5, userId);

            // Assert
            Assert.Equal(reminderId, result.ReminderId);
            Assert.Equal(frequencyType, result.FrequencyType);
            Assert.Equal(totalRemindersPerCycle, result.TotalRemindersPerCycle);
            Assert.Equal(remainder1Date, result.Remainder1Date);
            Assert.Equal(remainder2, result.Remainder2);
            Assert.Equal(remainder3, result.Remainder3);
            Assert.Equal(remainder4, result.Remainder4);
            Assert.Equal(remainder5, result.Remainder5);
            Assert.Equal(userId, result.CreatedBy);
            Assert.True(result.CreatedOn <= DateTime.UtcNow);
            Assert.True(result.CreatedOn > DateTime.UtcNow.AddMinutes(-1));
            Assert.False(result.IsDeleted);
        }

        [Fact]
        public void CreateEmailReminderScheduleWithAudit_ValidParameters_ReturnsCorrectEntity()
        {
            // Arrange
            var configId = 100;
            var reminderDate = new DateTime(2024, 1, 15);
            var scheduleOccurrence = 2;
            var userId = 123;

            // Act
            var result = EmailReminderAuditHelper.CreateEmailReminderScheduleWithAudit(
                configId, reminderDate, scheduleOccurrence, userId);

            // Assert
            Assert.Equal(configId, result.ReminderConfigId);
            Assert.Equal(reminderDate, result.ReminderDate);
            Assert.Equal(scheduleOccurrence, result.ScheduleOccurrence);
            Assert.Equal(ReminderStatus.Pending, result.Status); // 0=Pending
            Assert.True(result.IsActive);
            Assert.Equal(userId, result.CreatedBy);
            Assert.True(result.CreatedOn <= DateTime.UtcNow);
            Assert.True(result.CreatedOn > DateTime.UtcNow.AddMinutes(-1));
            Assert.False(result.IsDeleted);
        }

        [Fact]
        public void SetCreateAuditFields_EntityWithoutProperties_DoesNotThrow()
        {
            // Arrange
            var entity = new EmptyEntity();
            var userId = 123;

            // Act & Assert
            var exception = Record.Exception(() => EmailReminderAuditHelper.SetCreateAuditFields(entity, userId));
            Assert.Null(exception);
        }

        [Fact]
        public void SetUpdateAuditFields_EntityWithoutProperties_DoesNotThrow()
        {
            // Arrange
            var entity = new EmptyEntity();
            var userId = 123;

            // Act & Assert
            var exception = Record.Exception(() => EmailReminderAuditHelper.SetUpdateAuditFields(entity, userId));
            Assert.Null(exception);
        }

        [Fact]
        public void SetSoftDeleteAuditFields_EntityWithoutProperties_DoesNotThrow()
        {
            // Arrange
            var entity = new EmptyEntity();
            var userId = 123;

            // Act & Assert
            var exception = Record.Exception(() => EmailReminderAuditHelper.SetSoftDeleteAuditFields(entity, userId));
            Assert.Null(exception);
        }

        [Fact]
        public void CreateEmailReminderWithAudit_EmptyStrings_HandlesCorrectly()
        {
            // Arrange
            var featureId = 1;
            var entityIds = "";
            var documentTypeIds = "";
            var userId = 123;

            // Act
            var result = EmailReminderAuditHelper.CreateEmailReminderWithAudit(featureId, entityIds, documentTypeIds, userId);

            // Assert
            Assert.NotEqual(Guid.Empty, result.ReminderId);
            Assert.Equal(featureId, result.FeatureID);
            Assert.Equal(entityIds, result.EntityIDs);
            Assert.Equal(documentTypeIds, result.DocumentTypeIds);
            Assert.Equal(userId, result.CreatedBy);
            Assert.False(result.IsDeleted);
        }

        [Fact]
        public void CreateEmailReminderConfigWithAudit_EmptyStrings_HandlesCorrectly()
        {
            // Arrange
            var reminderId = Guid.NewGuid();
            var frequencyType = 1;
            var totalRemindersPerCycle = 1;
            var remainder1Date = new DateTime(2024, 1, 1);
            var remainder2 = "";
            var remainder3 = "";
            var remainder4 = "";
            var remainder5 = "";
            var userId = 123;

            // Act
            var result = EmailReminderAuditHelper.CreateEmailReminderConfigWithAudit(
                reminderId, frequencyType, totalRemindersPerCycle, remainder1Date,
                remainder2, remainder3, remainder4, remainder5, userId);

            // Assert
            Assert.Equal(reminderId, result.ReminderId);
            Assert.Equal(frequencyType, result.FrequencyType);
            Assert.Equal(totalRemindersPerCycle, result.TotalRemindersPerCycle);
            Assert.Equal(remainder1Date, result.Remainder1Date);
            Assert.Equal(remainder2, result.Remainder2);
            Assert.Equal(remainder3, result.Remainder3);
            Assert.Equal(remainder4, result.Remainder4);
            Assert.Equal(remainder5, result.Remainder5);
            Assert.Equal(userId, result.CreatedBy);
            Assert.False(result.IsDeleted);
        }

        private class TestEntity
        {
            public int CreatedBy { get; set; }
            public DateTime CreatedOn { get; set; }
            public int? ModifiedBy { get; set; }
            public DateTime? ModifiedOn { get; set; }
            public bool IsDeleted { get; set; }
        }

        private class EmptyEntity
        {
            // No properties
        }
    }
}
