﻿using Amazon.S3;
using Amazon.S3.Model;
using Contract.Documents;
using DapperRepository;
using DataAccessLayer.Models.DocumentCollection;
using DataAccessLayer.UnitOfWork;
using DocumentCollection.DTOs;
using DocumentCollection.Helpers;
using DocumentCollection.Interfaces;
using DocumentCollection.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using S3FileLayer;
using Shared;
using System.Collections.Concurrent;
using System.Net;


namespace DocumentCollection.Services
{
    public class DocumentRepositoryService : IDocumentRepositoryService
    {

        private readonly IAmazonS3 _amazonS3;
        private readonly S3ServiceConfiguration _settings;
        private readonly IRepositoryConfigurationService _repoConfigService;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<DocumentRepositoryService> _logger;
        private readonly IDapperGenericRepository _dapper;        
        private readonly HashSet<string> _allowedExtensions;
        private readonly long _maxFileSize;
        public DocumentRepositoryService(IAmazonS3 amazonS3, IOptions<S3ServiceConfiguration> settings, IRepositoryConfigurationService repoConfigService, IUnitOfWork unitOfWork, IDapperGenericRepository dapper, ILogger<DocumentRepositoryService> logger, IConfiguration configuration)

        {
            _amazonS3 = amazonS3;
            _settings = settings.Value;
            _repoConfigService = repoConfigService;
            _unitOfWork = unitOfWork;
            _logger = logger;

            _allowedExtensions = configuration.GetSection("AllowedFileExtensions").Get<HashSet<string>>() ?? new HashSet<string>();
            _maxFileSize = configuration.GetValue<long>("MaxFileSizeInMB") * 1024 * 1024;

            _dapper = dapper;

        }

        private readonly string _DocCollectionRootDirectory = "DocumentCollection";
        /// <summary>
        /// Retrieves the repository folder structure for a given company
        /// </summary>
        /// <param name="companyid">The ID of the company</param>
        /// <param name="featureId">The ID of the feature</param>
        /// <returns>A response containing a list of repository tree models representing the folder structure</returns>
        public async Task<ResponseDto<List<RepositoryTreeModel>>> GetRepositoryData(int companyid, int featureId)
        {
            var response = new ResponseDto<List<RepositoryTreeModel>> { IsSuccess = true };
            try
            {
                IEnumerable<DocumentConfigurationDto> configData = await _repoConfigService.GetRepositoryConfigurationDataByCompany(companyid);

                List<RepositoryTreeModel> folderData = new List<RepositoryTreeModel>();

                DocumentServiceHelper.FillDocumentTypeFolders(configData, folderData);

                DocumentServiceHelper.FillAnnualTypeFolders(configData, folderData);

                DocumentServiceHelper.FillQuarterTypeFolders(configData, folderData);

                DocumentServiceHelper.FillMonthTypeFolders(configData, folderData);

                List<FolderAndDocumentModel> items = await _dapper.Query<FolderAndDocumentModel>(QueryConstants.QueryDocumentswithFolders, new { CompanyId = companyid });
                List<DocumentTypes> documentTypes = await _repoConfigService.GetDocumentTypes(featureId);

                DocumentServiceHelper.FillUnConfiguredFolders(documentTypes, configData, folderData, items);

                response.Data = folderData;

            }
            catch (Exception ex)
            {
                response.IsSuccess = false;
                response.Message = $"Failed to get document configurations: {ex.Message}";
            }
            return response;
        }
        /// <summary>
        /// Uploads documents to the specified folder path for a given company
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="uploadRequest"></param>
        /// <returns></returns>
        public async Task<ResponseDto<string>> UploadDocuments(int companyId, DocumentUploadRequestDto uploadRequest)
        {
            bool isProcessSuccess = false;
            if (uploadRequest.FolderPath == null || uploadRequest.FolderPath == Constants.UnConfigured)
            {
                return new ResponseDto<string> { IsSuccess = false, Message = "Invalid folder path" };
            }
            Guid mappingId = await UpdateDocumentMappingDetails(companyId, uploadRequest);
            if (mappingId != Guid.Empty)
            {
                var uploadSuccess = await UploadDocumentstoS3ServiceAsync(mappingId, uploadRequest);
                if (uploadSuccess.Item1)
                {
                    isProcessSuccess = await UpdateDocumentStoreDetails(uploadSuccess.Item2);
                }
            }
            return isProcessSuccess ? new ResponseDto<string> { IsSuccess = true, Message = "Documents uploaded successfully" } :
                 new ResponseDto<string> { IsSuccess = false, Message = "Failed to upload documents" };
        }

        /// <summary>
        /// Updates the document store details in the database with the provided items.
        /// </summary>
        /// <param name="itemsStore">List of document store items to add.</param>
        /// <returns>True if update is successful, otherwise false.</returns>
        public async Task<Boolean> UpdateDocumentStoreDetails(List<DocumentCollectionStore> itemsStore)
        {
            try
            {
                if (itemsStore == null || itemsStore.Count == 0)
                {
                    return false;
                }
                await _unitOfWork.DocumentCollectionRepository.AddBulkAsyn(itemsStore);
                await _unitOfWork.SaveAsync();
            }
            catch (Exception e)
            {
                _logger.LogError($"Error updating document store details: {e.Message}");
                return false;
            }
            return true;
        }

        /// <summary>
        /// Uploads documents to AWS S3 and returns the upload status and document store list.
        /// </summary>
        /// <param name="mappingid">The folder mapping ID.</param>
        /// <param name="uploadRequest">The document upload request DTO.</param>
        /// <returns>Tuple indicating success and list of document store items.</returns>
        public async Task<(Boolean, List<DocumentCollectionStore>)> UploadDocumentstoS3ServiceAsync(Guid mappingid, DocumentUploadRequestDto uploadRequest)
        {
            ConcurrentDictionary<string, DocumentCollectionStore> docUploadStatus = new ConcurrentDictionary<string, DocumentCollectionStore>();
            foreach (IFormFile item in uploadRequest.Files)
            {
                try
                {
                    using var inputStream = new MemoryStream();
                    await item.CopyToAsync(inputStream);
                    Guid newGuid = Guid.NewGuid();
                    string extension = Path.GetExtension(item.FileName);
                    string fileid = string.Format($"{newGuid}{extension}");
                    PutObjectRequest request = new()
                    {
                        InputStream = inputStream,
                        BucketName = _settings.AWSS3.BucketName,
                        Key = GenerateKey(fileid),
                    };
                    PutObjectResponse response = await _amazonS3.PutObjectAsync(request);
                    if (response.HttpStatusCode == System.Net.HttpStatusCode.OK)
                    {
                        _logger.LogInformation("File inserted successfully");
                        docUploadStatus.TryAdd(item.FileName, new DocumentCollectionStore()
                        {
                            ID = newGuid,
                            FileName = item.FileName,
                            S3Path = GenerateKey(fileid),
                            Type = extension,
                            SourceTypeId = 1,
                            FolderMappingId = mappingid,
                            UploadType = (int)UploadType.Manual
                        });
                    }
                    else
                    {
                        _logger.LogError($"Error uploading file {item.FileName}: {response.HttpStatusCode}");
                        docUploadStatus.TryAdd(item.FileName, null);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError($"Error uploading file {item.FileName}: {ex.Message}");
                }
            }
            return ((docUploadStatus.Count == uploadRequest.Files.Count && docUploadStatus.All(x => x.Value != null)), docUploadStatus.Select(i => i.Value).ToList());

        }

        /// <summary>
        /// Generates the S3 key for a given file ID.
        /// </summary>
        /// <param name="fileid">The file identifier.</param>
        /// <returns>The S3 key string.</returns>
        private string GenerateKey(string fileid)
        {
            return $"{_settings.AWSS3.KeyPrefix}{_DocCollectionRootDirectory}/{fileid}";
        }

        /// <summary>
        /// Updates or inserts document mapping details for a company and folder path.
        /// </summary>
        /// <param name="companyId">The company ID.</param>
        /// <param name="uploadRequest">The document upload request DTO.</param>
        /// <returns>The mapping ID as a Guid.</returns>
        public async Task<Guid> UpdateDocumentMappingDetails(int companyId, DocumentUploadRequestDto uploadRequest)
        {
            try
            {
                var repoMappingDetails = new RepositoryDocumentMappingDetail()
                {
                    FeatureId = uploadRequest.FeatureID,
                    EntityId = companyId,
                    DocTypeID = DocumentServiceHelper.GetDocTypeFromFolderPath(uploadRequest.FolderPath),
                    Year = DocumentServiceHelper.GetYearFolderPath(uploadRequest.FolderPath),
                    Quarter = DocumentServiceHelper.GetQuarterFolderPath(uploadRequest.FolderPath),
                    Month = DocumentServiceHelper.GetMonthFolderPath(uploadRequest.FolderPath),
                    CreatedBy = uploadRequest.CreatedBy,
                    CreatedOn = DateTime.UtcNow,
                    IsDeleted = false
                };

                var existingMapping = await _unitOfWork.DocumentMappingRepository.FindAllAsync(d =>
                d.FeatureId == uploadRequest.FeatureID &&
                d.EntityId == companyId &&
                !d.IsDeleted &&
                d.DocTypeID == repoMappingDetails.DocTypeID &&
                d.Year == repoMappingDetails.Year &&
                d.Quarter == repoMappingDetails.Quarter &&
                d.Month == repoMappingDetails.Month
                );
                if (existingMapping?.FirstOrDefault() != null)
                {
                    repoMappingDetails.ID = existingMapping.FirstOrDefault()!.ID;
                }
                else
                {
                    _unitOfWork.DocumentMappingRepository.Insert(repoMappingDetails);
                    await _unitOfWork.SaveAsync();
                }
                return repoMappingDetails.ID;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error updating document mapping details: {ex.Message}");
                return Guid.Empty;
            }
        }

        /// <summary>
        /// Retrieves documents for a given company and folder path.
        /// </summary>
        /// <param name="companyId">The company ID.</param>
        /// <param name="folderPath">The folder path.</param>
        /// <param name="featureId">The folder path.</param>
        /// <returns>Response containing a list of document info DTOs.</returns>
        public async Task<ResponseDto<List<DocumentsInfoDto>>> GetDocuments(int companyId, string folderPath, int featureId)
        {
            try
            {
                if (folderPath == null || folderPath == Constants.UnConfigured)
                {
                    return new ResponseDto<List<DocumentsInfoDto>>() { IsSuccess = true, Data = new List<DocumentsInfoDto>() };
                }
                var existingMapping = await _unitOfWork.DocumentMappingRepository.FindAllAsync(d =>
                    d.FeatureId == featureId &&
                    d.EntityId == companyId &&
                    !d.IsDeleted &&
                    d.DocTypeID == DocumentServiceHelper.GetDocTypeFromFolderPath(folderPath) &&
                    d.Year == DocumentServiceHelper.GetYearFolderPath(folderPath) &&
                    d.Quarter == DocumentServiceHelper.GetQuarterFolderPath(folderPath) &&
                    d.Month == DocumentServiceHelper.GetMonthFolderPath(folderPath)
                );

                var folderMapping = existingMapping?.FirstOrDefault();
                if (folderMapping != null)
                {
                    var documents = await _unitOfWork.DocumentCollectionRepository.FindAllAsync(d => d.FolderMappingId == folderMapping.ID && !d.IsDeleted);
                    var documentList = documents.Select(d => new DocumentsInfoDto
                    {
                        DocumentId = d.ID.ToString(),
                        DocumentName = d.FileName,
                        DocumentS3Path = d.S3Path,
                        DocumentType = d.Type,
                        DocumentCreatedDate = d.CreatedOn,
                        DocumentUploadType = GetUploadType(d)

                    }).ToList();
                    return new ResponseDto<List<DocumentsInfoDto>>() { IsSuccess = true, Data = documentList };
                }
                else
                {
                    return new ResponseDto<List<DocumentsInfoDto>>() { IsSuccess = true, Data = new List<DocumentsInfoDto>(), Message = "No documents found for the specified folder." };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error retrieving documents: {ex.Message}");
                return new ResponseDto<List<DocumentsInfoDto>>() { IsSuccess = false, Message = $"Error retrieving documents: {ex.Message}" };
            }
        }

        /// <summary>
        /// Gets the upload type as a string for a document collection store item.
        /// </summary>
        /// <param name="d">The document collection store item.</param>
        /// <returns>The upload type as a string.</returns>
        private static string GetUploadType(DocumentCollectionStore d)
        {
            switch (d.UploadType)
            {
                case (int)UploadType.Manual:
                    return "Manual";
                case (int)UploadType.DataIngestion:
                    return "Data Ingestion";
                default:
                    return "NA";
            }
        }

        /// <summary>
        /// Validates the uploaded files for allowed extensions and file size.
        /// </summary>
        /// <param name="uploadRequest">The document upload request DTO.</param>
        /// <returns>Empty string if valid, otherwise an error message.</returns>
        public string ValidateUploadedFiles(DocumentUploadRequestDto uploadRequest)
        {

            foreach (var file in uploadRequest.Files)
            {
                var fileExtension = Path.GetExtension(file.FileName).ToLower();
                if (!_allowedExtensions.Contains(fileExtension))
                {
                    return $"File '{file.FileName}' does not have a valid format. Allowed formats are: {string.Join(", ", _allowedExtensions)}.";
                }

                if (file.Length > _maxFileSize)
                {
                    return $"File '{file.FileName}' exceeds the maximum allowed size of 20 MB.";
                }
            }

            return string.Empty;
        }


        /// <summary>
        /// Deletes documents from the S3 bucket and marks them as deleted in the database
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="deletRequest"></param>
        /// <returns></returns>

        public async Task<ResponseDto<string>> DeleteDocuments(int companyId, DocumentDeleteRequestDto deletRequest)
        {
            try
            {
                // Convert string IDs to Guid
                var documentGuids = deletRequest.DocumentIds.Select(id => Guid.Parse(id)).ToList();

                // Find documents that match the IDs in the delete request
                var documentsToDelete = await _unitOfWork.DocumentCollectionRepository.FindAllAsync(d =>
                    documentGuids.Contains(d.ID));
                if (!documentsToDelete.Any())
                {
                    return new ResponseDto<string>() { IsSuccess = false, Message = "No matching documents found to delete" };
                }
                foreach (var doc in documentsToDelete)
                {
                    try
                    {
                        string fileid = string.Format($"{doc.ID}{"."}{doc.Type}");
                        string key = GenerateKey(fileid);
                        DeleteObjectResponse response = await _amazonS3.DeleteObjectAsync(_settings.AWSS3.BucketName, key);
                        if (response.HttpStatusCode == HttpStatusCode.NoContent)
                        {
                            _logger.LogInformation("{Source} delete success", key);
                        }
                        else
                            _logger.LogInformation("{Source} delete failed:{StatusCode}", key, response.HttpStatusCode);
                    }
                    catch (Exception e)
                    {
                        _logger.LogError($"Error deleting file from S3: {e.Message}");
                    }
                    doc.IsDeleted = true;
                    doc.ModifiedBy = deletRequest.ModifiedBy;
                    doc.ModifiedOn = DateTime.UtcNow;
                }
                _unitOfWork.DocumentCollectionRepository.UpdateBulk(documentsToDelete);
                await _unitOfWork.SaveAsync();

                _logger.LogInformation($"Successfully deleted {documentsToDelete.Count()} documents");
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error deleting documents: {ex.Message}");
                return new ResponseDto<string>() { IsSuccess = false, Message = $"Error deleting documents: {ex.Message}" };
            }

            return new ResponseDto<string>() { IsSuccess = true, Message = "Document Deleted Successfully" };
        }


        public async Task<DownloadDocumentResult> DownloadDocument(int companyId, string documentId, string folderPath)
        {
            try
            {
                // Find the document in the database
                if (!Guid.TryParse(documentId, out var docGuid))
                    return new DownloadDocumentResult { IsSuccess = false };

                var document = (await _unitOfWork.DocumentCollectionRepository.FindAllAsync(d => d.ID == docGuid && !d.IsDeleted)).FirstOrDefault();
                if (document == null)
                    return new DownloadDocumentResult { IsSuccess = false };

                // Build S3 key
                string fileid = $"{document.ID}{document.Type}";
                string key = GenerateKey(fileid);

                // Get file from S3
                var response = await _amazonS3.GetObjectAsync(_settings.AWSS3.BucketName, key);
                if (response.ResponseStream == null)
                    return new DownloadDocumentResult { IsSuccess = false };

                // Set content type and file name
                string contentType = response.Headers.ContentType ?? "application/octet-stream";
                string fileName = document.FileName ?? $"document{document.Type}";

                // Copy stream to memory to avoid S3 stream disposal issues
                var memoryStream = new MemoryStream();
                await response.ResponseStream.CopyToAsync(memoryStream);
                memoryStream.Position = 0;

                return new DownloadDocumentResult
                {
                    IsSuccess = true,
                    FileStream = memoryStream,
                    FileName = fileName,
                    ContentType = contentType
                };
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error downloading document: {ex.Message}");
                return new DownloadDocumentResult { IsSuccess = false };
            }
        }
    }
}