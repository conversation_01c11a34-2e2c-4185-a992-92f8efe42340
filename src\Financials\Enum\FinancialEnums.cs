﻿using System.ComponentModel;

namespace Financials
{
    public enum Methodology
    {
        [Description("Daily average")]
        DailyAverage = 1,
        [Description("As on latest date")]
        LatestDate = 3,
        [Description("As on day before start date")]
        DayBeforeStartDate = 4,
        [Description("Quarterly average")]
        Average=5
    }

    public enum ValueTypes
    {
        [Description("Monthly")]
        Monthly,
        [Description("Annual")]
        Annual,
        [Description("Quarter")]
        Quarter,
        [Description("Half Yearly")]
        HalfYear,
    }
    public enum KpiModuleType
    {
        [Description("TradingRecords")]
        TradingRecords = 1,
        [Description("CreditKPI")]
        CreditKPI = 2,
        [Description("Operational")]
        Operational = 3,
        [Description("Investment")]
        Investment = 4,
        [Description("Company")]
        Company = 5,
        [Description("Impact")]
        Impact = 6,
        [Description("ProfitAndLoss")]
        ProfitAndLoss = 7,
        [Description("BalanceSheet")]
        BalanceSheet = 8,
        [Description("CashFlow")]
        CashFlow = 9,
        [Description("ESG")]
        ESG = 10,
        [Description("CustomTable1")]
        CustomTable1 = 17,
        [Description("CustomTable2")]
        CustomTable2 = 18,
        [Description("CustomTable3")]
        CustomTable3 = 19,
        [Description("CustomTable4")]
        CustomTable4 = 20,
        [Description("OtherKPI1")]
        OtherKPI1 = 21,
        [Description("OtherKPI2")]
        OtherKPI2 = 22,
        [Description("OtherKPI3")]
        OtherKPI3 = 23,
        [Description("OtherKPI4")]
        OtherKPI4 = 24,
        [Description("OtherKPI5")]
        OtherKPI5 = 25,
        [Description("OtherKPI6")]
        OtherKPI6 = 26,
        [Description("OtherKPI7")]
        OtherKPI7 = 27,
        [Description("OtherKPI8")]
        OtherKPI8 = 28,
        [Description("OtherKPI9")]
        OtherKPI9 = 29,
        [Description("OtherKPI10")]
        OtherKPI10 = 30
    }
    public enum ValueTypeFeature
    {
        [Description("Absolute")]
        Absolute,
        [Description("Thousands")]
        Thousands,
        [Description("Millions")]
        Millions,
        [Description("Billions")]
        Billions
    }

    public enum FundFinancials
    {
        [Description("FundFinancials")]
        FundFinancials = 1001,
    }
    public enum StaticInformation
    {
        [Description("Static Information")]
        StaticInformation = 1000,
    }

}
