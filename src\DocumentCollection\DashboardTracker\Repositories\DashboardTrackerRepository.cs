using System;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using DocumentCollection.DashboardTracker.Interfaces;
using DocumentCollection.DashboardTracker.Models;
using Microsoft.Extensions.Configuration;

namespace DocumentCollection.DashboardTracker.Repositories
{
    public class DashboardTrackerRepository : IDashboardTrackerRepository
    {
        private readonly string _connectionString;
        public DashboardTrackerRepository(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection");
        }

        public async Task<int> SaveDashboardTrackerConfigAsync(DashboardTrackerConfigDto dto)
        {
            using (var conn = new SqlConnection(_connectionString))
            using (var cmd = new SqlCommand("sp_SaveDashboardTrackerConfig", conn))
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddWithValue("@ID", (object)dto.ID ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@FieldType", dto.FieldType);
                cmd.Parameters.AddWithValue("@DataType", dto.DataType);
                cmd.Parameters.AddWithValue("@Name", dto.Name);
                cmd.Parameters.AddWithValue("@FrequencyType", (object)dto.FrequencyType ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@StartPeriod", (object)dto.StartPeriod ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@EndPeriod", (object)dto.EndPeriod ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@MapTo", (object)dto.MapTo ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@IsActive", dto.IsActive);
                cmd.Parameters.AddWithValue("@IsDeleted", dto.IsDeleted);
                cmd.Parameters.AddWithValue("@CreatedBy", (object)dto.CreatedBy ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@ModifiedBy", (object)dto.ModifiedBy ?? DBNull.Value);
                await conn.OpenAsync();
                var result = await cmd.ExecuteScalarAsync();
                return Convert.ToInt32(result);
            }
        }
    }
}
