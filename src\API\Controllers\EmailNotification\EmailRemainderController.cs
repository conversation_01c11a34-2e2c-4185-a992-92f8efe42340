using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using API.Helpers;
using System;
using System.Collections.Generic;
using EmailConfiguration.Interfaces;
using EmailConfiguration.DTOs;
using API.Filters.CustomAuthorization;
using Contract.Account;
using Microsoft.Extensions.Logging;

namespace API.Controllers.EmailNotification
{
    [Route("api")]
    [Authorize(Policy = AppSettings.DefaultAuthPolicy)]
    [ApiController]
    public class EmailRemainderController : ControllerBase
    {
        private readonly IEmailRemainderService _emailRemainderService;
        private readonly IHelperService _helperService;
        private readonly ILogger<EmailRemainderController> _logger;

        public EmailRemainderController(
            IEmailRemainderService emailRemainderService,
            IHelperService helperService,
            ILogger<EmailRemainderController> logger)
        {
            _emailRemainderService = emailRemainderService ?? throw new ArgumentNullException(nameof(emailRemainderService));
            _helperService = helperService ?? throw new ArgumentNullException(nameof(helperService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Creates a new email reminder
        /// </summary>
        /// <param name="dto">Email reminder creation data</param>
        /// <returns>The created reminder ID</returns>
        [HttpPost("email-reminders/create")]
        [UserFeatureAuthorize((int)Features.RepositoryConfiguration)]
        public async Task<IActionResult> CreateEmailRemainder([FromBody] CreateEmailRemainderDto dto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    _logger.LogWarning("Invalid model state for email reminder creation");
                    return BadRequest(ModelState);
                }
                dto.FeatureID = (int)Features.PortfolioCompany;
                var userId = _helperService.GetCurrentUserId(User);
                _logger.LogInformation("Creating email reminder for feature {FeatureId} by user {UserId}", dto.FeatureID, userId);

                var reminderId = await _emailRemainderService.CreateRemainderAsync(dto, userId);

                _logger.LogInformation("Successfully created email reminder with ID {ReminderId}", reminderId);
                return Ok(new { ReminderId = reminderId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating email reminder");
                return StatusCode(500, "An error occurred while creating the email reminder");
            }
        }

        /// <summary>
        /// Gets an email reminder by ID
        /// </summary>
        /// <param name="reminderId">The reminder ID</param>
        /// <returns>Email reminder details</returns>
        [HttpGet("email-reminders/{reminderId}")]
        [UserFeatureAuthorize((int)Features.RepositoryConfiguration)]
        public async Task<IActionResult> GetEmailRemainder(Guid reminderId)
        {
            try
            {
                _logger.LogInformation("Retrieving email reminder with ID {ReminderId}", reminderId);

                var reminder = await _emailRemainderService.GetRemainderDefaultsByIdAsync(reminderId);

                if (reminder == null)
                {
                    _logger.LogWarning("Email reminder with ID {ReminderId} not found", reminderId);
                    return NotFound($"Email reminder with ID {reminderId} not found");
                }

                return Ok(reminder);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving email reminder with ID {ReminderId}", reminderId);
                return StatusCode(500, "An error occurred while retrieving the email reminder");
            }
        }

        /// <summary>
        /// Updates an existing email reminder
        /// </summary>
        /// <param name="reminderId">The reminder ID</param>
        /// <param name="dto">Updated reminder data</param>
        /// <returns>Success status</returns>
        [HttpPost("update-email-reminders")]
        [UserFeatureAuthorize((int)Features.RepositoryConfiguration)]
        public async Task<IActionResult> UpdateEmailRemainder([FromBody] UpdateEmailRemainderDto dto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    _logger.LogWarning("Invalid model state for email reminder update");
                    return BadRequest(ModelState);
                }
                dto.FeatureID = (int)Features.PortfolioCompany;
                var userId = _helperService.GetCurrentUserId(User);
                _logger.LogInformation("Updating email reminder {ReminderID} by user {UserId}", dto.ReminderID, userId);

                var success = await _emailRemainderService.UpdateRemainderAsync(dto.ReminderID, dto, userId);

                if (!success)
                {
                    _logger.LogWarning("Email reminder with ID {ReminderID} not found for update", dto.ReminderID);
                    return NotFound($"Email reminder with ID {dto.ReminderID} not found");
                }

                _logger.LogInformation("Successfully updated email reminder {ReminderId}", dto.ReminderID);
                return Ok(new { Message = "Email reminder updated successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating email reminder {ReminderID}", dto.ReminderID);
                return StatusCode(500, "An error occurred while updating the email reminder");
            }
        }

        /// <summary>
        /// Deletes an email reminder
        /// </summary>
        /// <param name="reminderId">The reminder ID</param>
        /// <returns>Success status</returns>
        [HttpDelete("email-reminders/{reminderId}")]
        [UserFeatureAuthorize((int)Features.RepositoryConfiguration)]
        public async Task<IActionResult> DeleteEmailRemainder(Guid reminderId)
        {
            try
            {
                var userId = _helperService.GetCurrentUserId(User);
                _logger.LogInformation("Deleting email reminder {ReminderId} by user {UserId}", reminderId, userId);

                var success = await _emailRemainderService.DeleteRemainderAsync(reminderId, userId);

                if (!success)
                {
                    _logger.LogWarning("Email reminder with ID {ReminderId} not found for deletion", reminderId);
                    return NotFound($"Email reminder with ID {reminderId} not found");
                }

                _logger.LogInformation("Successfully deleted email reminder {ReminderId}", reminderId);
                return Ok(new { Message = "Email reminder deleted successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting email reminder {ReminderId}", reminderId);
                return StatusCode(500, "An error occurred while deleting the email reminder");
            }
        }

        /// <summary>
        /// Gets a list of all email reminders with their details
        /// </summary>
        /// <returns>List of email reminders</returns>
        [HttpGet("email-reminders")]
        [UserFeatureAuthorize((int)Features.RepositoryConfiguration)]
        public async Task<IActionResult> GetEmailReminderList()
        {
            try
            {
                _logger.LogInformation("Retrieving list of all email reminders");

                var reminders = await _emailRemainderService.GetEmailReminderListAsync();

                _logger.LogInformation("Successfully retrieved {Count} email reminders", reminders.Count);
                return Ok(reminders);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving email reminder list");
                return StatusCode(500, "An error occurred while retrieving the email reminder list");
            }
        }

        /// <summary>
        /// Gets email reminder details including TO, CC recipients, Subject and MessageBody
        /// </summary>
        /// <param name="reminderId">The reminder ID</param>
        /// <returns>Email reminder details with recipients</returns>
        [HttpGet("email-reminders/{reminderId}/details")]
        [UserFeatureAuthorize((int)Features.RepositoryConfiguration)]
        public async Task<IActionResult> GetEmailReminderDetails(Guid reminderId)
        {
            try
            {
                _logger.LogInformation("Retrieving email reminder details for ID {ReminderId}", reminderId);

                var reminderDetails = await _emailRemainderService.GetEmailReminderDetailsAsync(reminderId);

                if (reminderDetails == null)
                {
                    _logger.LogWarning("Email reminder with ID {ReminderId} not found", reminderId);
                    return NotFound($"Email reminder with ID {reminderId} not found");
                }

                _logger.LogInformation("Successfully retrieved email reminder details for ID {ReminderId}", reminderId);
                return Ok(reminderDetails);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving email reminder details for ID {ReminderId}", reminderId);
                return StatusCode(500, "An error occurred while retrieving the email reminder details");
            }
        }

        /// <summary>
        /// Gets email reminder details including TO, CC recipients, Subject and MessageBody
        /// </summary>
        /// <param name="reminderId">The reminder ID</param>
        /// <returns>Email reminder details with recipients</returns>
        [HttpGet("email-reminders/edit/{reminderId}")]
        [UserFeatureAuthorize((int)Features.RepositoryConfiguration)]
        public async Task<IActionResult> GetEmailReminderDetailsForEdit(Guid reminderId)
        {
            try
            {
                _logger.LogInformation("Retrieving email reminder details for ID {ReminderId}", reminderId);

                var reminderDetails = await _emailRemainderService.GetEmailRemainderDetailsForEditAsync(reminderId);

                if (reminderDetails == null)
                {
                    _logger.LogWarning("Email reminder with ID {ReminderId} not found", reminderId);
                    return NotFound($"Email reminder with ID {reminderId} not found");
                }

                _logger.LogInformation("Successfully retrieved email reminder details for ID {ReminderId}", reminderId);
                return Ok(reminderDetails);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving email reminder details for ID {ReminderId}", reminderId);
                return StatusCode(500, "An error occurred while retrieving the email reminder details");
            }
        }

        /// <summary>
        /// Skips pending reminders in current cycle and creates next cycle schedules
        /// </summary>
        /// <param name="dto">Skip reminder cycle data</param>
        /// <returns>Success status</returns>
        [HttpPost("email-reminders/skip-cycle")]
        [UserFeatureAuthorize((int)Features.RepositoryConfiguration)]
        public async Task<IActionResult> SkipReminderCycle([FromBody] SkipReminderCycleDto dto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    _logger.LogWarning("Invalid model state for skip reminder cycle");
                    return BadRequest(ModelState);
                }

                var userId = _helperService.GetCurrentUserId(User);
                _logger.LogInformation("Skipping reminder cycle for reminder {ReminderId} by user {UserId}", dto.ReminderId, userId);

                var success = await _emailRemainderService.SkipReminderCycleAsync(dto.ReminderId, userId);

                if (!success)
                {
                    _logger.LogWarning("Failed to skip reminder cycle for reminder {ReminderId}", dto.ReminderId);
                    return BadRequest("Failed to skip reminder cycle. No pending reminders found or reminder not found.");
                }

                _logger.LogInformation("Successfully skipped reminder cycle for reminder {ReminderId}", dto.ReminderId);
                return Ok(new { Message = "Reminder cycle skipped successfully and next cycle created" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error skipping reminder cycle for reminder {ReminderId}", dto.ReminderId);
                return StatusCode(500, "An error occurred while skipping the reminder cycle");
            }
        }
    }
}
