﻿using API.Filters.CustomAuthorization;
using API.Helpers;
using Contract.Account;
using EmailConfiguration.DTOs;
using EmailConfiguration.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace API.Controllers.EmailNotification
{
    [Route("api")]
    [Authorize(Policy = AppSettings.DefaultAuthPolicy)]
    [ApiController]
    public class EmailGroupController : ControllerBase
    {
        private readonly IEmailGroupService _emailGroupService;
        private readonly IHelperService _helperService;

        public EmailGroupController(IEmailGroupService emailGroupService, IHelperService helperService)
        {
            _emailGroupService = emailGroupService ?? throw new ArgumentNullException(nameof(emailGroupService));
            _helperService = helperService ?? throw new ArgumentNullException(nameof(helperService));
        }

        /// <summary>
        /// Retrieves all email groups
        /// </summary>
        [HttpPost("email-groups/create")]
        [UserFeatureAuthorize((int)Features.RepositoryConfiguration)]

        public async Task<IActionResult> CreateEmailGroup([FromBody] EmailGroupCreateDto dto)
        {
            if (!ModelState.IsValid) return BadRequest(ModelState);

            var userId = _helperService.GetCurrentUserId(User);
            var groupId = await _emailGroupService.CreateEmailGroupAsync(dto, userId);

            if (groupId == null) return BadRequest("Failed to create group");
            return Ok(new { groupId });
        }

        [HttpGet("email-groups/all")]
        [UserFeatureAuthorize((int)Features.RepositoryConfiguration)]

        public async Task<IActionResult> GetEmailGroups()
        {
            var groups = await _emailGroupService.GetEmailGroupsWithCountAsync();
            if (groups == null || !groups.Any())
                return NoContent();
            return Ok(groups);

        }
        [HttpGet("email-groups/{groupId}/members")]
        [UserFeatureAuthorize((int)Features.RepositoryConfiguration)]

        public async Task<IActionResult> GetEmailListByGroupId(int groupId)
        {
            var members = await _emailGroupService.GetEmailMembersByGroupIdAsync(groupId);
            if (members == null || !members.Any())
                return NoContent();
            return Ok(members);
        }

        /// <summary>
        /// Deletes multiple email members from an email group with soft delete functionality
        /// </summary>
        /// <param name="groupId">The ID of the group</param>
        /// <param name="dto">The DTO containing member IDs to delete</param>
        /// <returns>Success status with details of deleted and not found members</returns>
        [HttpDelete("email-groups/{groupId}/members")]
        [UserFeatureAuthorize((int)Features.RepositoryConfiguration)]
        public async Task<IActionResult> DeleteEmailMembers(int groupId, [FromBody] DeleteEmailMembersDto dto)
        {
            try
            {
                if (groupId <= 0)
                {
                    return BadRequest("Invalid group ID");
                }

                if (dto == null || dto.MemberIds == null || !dto.MemberIds.Any())
                {
                    return BadRequest("No member IDs provided for deletion");
                }

                var userId = _helperService.GetCurrentUserId(User);
                var result = await _emailGroupService.DeleteEmailMembersAsync(groupId, dto.MemberIds, userId);

                if (!result.Success)
                {
                    return NotFound($"Email group with ID {groupId} not found");
                }

                var response = new
                {
                    message = $"Bulk delete operation completed. {result.DeletedMemberIds.Count} members deleted successfully.",
                    deletedMemberIds = result.DeletedMemberIds,
                    notFoundMemberIds = result.NotFoundMemberIds,
                    totalRequested = dto.MemberIds.Count,
                    totalDeleted = result.DeletedMemberIds.Count,
                    totalNotFound = result.NotFoundMemberIds.Count
                };

                // Return 207 Multi-Status if some members were not found, otherwise 200 OK
                if (result.NotFoundMemberIds.Any())
                {
                    return StatusCode(207, response); // 207 Multi-Status for partial success
                }

                return Ok(response);
            }
            catch (Exception)
            {
                return StatusCode(500, new { message = "An error occurred while deleting the email members" });
            }
        }

        /// <summary>
        /// Deletes a specific email member from an email group with soft delete functionality
        /// </summary>
        /// <param name="groupId">The ID of the group</param>
        /// <param name="memberId">The ID of the member to delete</param>
        /// <returns>Success status</returns>
        [HttpDelete("email-groups/{groupId}/members/{memberId}")]
        [UserFeatureAuthorize((int)Features.RepositoryConfiguration)]
        public async Task<IActionResult> DeleteEmailMember(int groupId, int memberId)
        {
            try
            {
                if (groupId <= 0)
                {
                    return BadRequest("Invalid group ID");
                }

                if (memberId <= 0)
                {
                    return BadRequest("Invalid member ID");
                }

                var userId = _helperService.GetCurrentUserId(User);
                var success = await _emailGroupService.DeleteEmailMemberAsync(groupId, memberId, userId);

                if (!success)
                {
                    return NotFound($"Email member with ID {memberId} not found in group {groupId}");
                }

                return Ok(new { message = "Email member deleted successfully" });
            }
            catch (Exception)
            {
                return StatusCode(500, new { message = "An error occurred while deleting the email member" });
            }
        }

        /// <summary>
        /// Gets complete email group details for editing
        /// </summary>
        /// <param name="groupId">The ID of the group to retrieve</param>
        /// <returns>Complete group details including members and company associations</returns>
        [HttpGet("email-groups/{groupId}/detail")]
        [UserFeatureAuthorize((int)Features.RepositoryConfiguration)]
        public async Task<IActionResult> GetEmailGroupDetail(int groupId)
        {
            try
            {
                if (groupId <= 0)
                {
                    return BadRequest("Invalid group ID");
                }

                var groupDetail = await _emailGroupService.GetEmailGroupDetailAsync(groupId);

                if (groupDetail == null)
                {
                    return NotFound($"Email group with ID {groupId} not found");
                }

                return Ok(groupDetail);
            }
            catch (Exception)
            {
                return StatusCode(500, new { message = "An error occurred while retrieving the email group details" });
            }
        }

        /// <summary>
        /// Updates an email group with new information
        /// </summary>
        /// <param name="groupId">The ID of the group to update</param>
        /// <param name="dto">The update data</param>
        /// <returns>Success status</returns>
        [HttpPut("email-groups/{groupId}")]
        [UserFeatureAuthorize((int)Features.RepositoryConfiguration)]
        public async Task<IActionResult> UpdateEmailGroup(int groupId, [FromBody] EmailGroupUpdateDto dto)
        {
            try
            {
                if (groupId <= 0)
                {
                    return BadRequest("Invalid group ID");
                }

                if (dto == null)
                {
                    return BadRequest("Update data is required");
                }

                if (string.IsNullOrWhiteSpace(dto.GroupName))
                {
                    return BadRequest("Group name is required");
                }

                var userId = _helperService.GetCurrentUserId(User);
                var success = await _emailGroupService.UpdateEmailGroupAsync(groupId, dto, userId);

                if (!success)
                {
                    return NotFound($"Email group with ID {groupId} not found");
                }

                return Ok(new { message = "Email group updated successfully" });
            }
            catch (Exception)
            {
                return StatusCode(500, new { message = "An error occurred while updating the email group" });
            }
        }

        /// <summary>
        /// Deletes an email group with soft delete functionality
        /// </summary>
        /// <param name="groupId">The ID of the group to delete</param>
        /// <returns>Success status</returns>
        [HttpDelete("email-groups/{groupId}")]
        [UserFeatureAuthorize((int)Features.RepositoryConfiguration)]
        public async Task<IActionResult> DeleteEmailGroup(int groupId)
        {
            try
            {
                if (groupId <= 0)
                {
                    return BadRequest("Invalid group ID");
                }

                var userId = _helperService.GetCurrentUserId(User);
                var success = await _emailGroupService.DeleteEmailGroupAsync(groupId, userId);

                if (!success)
                {
                    return NotFound($"Email group with ID {groupId} not found");
                }

                return Ok(new { message = "Email group deleted successfully" });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception)
            {
                return StatusCode(500, new { message = "An error occurred while deleting the email group" });
            }
        }

        /// <summary>
        /// Checks if a group name is a duplicate for the current user.
        /// </summary>
        /// <param name="dto">The DTO containing the group name and optional group ID.</param>
        /// <returns>A boolean indicating whether the group name is a duplicate.</returns>
        [HttpPost("email-groups/check-duplicate-name")]        
        [UserFeatureAuthorize((int)Features.RepositoryConfiguration)]
        public async Task<IActionResult> CheckDuplicateGroupName(CheckGroupNameDto dto)
        {
            if (!ModelState.IsValid) return BadRequest(ModelState);
                  
            var isDuplicateName = await _emailGroupService.CheckDuplicateName(dto.GroupName, dto.GroupId);
            
            return Ok(new { isDuplicateName });
        }
    }
}